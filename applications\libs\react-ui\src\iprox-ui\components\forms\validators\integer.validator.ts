import { ValidationRule, ValidationRuleType, ValidatorFn, ValidatorFnFactory } from '../models/validator.models';

export interface IntegerOnlyValidationRule extends ValidationRule<ValidationRuleType.IntegerOnly> {
  ruleValue: 'IntegerOnly';
}

export const isIntegerOnlyRule = (rule: ValidationRule): rule is IntegerOnlyValidationRule => {
  return rule.ruleType === ValidationRuleType.IntegerOnly;
};

export const integerOnlyValidatorFactory: ValidatorFnFactory<string> = (_ruleValue): ValidatorFn => {
  return ({ value }) => {
    if (typeof value !== 'number') {
      return null;
    }

    if (!Number.isInteger(value)) {
      return { integerOnly: true };
    }

    return null;
  };
};

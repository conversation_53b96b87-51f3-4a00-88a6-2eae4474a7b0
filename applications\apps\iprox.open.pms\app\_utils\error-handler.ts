import { components } from '@/iprox-open.interface';
import { AxiosError } from 'axios';
import { HTTPError } from 'ky';

export const getErrorMessages = async (error: unknown): Promise<string | string[]> => {
  if (error instanceof AxiosError) {
    const data: components['schemas']['ProblemDetails'] = error.response?.data;

    if (data?.detail) {
      return data.detail;
    }

    if (data?.errors) {
      const errors = data.errors as Record<string, string[]>;
      return Object.keys(errors).flatMap((key) => errors[key]);
    }
  }

  if (error instanceof HTTPError) {
    const data: components['schemas']['ProblemDetails'] = await error.response?.json();

    if (data?.detail) {
      return data.detail;
    }

    if (data?.errors) {
      const errors = data.errors as Record<string, string[]>;
      return Object.keys(errors).flatMap((key) => errors[key]);
    }
  }

  return 'UnknownError';
};

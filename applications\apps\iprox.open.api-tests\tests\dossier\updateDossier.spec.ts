import { colors } from '../../config';
import * as http from '../../enums/httpEnums';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';
import { DateTime } from '../../utils/getCurrentDateTime';

let dossierId: string;
let dynamicFieldValues: any;
let bearerToken: any;
let dossierCategoryId: string | null;

describe('Update Dossier Test', () => {
  beforeAll(async () => {
    bearerToken = await loginAndGetToken();
    dossierCategoryId = await DossierHelpersNew.getRandomCategoryUuid(false, bearerToken);

    const dossierResponse: any = await DossierHelpersNew.createDossier(
      `update${DateTime.getCurrentDateTimeIsoFormat()}`,
      bearerToken,
      dossierCategoryId
    );
    dossierId = await dossierResponse.body.dossier.dossierId;
    dynamicFieldValues = await dossierResponse.body.dossier.dynamicFieldValues;
  }, 90000);

  it('should update the dossier', async () => {
    // verify dynamic field values are empty before updating
    expect(await dynamicFieldValues).toHaveLength(0);

    // update dossier
    const dossierUpdateResponse = await DossierHelpersNew.updateDossier(dossierId, bearerToken);

    // verify dynamic field values are not empty after updating
    expect(await dossierUpdateResponse.status).toBe(http.StatusCode.OK_200);
    expect(await dynamicFieldValues).not.toBeNull();
  }, 90000);

  afterAll(async () => {
    await DossierHelpersNew.deleteDossier(dossierId, bearerToken);
  });
});

export type MenuIcon = React.ForwardRefExoticComponent<
  React.PropsWithoutRef<React.SVGProps<SVGSVGElement>> & {
    title?: string;
    titleId?: string;
  } & React.RefAttributes<SVGSVGElement>
>;

export interface MenuItem {
  name: string;
  icon?: React.ReactElement<React.SVGProps<SVGSVGElement>>;
  route: string;
  subMenu?: MenuItem[];
  activeRouteMatch?: {
    /** When set to `exact` `match` has to exactly match the pathname. Defaults to `starts-with` */
    mode?: 'exact' | 'starts-with';
    /** string used to match the route pathname */
    match: string;
  };
}

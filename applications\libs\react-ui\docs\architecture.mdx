import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@storybook/blocks';

<Meta title="Resources" />

# Architecture

## NextJS

NextJS

## CSS-variables

Please refer to the Design Tokens documentation.

## TailwindCSS

iprox.open is built using TailwindCSS. Tailwind is configured to be more opinionated, colors are named `primary` and `secondary` instead of `orange` and `blue`. Using tailwind we have complete control over the HTML and CSS while still being able to take a headstart with third-party components. Excellent third-party components include:

- [tailwindUI](https://tailwindui.com/)
- [flowbite](https://flowbite.com/)
- [Tailwind-elements](https://tailwind-elements.com/)

## Libraries

Forms are managed using the [Formik](https://formik.org/) library.

- headlessUI
- Heroicons
- Formik

## Codestyle

- eslint
- stylelint
- prettier

## Resources

Other interesting resources for developing components:
[open-ui](https://open-ui.org/)

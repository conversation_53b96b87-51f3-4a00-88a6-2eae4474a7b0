import type { Meta, StoryObj } from '@storybook/react';

import { ButtonGroup } from './button-group';

const meta: Meta<typeof ButtonGroup> = {
  title: 'iprox-ui/components/button-group',
  component: ButtonGroup,
};

export default meta;
type Story = StoryObj<typeof ButtonGroup>;

const OPTIONS = [
  {
    text: 'Verwijderen',
    onClick: () => null,
  },
  {
    text: 'Publiceren',
    disabled: true,
    onClick: () => null,
  },
  {
    text: 'Hide',
    onClick: () => null,
  },
];

export const Default: Story = {
  name: 'default',
  args: {
    options: OPTIONS,
  },
};

export const Disabled: Story = {
  name: 'disabled',
  args: {
    label: 'Actie',
    options: OPTIONS,
    disabled: true,
  },
};

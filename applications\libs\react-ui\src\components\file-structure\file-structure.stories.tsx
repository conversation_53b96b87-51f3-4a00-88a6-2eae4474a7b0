import { components } from '@/iprox-open.interface';
import { useArgs } from '@storybook/preview-api';
import type { Meta, StoryObj } from '@storybook/react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Button } from '../button/button';
import { FileStructure } from './file-structure';
import { fileStructurechildren } from './file-structure-children.content';
import { rootFolderNode } from './file-structure.content';
import { FolderNode, Node } from './models/file-structure';
import { mapNode, removeNodesById, updateNodeAndChildren } from './utils/file-structure-utils';

const meta: Meta<typeof FileStructure> = {
  title: 'components/file-structure',
  component: FileStructure,
  argTypes: {
    onSelectItem: {
      action: 'onClick',
      table: {
        disable: true,
      },
    },
    onFileClick: {
      action: 'onClick',
      table: {
        disable: true,
      },
    },
    onLoadChildren: {
      action: 'onClick',
      table: {
        disable: true,
      },
    },
    onToggleFolderNode: {
      action: 'clicked',
      table: {
        disable: true,
      },
    },
  },
};

const TemplateFileStructure = ({
  rootNode,
  selected,
  onSelectItem,
  onLoadChildren,
}: {
  rootNode: FolderNode;
  selected: Node[];
  onSelectItem: (updatedRootNode: FolderNode, selectedNodes: Node[]) => void;
  onLoadChildren: (updatedRootNode: FolderNode) => void;
}) => {
  const t = useTranslations('components.fileStructure');
  const [selectedNodes, setSelectedNodes] = useState<Node[]>(selected);
  const [rootFolderNode, setRootFolderNode] = useState<FolderNode | null>(rootNode);

  const getNodeChildren = (nodeId: string) => {
    return (fileStructurechildren as Record<string, Node[]>)[nodeId];
  };

  const handleSelectItems = (updatedRoot: FolderNode, items: Node[]) => {
    setRootFolderNode(updatedRoot);
    setSelectedNodes([...items]);
    onSelectItem(updatedRoot, items);
  };

  const handleOnLoadChildren = async (item: FolderNode) => {
    try {
      const chidren = await getNodeChildren(item.nodeId);

      item.children = chidren.map<Node>((child) =>
        mapNode(child as unknown as components['schemas']['DossierFileStructureViewDto'], item)
      );

      if (rootFolderNode) {
        const updatedRootNode = updateNodeAndChildren(rootFolderNode, item.nodeId, item);
        setRootFolderNode(updatedRootNode as FolderNode);
        onLoadChildren(updatedRootNode as FolderNode);
      }
    } catch (_error) {
      new Error('Loading children error');
    }
  };

  const handleOnToggleFolderNode = (item: FolderNode) => {
    const updatedNode: FolderNode = {
      ...item,
      isExpanded: !item.isExpanded,
    };

    if (rootFolderNode) {
      const updatedRootNode = updateNodeAndChildren(rootFolderNode, item.nodeId, updatedNode);
      setRootFolderNode(updatedRootNode as FolderNode);
    }
  };

  const deleteSelectedItems = () => {
    if (rootFolderNode && selectedNodes.length) {
      const selectedNodeIds = selectedNodes.map((node) => node.nodeId);
      const updatedRoot = removeNodesById(rootFolderNode, selectedNodeIds);
      setRootFolderNode({ ...updatedRoot });
    }
  };

  return (
    <>
      <div className="grid grid-flow-col justify-end gap-2">
        <Button type="button" variant="secondary" onClick={deleteSelectedItems}>
          {t('delete')}
        </Button>
      </div>
      {rootFolderNode && (
        <FileStructure
          rootNode={rootFolderNode}
          onLoadChildren={handleOnLoadChildren}
          onToggleFolderNode={handleOnToggleFolderNode}
          onSelectItem={handleSelectItems}
          onFileClick={(_item: Node) => null}
          selectedNodes={selectedNodes}
          setRootFolderNode={setRootFolderNode}
        />
      )}
    </>
  );
};

export default meta;
type Story = StoryObj<typeof FileStructure>;

const templateStory: Story = {
  render: (args) => {
    const [_, updateArgs] = useArgs();

    const handleSelectItems = (updatedRoot: FolderNode, selectedNodes: Node[]) => {
      updateArgs({ rootNode: { ...updatedRoot } });
      updateArgs({ selectedNodes: [...selectedNodes] });
    };

    const handleOnLoadChildren = (updatedRoot: FolderNode) => {
      updateArgs({ rootNode: { ...updatedRoot } });
    };

    return (
      <TemplateFileStructure
        rootNode={args.rootNode}
        selected={args.selectedNodes}
        onSelectItem={handleSelectItems}
        onLoadChildren={handleOnLoadChildren}
      />
    );
  },
};

export const Default: Story = {
  ...templateStory,
  args: {
    rootNode: rootFolderNode,
    selectedNodes: [],
  },
};

'use client';

import { useClientApi } from '@/http/fetcher-api.client';
import { CommonPageDto } from '@/models/page-dto.model';
import { getPagesPaged, managePage } from '@/services/page-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { usePageAction } from '@/utils/page-action-utils';
import { Button, ButtonGroup, Pagination, showToast, useConfirmDialog } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { NewPageModal } from '@/components/new-page-modal';
import { PageHeader } from '@/components/page-header';
import { PagesTableData } from '@/components/pages-table-data';

interface PagesListProps {
  pages: CommonPageDto[];
  count: number;
  start: number;
  totalCount: number;
}

export function PagesList({ pages, count, start, totalCount }: PagesListProps) {
  const t = useTranslations('pages');
  const clientApi = useClientApi();
  const { showDialog } = useConfirmDialog();
  const { displaySuccessToast, getConfirmationMessage } = usePageAction();

  const [selectedPage, setSelectedPage] = useState<CommonPageDto | null>(null);
  const [isNewPageModalOpen, setIsNewPageModalOpen] = useState(false);
  const [updatedPages, setUpdatedPages] = useState<CommonPageDto[]>(pages);
  const [isActionProcessing, setIsActionProcessing] = useState(false);

  const renderTableHeader = () => {
    const headings: {
      label: string;
      style?: React.CSSProperties;
    }[] = [
      {
        label: '', // checkbox column
        style: {
          flex: 1 / 5,
          maxWidth: '128px',
        },
      },
      {
        label: t('title'),
        style: {
          flex: 2 / 5,
        },
      },
      {
        label: t('published'),
        style: {
          flex: 1 / 5,
        },
      },
      {
        label: t('type'),
        style: {
          flex: 1 / 5,
        },
      },
    ];
    return headings.map((heading, index) => (
      <th key={index} style={heading.style}>
        {heading.label}
      </th>
    ));
  };

  const handleOnConfirm = async (action: 'delete' | 'publish' | 'unpublish') => {
    if (!selectedPage) {
      return;
    }

    try {
      setIsActionProcessing(true);
      await managePage(
        clientApi,
        selectedPage.id,
        selectedPage.pageType as 'Search' | 'Announcement' | 'Simple',
        action
      );
      displaySuccessToast(action);
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    } finally {
      setIsActionProcessing(false);
    }

    setSelectedPage(null);
    getUpdatedPages();
  };

  const handleAction = async (action: 'delete' | 'publish' | 'unpublish') => {
    showDialog({
      message: getConfirmationMessage(action),
      onConfirm: () => handleOnConfirm(action),
    });
  };

  const getUpdatedPages = async () => {
    const pagesData = await getPagesPaged(clientApi, {
      count: count.toString(),
      start: start.toString(),
    });
    setUpdatedPages((pagesData.items as CommonPageDto[]) ?? []);
  };

  useEffect(() => {
    setUpdatedPages(pages);
  }, [pages]);

  return (
    <>
      <PageHeader title={t('heading')} />
      <div className="border-content-extra-lite rounded-medium overflow-hidden border">
        <table className="w-full">
          <thead>
            <tr className="bg-base-10 font-text text-base-100 flex h-[50px] items-center gap-x-4 text-left text-sm font-bold">
              {renderTableHeader()}
            </tr>
          </thead>
          <tbody>
            <PagesTableData pages={updatedPages} selectedPage={selectedPage} setSelectedPage={setSelectedPage} />
          </tbody>
        </table>
      </div>

      <div className={`mb-7 flex flex-row justify-center ${totalCount !== 0 ? 'mt-10' : ''}`}>
        <Pagination count={count} start={start} totalCount={totalCount} maxVisiblePages={5} />
      </div>

      <div className="mb-20 mt-10 flex flex-row gap-x-5">
        <Button variant="primary" icon="PlusSmallIcon" onClick={() => setIsNewPageModalOpen(true)}>
          {t('addAPage')}
        </Button>
        <ButtonGroup
          label={t('action')}
          options={[
            {
              text: t('delete'),
              type: 'button',
              onClick: () => handleAction('delete'),
            },
            {
              text: t('publish'),
              type: 'button',
              onClick: () => handleAction('publish'),
              disabled: selectedPage?.pageState === 'Published',
            },
            {
              text: t('hide'),
              type: 'button',
              onClick: () => handleAction('unpublish'),
              disabled: selectedPage?.pageState === 'Unpublished',
            },
          ]}
          disabled={selectedPage === null || isActionProcessing}
        />
      </div>

      <NewPageModal isOpen={isNewPageModalOpen} onClose={() => setIsNewPageModalOpen(false)} />
    </>
  );
}

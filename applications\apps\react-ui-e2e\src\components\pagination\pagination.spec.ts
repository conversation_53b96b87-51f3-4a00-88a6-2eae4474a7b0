import AxeBuilder from '@axe-core/playwright';
import { expect, test } from '@playwright/test';

import { loadStory } from '../../utils/common-utils';

type PaginationArgs = {
  count: number;
  start: number;
  maxVisiblePages?: number;
  totalCount?: number;
};

const storyId = 'iprox-ui-components-pagination--default';

test.describe('<Pagination />', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should disable previous button on first page', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 0,
    });

    const prevButton = page.getByRole('link', { name: 'previous page' });
    expect(await prevButton.getAttribute('aria-disabled')).toBe('true');
  });

  test('should enable the previous button on second page', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 10,
    });

    const prevButton = page.getByRole('link', { name: 'previous page' });
    expect(await prevButton.getAttribute('aria-disabled')).toBe('false');
  });

  test('should disable next button on last page', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 140,
    });

    const nextButton = page.getByRole('link', { name: 'next page' });
    expect(await nextButton.getAttribute('aria-disabled')).toBe('true');
  });

  test('should enable the next button on the page preceding the last page', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 130,
    });

    const prevButton = page.getByRole('link', { name: 'next page' });
    expect(await prevButton.getAttribute('aria-disabled')).toBe('false');
  });

  test('should display the correct number of page links based on maxVisiblePages', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 0,
      maxVisiblePages: 7,
    });

    expect(await page.getByRole('link', { name: /Go to page/ }).count()).toBe(5);
  });

  test('should display the correct number of page links when totalPages is lower than maxVisiblePages', async ({
    page,
  }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 0,
      maxVisiblePages: 7,
      totalCount: 50,
    });

    expect(await page.getByRole('link', { name: /Go to page/ }).count()).toBe(4);
  });

  test('should navigate to specific page', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 30,
    });

    const pageLink = page.getByText('4');
    expect(await pageLink.getAttribute('aria-current')).toBe('page');
  });

  test('should always display the first page and last page', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 30,
    });

    expect(
      await page
        .getByText('1', {
          exact: true,
        })
        .isVisible()
    ).toBe(true);
    expect(
      await page
        .getByText('15', {
          exact: true,
        })
        .isVisible()
    ).toBe(true);
  });

  test('should display ellipsis after the first page and before the last page', async ({ page }) => {
    await loadStory<PaginationArgs>(page, storyId, {
      count: 10,
      start: 30,
    });

    expect(await page.getByText('...').count()).toBe(2);
  });
});

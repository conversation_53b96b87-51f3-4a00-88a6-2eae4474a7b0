import request from 'supertest';

import { apiURL } from '../../config';
import * as api from '../../enums/apiEnums';
import * as http from '../../enums/httpEnums';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';
import { Helpers } from '../../utils/getRandomIndex';

describe('GET /', () => {
  let bearerToken: any;
  beforeAll(async () => {
    bearerToken = await loginAndGetToken();
  }, 90000);

  it('should return a dossier category object', async () => {
    const response = await request(apiURL)
      .get(api.EndpointsDossierCategory.GET_DOSSIER_CATEGORIES)
      .set('Authorization', `Bearer ${bearerToken}`);

    console.log(response.body.dossierCategories.length);
    const randomIndex = Helpers.getRandomIndex(response.body.dossierCategories);

    const randomDossierCategoryID = response.body.dossierCategories[randomIndex].id;
    console.log(randomDossierCategoryID);

    expect(response.status).toBe(http.StatusCode.OK_200);
  }, 90000);
});

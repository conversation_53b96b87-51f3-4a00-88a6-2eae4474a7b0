import { useDossierUploadService } from '@/context/dossier-upload-context';
import { ButtonGroup } from '@iprox/iprox-ui';
import { DossierStatus } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';
import React from 'react';

interface DossierStatusControlsProps {
  dossierId: string;
  formId: string;
  disabled: boolean;
  status: DossierStatus;
  handleUnpublishDossier: () => void;
  handleDeleteDossier: () => void;
  setIsSaveAndPublish: React.Dispatch<React.SetStateAction<boolean>>;
}

export function DossierStatusControls({
  dossierId,
  formId,
  disabled,
  status,
  handleUnpublishDossier,
  handleDeleteDossier,
  setIsSaveAndPublish,
}: DossierStatusControlsProps) {
  const t = useTranslations('dossier');

  const { dossierUploads } = useDossierUploadService(dossierId);

  const hasIncompleteUpload = dossierUploads.some((uploadItem) => {
    const uploadState = Object.values(uploadItem)[0];
    return uploadState.status !== 'uploadComplete';
  });

  return (
    <>
      <div className="mt-4">
        <ButtonGroup
          label={t('action')}
          options={[
            {
              text: t('save'),
              type: 'submit',
              form: formId,
              onClick: () => {
                setIsSaveAndPublish(false);
              },
            },
            {
              text: t('publish'),
              type: 'submit',
              form: formId,
              onClick: () => {
                setIsSaveAndPublish(true);
              },
            },
            {
              text: t('unpublish'),
              onClick: handleUnpublishDossier,
              type: 'button',
              disabled: status !== 'Published',
            },
            {
              text: t('delete'),
              onClick: handleDeleteDossier,
              type: 'button',
            },
          ]}
          disabled={disabled || hasIncompleteUpload}
        />
      </div>
    </>
  );
}

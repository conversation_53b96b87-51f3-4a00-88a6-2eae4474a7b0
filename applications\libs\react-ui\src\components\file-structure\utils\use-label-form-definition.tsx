import { useMemo } from 'react';

import { FieldDefinition, FieldType } from '../../forms/models/form.models';
import { ValidationRuleType } from '../../forms/models/validator.models';

export function useLabelFormDefinition(label: string) {
  const labelDefinition = useMemo<FieldDefinition<FieldType.Text, string>>(
    () => ({
      name: 'name',
      label: '',
      fieldType: FieldType.Text,
      value: label,
      validationRules: [
        {
          ruleType: ValidationRuleType.RequiredProperty,
          ruleValue: {},
        },
        {
          ruleType: ValidationRuleType.ValidateStringLength,
          ruleValue: { minimumLength: 1, maximumLength: 256 },
        },
        {
          ruleType: ValidationRuleType.ValidateDisallowedCharacters,
          ruleValue: {
            regExp: new RegExp('[<>:"/\\\\|?*]|^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\\.|$)|[ .]$'),
          },
        },
      ],
    }),
    [label]
  );

  return [labelDefinition];
}

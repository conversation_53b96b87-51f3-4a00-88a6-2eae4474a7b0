import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { WooIndexSearchParams } from '@/models/search.model';

export async function getWooCategories(): Promise<components['schemas']['GetWooCategoriesResponse']> {
  return await serverApi.get(`woo-categories`).json<components['schemas']['GetWooCategoriesResponse']>();
}

export async function getSitemap(params: WooIndexSearchParams) {
  try {
    const paramString = new URLSearchParams(
      Object.entries(params).reduce<Record<string, string>>((acc, [key, value]: [string, string | undefined]) => {
        if (value) {
          acc[key] = value;
        }
        return acc;
      }, {})
    ).toString();

    return await serverApi.get(`sitemap?${paramString}`);
  } catch (error) {
    return Promise.reject(error);
  }
}

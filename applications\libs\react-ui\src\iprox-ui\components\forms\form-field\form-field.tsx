import cx from 'classnames';
import { useTranslations } from 'next-intl';

import { DescriptionAttributes, ErrorMessageAttributes, LabelAttributes } from '../hooks/use-form-field.hook';
import { FieldDefinition, FieldType, ValueTypes } from '../models/form.models';
import { ValidationMessageTypes } from '../models/validator.models';
import { useValidationRuleValues } from '../utils/get-validation-rule-values.hook';

export interface FormFieldProps {
  children: React.ReactNode;
  definition: FieldDefinition<FieldType, ValueTypes>;
  labelProps: LabelAttributes;
  descriptionProps?: DescriptionAttributes;
  errorMessage?: string;
  errorMessageProps?: ErrorMessageAttributes;
  hideLabel?: boolean;
  labelColor?: string;
  descriptionColor?: string;
}

export function FormField({
  children,
  definition,
  labelProps,
  descriptionProps,
  errorMessage,
  errorMessageProps,
  labelColor,
  descriptionColor,
}: FormFieldProps) {
  const t = useTranslations('components.validation');

  const { min, max } = useValidationRuleValues(definition.validationRules, definition.fieldType);

  return (
    <>
      {definition.label && (
        <label
          {...labelProps}
          className={cx(
            'font-heading mb-1 inline-block text-lg font-bold leading-none',
            {
              hidden: definition.hideLabel,
            },
            labelColor || 'text-heading'
          )}
        >
          {definition.label}
        </label>
      )}
      {definition.description && (
        <div {...descriptionProps} className={cx('font-text mb-3 text-sm', descriptionColor || 'text-base-75')}>
          {definition.description}
        </div>
      )}
      <div className="relative">{children}</div>
      {errorMessage && (
        <p className="font-text text-error mt-1.5 text-sm" {...errorMessageProps}>
          {t(errorMessage as ValidationMessageTypes, {
            fieldName: definition.label,
            min,
            max,
          })}
        </p>
      )}
    </>
  );
}

'use client';

import { Button } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function NewDossierCta() {
  const router = useRouter();
  const t = useTranslations('dashboard');

  const [disabled, setDisabled] = useState(false);

  const handleButtonClick = () => {
    if (!disabled) {
      setDisabled(true);
    }
    router.push('/dossier/new');
  };

  return (
    <Button variant="primary" type="button" onClick={handleButtonClick} icon="PlusCircleIcon" disabled={disabled}>
      {t('cta')}
    </Button>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-isomorphic-layout-effect";
exports.ids = ["vendor-chunks/use-isomorphic-layout-effect"];
exports.modules = {

/***/ "(ssr)/../../node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nvar index = typeof document !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (index);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3VzZS1pc29tb3JwaGljLWxheW91dC1lZmZlY3QvZGlzdC91c2UtaXNvbW9ycGhpYy1sYXlvdXQtZWZmZWN0LmVzbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7O0FBRW5ELDhDQUE4QyxrREFBZSxHQUFHLDRDQUFTOztBQUV6RSxpRUFBZSxLQUFLLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL3VzZS1pc29tb3JwaGljLWxheW91dC1lZmZlY3QvZGlzdC91c2UtaXNvbW9ycGhpYy1sYXlvdXQtZWZmZWN0LmVzbS5qcz9mNGNiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUxheW91dEVmZmVjdCwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG52YXIgaW5kZXggPSB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnID8gdXNlTGF5b3V0RWZmZWN0IDogdXNlRWZmZWN0O1xuXG5leHBvcnQgZGVmYXVsdCBpbmRleDtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\n");

/***/ })

};
;
'use client';

import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { downloadZip, getNodeChildren } from '@/services/file-service';
import { useAppSettings } from '@/services/settings.context';
import { getErrorMessages } from '@/utils/error-handler';
import {
  Button,
  FieldAlias,
  FieldType,
  FileNode,
  FileStructure,
  FolderNode,
  Image,
  Node,
  mapFolderNode,
  mapNode,
  showToast,
  updateNodeAndChildren,
} from '@iprox/react-ui';
import { PublicReviewPeriodContainer } from '@iprox/react-ui';
import saveAs from 'file-saver';
import { useTranslations } from 'next-intl';
import React, { useMemo, useState } from 'react';

import { DossierDynamicFields } from '@/components/dossier-dynamic-fields';

interface DossierInfoProps {
  dossier: components['schemas']['PublicDossierDto'];
}

export function DossierInfo({ dossier }: DossierInfoProps) {
  const t = useTranslations('dossierInfo');
  const settings = useAppSettings();

  const clientApi = useClientApi();
  const [selectedNodes, setSelectedNodes] = useState<Node[]>([]);

  const mapRootNode = (apiRootNode: components['schemas']['DossierFileStructureViewDto']): FolderNode => {
    return mapFolderNode(apiRootNode);
  };

  const [rootFolderNode, setRootFolderNode] = useState<FolderNode | null>(
    dossier?.rootFolderNode ? mapRootNode(dossier.rootFolderNode) : null
  );

  /**
   * handle selecting items from the file structure
   * @param updatedRoot FolderNode
   * @param items string[]
   */
  const handleSelectItems = (updatedRoot: FolderNode, items: Node[]) => {
    setRootFolderNode(updatedRoot);
    setSelectedNodes([...items]);
  };

  /**
   * get the children from the api call and update the root node
   * @param item FolderNode
   */
  const handleOnLoadChildren = async (item: FolderNode): Promise<void> => {
    try {
      const response = await getNodeChildren(clientApi, item.nodeId);

      item.children = response.nodes.map<Node>((child) => mapNode(child, item));

      if (rootFolderNode) {
        const updatedRootNode = updateNodeAndChildren(rootFolderNode, item.nodeId, item);
        setRootFolderNode(updatedRootNode as FolderNode);
      }
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    }
  };

  /**
   * toggle folder node which already got children from api call
   * @param item FolderNode
   */
  const handleOnToggleFolderNode = (item: FolderNode) => {
    const updatedNode: FolderNode = {
      ...item,
      isExpanded: !item.isExpanded,
    };

    if (rootFolderNode) {
      const updatedRootNode = updateNodeAndChildren(rootFolderNode, item.nodeId, updatedNode);
      setRootFolderNode(updatedRootNode as FolderNode);
    }
  };

  /**
   * download clicked file
   * @param item FileNode
   */
  const downloadFileNode = async (item: FileNode) => {
    if (item.nodeName) {
      try {
        saveAs(`/public/download/${item.nodeId}`);
      } catch (error) {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      }
    }
  };

  /**
   * download selected multiple nodes
   */
  const downloadSelectedNodes = async () => {
    if (selectedNodes.length) {
      try {
        await downloadZip(clientApi, dossier.dossierId, { nodeIds: selectedNodes.map(({ nodeId }) => nodeId) });
      } catch (error) {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      }
    }
  };

  const { fromDate, toDate } = useMemo(() => {
    const publicReviewPeriod = dossier?.dynamicFieldValues.find(
      (field) => field.alias === FieldAlias.PublicReviewPeriod
    );
    if (!publicReviewPeriod) {
      return { fromDate: null, toDate: null };
    }

    const fromDate = new Date(publicReviewPeriod.value?.start as string);
    const toDate = new Date(publicReviewPeriod.value?.end as string);

    return { fromDate, toDate };
  }, [dossier?.dynamicFieldValues]);

  const hasValidPublicationReviewPeriod = useMemo(
    () => (fromDate?.getTime() ?? 0) > 0 && (toDate?.getTime() ?? 0) < new Date('9999-12-31').getTime(),
    [fromDate, toDate]
  );

  const dossierDynamicFields =
    dossier?.category?.dynamicFields.filter((field) => field.alias !== FieldAlias.PublicReviewPeriod) ?? [];
  const dossierDynamicFieldValues =
    dossier?.dynamicFieldValues.filter((field) => field.alias !== FieldAlias.PublicReviewPeriod) ?? [];

  return (
    <div>
      <h1 className="font-heading text-heading mb-8 hyphens-auto break-words text-3xl font-bold">
        {dossier?.title ?? ''}
      </h1>
      {dossier.decorativeImageNode?.fullName && (
        <div className="relative mb-8 h-60">
          <Image
            src={`${settings.apiUrl}/dossier${dossier.decorativeImageNode?.fullName}`}
            alt={t('dossierImageAlt', { dossierTitle: dossier.title })}
            fill
            className="h-auto w-full object-cover object-center"
          />
        </div>
      )}
      {hasValidPublicationReviewPeriod && <PublicReviewPeriodContainer fromDate={fromDate} toDate={toDate} />}
      {(dossier?.category?.dynamicFields[0]?.dynamicFieldType as FieldType) !== FieldType.RichText && (
        <div
          dangerouslySetInnerHTML={{
            __html: dossier?.summary ?? '',
          }}
          className="font-text"
        ></div>
      )}
      <div className="mb-12 mt-8">
        <DossierDynamicFields dynamicFields={dossierDynamicFields} dynamicFieldValues={dossierDynamicFieldValues} />
      </div>
      {rootFolderNode && (
        <>
          <div className="mb-2 grid grid-flow-col justify-end">
            <Button variant="secondary" onClick={downloadSelectedNodes} disabled={selectedNodes.length <= 0}>
              {t('downloadSelected')}
            </Button>
          </div>
          <div className="border-base-25 rounded-input h-full w-full border p-6">
            <FileStructure
              rootNode={rootFolderNode}
              onFileClick={downloadFileNode}
              onLoadChildren={handleOnLoadChildren}
              onToggleFolderNode={handleOnToggleFolderNode}
              onSelectItem={handleSelectItems}
              setRootFolderNode={setRootFolderNode}
              selectedNodes={selectedNodes}
            />
          </div>
        </>
      )}
    </div>
  );
}

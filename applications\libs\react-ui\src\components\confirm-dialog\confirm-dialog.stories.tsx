import type { Meta, StoryObj } from '@storybook/react';

import { Button } from '../../iprox-ui';
import { ConfirmDialog } from './confirm-dialog';
import { ConfirmDialogProvider, useConfirmDialog } from './context/confirm-dialog.context';

const TemplateDialog = ({ title, message }: { title: string; message: string }) => {
  const { showDialog } = useConfirmDialog();

  return (
    <Button
      type="button"
      onClick={() => {
        showDialog({
          title,
          message,
          onConfirm: () => null,
        });
      }}
      variant="primary"
    >
      show dialog
    </Button>
  );
};

const meta: Meta<typeof ConfirmDialog> = {
  component: ConfirmDialog,
  title: 'components/confirm-dialog',
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof TemplateDialog>;

export const Base: Story = {
  name: 'default',
  args: {
    title: 'Warning!',
    message: 'Are you sure you want to perform this action?',
  },
  render: (args) => {
    return (
      <ConfirmDialogProvider>
        <TemplateDialog title={args.title} message={args.message} />
      </ConfirmDialogProvider>
    );
  },
  decorators: [
    (Story) => (
      <div className="w-100 flex flex-row justify-center p-10">
        <Story />
      </div>
    ),
  ],
};

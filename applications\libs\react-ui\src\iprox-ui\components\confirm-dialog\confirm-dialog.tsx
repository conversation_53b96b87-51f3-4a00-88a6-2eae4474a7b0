import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { Text } from '../../../components/text/text';
import { Button } from '../button/button';
import { Modal } from '../modal/modal';
import { useConfirmDialog } from './context/confirm-dialog.context';

export function ConfirmDialog() {
  const [disabled, setDisabled] = useState(false);
  const t = useTranslations('components.confirmModal');

  const { state, closeDialog } = useConfirmDialog();

  const handleConfirm = () => {
    setDisabled(true);
    state.onConfirm();
    closeDialog();
  };

  const handleCancel = () => {
    closeDialog();
    state.onCancel?.();
  };

  useEffect(() => {
    if (state.isOpen) {
      setDisabled(false);
    }
  }, [state.isOpen]);

  return (
    <Modal isOpen={state.isOpen ?? false} onClose={handleCancel}>
      <div>
        <Text className="font-heading text-primary-content mb-10 max-w-lg text-center text-lg font-bold">
          {state.message}
        </Text>
        <div className="flex flex-col items-center gap-y-5 lg:flex-row lg:justify-center lg:gap-x-10">
          <Button type="button" variant="primary" onClick={handleConfirm} disabled={disabled}>
            {t('confirm')}
          </Button>
          <Button type="button" variant="secondary" onClick={handleCancel} disabled={disabled}>
            {t('cancel')}
          </Button>
        </div>
      </div>
    </Modal>
  );
}

import ChevronDownIcon from '@heroicons/react/24/outline/ChevronDownIcon';
import { Meta, StoryObj } from '@storybook/react';

import { Dropdown, DropdownMenuItem } from './dropdown';

const menu: DropdownMenuItem[] = [
  {
    label: 'Label 1',
    route: '/label-1',
  },
  {
    label: 'Label 2',
    route: '/label-2',
  },
  {
    label: 'Label 3',
    route: '/label-3',
  },
];

const meta: Meta<typeof Dropdown> = {
  title: 'components/dropdown',
  component: Dropdown,
  argTypes: {
    children: {
      control: 'text',
    },
  },
};

export default meta;

type Story = StoryObj<typeof Dropdown>;

export const Default: Story = {
  name: 'default',
  args: {
    menu,
    children: (
      <div className="flex-raw border-base-75 rounded-input flex items-center border px-3 py-1">
        Click Me
        <ChevronDownIcon className="ml-1.5 mt-0.5 h-4 w-4 text-gray-400" aria-hidden="true" />
      </div>
    ),
  },
  decorators: [
    (Story) => (
      <div className="w-100 flex flex-row justify-center">
        <Story />
      </div>
    ),
  ],
};

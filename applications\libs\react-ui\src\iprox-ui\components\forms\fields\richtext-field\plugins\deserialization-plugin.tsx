import { $generateNodesFromDOM } from '@lexical/html';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getRoot, $insertNodes } from 'lexical';
import { useRef } from 'react';

export class DeserializationError extends Error {
  constructor(msg: string) {
    super(msg);
    Object.setPrototypeOf(this, DeserializationError.prototype);
  }
}

interface DeserializationPluginProps {
  value: string;
}

function DeserializationPlugin({ value }: DeserializationPluginProps): null {
  const [editor] = useLexicalComposerContext();
  const hasRun = useRef(false);

  if (!hasRun.current) {
    editor.update(() => {
      try {
        const parser = new DOMParser();
        const dom = parser.parseFromString(value, 'text/html');

        const nodes = $generateNodesFromDOM(editor, dom);

        $getRoot().select();

        $insertNodes(nodes);
      } catch (e) {
        throw new DeserializationError('Deserialization Error');
      }
    });
    hasRun.current = true;
  }

  return null;
}

export default DeserializationPlugin;

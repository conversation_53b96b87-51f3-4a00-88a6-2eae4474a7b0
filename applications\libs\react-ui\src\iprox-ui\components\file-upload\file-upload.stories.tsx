import type { Meta, StoryObj } from '@storybook/react';

import { FileUpload } from './file-upload';

const meta: Meta<typeof FileUpload> = {
  title: 'iprox-ui/components/file-upload',
  component: FileUpload,
  argTypes: {
    onFileSelect: {
      action: 'onFileSelect',
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof FileUpload>;

export const Default: Story = {
  name: 'default',
  args: {
    allowedFileTypes: [
      'zip',
      'application/octet-stream',
      'application/zip',
      'application/x-zip',
      'application/x-zip-compressed',
    ],
  },
};

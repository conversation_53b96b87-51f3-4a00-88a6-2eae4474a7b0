import AxeBuilder from '@axe-core/playwright';
import { expect, test } from '@playwright/test';

import { loadStory } from '../../utils/common-utils';

const storyId = 'components-file-structure--default';

test.describe('<FileStructure />', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await loadStory(page, storyId);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should display the file structure with passed root node', async ({ page }) => {
    await loadStory(page, storyId);

    /** labels to check */
    const labelsToCheck = ['A. Zakelijke beschrijving', 'Beterschap', 'E. Grondtekeningen', 'sample.pdf'];

    for (const label of labelsToCheck) {
      await expect(page.getByText(`${label}`)).toBeVisible();
    }
  });

  test('should expand the folder node, if clicked', async ({ page }) => {
    await loadStory(page, storyId);

    const folderNode = page.getByRole('button', { name: 'Folder A. Zakelijke beschrijving' });

    const isInitiallyExpanded = await folderNode?.getAttribute('aria-expanded');

    await folderNode?.click();
    await page.waitForTimeout(1000);

    const isUpdatedExpanded = await folderNode?.getAttribute('aria-expanded');

    expect(isInitiallyExpanded).toBe('false');
    expect(isUpdatedExpanded).toBe('true');
  });

  test('should collapse the folder node, if clicked on a expanded folder node', async ({ page }) => {
    await loadStory(page, storyId);

    const folderNode = page.getByRole('button', { name: 'Folder A. Zakelijke beschrijving' });

    /** expand the folder node first */
    await folderNode?.click();
    await page.waitForTimeout(1000);

    const isInitiallyExpanded = await folderNode?.getAttribute('aria-expanded');

    /** collapse the folder node */
    await folderNode?.click();
    await page.waitForTimeout(1000);

    const isUpdatedExpanded = await folderNode?.getAttribute('aria-expanded');

    expect(isInitiallyExpanded).toBe('true');
    expect(isUpdatedExpanded).toBe('false');
  });

  test('should expand the folder node and if the clicked node is selected, all the children should be selected as well', async ({
    page,
  }) => {
    await loadStory(page, storyId);

    const firstLevelFolderCheckbox = page.getByLabel('Selecteer F. Kadaster');
    await firstLevelFolderCheckbox.check();

    const firstLevelfolderNode = page.getByRole('button', { name: 'Folder F. Kadaster' });

    const isFirstLevelFolderInitiallyExpanded = await firstLevelfolderNode?.getAttribute('aria-expanded');

    await firstLevelfolderNode?.click();
    await page.waitForTimeout(1000);

    const isFirstLevelUpdatedExpanded = await firstLevelfolderNode?.getAttribute('aria-expanded');

    expect(isFirstLevelFolderInitiallyExpanded).toBe('false');
    expect(isFirstLevelUpdatedExpanded).toBe('true');

    /** labels to check */
    const firstLevellabelsToCheck = ['Schipluiden', 's-Gravenhage', 'Wateringen'];

    for (const label of firstLevellabelsToCheck) {
      /** Find the child node by label */
      const childNode = page.getByLabel(`${label}`).and(page.getByRole('checkbox'));

      if (childNode) {
        expect(await childNode.isChecked()).toBe(true);
      } else {
        throw new Error(`Child node with label "${label}" and checkbox not found.`);
      }
    }

    expect(await firstLevelFolderCheckbox.isChecked()).toBe(true);

    await firstLevelFolderCheckbox.uncheck();

    /** checking the second level children expansion and selection state */
    const secondLevelFolderCheckbox = page.getByLabel('Selecteer s-Gravenhage');
    await secondLevelFolderCheckbox.check();

    const secondLevelfolderNode = page.getByRole('button', { name: 'Folder s-Gravenhage' });

    const isSecondLevelFolderInitiallyExpanded = await secondLevelfolderNode?.getAttribute('aria-expanded');

    await secondLevelfolderNode?.click();
    await page.waitForTimeout(1000);

    const isSecondLevelUpdatedExpanded = await secondLevelfolderNode?.getAttribute('aria-expanded');

    expect(isSecondLevelFolderInitiallyExpanded).toBe('false');
    expect(isSecondLevelUpdatedExpanded).toBe('true');

    /** labels to check */
    const secondLevellabelsToCheck = [
      'Eigendomsinformatie - s-Gravenhage AY 4969.pdf',
      'Eigendomsinformatie - s-Gravenhage AY 5405.pdf',
    ];

    for (const label of secondLevellabelsToCheck) {
      /** Find the child node by label */
      const childNode = page.getByLabel(`${label}`).and(page.getByRole('checkbox'));

      if (childNode) {
        expect(await childNode.isChecked()).toBe(true);
      } else {
        throw new Error(`Child node with label "${label}" and checkbox not found.`);
      }
    }

    expect(await secondLevelFolderCheckbox.isChecked()).toBe(true);
  });

  test("click on check box should select the node and it's children/grand children", async ({ page }) => {
    await loadStory(page, storyId);

    const folderNode = page.getByRole('button', { name: 'Folder A. Zakelijke beschrijving' });

    await folderNode?.click();
    await page.waitForTimeout(1000);

    const folderCheckbox = page.getByLabel('Selecteer A. Zakelijke beschrijving');

    await folderCheckbox.check();
    await page.waitForTimeout(1000);

    /** labels to check */
    const labelsToCheck = [
      'Eigendomsinformatie - Wateringen B 2588.pdf',
      'Eigendomsinformatie - Wateringen B 2589.pdf',
      'Eigendomsinformatie - Wateringen B 2591 A1.pdf',
    ];

    for (const label of labelsToCheck) {
      /** Find the child node by label */
      const childNode = page.getByLabel(`${label}`).and(page.getByRole('checkbox'));

      if (childNode) {
        expect(await childNode.isChecked()).toBe(true);
      } else {
        throw new Error(`Child node with label "${label}" and checkbox not found.`);
      }
    }

    expect(await folderCheckbox.isChecked()).toBe(true);
  });

  test('deselect any children makes the parent node into indeterminated mode', async ({ page }) => {
    await loadStory(page, storyId);

    const folderNode = page.getByRole('button', { name: 'Folder A. Zakelijke beschrijving' });

    await folderNode?.click();
    await page.waitForTimeout(1000);

    const folderCheckbox = page.getByLabel('Selecteer A. Zakelijke beschrijving');

    await folderCheckbox.check();
    await page.waitForTimeout(1000);

    const deselectChild = page
      .getByLabel('Eigendomsinformatie - Wateringen B 2589.pdf')
      .and(page.getByRole('checkbox'));

    if (deselectChild) {
      deselectChild.uncheck();
    } else {
      throw new Error(`Child node with label "Eigendomsinformatie - Wateringen B 2589.pdf" and checkbox not found.`);
    }

    await page.waitForTimeout(1000);

    /** Check if the element exists in the indeterminate state */
    const isIndeterminate = await page.$eval('input[type="checkbox"]:indeterminate', (checkbox) => {
      return checkbox !== null;
    });

    expect(isIndeterminate).toBe(true);
  });

  test('select all children makes parent deslected', async ({ page }) => {
    await loadStory(page, storyId);

    const folderNode = page.getByRole('button', { name: 'Folder A. Zakelijke beschrijving' });

    await folderNode?.click();
    await page.waitForTimeout(1000);

    const folderCheckbox = page.getByLabel('Selecteer A. Zakelijke beschrijving');

    /** check the folder node first */
    await folderCheckbox.check();
    await page.waitForTimeout(1000);

    /** labels to check */
    const labelsToCheck = [
      'Eigendomsinformatie - Wateringen B 2588.pdf',
      'Eigendomsinformatie - Wateringen B 2589.pdf',
      'Eigendomsinformatie - Wateringen B 2591 A1.pdf',
    ];

    for (const label of labelsToCheck) {
      /** Find the child node by label */
      const childNode = page.getByLabel(`${label}`).and(page.getByRole('checkbox'));

      if (childNode) {
        /** deselect each child */
        childNode.uncheck();
        await page.waitForTimeout(500);
      } else {
        throw new Error(`Child node with label "${label}" and checkbox not found.`);
      }
    }

    expect(await folderCheckbox.isChecked()).toBe(false);
  });
});

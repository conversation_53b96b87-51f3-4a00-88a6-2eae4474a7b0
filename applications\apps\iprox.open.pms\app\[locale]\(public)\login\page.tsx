'use client';

import { Button } from '@iprox/iprox-ui';
import { getSession, signIn, signOut } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useTranslations } from 'use-intl';

import { IproxOpenLogo } from '@/components/logo';

export default function Page() {
  const t = useTranslations('login');

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [errorMessage, setErrorMesssage] = useState('');

  // When the user opens this page he might be redirected from a 401 interceptor. Clear cookies.
  useEffect(() => {
    getSession().then((session) => {
      if (session) {
        signOut({ redirect: false });
      }
    });
  }, []);

  const handleLoginWithAzureAd = () => {
    signIn('azure-ad', { callbackUrl: '/dashboard' }).catch((error) => {
      setErrorMesssage(t(error as 'CredentialsSignin' | 'FetchUser'));
    });
  };

  return (
    <div className="bg-base-10 flex h-screen min-h-screen w-full min-w-full flex-col items-center px-3 py-9 md:px-0">
      <IproxOpenLogo />
      <div className="flex h-full w-full max-w-3xl flex-col items-center justify-center md:w-2/3 xl:w-2/5">
        <h1 className="font-heading text-heading mb-5 self-start text-4xl font-bold">Login</h1>
        <div className="rounded-input bg-base-00 flex w-full flex-row justify-center p-3 md:p-9">
          <Button onClick={handleLoginWithAzureAd} variant="secondary" type="button" className="w-full">
            {t('azureLoginButton')}
          </Button>
        </div>
      </div>
    </div>
  );
}

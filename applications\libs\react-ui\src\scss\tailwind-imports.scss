/* stylelint-disable scss/at-rule-no-unknown -- for now */
@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'ArticulatCF';
  font-weight: 700;
  src: url('../assets/fonts/ArticulatCF/ArticulatCF-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'ArticulatCF';
  font-weight: 600;
  src: url('../assets/fonts/ArticulatCF/ArticulatCF-DemiBold.ttf') format('truetype');
}

@font-face {
  font-family: 'ArticulatCF';
  font-weight: 500;
  src: url('../assets/fonts/ArticulatCF/ArticulatCF-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'ArticulatCF';
  font-weight: 400;
  src: url('../assets/fonts/ArticulatCF/ArticulatCF-Normal.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-weight: 700;
  src: url('../assets/fonts/Inter/Inter-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-weight: 500;
  src: url('../assets/fonts/Inter/Inter-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-weight: 400;
  src: url('../assets/fonts/Inter/Inter-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-weight: 300;
  src: url('../assets/fonts/Inter/Inter-Light.ttf') format('truetype');
}

/* TODO place refer to reference design tokens. */
@layer base {
  :root {
    --ref_color_orange_100_rgb: #ff9900;
    --ref_color_orange_75_rgb: #ffad33;
    --ref_color_orange_50_rgb: #ffc266;
    --ref_color_orange_25_rgb: #ffe0b3;

    --ref_color_darkblue_100_rgb: #000033;
    --ref_color_darkblue_75_rgb: #33335c;
    --ref_color_darkblue_50_rgb: #666685;
    --ref_color_darkblue_25_rgb: #9999ad;
    --ref_color_darkblue_10_rgb: #e5e5eb;

    --ref_color_green_100_rgb: #33cc66;
    --ref_color_green_75_rgb: #5cd685;
    --ref_color_green_50_rgb: #85e0a3;
    --ref_color_green_25_rgb: #adebc2;

    --ref_color_lightblue_100_rgb: #3399cc;
    --ref_color_lightblue_75_rgb: #5cadd6;
    --ref_color_lightblue_50_rgb: #85c2e0;
    --ref_color_lightblue_25_rgb: #add6eb;

    --ref_color_lightgrey_rgb: #dbdbdb;
    --ref_color_darkorange_100_rgb: #df8500;

    --ref_font_articulat_cf: 'ArticulatCF';
    --ref_font_inter: 'Inter';

    --sys_color_base_100: #000000;
    --sys_color_base_85: #4a5568;
    --sys_color_base_75: #676767;
    --sys_color_base_35: #848484;
    --sys_color_base_25: #bebebe;
    --sys_color_base_15: #e4e4e4;
    --sys_color_base_10: #ebebeb;
    --sys_color_base_05: #f2f2f2;
    --sys_color_base_00: #ffffff;

    --sys_color_primary: var(--ref_color_orange_100_rgb);
    --sys_color_primary_content: var(--ref_color_darkblue_100_rgb);
    --sys_color_primary_hover: var(--ref_color_darkorange_100_rgb);
    --sys_color_primary_hover_content: var(--ref_color_darkblue_100_rgb);

    --sys_color_secondary: var(--ref_color_lightgrey_rgb);
    --sys_color_secondary_content: var(--ref_color_darkblue_100_rgb);
    --sys_color_secondary_hover: #bebebe; // TODO
    --sys_color_secondary_hover_content: var(--ref_color_darkblue_100_rgb);

    --sys_color_tertiary: #f4f5f6; // TODO
    --sys_color_tertiary_content: #ffffff; // TODO
    --sys_color_tertiary_hover: var(--ref_color_darkorange_100_rgb); // TODO
    --sys_color_tertiary_hover_content: var(--ref_color_darkblue_100_rgb); // TODO

    --sys_color_heading_text: #000033;
    --sys_color_body_text: #000000;
    --sys_color_anchor_text: #216aa1;
    --sys_color_content_lite: var(--ref_color_darkblue_50_rgb); // @iprox/iprox-ui only
    --sys_color_content_extra_lite: var(--ref_color_darkblue_25_rgb);

    --sys_color_accent: var(--ref_color_lightblue_100_rgb);
    --sys_color_accent_medium: var(--ref_color_lightblue_75_rgb);
    --sys_color_accent_light: var(--ref_color_lightblue_50_rgb);
    --sys_color_accent_lighter: var(--ref_color_lightblue_25_rgb);

    --sys_color_light_grey: var(--ref_color_lightgrey_rgb);

    --sys_color_error: #de0a0a;

    --sys_color_navigation_background: #ffffff;
    --sys_color_navigation_text: #00365f;

    --sys_color_superlink_background: #dddddd;
    --sys_color_superlink_text: #00365f;

    --sys_font_family_heading: var(--ref_font_articulat_cf);
    --sys_font_family_text: var(--ref_font_inter);

    --sys_font_size_h1: 2rem;
    --sys_font_size_h2: 1.75rem;
    --sys_font_size_h3: 1.5rem;
    --sys_font_size_h4: 1.375rem;
    --sys_font_size_h5: 1.25rem;
    --sys_font_size_h6: 1rem;
    --sys_font_size_base: 1rem;

    // Consider switching these to --comp_ tokens
    --sys_border_radius_input: 10px;
    --sys_border_radius_medium: 10px;
    --sys_height_input: 50px;

    --sys_color_file_structure_expanded: #f4f5f6;

    --sys_color_button_primary-border: transparent;
    --sys_color_button_secondary-border: transparent;
    --sys_color_button_tertiary-border: transparent;

    --sys_color_highlight: var(--ref_color_darkblue_100_rgb);
    --sys_color_progress_frame: var(--ref_color_green_50_rgb);

    /** These are @iprox/iprox-ui only colors. */
    --sys_color_secondary_medium: var(--ref_color_darkblue_75_rgb);
    --sys_color_secondary_extra_light: var(--ref_color_darkblue_10_rgb);
    --sys_color_published: #00856a;
    --sys_color_not_published: #de0a0a;
    --sys_color_unused: #ffad33;
  }
}

@layer theme;

import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Button } from './button';

const meta: Meta<typeof Button> = {
  title: 'components/button',
  component: Button,
  argTypes: {
    onClick: {
      action: 'onClick',
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {
  name: 'primary',
  args: {
    children: 'Login',
    variant: 'primary',
  },
};

export const PrimaryDisabled: Story = {
  name: 'primary disabled',
  args: {
    children: 'Login',
    variant: 'primary',
    disabled: true,
  },
};

export const PrimaryWithIconLeft: Story = {
  name: 'primary with icon left',
  args: {
    children: 'Login',
    variant: 'primary',
    icon: 'PlusSmallIcon',
    iconPosition: 'left',
  },
};

export const PrimaryWithIconRight: Story = {
  name: 'primary with icon right',
  args: {
    children: 'Login',
    variant: 'primary',
    icon: 'PlusSmallIcon',
    iconPosition: 'right',
  },
};

export const Secondary: Story = {
  name: 'secondary',
  args: {
    children: 'Login',
    variant: 'secondary',
  },
};

export const SecondaryDisabled: Story = {
  name: 'secondary disabled',
  args: {
    children: 'Login',
    variant: 'secondary',
    disabled: true,
  },
};

export const SecondaryWithIconLeft: Story = {
  name: 'secondary with icon left',
  args: {
    children: 'Login',
    variant: 'secondary',
    icon: 'PlusSmallIcon',
    iconPosition: 'left',
  },
};

export const SecondaryWithIconRight: Story = {
  name: 'secondary with icon right',
  args: {
    children: 'Login',
    variant: 'secondary',
    icon: 'PlusSmallIcon',
    iconPosition: 'right',
  },
};

export const Tertiary: Story = {
  name: 'tertiary',
  args: {
    children: 'Login',
    variant: 'tertiary',
  },
};

export const TertiaryDisabled: Story = {
  name: 'tertiary disabled',
  args: {
    children: 'Login',
    variant: 'tertiary',
    disabled: true,
  },
};

export const TertiaryWithIconLeft: Story = {
  name: 'tertiary with icon left',
  args: {
    children: 'Login',
    variant: 'tertiary',
    icon: 'PlusSmallIcon',
    iconPosition: 'left',
  },
};

export const TertiaryWithIconRight: Story = {
  name: 'tertiary with icon right',
  args: {
    children: 'Login',
    variant: 'tertiary',
    icon: 'PlusSmallIcon',
    iconPosition: 'right',
  },
};

import { $generateHtmlFromNodes } from '@lexical/html';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { useEffect } from 'react';

export class SerializationError extends Error {
  constructor(msg: string) {
    super(msg);
    Object.setPrototypeOf(this, SerializationError.prototype);
  }
}

interface SerializationPluginProps {
  onChange: (value: string) => void;
}

export default function SerializationPlugin({ onChange }: SerializationPluginProps): null {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return editor.registerUpdateListener(() => {
      editor.getEditorState().read(() => {
        try {
          const htmlString = $generateHtmlFromNodes(editor, null);
          onChange(htmlString);
        } catch (e) {
          throw new SerializationError('Serialization Error');
        }
      });
    });
  }, [editor, onChange]);

  return null;
}

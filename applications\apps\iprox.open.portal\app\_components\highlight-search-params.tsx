'use client';

import { useSearchParams } from 'next/navigation';
import { Fragment, useMemo } from 'react';

const useSearchParamHighlighting = (content: string, paramName = 'query') => {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get(paramName) || '';

  const wordsToHighlight = useMemo(() => {
    return (searchQuery.match(/\b\w+\b/g) || []).map((word) => word.toLowerCase());
  }, [searchQuery]);

  return useMemo(() => {
    return content.split(/(\s+)/).map((word, index) => {
      const cleanedWord = word.replace(/[.,!?]/g, '').toLowerCase();

      if (wordsToHighlight.includes(cleanedWord)) {
        return (
          <mark className="search-highlight" key={index}>
            {word}
          </mark>
        );
      }

      return <Fragment key={index}>{word}</Fragment>;
    });
  }, [content, wordsToHighlight]);
};

export function HighlightSearchParams({ content, query }: { content: string; query?: string }) {
  return useSearchParamHighlighting(content, query);
}

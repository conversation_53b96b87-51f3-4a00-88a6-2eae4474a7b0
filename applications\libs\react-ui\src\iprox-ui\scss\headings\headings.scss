@mixin base() {
  color: theme('colors.heading');
  margin-block-end: 16px;
  &:not(:first-child) {
    margin-block-start: 24px;
  }
}

@mixin h1() {
  @include base();

  font-family: theme('fontFamily.heading');
  font-size: theme('fontSize.h1');
  line-height: 1.2;
  font-weight: 700;

  // @media screen and (min-width: media-query-breakpoints.$screen-xs-min) {
  //   font-size: units.$font-size-h1;
  // }
}

@mixin h2() {
  @include base();

  font-family: theme('fontFamily.heading');
  font-size: theme('fontSize.h2');
  line-height: 1.2;
  font-weight: 700;
}

@mixin h3() {
  @include base();

  font-family: theme('fontFamily.heading');
  font-size: theme('fontSize.h3');
  line-height: 1.2;
  font-weight: 700;
}

@mixin h4() {
  @include base();

  font-family: theme('fontFamily.heading');
  font-size: theme('fontSize.h4');
  line-height: 1.2;
  font-weight: 700;
}

@mixin h5() {
  @include base();

  font-family: theme('fontFamily.heading');
  font-size: theme('fontSize.h5');
  line-height: 1.2;
  font-weight: 700;
}

'use client';

import { useAppSettings } from '@iprox/shared-context';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';

export default function ClientPage() {
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      redirect('/login');
    },
  });

  const settings = useAppSettings();

  return (
    <>
      <div>
        <span>Session Client</span>
      </div>
      <div>
        <pre>{JSON.stringify(session, null, 2)}</pre>
      </div>
      <div>
        <span>Settings Client</span>
      </div>
      <div>
        <pre>
          {JSON.stringify(
            {
              IPROX_OPEN_API_URL: settings.apiUrl,
            },
            null,
            2
          )}
        </pre>
      </div>
    </>
  );
}

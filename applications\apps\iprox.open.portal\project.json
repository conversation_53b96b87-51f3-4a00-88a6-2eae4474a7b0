{"name": "iprox.open.portal", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/iprox.open.portal", "projectType": "application", "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/iprox.open.portal"}, "configurations": {"development": {"outputPath": "apps/iprox.open.portal"}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "iprox.open.portal:build", "dev": true}, "configurations": {"development": {"buildTarget": "iprox.open.portal:build:development", "dev": true}, "production": {"buildTarget": "iprox.open.portal:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "iprox.open.portal:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/iprox.open.portal/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"maxWarnings": 0}}}, "tags": []}
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stack-chain";
exports.ids = ["vendor-chunks/stack-chain"];
exports.modules = {

/***/ "(instrument)/../../node_modules/stack-chain/format.js":
/*!************************************************!*\
  !*** ../../node_modules/stack-chain/format.js ***!
  \************************************************/
/***/ ((module) => {

eval("// Copyright 2012 the V8 project authors. All rights reserved.\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are\n// met:\n//\n//     * Redistributions of source code must retain the above copyright\n//       notice, this list of conditions and the following disclaimer.\n//     * Redistributions in binary form must reproduce the above\n//       copyright notice, this list of conditions and the following\n//       disclaimer in the documentation and/or other materials provided\n//       with the distribution.\n//     * Neither the name of Google Inc. nor the names of its\n//       contributors may be used to endorse or promote products derived\n//       from this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nfunction FormatErrorString(error) {\n  try {\n    return Error.prototype.toString.call(error);\n  } catch (e) {\n    try {\n      return \"<error: \" + e + \">\";\n    } catch (ee) {\n      return \"<error>\";\n    }\n  }\n}\n\nmodule.exports = function FormatStackTrace(error, frames) {\n  var lines = [];\n  lines.push(FormatErrorString(error));\n  for (var i = 0; i < frames.length; i++) {\n    var frame = frames[i];\n    var line;\n    try {\n      line = frame.toString();\n    } catch (e) {\n      try {\n        line = \"<error: \" + e + \">\";\n      } catch (ee) {\n        // Any code that reaches this point is seriously nasty!\n        line = \"<error>\";\n      }\n    }\n    lines.push(\"    at \" + line);\n  }\n  return lines.join(\"\\n\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/stack-chain/format.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/stack-chain/index.js":
/*!***********************************************!*\
  !*** ../../node_modules/stack-chain/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// If a another copy (same version or not) of stack-chain exists it will result\n// in wrong stack traces (most likely dublicate callSites).\nif (global._stackChain) {\n  // In case the version match, we can simply return the first initialized copy\n  if (global._stackChain.version === (__webpack_require__(/*! ./package.json */ \"(instrument)/../../node_modules/stack-chain/package.json\").version)) {\n    module.exports = global._stackChain;\n  }\n  // The version don't match, this is really bad. Lets just throw\n  else {\n    throw new Error('Conflicting version of stack-chain found');\n  }\n}\n// Yay, no other stack-chain copy exists, yet :/\nelse {\n  module.exports = global._stackChain = __webpack_require__(/*! ./stack-chain */ \"(instrument)/../../node_modules/stack-chain/stack-chain.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9zdGFjay1jaGFpbi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQywrR0FBaUM7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLG1CQUFPLENBQUMsaUZBQWU7QUFDL0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL3N0YWNrLWNoYWluL2luZGV4LmpzP2Q0NjgiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gSWYgYSBhbm90aGVyIGNvcHkgKHNhbWUgdmVyc2lvbiBvciBub3QpIG9mIHN0YWNrLWNoYWluIGV4aXN0cyBpdCB3aWxsIHJlc3VsdFxuLy8gaW4gd3Jvbmcgc3RhY2sgdHJhY2VzIChtb3N0IGxpa2VseSBkdWJsaWNhdGUgY2FsbFNpdGVzKS5cbmlmIChnbG9iYWwuX3N0YWNrQ2hhaW4pIHtcbiAgLy8gSW4gY2FzZSB0aGUgdmVyc2lvbiBtYXRjaCwgd2UgY2FuIHNpbXBseSByZXR1cm4gdGhlIGZpcnN0IGluaXRpYWxpemVkIGNvcHlcbiAgaWYgKGdsb2JhbC5fc3RhY2tDaGFpbi52ZXJzaW9uID09PSByZXF1aXJlKCcuL3BhY2thZ2UuanNvbicpLnZlcnNpb24pIHtcbiAgICBtb2R1bGUuZXhwb3J0cyA9IGdsb2JhbC5fc3RhY2tDaGFpbjtcbiAgfVxuICAvLyBUaGUgdmVyc2lvbiBkb24ndCBtYXRjaCwgdGhpcyBpcyByZWFsbHkgYmFkLiBMZXRzIGp1c3QgdGhyb3dcbiAgZWxzZSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdDb25mbGljdGluZyB2ZXJzaW9uIG9mIHN0YWNrLWNoYWluIGZvdW5kJyk7XG4gIH1cbn1cbi8vIFlheSwgbm8gb3RoZXIgc3RhY2stY2hhaW4gY29weSBleGlzdHMsIHlldCA6L1xuZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gZ2xvYmFsLl9zdGFja0NoYWluID0gcmVxdWlyZSgnLi9zdGFjay1jaGFpbicpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/stack-chain/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/stack-chain/stack-chain.js":
/*!*****************************************************!*\
  !*** ../../node_modules/stack-chain/stack-chain.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n// use a already existing formater or fallback to the default v8 formater\nvar defaultFormater = __webpack_require__(/*! ./format.js */ \"(instrument)/../../node_modules/stack-chain/format.js\");\n\n// public define API\nfunction stackChain() {\n  this.extend = new TraceModifier();\n  this.filter = new TraceModifier();\n  this.format = new StackFormater();\n  this.version = (__webpack_require__(/*! ./package.json */ \"(instrument)/../../node_modules/stack-chain/package.json\").version);\n}\n\n\nvar SHORTCIRCUIT_CALLSITE = false;\nstackChain.prototype.callSite = function collectCallSites(options) {\n  if (!options) options = {};\n\n  // Get CallSites\n  SHORTCIRCUIT_CALLSITE = true;\n  var obj = {};\n  Error.captureStackTrace(obj, collectCallSites);\n  var callSites = obj.stack;\n  SHORTCIRCUIT_CALLSITE = false;\n\n  // Slice\n  callSites = callSites.slice(options.slice || 0);\n\n  // Modify CallSites\n  if (options.extend) callSites = this.extend._modify(obj, callSites);\n  if (options.filter) callSites = this.filter._modify(obj, callSites);\n\n  // Done\n  return callSites;\n};\n\nvar chain = new stackChain();\n\nfunction TraceModifier() {\n  this._modifiers = [];\n}\n\nTraceModifier.prototype._modify = function (error, frames) {\n  for (var i = 0, l = this._modifiers.length; i < l; i++) {\n    frames = this._modifiers[i](error, frames);\n  }\n\n  return frames;\n};\n\nTraceModifier.prototype.attach = function (modifier) {\n  this._modifiers.push(modifier);\n};\n\nTraceModifier.prototype.deattach = function (modifier) {\n  var index = this._modifiers.indexOf(modifier);\n\n  if (index === -1) return false;\n\n  this._modifiers.splice(index, 1);\n  return true;\n};\n\nfunction StackFormater() {\n  this._formater = defaultFormater;\n  this._previous = undefined;\n}\n\nStackFormater.prototype.replace = function (formater) {\n  if (formater) {\n    this._formater = formater;\n  } else {\n    this.restore();\n  }\n};\n\nStackFormater.prototype.restore  = function () {\n  this._formater = defaultFormater;\n  this._previous = undefined;\n};\n\nStackFormater.prototype._backup = function () {\n  this._previous = this._formater;\n};\n\nStackFormater.prototype._roolback = function () {\n  if (this._previous === defaultFormater) {\n    this.replace(undefined);\n  } else {\n    this.replace(this._previous);\n  }\n\n  this._previous = undefined;\n};\n\n\n//\n// Set Error.prepareStackTrace thus allowing stack-chain\n// to take control of the Error().stack formating.\n//\n\n// If there already is a custom stack formater, then set\n// that as the stack-chain formater.\nif (Error.prepareStackTrace) {\n    chain.format.replace(Error.prepareStackTrace);\n}\n\nvar SHORTCIRCUIT_FORMATER = false;\nfunction prepareStackTrace(error, originalFrames) {\n  if (SHORTCIRCUIT_CALLSITE) return originalFrames;\n  if (SHORTCIRCUIT_FORMATER) return defaultFormater(error, originalFrames);\n\n  // Make a loss copy of originalFrames\n  var frames = originalFrames.concat();\n\n  // extend frames\n  frames = chain.extend._modify(error, frames);\n\n  // filter frames\n  frames = chain.filter._modify(error, frames);\n\n  // reduce frames to match Error.stackTraceLimit\n  frames = frames.slice(0, Error.stackTraceLimit);\n\n  // Set the callSite property\n  // But only if it hasn't been explicitly set, otherwise\n  // error.stack would have unintended side effects. Check also for\n  // non-extensible/sealed objects, such as those from Google's Closure Library\n  if (Object.isExtensible(error) &&\n      (Object.getOwnPropertyDescriptor(error, \"callSite\") === undefined)) {\n    error.callSite = {\n      original: originalFrames,\n      mutated: frames\n    };\n  }\n\n  // format frames\n  SHORTCIRCUIT_FORMATER = true;\n  var format = chain.format._formater(error, frames);\n  SHORTCIRCUIT_FORMATER = false;\n\n  return format;\n}\n\n// Replace the v8 stack trace creator\nObject.defineProperty(Error, 'prepareStackTrace', {\n  'get': function () {\n    return prepareStackTrace;\n  },\n\n  'set': function (formater) {\n    // If formater is prepareStackTrace it means that someone ran\n    // var old = Error.prepareStackTrace;\n    // Error.prepareStackTrace = custom\n    // new Error().stack\n    // Error.prepareStackTrace = old;\n    // The effect of this, should be that the old behaviour is restored.\n    if (formater === prepareStackTrace) {\n      chain.format._roolback();\n    }\n    // Error.prepareStackTrace was set, this means that someone is\n    // trying to take control of the Error().stack format. Make\n    // them belive they succeeded by setting them up as the stack-chain\n    // formater.\n    else {\n      chain.format._backup();\n      chain.format.replace(formater);\n    }\n  }\n});\n\n//\n// Manage call site storeage\n//\nfunction callSiteGetter() {\n  // calculate call site object\n  this.stack;\n\n  // return call site object\n  return this.callSite;\n}\n\nObject.defineProperty(Error.prototype, 'callSite', {\n  'get': callSiteGetter,\n\n  'set': function (frames) {\n    // In case callSite was set before [[getter]], just set\n    // the value\n    Object.defineProperty(this, 'callSite', {\n        value: frames,\n        writable: true,\n        configurable: true\n    });\n  },\n\n  configurable: true\n});\n\nmodule.exports = chain;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/stack-chain/stack-chain.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/stack-chain/package.json":
/*!***************************************************!*\
  !*** ../../node_modules/stack-chain/package.json ***!
  \***************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"stack-chain","description":"API for combining call site modifiers","version":"1.3.7","author":"Andreas Madsen <<EMAIL>>","scripts":{"test":"tap ./test/simple"},"repository":{"type":"git","url":"git://github.com/AndreasMadsen/stack-chain.git"},"keywords":["stack","chain","trace","call site","concat","format"],"devDependencies":{"tap":"2.x.x","uglify-js":"2.5.x"},"license":"MIT"}');

/***/ })

};
;
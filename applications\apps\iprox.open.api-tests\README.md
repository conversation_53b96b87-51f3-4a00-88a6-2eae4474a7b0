# iprox.open.api-tests

This repository contains the API tests for the `iprox.open` application.

## Table of Contents

- [Introduction](#introduction)
- [Installation](#installation)
- [Running Tests](#running-tests)

## Introduction

The `iprox.open.api-tests` project is designed to ensure the reliability and correctness of the `iprox.open` application's API endpoints.

## Installation

To install the necessary dependencies, run:

```bash
yarn install
```

To run locally, set the "base api url" in the iprox.open.api-tests/config.ts

Create a user.ts in the project root folder and add the following lines for authenticate.

```bash
export const username = '<user email>';
export const password = '<user password>';
```

## Running Tests

You can run all tests by executing:

```bash
npx jest
```

To run all tests sequentially:(recommended)

```bash
npx jest --runInBand
```

For more detailed output, use:

```bash
npx jest --runInBand --verbose
```

// https://codebeautify.org/xml-to-typescript-pojo-generator
// Afterwards some models have been edited to reflect the actual data. eg. hasVersion and subject.
export interface BekendmakingenApiResponse {
  searchRetrieveResponse: SearchRetrieveResponse;
}

export interface SearchRetrieveResponse {
  version: number;
  numberOfRecords: number;
  records: Records;
  diagnostics: Diagnostics;
  nextRecordPosition: number;
  echoedSearchRetrieveRequest: EchoedSearchRetrieveRequest;
  resultCountPrecision: string;
}

export interface Diagnostics {
  diagnostic: Diagnostic;
}

export interface Diagnostic {
  uri: string;
  message: string;
}

export interface EchoedSearchRetrieveRequest {
  query: string;
  startRecord: number;
  maximumRecords: number;
  recordXMLEscaping: RecordIng;
  recordSchema: string;
}

export enum RecordIng {
  XML = 'xml',
}

export interface Records {
  record: Record[];
}

export interface Record {
  recordSchema: string;
  recordPacking: RecordIng;
  recordData: RecordDataClass | string;
  recordPosition: number;
}

export interface RecordDataClass {
  gzd: Gzd;
}

export interface Gzd {
  originalData: OriginalData;
  enrichedData: EnrichedData;
}

export interface EnrichedData {
  url: string;
  preferredUrl: string;
  itemUrl: {
    _: string;
    $: { manifestation: string };
  }[];
  timestamp: string;
}

export interface OriginalData {
  meta: Meta;
}

export interface Meta {
  owmskern: Owmskern;
  owmsmantel: Owmsmantel;
  tpmeta: Tpmeta;
}

export interface Owmskern {
  identifier: string;
  title: string;
  language: string;
  type: TypeElement[] | string;
  creator: string;
  modified: Date;
  authority: string;
  spatial?: string;
}

export enum TypeElement {
  AndereVergunning = 'andere vergunning',
  BeschikkingenAfhandeling = 'Beschikkingen | afhandeling',
}

export interface Owmsmantel {
  available: Date;
  date: Date;
  hasVersion: { _: string; $: { resourceIdentifier: string } };
  subject: { _: string };
  publisher: string;
  alternative?: string;
  source?: string;
}

export interface Tpmeta {
  'product-area': string;
  'content-area': string;
  externeBijlage?: string;
  jaargang: number;
  organisatietype: string;
  postcodeHuisnummer?: string;
  publicatienummer: number;
  publicatienaam: string;
  straatnaam?: string;
  woonplaats?: string;
  spatialtype?: string;
  gebiedsmarkering: Gebiedsmarkering;
  datumTijdstipWijzigingWork: string;
  datumTijdstipWijzigingExpression: string;
  betreftRegeling?: string;
  startdatum?: Date;
}

export interface Gebiedsmarkering {
  Punt?: Punt;
  Waterschap?: Waterschap;
  Weg?: Weg;
}

export interface Punt {
  geometrie: string;
  locatiepunt: string;
  locatiegebied: string;
  geometrielabel: string;
}

export interface Waterschap {
  waterschapsnaam: string;
}

export interface Weg {
  geometrie: string;
  locatiegebied: string[] | string;
  geometrielabel: string;
  ligtInGemeente: string;
}

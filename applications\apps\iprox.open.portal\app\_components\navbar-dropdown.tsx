import { getCurrentRoute } from '@/utils/get-current-route';
import { Menu, Transition } from '@headlessui/react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { Text } from '@iprox/react-ui';
import cx from 'classnames';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { Fragment, ReactNode, useEffect, useState } from 'react';

import { NavOptions } from './navbar';

type NavbarDropdownProps = {
  children: ReactNode;
  options: NavOptions[];
  dropDownSide?: 'left' | 'right';
};

export function NavbarDropdown({ options, children, dropDownSide = 'left' }: NavbarDropdownProps) {
  const pathname = usePathname();

  const [expandedItem, setExpandedItem] = useState<string | null>(null);

  const renderCollapsibleToggle = (href: string) => {
    if (expandedItem === href) {
      return (
        <Menu.Item>
          {({ active }) => (
            <button
              className={cx('ml-auto h-5 w-5 font-bold', {
                ring: active,
              })}
              onClick={(event) => {
                event.preventDefault();
                setExpandedItem(null);
              }}
              onKeyDown={(event) => {
                if (event.key === 'Enter') {
                  setExpandedItem(null);
                }
              }}
            >
              <ChevronUpIcon />
            </button>
          )}
        </Menu.Item>
      );
    }
    return (
      <Menu.Item>
        {({ active }) => (
          <button
            className={cx('ml-auto h-5 w-5 font-bold', {
              ring: active,
            })}
            onClick={(event) => {
              event.preventDefault();
              setExpandedItem(href);
            }}
            onKeyDown={(event) => {
              if (event.key === 'Enter') {
                setExpandedItem(href);
              }
            }}
          >
            <ChevronDownIcon />
          </button>
        )}
      </Menu.Item>
    );
  };

  const renderSubNav = (children: NavOptions[]) => {
    return (
      <div className="pl-3">
        {children.map((child, index) => {
          const isCurrent = getCurrentRoute(pathname) === child.href;
          return (
            <Menu.Item key={index}>
              {({ active }) => (
                <Link
                  key={index}
                  href={child.href}
                  className={cx(
                    'font-text text-navigation-text focus-visible::ring-1 block break-words border-l-4 py-2 pl-3 pr-4 text-lg font-medium after:scale-0',
                    {
                      '!bg-base-15': active,
                    },
                    {
                      'border-l-primary after:border-l-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-l-4 after:transition-transform hover:after:scale-x-100':
                        isCurrent,
                    },
                    {
                      'border-l-transparent': !isCurrent,
                    }
                  )}
                >
                  {child.name}
                </Link>
              )}
            </Menu.Item>
          );
        })}
      </div>
    );
  };

  useEffect(() => {
    const currentRoute = getCurrentRoute(pathname);
    const currentRouteParent = options.find((option) => option.children.find((child) => child.href === currentRoute));

    if (currentRouteParent) {
      setExpandedItem(currentRouteParent.href);
    }

    return () => {
      setExpandedItem(null);
    };
  }, [options, pathname]);

  return (
    <Menu as="div" className="relative h-full">
      {({ open, close }) => (
        <div className="relative h-full" onMouseLeave={() => open && close()}>
          <Menu.Button
            onMouseEnter={(event: React.MouseEvent<HTMLButtonElement>) =>
              !open ? event.currentTarget.click() : () => null
            }
            className={cx(
              'font-text nav-link text-navigation-text focus:ring-base-100 relative flex h-full items-center overflow-hidden whitespace-nowrap text-lg font-bold outline-none after:scale-x-0 focus:outline-none focus:ring-2 focus:ring-offset-0',
              {
                'after:border-b-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-b-4 after:transition-transform hover:after:scale-x-100':
                  options.findIndex((item) => item.href === getCurrentRoute(pathname)) > -1,
              }
            )}
          >
            {children}
          </Menu.Button>
          <Transition
            as={Fragment}
            enter="transition ease-out duration-100"
            enterFrom="transform opacity-0 scale-95"
            enterTo="transform opacity-100 scale-100"
            leave="transition ease-in duration-75"
            leaveFrom="transform opacity-100 scale-100"
            leaveTo="transform opacity-0 scale-95"
          >
            <Menu.Items
              className={cx(
                'bg-navigation-background shadow-navbar-dropdown absolute top-[100%] -z-10 max-h-[calc(100vh_-_theme(height.navbar))] min-w-[300px] max-w-[300px] origin-top-right overflow-y-auto overflow-x-hidden py-4 font-medium outline-none ring-0',
                { 'left-auto right-0': dropDownSide === 'right' },
                { 'left-0 right-auto': dropDownSide === 'left' }
              )}
            >
              {options.map((navOption, index) => {
                const isCurrent = getCurrentRoute(pathname) === navOption.href;
                const hasChildren = navOption.children.length > 0;
                return navOption.href ? (
                  hasChildren ? (
                    <Fragment key={`${navOption}-${index}`}>
                      <Menu.Items
                        className={cx(
                          'font-text text-navigation-text focus-visible::ring-1 hover:!bg-base-15 focus:!bg-base-15 focus-visible:!bg-base-15 flex h-full w-full items-center break-words border-l-4 pr-2 text-lg font-medium after:scale-0',
                          {
                            'border-l-primary after:border-l-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-l-4 after:transition-transform hover:after:scale-x-100':
                              isCurrent,
                          },
                          {
                            'border-l-transparent': !isCurrent,
                          }
                        )}
                      >
                        <Menu.Item>
                          {({ active }) => (
                            <Link
                              href={navOption.href}
                              className={cx(
                                'font-text text-navigation-text focus-visible::ring-1 block break-words border-l-4 py-2 pl-3 pr-4 text-lg font-medium after:scale-0',
                                {
                                  'border-l-primary after:border-l-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-l-4 after:transition-transform hover:after:scale-x-100':
                                    isCurrent,
                                },
                                {
                                  'border-l-transparent': !isCurrent,
                                },
                                {
                                  '!bg-base-15': active,
                                }
                              )}
                            >
                              {navOption.name}
                            </Link>
                          )}
                        </Menu.Item>
                        {hasChildren ? renderCollapsibleToggle(navOption.href) : null}
                      </Menu.Items>
                      {expandedItem === navOption.href ? renderSubNav(navOption.children) : null}
                    </Fragment>
                  ) : (
                    <Menu.Item key={index}>
                      {({ active }) => (
                        <Link
                          href={navOption.href}
                          className={cx(
                            'font-text text-navigation-text focus-visible::ring-1 block break-words border-l-4 py-2 pl-3 pr-4 text-lg font-medium after:scale-0',
                            {
                              '!bg-base-15': active,
                            },
                            {
                              'border-l-primary after:border-l-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-l-4 after:transition-transform hover:after:scale-x-100':
                                isCurrent,
                            },
                            {
                              'border-l-transparent': !isCurrent,
                            }
                          )}
                        >
                          {navOption.name}
                        </Link>
                      )}
                    </Menu.Item>
                  )
                ) : (
                  <Menu.Item key={index}>
                    {({ active }) => (
                      <>
                        <Text
                          className={cx(
                            'font-text text-navigation-text focus-visible::ring-1 flex flex-row items-center break-words border-l-4 py-2 pl-3 pr-4 text-lg font-medium after:scale-0',
                            {
                              '!bg-base-15': active,
                            },
                            {
                              'border-l-primary after:border-l-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-l-4 after:transition-transform hover:after:scale-x-100':
                                isCurrent,
                            },
                            {
                              'border-l-transparent': !isCurrent,
                            }
                          )}
                        >
                          {navOption.name}
                          {hasChildren ? renderCollapsibleToggle(navOption.href) : null}
                        </Text>
                        {expandedItem === navOption.href ? renderSubNav(navOption.children) : null}
                      </>
                    )}
                  </Menu.Item>
                );
              })}
            </Menu.Items>
          </Transition>
        </div>
      )}
    </Menu>
  );
}

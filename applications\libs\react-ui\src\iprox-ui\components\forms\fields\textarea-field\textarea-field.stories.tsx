import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { TextareaField } from './textarea-field';

const meta: Meta<typeof TextareaField> = {
  title: 'iprox-ui/forms/fields/textarea',
  component: TextareaField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof TextareaField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'text-field',
    fieldType: FieldType.TextArea,
  },
};

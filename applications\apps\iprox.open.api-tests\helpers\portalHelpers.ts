import * as api from '../enums/apiEnums';
import { Method } from '../enums/httpEnums';
import { BaseHelper } from './baseHelper';

export class PortalHelpers extends BaseHelper {
  // get all the woo-categories
  static async getWooCategories() {
    return this.makeRequest(Method.Get, api.EndpointsPortal.GET_WOO_CATEGORIES);
  }

  // get public page list
  static async getPublicPageList() {
    return this.makeRequest(Method.Get, api.EndpointsPortal.GET_PUBLIC_PAGE_LIST);
  }

  // get public page slug details
  static async getPageSlugDetails(slug: string) {
    return this.makeRequest(Method.Get, api.EndpointsPortal.GET_PUBLIC_PAGE_SLUG_DETAILS.replace('{SLUG}', `${slug}`));
  }

  // get public dossier
  static async getPublicDossier(dossierId: string) {
    return this.makeRequest(Method.Get, api.EndpointsPortal.GET_PUBLIC_DOSSIER.replace('{ID}', `${dossierId}`));
  }

  // get navigation structure
  static async getNavigationStructure() {
    return this.makeRequest(Method.Get, api.EndpointsPortal.GET_NAVIGATION_STRUCTURE);
  }
}

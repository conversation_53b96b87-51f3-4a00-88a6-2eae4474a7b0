{"$schema": "./node_modules/nx/schemas/nx-schema.json", "useDaemonProces": false, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "cache": true}, "e2e": {"inputs": ["default", "^production"], "cache": true}, "build-storybook": {"inputs": ["default", "^production", "{projectRoot}/.storybook/**/*", "{projectRoot}/tsconfig.storybook.json"], "cache": true}, "@nx/eslint:lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore"], "cache": true}, "@nx/jest:jest": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "cache": true, "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)", "!{projectRoot}/.storybook/**/*", "!{projectRoot}/tsconfig.storybook.json", "!{projectRoot}/src/test-setup.[jt]s"], "sharedGlobals": ["{workspaceRoot}/babel.config.json"]}, "generators": {"@nx/react": {"application": {"babel": true}, "library": {"unitTestRunner": "jest"}}, "@nx/next": {"application": {"style": "scss", "linter": "eslint"}}}, "projects": {"react-ui": {"tags": ["scope:shared"]}, "admin-app": {"tags": ["scope:admin"]}, "untagged-project": {"tags": []}}, "useLegacyCache": false}
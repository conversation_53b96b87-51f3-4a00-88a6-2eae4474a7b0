import React from 'react';

export interface TextProps {
  children?: React.ReactNode;
  className?: string;
  role?: React.AriaRole;
  tooltipId?: string;
  tooltipContent?: string;
}

export const Text = React.forwardRef<HTMLSpanElement, TextProps>((props, ref) => {
  const { children, className, role, tooltipId, tooltipContent } = props;
  return (
    <span
      className={className + ' block whitespace-pre'}
      role={role}
      data-tooltip-id={tooltipId}
      data-tooltip-content={tooltipContent}
      ref={ref}
    >
      {children}
    </span>
  );
});

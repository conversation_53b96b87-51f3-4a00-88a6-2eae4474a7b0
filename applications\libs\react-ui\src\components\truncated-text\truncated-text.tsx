import { debounce } from 'debounce';
import React, { useEffect, useId, useRef, useState } from 'react';
import { Tooltip } from 'react-tooltip';

import { Text, TextProps } from '../text/text';

interface TruncatedTextProps extends TextProps {
  showTooltip?: boolean;
}

export function TruncatedText(props: TruncatedTextProps) {
  const { showTooltip = true, className, ...textProps } = props;
  const textRef = useRef<HTMLSpanElement>(null);

  const [isTextTruncated, setIsTextTruncated] = useState(false);
  const [tooltipId, setTooltipId] = useState<string | undefined>(undefined);
  const generatedTooltipId = useId();

  useEffect(() => {
    setTooltipId(generatedTooltipId);

    const handleResize = debounce(() => {
      const textElement = textRef.current;
      if (textElement) {
        setIsTextTruncated(textElement.scrollHeight > textElement.clientHeight);
      }
    }, 200);

    window.addEventListener('resize', handleResize);
    handleResize();
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [generatedTooltipId]);

  const getTooltipContent = (): string | undefined => {
    if (React.isValidElement(textProps.children)) {
      const childrenProps = (textProps.children as React.ReactElement).props;
      if (childrenProps.children) {
        return childrenProps.children.toString();
      }
    } else {
      return textProps.children?.toString();
    }
  };

  return (
    <>
      <Text
        {...textProps}
        tooltipId={tooltipId}
        tooltipContent={getTooltipContent()}
        className={className + ' line-clamp-1 w-fit'}
        ref={textRef}
      />
      {showTooltip && isTextTruncated && (
        <Tooltip
          id={tooltipId}
          className="font-text z-50"
          style={{
            fontSize: '12px',
            lineHeight: '16px',
          }}
        />
      )}
    </>
  );
}

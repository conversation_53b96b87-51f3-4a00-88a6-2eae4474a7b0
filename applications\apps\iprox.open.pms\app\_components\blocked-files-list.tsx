import { components } from '@/iprox-open.interface';
import { Button, Text } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';

interface BlockedFilesListProps {
  files: components['schemas']['NotUploadedFile'][];
  onOk: () => void;
}

export function BlockedFilesList({ files, onOk }: BlockedFilesListProps) {
  const t = useTranslations('dossier');
  return (
    <div>
      <Text className="font-heading text-heading mb-3 text-sm font-bold">{t('blockedFiles')}</Text>
      <div className="mb-6 max-h-32 overflow-y-auto">
        {files.map((file, index) => {
          return (
            <div
              key={index}
              className={`${index !== files.length - 1 ? 'mb-1.5' : ''} border-base-10 mr-2 rounded border p-1`}
            >
              <Text className="font-text text-content-lite overflow-hidden !whitespace-normal break-words text-sm">
                {file.reason}
              </Text>
              <Text className="font-text text-content-lite overflow-hidden !whitespace-normal break-words text-sm">
                {file.fullName}
              </Text>
              <Text className="font-text text-content-lite overflow-hidden !whitespace-normal break-words text-sm font-semibold">
                {file.name}
              </Text>
            </div>
          );
        })}
      </div>
      <Button variant="secondary" onClick={onOk}>
        {t('ok')}
      </Button>
    </div>
  );
}

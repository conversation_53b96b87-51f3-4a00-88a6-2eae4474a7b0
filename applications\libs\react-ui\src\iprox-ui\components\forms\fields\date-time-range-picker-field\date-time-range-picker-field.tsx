import nl from 'date-fns/locale/nl';
import { useField } from 'formik';
import { useLocale, useTranslations } from 'next-intl';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { DateTimeRange, DateTimeRangeFieldDefinition } from '../../models/form.models';
import './date-time-range-picker-field.scss';

const getSelectedValue = (value: string | Date | null | undefined): Date | null => {
  if (value) {
    if (typeof value === 'string') {
      return new Date(value);
    }

    if (value instanceof Date) {
      return value;
    }
  }

  return null;
};

const formatDateToISOString = (date?: Date | null): string | null => {
  return date ? date.toISOString() : null;
};

export function DateTimeRangePickerField(props: DateTimeRangeFieldDefinition) {
  const t = useTranslations('components.dateTimeRangePickerField');

  const locale = useLocale();

  const updatedProps = {
    ...props,
    defaultValue: props.value,
  };
  const [field, meta, helpers] = useField<DateTimeRange>(updatedProps.name);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'date');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  const handleStartDateChange = (date?: Date | null) => {
    helpers.setValue({ start: formatDateToISOString(date)!, end: field.value?.end }, true);
  };

  const handleEndDateChange = (date?: Date | null) => {
    helpers.setValue({ start: field.value?.start, end: formatDateToISOString(date)! }, true);
  };

  return (
    <FormField
      labelColor={props.labelColor}
      descriptionColor={props.descriptionColor}
      definition={props}
      errorMessage={meta.touched ? meta.error : undefined}
      {...formControlProps}
    >
      <div className="flex gap-4">
        <span>
          <p className="font-text text-base-75 text-sm">{t('from')}</p>
          <DatePicker
            selected={getSelectedValue(field.value?.start)}
            onChange={handleStartDateChange}
            id={`${inputProps?.id}-start`}
            ariaDescribedBy={inputProps?.['aria-describedby']}
            name={`${field.name}.start`}
            onBlur={() => helpers.setTouched(true, true)}
            minDate={
              props?.minDate instanceof Date || typeof props?.minDate === 'string'
                ? new Date(props?.minDate)
                : undefined
            }
            maxDate={
              props?.maxDate instanceof Date || typeof props?.maxDate === 'string'
                ? new Date(props?.maxDate)
                : undefined
            }
            locale={locale === 'nl' ? nl : undefined}
            dateFormat={locale === 'nl' ? `dd-MM-yyyy '-' HH:mm` : `dd/MM/yyyy '-' HH:mm`}
            showTimeInput
            timeInputLabel="Time:"
            timeFormat="HH:mm"
            showMonthDropdown
            showYearDropdown
            dropdownMode="select"
            isClearable
          />
        </span>
        <span>
          <p className="font-text text-base-75 text-sm">{t('to')}</p>
          <DatePicker
            selected={getSelectedValue(field.value?.end)}
            onChange={handleEndDateChange}
            id={`${inputProps?.id}-end`}
            ariaDescribedBy={inputProps?.['aria-describedby']}
            name={`${field.name}.end`}
            onBlur={() => helpers.setTouched(true, true)}
            minDate={
              props?.minDate instanceof Date || typeof props?.minDate === 'string'
                ? new Date(props?.minDate)
                : undefined
            }
            maxDate={
              props?.maxDate instanceof Date || typeof props?.maxDate === 'string'
                ? new Date(props?.maxDate)
                : undefined
            }
            locale={locale === 'nl' ? nl : undefined}
            dateFormat={locale === 'nl' ? `dd-MM-yyyy '-' HH:mm` : `dd/MM/yyyy '-' HH:mm`}
            showTimeInput
            timeInputLabel="Time:"
            timeFormat="HH:mm"
            showMonthDropdown
            showYearDropdown
            dropdownMode="select"
            isClearable
          />
        </span>
      </div>
    </FormField>
  );
}

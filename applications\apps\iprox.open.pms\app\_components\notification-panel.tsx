'use client';

import { useNotificationManager } from '@/context/notifications-context';
import { Button, Text } from '@iprox/iprox-ui';
import { toggleSidePanel, useApplicationShellContext } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';

import { NotificationItem } from './notification-item';

export function NotificationPanel() {
  const t = useTranslations('notifications');

  const { dispatch } = useApplicationShellContext();
  const { notifications, removeNotification, removeAllNotifications, handleAction } = useNotificationManager();
  const isNotificationsAvailable = notifications.length > 0;

  return (
    <div className="flex h-full flex-col pt-8">
      <div className="mb-5 flex flex-row items-center justify-between">
        <Text className="font-heading text-heading text-4xl font-bold">{t('notifications')}</Text>
        {isNotificationsAvailable ? (
          <Button
            type="button"
            variant="tertiary"
            className="text-sm"
            onClick={() => removeAllNotifications()}
            disabled={
              notifications.filter((item) => item.status === 'uploading' || item.status === 'queued').length !== 0
            }
          >
            {t('dismissAll')}
          </Button>
        ) : null}
      </div>
      <div className={`flex flex-1 flex-col gap-y-5 ${!isNotificationsAvailable ? 'justify-center' : ''}`}>
        {isNotificationsAvailable ? (
          <>
            {notifications.map((item) => (
              <NotificationItem
                key={item.id}
                notification={item}
                onDismiss={(id) => removeNotification(id)}
                onAction={(id) => {
                  handleAction(id);
                  dispatch(toggleSidePanel());
                }}
              />
            ))}
          </>
        ) : (
          <Text className="font-text text-body text-center text-sm">{t('noNotifications')}</Text>
        )}
      </div>
    </div>
  );
}

import { expect, test } from '@playwright/test';

import { baseUrl } from '../config';
import { DashboardPage } from '../pages/dashboard_page';
import { EditDossierPage } from '../pages/edit_dossier_page';
import { CreateNewDossierPage } from '../pages/new_dossier_page';
import { DateTimeFormatter } from '../utils/date_time_formatter';

let dashboardPage: DashboardPage;
let newDossierPage: CreateNewDossierPage;
let editDossierPage: EditDossierPage;
let updatedText = 'updated text';

test.beforeEach(async ({ page }) => {
  await page.goto(baseUrl);
  dashboardPage = new DashboardPage(page);
  newDossierPage = new CreateNewDossierPage(page);
  editDossierPage = new EditDossierPage(page);
});

test('Verify the initial dossier edit flow', async ({ page }) => {
  test.slow();
  const title = `Playwright dossier with publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;
  let locators = [
    page.getByRole('textbox', { name: 'Uitgever' }),
    // page.getByRole('textbox', { name: 'Inhoud' }),
    page.getByRole('textbox', { name: 'Documentnummer' }),
    page.getByRole('textbox', { name: 'Contact' }),
    page.getByRole('textbox', { name: 'Annotatie' }),
  ];

  // Create new dossier
  await dashboardPage.clickNewDossierButton();

  // create a dossier with publish From To dates.
  await newDossierPage.createNewDossier(title, true);
  await expect(page).toHaveURL(new RegExp('/dossier/.*'));

  // Edit the title and save
  await editDossierPage.editTitle(updatedText);
  expect(await editDossierPage.titleEditButton.isVisible());

  // Input dynamic fields
  await editDossierPage.inputDynamicFields();

  // uploadImageFile
  // await editDossierPage.uploadImage('iprox_1.png');

  // save dossier
  await editDossierPage.clickActionButton('Opslaan');

  // check the initially updated values in the dynamic fields
  await editDossierPage.verifyUpdatedDynamicFields(page, locators, 'update 1');
  await editDossierPage.clickActionButton('Opslaan');

  // update the dynamic fields for the 2nd time and verify the updated dynmaic fields values.
  await editDossierPage.verifyUpdatedDynamicFields(page, locators, 'update 2');
});

test('Verify the dossier edit for dossiers without publish `From` `To` dates', async ({ page }) => {
  test.slow();
  const title = `Playwright dossier without publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;
  let locators = [
    // page.getByRole('textbox', { name: 'Samenvatting' }),
    page.getByRole('textbox', { name: 'Uitgever' }),
    // page.getByRole('textbox', { name: 'Inhoud' }),
    page.getByRole('textbox', { name: 'Documentnummer' }),
    page.getByRole('textbox', { name: 'Contact' }),
    page.getByRole('textbox', { name: 'Annotatie' }),
  ];

  // Create new dossier
  await dashboardPage.clickNewDossierButton();

  // create a dossier with publish From To dates.
  await newDossierPage.createNewDossier(title, false);
  await expect(page).toHaveURL(new RegExp('/dossier/.*'));

  // Edit the title and save
  await editDossierPage.editTitle(updatedText);
  expect(await editDossierPage.titleEditButton.isVisible());

  // Input dynamic fields
  await editDossierPage.inputDynamicFields();

  // uploadZipFile
  // await editDossierPage.uploadImage('iprox_1.png');

  // save dossier
  await editDossierPage.clickActionButton('Opslaan');

  // check the initially updated values in the dynamic fields
  await editDossierPage.verifyUpdatedDynamicFields(page, locators, 'update 1');
  await editDossierPage.clickActionButton('Opslaan');

  // update the dynamic fields for the 2nd time and verify the updated dynmaic fields values.
  await editDossierPage.verifyUpdatedDynamicFields(page, locators, 'update 2');
});

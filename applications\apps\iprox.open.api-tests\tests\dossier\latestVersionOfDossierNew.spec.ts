import { colors } from '../../config';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';

describe('Dossier latest version tests', () => {
  let dossierId: string;
  let bearerToken: any;
  let dossierCategoryId: string | null;

  beforeAll(async () => {
    bearerToken = await loginAndGetToken();
    dossierCategoryId = await DossierHelpersNew.getRandomCategoryUuid(false, bearerToken);
    const response: any = await DossierHelpersNew.createDossier(
      'Latest version dossier',
      bearerToken,
      dossierCategoryId
    );
    dossierId = await response.body.dossier.dossierId;
  }, 90000);

  it('should get the latest version of a dossier', async () => {
    // initial version is 1
    const response: any = await DossierHelpersNew.latestVersionOfDossier(dossierId, bearerToken);
    // expect(await response.status).toBe(api.StatusCode.OK_200);
    expect(await response.body.dossier.version).toBe(1);

    // update > publish > unpublish the dossier
    const updateDossierResponse = await DossierHelpersNew.updateDossier(dossierId, bearerToken);
    // expect(await updateDossierResponse.body.dossier.version).toBe(1);
    await DossierHelpersNew.publishDossier(dossierId, bearerToken);
    await DossierHelpersNew.unpublishDossier(dossierId, bearerToken);

    // latest version should be 2
    const dossierLatestVersionResponse = await DossierHelpersNew.latestVersionOfDossier(dossierId, bearerToken);
    expect(await dossierLatestVersionResponse.body.dossier.version).toBe(2);
  }, 90000);

  afterAll(async () => {
    await DossierHelpersNew.deleteDossier(dossierId, bearerToken);
  });
});

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  $deleteTableColumn,
  $getElementGridForTableNode,
  $getTableColumnIndexFromTableCellNode,
  $getTableNodeFromLexicalNodeOrThrow,
  $getTableRowIndexFromTableCellNode,
  $insertTableColumn,
  $insertTableRow,
  $isTableCellNode,
  $isTableRowNode,
  $removeTableRowAtIndex,
  HTMLTableElementWithWithTableSelectionState,
  TableCellHeaderStates,
  TableCellNode,
  getTableSelectionFromTableElement,
} from '@lexical/table';
import { $getRoot, $getSelection, DEPRECATED_$isGridSelection } from 'lexical';
import { useTranslations } from 'next-intl';
import { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Text } from '../../../../../components/text/text';

type TableCellActionMenuProps = Readonly<{
  contextRef: { current: null | HTMLElement };
  onClose: () => void;
  setIsMenuOpen: (isOpen: boolean) => void;
  tableCellNode: TableCellNode;
  buttonPosition: { x: number; y: number };
}>;

export function TableActionMenu({
  onClose,
  tableCellNode: _tableCellNode,
  setIsMenuOpen,
  contextRef,
  buttonPosition,
}: TableCellActionMenuProps) {
  const t = useTranslations('components.richtexteditor.tabelActions');

  const [editor] = useLexicalComposerContext();
  const dropDownRef = useRef<HTMLDivElement | null>(null);
  const [tableCellNode, updateTableCellNode] = useState(_tableCellNode);
  const [selectionCounts, updateSelectionCounts] = useState({
    columns: 1,
    rows: 1,
  });

  useEffect(() => {
    return editor.registerMutationListener(TableCellNode, (nodeMutations) => {
      const nodeUpdated = nodeMutations.get(tableCellNode.getKey()) === 'updated';

      if (nodeUpdated) {
        editor.getEditorState().read(() => {
          updateTableCellNode(tableCellNode.getLatest());
        });
      }
    });
  }, [editor, tableCellNode]);

  useEffect(() => {
    editor.getEditorState().read(() => {
      const selection = $getSelection();

      if (DEPRECATED_$isGridSelection(selection)) {
        const selectionShape = selection.getShape();

        updateSelectionCounts({
          columns: selectionShape.toX - selectionShape.fromX + 1,
          rows: selectionShape.toY - selectionShape.fromY + 1,
        });
      }
    });
  }, [editor]);

  useEffect(() => {
    const menuButtonElement = contextRef.current;
    const dropDownElement = dropDownRef.current;

    if (menuButtonElement != null && dropDownElement != null) {
      const menuButtonRect = menuButtonElement.getBoundingClientRect();
      const dropdownrect = dropDownElement.getBoundingClientRect();

      dropDownElement.style.opacity = '1';

      dropDownElement.style.left =
        buttonPosition.x > dropdownrect.width ? `${menuButtonRect.width - dropDownElement.clientWidth}px` : '0';

      dropDownElement.style.top = `${menuButtonRect.height + 10}px`;
    }
  }, [buttonPosition, contextRef, dropDownRef]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropDownRef.current != null &&
        contextRef.current != null &&
        !dropDownRef.current.contains(event.target as Node) &&
        !contextRef.current.contains(event.target as Node)
      ) {
        setIsMenuOpen(false);
      }
    }

    window.addEventListener('click', handleClickOutside);

    return () => window.removeEventListener('click', handleClickOutside);
  }, [setIsMenuOpen, contextRef]);

  const clearTableSelection = useCallback(() => {
    editor.update(() => {
      if (tableCellNode.isAttached()) {
        const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);
        const tableElement = editor.getElementByKey(tableNode.getKey()) as HTMLTableElementWithWithTableSelectionState;

        if (!tableElement) {
          throw new Error('Expected to find tableElement in DOM');
        }

        const tableSelection = getTableSelectionFromTableElement(tableElement);
        if (tableSelection !== null) {
          tableSelection.clearHighlight();
        }

        tableNode.markDirty();
        updateTableCellNode(tableCellNode.getLatest());
      }

      const rootNode = $getRoot();
      rootNode.selectStart();
    });
  }, [editor, tableCellNode]);

  const insertTableRowAtSelection = useCallback(
    (shouldInsertAfter: boolean) => {
      editor.update(() => {
        const selection = $getSelection();

        const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);

        let tableRowIndex;

        if (DEPRECATED_$isGridSelection(selection)) {
          const selectionShape = selection.getShape();
          tableRowIndex = shouldInsertAfter ? selectionShape.toY : selectionShape.fromY;
        } else {
          tableRowIndex = $getTableRowIndexFromTableCellNode(tableCellNode);
        }

        const grid = $getElementGridForTableNode(editor, tableNode);

        $insertTableRow(tableNode, tableRowIndex, shouldInsertAfter, selectionCounts.rows, grid);

        clearTableSelection();

        onClose();
      });
    },
    [editor, tableCellNode, selectionCounts.rows, clearTableSelection, onClose]
  );

  const insertTableColumnAtSelection = useCallback(
    (shouldInsertAfter: boolean) => {
      editor.update(() => {
        const selection = $getSelection();

        const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);

        let tableColumnIndex;

        if (DEPRECATED_$isGridSelection(selection)) {
          const selectionShape = selection.getShape();
          tableColumnIndex = shouldInsertAfter ? selectionShape.toX : selectionShape.fromX;
        } else {
          tableColumnIndex = $getTableColumnIndexFromTableCellNode(tableCellNode);
        }

        const grid = $getElementGridForTableNode(editor, tableNode);

        $insertTableColumn(tableNode, tableColumnIndex, shouldInsertAfter, selectionCounts.columns, grid);

        clearTableSelection();

        onClose();
      });
    },
    [editor, tableCellNode, selectionCounts.columns, clearTableSelection, onClose]
  );

  const deleteTableRowAtSelection = useCallback(() => {
    editor.update(() => {
      const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);
      const tableRowIndex = $getTableRowIndexFromTableCellNode(tableCellNode);

      $removeTableRowAtIndex(tableNode, tableRowIndex);

      clearTableSelection();
      onClose();
    });
  }, [editor, tableCellNode, clearTableSelection, onClose]);

  const deleteTableAtSelection = useCallback(() => {
    editor.update(() => {
      const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);
      tableNode.remove();

      clearTableSelection();
      onClose();
    });
  }, [editor, tableCellNode, clearTableSelection, onClose]);

  const deleteTableColumnAtSelection = useCallback(() => {
    editor.update(() => {
      const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);

      const tableColumnIndex = $getTableColumnIndexFromTableCellNode(tableCellNode);

      $deleteTableColumn(tableNode, tableColumnIndex);

      clearTableSelection();
      onClose();
    });
  }, [editor, tableCellNode, clearTableSelection, onClose]);

  const toggleTableRowIsHeader = useCallback(() => {
    editor.update(() => {
      const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);

      const tableRowIndex = $getTableRowIndexFromTableCellNode(tableCellNode);

      const tableRows = tableNode.getChildren();

      if (tableRowIndex >= tableRows.length || tableRowIndex < 0) {
        throw new Error('Expected table cell to be inside of table row.');
      }

      const tableRow = tableRows[tableRowIndex];

      if (!$isTableRowNode(tableRow)) {
        throw new Error('Expected table row');
      }

      tableRow.getChildren().forEach((tableCell) => {
        if (!$isTableCellNode(tableCell)) {
          throw new Error('Expected table cell');
        }

        tableCell.toggleHeaderStyle(TableCellHeaderStates.ROW);
      });

      clearTableSelection();
      onClose();
    });
  }, [editor, tableCellNode, clearTableSelection, onClose]);

  const toggleTableColumnIsHeader = useCallback(() => {
    editor.update(() => {
      const tableNode = $getTableNodeFromLexicalNodeOrThrow(tableCellNode);

      const tableColumnIndex = $getTableColumnIndexFromTableCellNode(tableCellNode);

      const tableRows = tableNode.getChildren();

      for (let r = 0; r < tableRows.length; r++) {
        const tableRow = tableRows[r];

        if (!$isTableRowNode(tableRow)) {
          throw new Error('Expected table row');
        }

        const tableCells = tableRow.getChildren();

        if (tableColumnIndex >= tableCells.length || tableColumnIndex < 0) {
          throw new Error('Expected table cell to be inside of table row.');
        }

        const tableCell = tableCells[tableColumnIndex];

        if (!$isTableCellNode(tableCell)) {
          throw new Error('Expected table cell');
        }

        tableCell.toggleHeaderStyle(TableCellHeaderStates.COLUMN);
      }

      clearTableSelection();
      onClose();
    });
  }, [editor, tableCellNode, clearTableSelection, onClose]);

  const buttonList = useMemo(() => {
    return [
      [
        {
          label: t('insertRowAbove', { count: selectionCounts.rows }),
          onClick: () => insertTableRowAtSelection(false),
        },
        {
          label: t('insertRowBelow', { count: selectionCounts.rows }),
          onClick: () => insertTableRowAtSelection(true),
        },
      ],
      [
        {
          label: t('insertColumnLeft', { count: selectionCounts.columns }),
          onClick: () => insertTableColumnAtSelection(false),
        },
        {
          label: t('insertColumnRight', { count: selectionCounts.columns }),
          onClick: () => insertTableColumnAtSelection(true),
        },
      ],
      [
        {
          label: t('deleteColumn'),
          onClick: () => deleteTableColumnAtSelection(),
        },
        {
          label: t('deleteRow'),
          onClick: () => deleteTableRowAtSelection(),
        },
        {
          label: t('deleteTable'),
          onClick: () => deleteTableAtSelection(),
        },
      ],
      [
        {
          label: t(
            `${
              (tableCellNode.__headerState & TableCellHeaderStates.ROW) === TableCellHeaderStates.ROW
                ? 'removeRowHeader'
                : 'addRowHeader'
            }`
          ),
          onClick: () => toggleTableRowIsHeader(),
        },
        {
          label: t(
            `${
              (tableCellNode.__headerState & TableCellHeaderStates.COLUMN) === TableCellHeaderStates.COLUMN
                ? 'removeColumnHeader'
                : 'addColumnHeader'
            }`
          ),
          onClick: () => toggleTableColumnIsHeader(),
        },
      ],
    ];
  }, [
    deleteTableAtSelection,
    deleteTableColumnAtSelection,
    deleteTableRowAtSelection,
    insertTableColumnAtSelection,
    insertTableRowAtSelection,
    selectionCounts.columns,
    selectionCounts.rows,
    t,
    tableCellNode.__headerState,
    toggleTableColumnIsHeader,
    toggleTableRowIsHeader,
  ]);

  return (
    <div
      className="bg-base-00 ring-base-25 absolute z-10 mb-4 mr-2 flex flex-col overflow-hidden rounded-md shadow-lg ring-1 ring-opacity-5 focus:outline-none"
      ref={dropDownRef}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {buttonList.map((buttonGroup, index) => (
        <Fragment key={`table-action-menu-section-${index}`}>
          {buttonGroup.map((button) => (
            <button
              key={button.label}
              type="button"
              name={button.label}
              className="w-100 hover:bg-base-15 flex cursor-pointer p-2"
              onClick={button.onClick}
            >
              <Text className="text-base-85 text-sm">{button.label}</Text>
            </button>
          ))}
          <hr className="border-base-35" />
        </Fragment>
      ))}
    </div>
  );
}

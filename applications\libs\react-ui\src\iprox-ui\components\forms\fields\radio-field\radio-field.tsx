import cx from 'classnames';
import { FieldInputProps, useField } from 'formik';
import { useId } from 'react';

import { FormFieldGroup } from '../../form-group/form-group';
import { InputAttributes, useFormField } from '../../hooks/use-form-field.hook';
import { MultiFieldDefinition } from '../../models/form.models';
import { borderClassname } from '../../utils/border-classname';

export function RadioGroup(props: MultiFieldDefinition) {
  const [field, meta, _helpers] = useField(props);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'radio');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  return (
    <FormFieldGroup
      definition={props}
      errorMessage={meta.touched ? meta.error : undefined}
      {...formControlProps}
      displayModeRow={props.displayModeRow}
      value={field.value}
    >
      {props.options?.map(({ label, value }) => (
        <RadioField
          key={`radio-field-${value}`}
          value={value}
          inputProps={inputProps}
          field={field}
          error={meta.touched ? meta.error : undefined}
          displayModeRow={props.displayModeRow}
        >
          {label}
        </RadioField>
      ))}
    </FormFieldGroup>
  );
}

interface RadioFieldProps {
  children: string;
  inputProps: InputAttributes;
  value: string;
  field: FieldInputProps<unknown>;
  description?: string;
  error?: string;
  displayModeRow?: boolean;
}

export function RadioField({
  inputProps,
  field,
  value,
  children,
  description,
  error,
  displayModeRow = false,
}: RadioFieldProps) {
  const id = useId();

  return (
    <label
      htmlFor={id}
      className={cx(
        'font-text text-body flex w-fit cursor-pointer flex-row text-sm font-medium last:mb-0',
        {
          'mb-0': displayModeRow,
        },
        { 'mb-3.5': !displayModeRow }
      )}
    >
      <div className="relative mr-3 h-5 w-5">
        <span
          className={cx('bg-content-extra-lite absolute inset-0 scale-0 rounded-full opacity-50', {
            'animate-ripple': field.value === value,
          })}
        ></span>
        <input
          {...inputProps}
          id={id}
          type="radio"
          {...field}
          className={cx(
            'relative z-10 flex h-full w-full cursor-pointer items-center justify-center align-top focus:ring-0 focus:ring-offset-0',
            field.value === value
              ? "text-body after:outline-base-00 after:bg-highlight after:contents[''] after:outline-3 after:absolute after:inset-0 after:block after:rounded-full after:outline after:outline-offset-[-4px]"
              : 'text-base-25',
            borderClassname(error, value)
          )}
          value={value}
          checked={field.value === value}
        />
      </div>
      <div>
        {children}
        {description && <div className="font-text text-base-75 text-sm">{description}</div>}
      </div>
    </label>
  );
}

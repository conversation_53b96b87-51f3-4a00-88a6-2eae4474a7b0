import { components } from '@/iprox-open.interface';

export interface ContentZonesProps {
  pageZones: components['schemas']['PageZoneDto'][];
}

export function ContentZones({ pageZones }: ContentZonesProps) {
  // Avoid string concatenation for TailwindCSS compatibility
  const getColSpanForLargeScreen = (
    colspan: number
  ): 'lg:col-span-12' | 'lg:col-span-8' | 'lg:col-span-6' | 'lg:col-span-4' => {
    switch (colspan) {
      case 8:
        return 'lg:col-span-8';
      case 6:
        return 'lg:col-span-6';
      case 4:
        return 'lg:col-span-4';
      default:
        return 'lg:col-span-12';
    }
  };

  return (
    <div className="flex flex-col gap-y-6">
      {pageZones.map((zone) => (
        <div key={zone.id} className="grid grid-cols-12 gap-y-6 lg:flex-row lg:gap-x-12">
          {zone.blocks.map((block) => (
            <div
              key={block.id}
              dangerouslySetInnerHTML={{
                __html: block.blockContent.content ?? '',
              }}
              className={`col-span-12 break-words ${getColSpanForLargeScreen(block.colspan)}`}
            ></div>
          ))}
        </div>
      ))}
    </div>
  );
}

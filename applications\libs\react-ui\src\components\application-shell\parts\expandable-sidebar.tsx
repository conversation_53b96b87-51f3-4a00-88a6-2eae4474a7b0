import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { Fragment } from 'react';

import styles from '../application-shell.module.scss';
import { closeSideNavigation, toggleSideNavigation } from '../context/application-shell.actions';
import { useApplicationShellContext } from '../context/application-shell.context';

interface ExpandableSidebarProps {
  children: React.ReactNode;
  logo?: React.ReactNode;
}

export function ExpandableSidebar(props: ExpandableSidebarProps) {
  const { state, dispatch } = useApplicationShellContext();

  return (
    <Transition.Root show={state.isSideNavigationOpen ?? false} as={Fragment}>
      <Dialog
        as="div"
        className={`${styles['ipx-application-shell']} bg-base-10 relative z-50 lg:hidden`}
        onClose={() => dispatch(closeSideNavigation())}
      >
        <Transition.Child
          as={Fragment}
          enter="transition-opacity ease-iprox duration-standard"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="transition-opacity ease-iprox duration-standard"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-900/80" />
        </Transition.Child>

        <div className="fixed inset-0 flex">
          <Transition.Child
            as={Fragment}
            enter="transition ease-iprox duration-standard transform"
            enterFrom="-translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-iprox duration-standard transform"
            leaveFrom="translate-x-0"
            leaveTo="-translate-x-full"
          >
            <Dialog.Panel className="bg-base-10 relative mr-16 flex w-full max-w-xs flex-1">
              <Transition.Child
                as={Fragment}
                enter="ease-iprox duration-standard"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-iprox duration-standard"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                  <button type="button" className="-m-2.5 p-2.5" onClick={() => dispatch(toggleSideNavigation())}>
                    <span className="sr-only">Close sidebar</span>
                    <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </Transition.Child>

              <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4">
                <div className="flex h-16 shrink-0 items-center">
                  <Link href="/">{props.logo ? props.logo : null}</Link>
                </div>
                {props.children}
              </div>
            </Dialog.Panel>
          </Transition.Child>
        </div>
      </Dialog>
    </Transition.Root>
  );
}

import nl from 'date-fns/locale/nl';
import { FieldHookConfig, useField } from 'formik';
import { useLocale } from 'next-intl';
import { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { DateFieldDefinition } from '../../models/form.models';
import './date-range-picker.scss';

interface Props extends DateFieldDefinition {
  minDate?: string;
  maxDate?: string;
  initialDateRange?: [Date | null, Date | null];
}

export function DateRangePickerField(props: Props) {
  const locale = useLocale();

  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>(props.initialDateRange || [null, null]);
  const [startDate, endDate] = dateRange;

  const updatedProps = {
    ...props,
    defaultValue: startDate?.toISOString(),
  };
  const [field, meta, helpers] = useField(updatedProps as FieldHookConfig<[Date | null, Date | null]>);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'date');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  const handleDateChange = (update: [Date | null, Date | null]) => {
    setDateRange(update);
    helpers.setValue(update, true);
  };

  return (
    <FormField definition={props} errorMessage={meta.touched ? meta.error : undefined} {...formControlProps}>
      <DatePicker
        selectsRange={true}
        startDate={startDate}
        endDate={endDate}
        onChange={handleDateChange}
        showPopperArrow={false}
        id={inputProps?.id}
        ariaDescribedBy={inputProps?.['aria-describedby']}
        name={field.name}
        onBlur={() => helpers.setTouched(true, true)}
        minDate={props.minDate ? new Date(props.minDate) : undefined}
        maxDate={props.maxDate ? new Date(props.maxDate) : undefined}
        isClearable={true}
        wrapperClassName="date-range-picker-wrapper"
        locale={locale === 'nl' ? nl : undefined}
        dateFormat={locale === 'nl' ? 'dd-MM-yyyy' : 'dd/MM/yyyy'}
      />
    </FormField>
  );
}

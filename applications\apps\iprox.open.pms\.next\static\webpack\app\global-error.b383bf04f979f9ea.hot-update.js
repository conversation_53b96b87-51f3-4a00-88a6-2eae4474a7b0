"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/global-error",{

/***/ "(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx":
/*!**********************************************************************************!*\
  !*** ../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx ***!
  \**********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DossierViewLink: function() { return /* binding */ DossierViewLink; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nfunction DossierViewLink(param) {\n    let { url, label } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)(\"components.dossierViewLink\");\n    if (url) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            target: \"_blank\",\n            rel: \"noreferrer\",\n            href: url,\n            className: \"text-base-00 font-text-regular underline\",\n            children: label ? label : t(\"view\")\n        }, void 0, false, {\n            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\dossier-view-link\\\\dossier-view-link.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: \"font-text-regular text-base-25 text-sm\",\n        children: t(\"noAssociatedPage\")\n    }, void 0, false, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\dossier-view-link\\\\dossier-view-link.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\n_s(DossierViewLink, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations\n    ];\n});\n_c = DossierViewLink;\nvar _c;\n$RefreshReg$(_c, \"DossierViewLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9saWJzL3JlYWN0LXVpL3NyYy9jb21wb25lbnRzL2Rvc3NpZXItdmlldy1saW5rL2Rvc3NpZXItdmlldy1saW5rLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEM7QUFPckMsU0FBU0MsZ0JBQWdCLEtBQW9DO1FBQXBDLEVBQUVDLEdBQUcsRUFBRUMsS0FBSyxFQUF3QixHQUFwQzs7SUFDOUIsTUFBTUMsSUFBSUosMERBQWVBLENBQUM7SUFFMUIsSUFBSUUsS0FBSztRQUNQLHFCQUNFLDhEQUFDRztZQUFFQyxRQUFPO1lBQVNDLEtBQUk7WUFBYUMsTUFBTU47WUFBS08sV0FBVTtzQkFDdEROLFFBQVFBLFFBQVFDLEVBQUU7Ozs7OztJQUd6QjtJQUNBLHFCQUFPLDhEQUFDTTtRQUFFRCxXQUFVO2tCQUEwQ0wsRUFBRTs7Ozs7O0FBQ2xFO0dBWGdCSDs7UUFDSkQsc0RBQWVBOzs7S0FEWEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL2xpYnMvcmVhY3QtdWkvc3JjL2NvbXBvbmVudHMvZG9zc2llci12aWV3LWxpbmsvZG9zc2llci12aWV3LWxpbmsudHN4PzAxNzIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSAnbmV4dC1pbnRsJztcclxuXHJcbmludGVyZmFjZSBEb3NzaWVyVmlld0xpbmtQcm9wcyB7XHJcbiAgdXJsOiBzdHJpbmc7XHJcbiAgbGFiZWw6IHN0cmluZyB8IG51bGw7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBEb3NzaWVyVmlld0xpbmsoeyB1cmwsIGxhYmVsIH06IERvc3NpZXJWaWV3TGlua1Byb3BzKSB7XHJcbiAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucygnY29tcG9uZW50cy5kb3NzaWVyVmlld0xpbmsnKTtcclxuXHJcbiAgaWYgKHVybCkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGEgdGFyZ2V0PVwiX2JsYW5rXCIgcmVsPVwibm9yZWZlcnJlclwiIGhyZWY9e3VybH0gY2xhc3NOYW1lPVwidGV4dC1iYXNlLTAwIGZvbnQtdGV4dC1yZWd1bGFyIHVuZGVybGluZVwiPlxyXG4gICAgICAgIHtsYWJlbCA/IGxhYmVsIDogdCgndmlldycpfVxyXG4gICAgICA8L2E+XHJcbiAgICApO1xyXG4gIH1cclxuICByZXR1cm4gPHAgY2xhc3NOYW1lPVwiZm9udC10ZXh0LXJlZ3VsYXIgdGV4dC1iYXNlLTI1IHRleHQtc21cIj57dCgnbm9Bc3NvY2lhdGVkUGFnZScpfTwvcD47XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVRyYW5zbGF0aW9ucyIsIkRvc3NpZXJWaWV3TGluayIsInVybCIsImxhYmVsIiwidCIsImEiLCJ0YXJnZXQiLCJyZWwiLCJocmVmIiwiY2xhc3NOYW1lIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../libs/react-ui/src/components/status-box/status-box.tsx":
/*!********************************************************************!*\
  !*** ../../libs/react-ui/src/components/status-box/status-box.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatusBox: function() { return /* binding */ StatusBox; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/../../node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _iprox_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../iprox-ui */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/index.ts\");\n/* harmony import */ var _iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../iprox-ui/components/forms/fields/date-time-picker/date-time-picker */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/components/forms/fields/date-time-picker/date-time-picker.tsx\");\n/* harmony import */ var _dossier_view_link_dossier_view_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dossier-view-link/dossier-view-link */ \"(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst AutoSubmitForm = ()=>{\n    _s();\n    const { values, submitForm } = (0,formik__WEBPACK_IMPORTED_MODULE_1__.useFormikContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (values.fromDate) {\n            submitForm();\n        }\n    }, [\n        values,\n        submitForm\n    ]);\n    return null;\n};\n_s(AutoSubmitForm, \"eluiyIPyaFuYIzYXLcNy1h5e6gs=\", false, function() {\n    return [\n        formik__WEBPACK_IMPORTED_MODULE_1__.useFormikContext\n    ];\n});\n_c = AutoSubmitForm;\nfunction StatusBox(param) {\n    let { children, editStatus, dossierLiveStatus, publishDates, dossierId, categoryId, pages, portalUrl, submitForm } = param;\n    _s1();\n    const format = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useFormatter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"components.statusBox\");\n    const getStatusTranslationKey = (status)=>{\n        switch(status){\n            case \"Published\":\n                return \"lastPublished\";\n            case \"Unpublished\":\n                return \"lastUnpublished\";\n            default:\n                return null;\n        }\n    };\n    const initialValues = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            fromDate: (publishDates === null || publishDates === void 0 ? void 0 : publishDates.fromDate) || new Date(),\n            toDate: (publishDates === null || publishDates === void 0 ? void 0 : publishDates.toDate) || null\n        };\n    }, [\n        publishDates\n    ]);\n    const pageSlug = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const pagesWithCategory = pages.filter((page)=>page.pageState === \"Published\").filter((page)=>{\n            var _page_categories;\n            return (_page_categories = page.categories) === null || _page_categories === void 0 ? void 0 : _page_categories.some((category)=>category.id === categoryId);\n        }).sort((a, b)=>a.categories.length - b.categories.length);\n        return pagesWithCategory === null || pagesWithCategory === void 0 ? void 0 : pagesWithCategory.map((page)=>({\n                label: page.label,\n                slug: page.slug\n            }));\n    }, [\n        categoryId,\n        pages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sticky top-[110px] h-fit max-w-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-input bg-highlight p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-base-00 font-heading mb-2 text-2xl font-semibold\",\n                            children: t(\"dossier\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_1__.Formik, {\n                        initialValues: initialValues,\n                        onSubmit: (values)=>{\n                            submitForm === null || submitForm === void 0 ? void 0 : submitForm(values);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_1__.Form, {\n                            className: \"my-5 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AutoSubmitForm, {}, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__.DateTimePickerField, {\n                                        name: \"fromDate\",\n                                        label: t(\"dossierDate\"),\n                                        fieldType: _iprox_ui__WEBPACK_IMPORTED_MODULE_3__.FieldType.DateTime,\n                                        labelColor: \"text-base-00\",\n                                        descriptionColor: \"text-base-25\",\n                                        validationRules: [],\n                                        popperPlacement: \"left\",\n                                        description: t(\"dossierDateDescription\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__.DateTimePickerField, {\n                                        name: \"toDate\",\n                                        label: t(\"expirationDate\"),\n                                        fieldType: _iprox_ui__WEBPACK_IMPORTED_MODULE_3__.FieldType.DateTime,\n                                        labelColor: \"text-base-00\",\n                                        descriptionColor: \"text-base-25\",\n                                        validationRules: [],\n                                        popperPlacement: \"left\",\n                                        minDate: initialValues.fromDate,\n                                        isClearable: true,\n                                        description: t(\"expirationDateDescription\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-input bg-highlight mt-6 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-base-00 font-heading mb-2 text-2xl font-semibold\",\n                                children: t(\"title\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base-00 font-text capitalize\",\n                                children: t(editStatus.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            editStatus.status === \"modified\" && editStatus.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base-00 font-text\",\n                                children: [\n                                    format.dateTime(new Date(editStatus.date), {\n                                        dateStyle: \"short\"\n                                    }),\n                                    \" \",\n                                    format.dateTime(new Date(editStatus.date), {\n                                        timeStyle: \"short\"\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            getStatusTranslationKey(dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-base-00 font-heading text-lg font-bold\",\n                                children: t(getStatusTranslationKey(dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.status))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            (dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.date) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base-00 font-text\",\n                                children: [\n                                    format.dateTime(new Date(dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.date), {\n                                        dateStyle: \"short\"\n                                    }),\n                                    \" \",\n                                    format.dateTime(new Date(dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.date), {\n                                        timeStyle: \"short\"\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    editStatus.status === \"published\" || (dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.status) === \"Published\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-base-00 font-heading text-lg font-bold\",\n                                children: t(\"visibility\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            pageSlug === null || pageSlug === void 0 ? void 0 : pageSlug.map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dossier_view_link_dossier_view_link__WEBPACK_IMPORTED_MODULE_5__.DossierViewLink, {\n                                        url: \"\".concat(portalUrl, \"/\").concat(page.slug, \"/\").concat(dossierId),\n                                        label: page.label\n                                    }, page.slug, false, {\n                                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this) : null\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s1(StatusBox, \"2JmU5KFwTFr6cstnOZ61+Ev0kEA=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useFormatter,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations\n    ];\n});\n_c1 = StatusBox;\nvar _c, _c1;\n$RefreshReg$(_c, \"AutoSubmitForm\");\n$RefreshReg$(_c1, \"StatusBox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../libs/react-ui/src/components/status-box/status-box.tsx\n"));

/***/ })

});
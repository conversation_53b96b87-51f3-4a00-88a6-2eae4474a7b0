import { type Locator, type Page, expect } from '@playwright/test';

export class PageList {
  private page: Page;
  private newPageFurthermoreButton: Locator;
  private searchPageZoneModalButton: Locator;
  private searchPageIntroText: Locator;
  private AddNewPageButton: Locator;
  private pagesSearchPageButton: Locator;
  private newPageTitleInput: Locator;
  private modal: Locator;
  private modalSearchPageButton: Locator;
  private modalPageTypeButton: Locator;
  private modalSimplePageTypeButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.modal = page.getByText('BekendmakingenZoekpaginaEenvoudige paginaTitelVoer de naam van het nieuwe');
    this.modalSearchPageButton = this.page.getByText('Zoekpagina', { exact: true });
    this.modalSimplePageTypeButton = this.page.getByText('Eenvoudige pagina');
    this.AddNewPageButton = page.getByRole('button', { name: 'Voeg een pagina toe' });
    this.pagesSearchPageButton = page.getByRole('button', { name: 'Zoekpagina' });
    this.newPageTitleInput = this.modal.getByText('Titel');
    this.newPageFurthermoreButton = page.getByText('Verder');
    this.searchPageZoneModalButton = page.getByRole('button', { name: '4,4,4' });
    this.searchPageIntroText = page.getByTestId('modal').getByLabel('Titel');
  }

  async clickAddNewPageButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.AddNewPageButton.click();
  }

  async clickModalSimplePageButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.modalSimplePageTypeButton.click();
  }

  async clickModalSearchPageButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.modalSearchPageButton.click();
  }

  async clickModalPageTypeButton(modalPageTypeButton: string) {
    await this.page.waitForLoadState('domcontentloaded');
    this.modalPageTypeButton = this.modal.getByText(modalPageTypeButton);
    await this.modalPageTypeButton.click();
  }

  async clickNewPageFurthermoreButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.newPageFurthermoreButton.click();
  }

  async clickSearchPageZoneModalButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.searchPageZoneModalButton.click();
  }

  async inputNewPageTitle(text: string) {
    await this.page.waitForLoadState('domcontentloaded');
    await this.newPageTitleInput.click();
    await this.newPageTitleInput.fill(text);
  }
  async inputSearchPageIntroText(text: string) {
    await this.page.waitForLoadState('domcontentloaded');
    await this.searchPageIntroText.fill(text);
  }

  async selectOption(option?: 'Bekendmakingen' | 'Zoekpagina' | 'Eenvoudige pagina') {
    const label = this.page.getByText('Zoekpagina');
    await label.click();
  }

  async assertPageInList(pageName: string) {
    await this.page.waitForLoadState('domcontentloaded');
    const thisPageName = this.page.getByRole('link', { name: pageName });
    await expect(thisPageName).toBeVisible();
  }
}

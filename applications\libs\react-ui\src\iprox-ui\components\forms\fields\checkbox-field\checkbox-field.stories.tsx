import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { CheckboxField } from './checkbox-field';

const Story: Meta<typeof CheckboxField> = {
  component: CheckboxField,
  title: 'iprox-ui/forms/fields/checkbox',
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default Story;

export const Base: StoryObj = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'checkbox--field',
    fieldType: FieldType.CheckBox,
  },
};

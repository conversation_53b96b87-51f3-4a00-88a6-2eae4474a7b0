import { getFooterComponentContent } from '@/services/component-footer-service';
import { getNavigationStructure } from '@/services/navigation-service.server';

import { Navbar } from './navbar';
import { SiteFooter } from './site-footer';

interface LayoutWrapperProps {
  children: React.ReactNode;
  logoAssetPath?: string | null;
}

export async function LayoutWrapper({ children, logoAssetPath }: LayoutWrapperProps) {
  const data = await getNavigationStructure();
  const footerData = await getFooterComponentContent();

  const navOptions = data?.navigation.map((item) => ({
    name: item.name,
    href: item.page?.id ? `/${item.page?.slug}` : '',
    children:
      item.children?.map((child) => ({
        name: child.name,
        href: child.page?.id ? `/${child.page?.slug}` : '',
        children: [],
      })) ?? [],
  }));

  return (
    <>
      <Navbar logoAssetPath={logoAssetPath ?? ''} navOptions={navOptions} />
      <div className="bg-base-00 mt-navbar flex h-full w-full flex-1 flex-col items-center lg:mt-0">{children}</div>
      <SiteFooter pageZones={footerData.footerComponent.pageZones} />
    </>
  );
}

import { colors } from '../../config';
import * as http from '../../enums/httpEnums';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';

let dossierId: string;

describe('Delete dossier', () => {
  let bearerToken: any;
  let dossierCategoryId: string | null;
  beforeAll(async () => {
    bearerToken = await loginAndGetToken();
    dossierCategoryId = await DossierHelpersNew.getRandomCategoryUuid(false, bearerToken);
    const response: any = await DossierHelpersNew.createDossier('Delete dossier', bearerToken, dossierCategoryId);
    dossierId = await response.body.dossier.dossierId;
  }, 90000);

  it('should delete the created dossier', async () => {
    console.log(colors.red + `Deleting dossier with dossier id: ` + dossierId + colors.reset);
    const response: any = await DossierHelpersNew.deleteDossier(dossierId, bearerToken);

    console.log('Dossier deletion success.' + (await response.text));
    expect(await response.status).toBe(http.StatusCode.OK_200);
  }, 90000);

  afterAll(() => {});
});

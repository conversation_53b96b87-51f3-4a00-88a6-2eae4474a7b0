export type BaseNode = {
  nodeName: string;
  nodeId: string;
  nodeType: 'Folder' | 'File';
  isSelected: boolean;
  parentId?: string;
};

export type FolderNode = BaseNode & {
  nodeType: 'Folder';
  numberOfDescendantFiles: number;
  numberOfDescendantFolders: number;
  totalDescendantSize: number;
  isExpanded: boolean;
  children: Node[];
};

export type FileNode = BaseNode & {
  nodeType: 'File';
  /** Filesize in bytes */
  size: number;
  blobReference?: string;
};

export type Node = FolderNode | FileNode;

import { getPagesPaged } from '@/services/page-service.server';

import { PagesList } from '@/components/pages-list';

export default async function Page({ searchParams }: { searchParams: Record<string, string> }) {
  const { count = '30', start = '0' } = searchParams;

  const pagesData = await getPagesPaged({
    count,
    start,
  });

  return (
    <PagesList
      pages={pagesData.items ?? []}
      count={pagesData.count ?? 0}
      start={pagesData.start ?? 0}
      totalCount={pagesData.totalCount ?? 0}
    />
  );
}

import { addClassNamesToElement } from '@lexical/utils';
import type {
  DOMConversionMap,
  DOMConversionOutput,
  EditorConfig,
  LexicalCommand,
  LexicalNode,
  NodeKey,
  RangeSelection,
  SerializedElementNode,
  SerializedLexicalNode,
} from 'lexical';
import { $createTextNode, $getNodeByKey, $getSelection, $isElementNode, ElementNode, createCommand } from 'lexical';

import theme from '../themes/theme';
import { sanitizeUrl } from '../utils/url';
import { ChangeLinkParams, RemoveLinkParams, ToggleLinkParams } from './link';

interface SerializedSuperLinkNode extends SerializedElementNode<SerializedLexicalNode> {
  link?: string;
}

export class SuperLinkNode extends ElementNode {
  __url: string;
  __target?: string;
  __rel?: string;

  static portalUrl: string;

  static getType(): string {
    return 'superlink';
  }

  static clone(node: SuperLinkNode): SuperLinkNode {
    return new SuperLinkNode(node.__url, node.__key, node.__target, node.__rel);
  }

  constructor(url: string, key?: NodeKey, target?: string, rel?: string) {
    super(key);
    this.__url = url;
    this.__target = target;
    this.__rel = rel;
  }

  createDOM(config: EditorConfig): HTMLElement {
    const element = document.createElement('a');
    element.href = this.__url;

    if (this.__target) {
      element.target = this.__target;
    }

    if (this.__rel) {
      element.rel = this.__rel;
    }

    if (this.__title) {
      element.innerText = this.__title;
    }

    addClassNamesToElement(element, config.theme.superlink);
    return element;
  }

  updateDOM(prevNode: SuperLinkNode, dom: HTMLElement): boolean {
    if (dom instanceof HTMLAnchorElement) {
      const anchor: HTMLAnchorElement = dom;
      const url = this.__url;
      const target = this.__target;

      if (url !== prevNode.__url) {
        anchor.href = url;
      }

      if (target) {
        anchor.target = target;
      } else {
        anchor.removeAttribute('target');
      }
    }

    return false;
  }

  static importDOM(): DOMConversionMap | null {
    return {
      a: (node) => {
        if (node.classList.contains(theme.superlink)) {
          return {
            conversion: convertAnchorElement,
            priority: 1,
          };
        }

        return null;
      },
    };
  }

  static importJSON(jsonNode: SerializedSuperLinkNode): SuperLinkNode {
    const node = $createSuperLinkNode(jsonNode.link || '');
    node.setFormat(jsonNode.format);
    node.setIndent(jsonNode.indent);
    node.setDirection(jsonNode.direction);
    return node;
  }

  exportJSON(): SerializedSuperLinkNode {
    return {
      ...super.exportJSON(),
      type: 'super-link',
      version: 1,
      link: this.__url,
    };
  }

  getURL(): string {
    return this.getLatest().__url;
  }

  setURL(url: string): void {
    const writable = this.getWritable();
    writable.__url = url;
  }

  getTarget(): string | undefined {
    return this.getLatest().__target;
  }

  setTarget(target?: string): void {
    const writable = this.getWritable();
    writable.__target = target ?? undefined;
  }

  insertNewAfter(selection: RangeSelection): null | ElementNode {
    const element = this.getParentOrThrow().insertNewAfter(selection);
    if ($isElementNode(element)) {
      const linkNode = $createSuperLinkNode(this.__url);
      element.append(linkNode);
      return linkNode;
    }
    return null;
  }

  canInsertTextBefore(): false {
    return false;
  }

  canInsertTextAfter(): false {
    return false;
  }

  canBeEmpty(): boolean {
    return false;
  }

  isInline(): true {
    return true;
  }

  static setConfig({ portalUrl }: { portalUrl: string }) {
    SuperLinkNode.portalUrl = portalUrl;
  }
}

function convertAnchorElement(domNode: Node): DOMConversionOutput {
  let node = null;
  if (domNode instanceof HTMLAnchorElement) {
    if (sanitizeUrl(domNode.href, SuperLinkNode.portalUrl)) {
      node = $createSuperLinkNode(domNode.href);
    } else {
      node = $createSuperLinkNode('');
    }
  }
  return { node };
}

export function $createSuperLinkNode(url: string, target?: string, rel?: string): SuperLinkNode {
  return new SuperLinkNode(url, undefined, target, rel);
}

export function $isSuperLinkNode(node?: LexicalNode): boolean {
  return node instanceof SuperLinkNode;
}

export const TOGGLE_SUPER_LINK_COMMAND: LexicalCommand<ToggleLinkParams> = createCommand();
export const CHANGE_SUPER_LINK_COMMAND: LexicalCommand<ChangeLinkParams> = createCommand();
export const REMOVE_SUPER_LINK_COMMAND: LexicalCommand<RemoveLinkParams> = createCommand();

export function toggleSuperLink({ url, title, target, rel }: ToggleLinkParams) {
  if (url && title) {
    const linkItem = $createSuperLinkNode(url, target, rel);
    const textNode = $createTextNode(title);

    linkItem.append(textNode);

    const selection = $getSelection();

    if (selection) {
      selection.insertNodes([$createTextNode(''), linkItem, $createTextNode('')]);
    }

    $getSelection();
  }

  return null;
}

export function editSuperLink(props: ChangeLinkParams) {
  const linkNode = $getNodeByKey(props.nodeKey) as SuperLinkNode;
  linkNode.setURL(props.url);
  linkNode.setTarget(props.target);

  const [textNode] = linkNode.getAllTextNodes();
  textNode.setTextContent(props.title || '');
}

export function removeSuperLink(props: RemoveLinkParams) {
  const linkNode = $getNodeByKey(props.nodeKey) as SuperLinkNode;
  const [textNode] = linkNode.getAllTextNodes();
  linkNode.remove();

  const selection = $getSelection();

  if (selection) {
    selection.insertNodes([textNode]);
  }

  $getSelection();
}

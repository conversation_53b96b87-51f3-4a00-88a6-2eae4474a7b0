import { TableCellsIcon } from '@heroicons/react/24/outline';
import {
  $isListNode,
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
  ListNode,
  REMOVE_LIST_COMMAND,
} from '@lexical/list';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { NodeEventPlugin } from '@lexical/react/LexicalNodeEventPlugin';
import { $isHeadingNode } from '@lexical/rich-text';
import { $findMatchingParent, $getNearestNodeOfType, mergeRegister } from '@lexical/utils';
import cx from 'classnames';
import {
  $getNodeByKey,
  $getSelection,
  $isRangeSelection,
  $isRootOrShadowRoot,
  CAN_REDO_COMMAND,
  CAN_UNDO_COMMAND,
  COMMAND_PRIORITY_CRITICAL,
  FORMAT_TEXT_COMMAND,
  LexicalEditor,
  REDO_COMMAND,
  SELECTION_CHANGE_COMMAND,
  UNDO_COMMAND,
} from 'lexical';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';

import { IconButton } from '../../../../icon-button/icon-button';
import { BlockFormatDropDown, BlockType } from '../components/block-format-dropdown';
import ToolbarBoldIcon from '../components/icons/bold-icon';
import ToolbarBulletListIcon from '../components/icons/bullte-list-icon';
import ToolbarItalicIcon from '../components/icons/italic-icon';
import ToolbarLinkIcon from '../components/icons/link-icon';
import ToolbarNumberListIcon from '../components/icons/number-list-icon';
import ToolbarRedoIcon from '../components/icons/redo-icon';
import ToolbarSuperLinkIcon from '../components/icons/superlink-icon';
import ToolbarUnderlineIcon from '../components/icons/underline-icon';
import ToolbarUndoIcon from '../components/icons/undo-icon';
import { InsertTableModal } from '../components/insert-table-modal';
import LinkEditor, { LinkProps } from '../components/link-editor';
import { $isLinkNode, CHANGE_LINK_COMMAND, LinkNode, REMOVE_LINK_COMMAND, TOGGLE_LINK_COMMAND } from '../nodes/link';
import {
  $isSuperLinkNode,
  CHANGE_SUPER_LINK_COMMAND,
  REMOVE_SUPER_LINK_COMMAND,
  SuperLinkNode,
  TOGGLE_SUPER_LINK_COMMAND,
} from '../nodes/super-link';
import { getSelectedNode } from '../utils/getSelectedNode';

interface ToolbarPluginProps {
  enableSuperlink?: boolean;
}

export default function ToolbarPlugin({ enableSuperlink }: ToolbarPluginProps): JSX.Element {
  const [editor] = useLexicalComposerContext();
  const [activeEditor, setActiveEditor] = useState(editor);
  const [blockType, setBlockType] = useState<BlockType>('paragraph');
  const [isLink, setIsLink] = useState(false);
  const [isSuperLink, setIsSuperLink] = useState(false);
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [isEditable, setIsEditable] = useState(() => editor.isEditable());
  const [urlEditorOpen, setUrlEditorOpen] = useState(false);
  const [addTabelModalOpen, setAddTabelModalOpen] = useState(false);

  const [urlEditorLinkAttr, setUrlEditorLinkAttr] = useState<{
    isSuperLink: boolean;
    nodeKey: string | null;
    defaultUrl?: string;
    defaultTitle?: string;
    defaultNewTab?: boolean;
    isEdit: boolean;
  }>({
    isSuperLink: false,
    nodeKey: null,
    defaultUrl: '',
    defaultTitle: '',
    defaultNewTab: true,
    isEdit: false,
  });

  const IS_APPLE = false;

  const t = useTranslations('components.richtexteditor');

  const updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const anchorNode = selection.anchor.getNode();
      let element =
        anchorNode.getKey() === 'root'
          ? anchorNode
          : $findMatchingParent(anchorNode, (e) => {
              const parent = e.getParent();
              return parent !== null && $isRootOrShadowRoot(parent);
            });

      if (element === null) {
        element = anchorNode.getTopLevelElementOrThrow();
      }

      const elementKey = element.getKey();
      const elementDOM = activeEditor.getElementByKey(elementKey);

      // Update text format
      setIsBold(selection.hasFormat('bold'));
      setIsItalic(selection.hasFormat('italic'));
      setIsUnderline(selection.hasFormat('underline'));

      // Update links
      const node = getSelectedNode(selection);
      const parent = node.getParent();

      if ((parent && $isLinkNode(parent)) || $isLinkNode(node)) {
        setIsLink(true);
      } else {
        setIsLink(false);
      }

      if ((parent && $isSuperLinkNode(parent)) || $isSuperLinkNode(node)) {
        setIsSuperLink(true);
      } else {
        setIsSuperLink(false);
      }

      if (elementDOM !== null) {
        if ($isListNode(element)) {
          const parentList = $getNearestNodeOfType<ListNode>(anchorNode, ListNode);
          const type = parentList ? parentList.getListType() : element.getListType();
          setBlockType(type);
        } else {
          const type = $isHeadingNode(element) ? element.getTag() : element.getType();
          if (type) {
            setBlockType(type as BlockType);
          }
        }
      }
    }
  }, [activeEditor]);

  useEffect(() => {
    return editor.registerCommand(
      SELECTION_CHANGE_COMMAND,
      (_payload, newEditor) => {
        updateToolbar();
        setActiveEditor(newEditor);
        return false;
      },
      COMMAND_PRIORITY_CRITICAL
    );
  }, [editor, updateToolbar]);

  useEffect(() => {
    return mergeRegister(
      editor.registerEditableListener((editable) => {
        setIsEditable(editable);
      }),
      activeEditor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updateToolbar();
        });
      }),
      activeEditor.registerCommand<boolean>(
        CAN_UNDO_COMMAND,
        (payload) => {
          setCanUndo(payload);
          return false;
        },
        COMMAND_PRIORITY_CRITICAL
      ),
      activeEditor.registerCommand<boolean>(
        CAN_REDO_COMMAND,
        (payload) => {
          setCanRedo(payload);
          return false;
        },
        COMMAND_PRIORITY_CRITICAL
      )
    );
  }, [activeEditor, editor, updateToolbar]);

  const addLink = (isSuperLink: boolean) => {
    editor.update(() => {
      setUrlEditorLinkAttr({
        isSuperLink,
        nodeKey: null,
        defaultTitle: $getSelection()?.getTextContent(),
        defaultUrl: '',
        isEdit: false,
      });

      setUrlEditorOpen(true);
    });
  };

  const handleEditorClose = (linkProps?: LinkProps) => {
    if (linkProps) {
      const { url, title, newTab } = linkProps;

      const attributes = {
        url,
        title,
        ...(newTab ? { target: '_blank' } : {}),
        rel: 'noopener noreferrer',
      };

      if (urlEditorLinkAttr.isSuperLink) {
        if (urlEditorLinkAttr.nodeKey) {
          editor.dispatchCommand(CHANGE_SUPER_LINK_COMMAND, {
            nodeKey: urlEditorLinkAttr.nodeKey,
            ...attributes,
          });
        } else {
          editor.dispatchCommand(TOGGLE_SUPER_LINK_COMMAND, {
            ...attributes,
          });
        }
      } else {
        if (urlEditorLinkAttr.nodeKey) {
          editor.dispatchCommand(CHANGE_LINK_COMMAND, {
            nodeKey: urlEditorLinkAttr.nodeKey,
            ...attributes,
          });
        } else {
          editor.dispatchCommand(TOGGLE_LINK_COMMAND, {
            ...attributes,
          });
        }
      }
    }

    setUrlEditorOpen(false);
  };

  const handleRemoveLink = () => {
    if (urlEditorLinkAttr.isSuperLink) {
      if (urlEditorLinkAttr.nodeKey) {
        editor.dispatchCommand(REMOVE_SUPER_LINK_COMMAND, {
          nodeKey: urlEditorLinkAttr.nodeKey,
        });
      }
    } else {
      if (urlEditorLinkAttr.nodeKey) {
        editor.dispatchCommand(REMOVE_LINK_COMMAND, {
          nodeKey: urlEditorLinkAttr.nodeKey,
        });
      }
    }

    setUrlEditorOpen(false);
  };

  const handleLinkClick = (e: Event, _editor: LexicalEditor, nodeKey: string) => {
    e.preventDefault();

    const linkNode = $getNodeByKey(nodeKey) as LinkNode | SuperLinkNode;
    const [textNode] = linkNode.getAllTextNodes();

    const title = textNode.getTextContent();
    const url = linkNode.getURL();
    const target = linkNode.getTarget();

    setUrlEditorLinkAttr({
      isSuperLink: linkNode instanceof SuperLinkNode,
      nodeKey,
      defaultTitle: title,
      defaultUrl: url,
      defaultNewTab: target === '_blank',
      isEdit: true,
    });

    setUrlEditorOpen(true);
  };

  const formatBulletList = () => {
    if (blockType !== 'bullet') {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }
  };

  const formatNumberedList = () => {
    if (blockType !== 'number') {
      editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }
  };

  const iconButtonClassName = '!w-10 !h-11 rounded-[10px]';

  return (
    <>
      <div className="bg-highlight rounded-tr-input mb-1 flex h-11 items-center rounded-tl-[10px]">
        <IconButton
          disabled={!canUndo || !isEditable}
          onClick={() => {
            activeEditor.dispatchCommand(UNDO_COMMAND, undefined);
          }}
          title={IS_APPLE ? `${t('undo')} (⌘Z)` : `${t('undo')} (Ctrl+Z)`}
          className={cx(iconButtonClassName, 'rounded-tl-[10px]')}
          aria-label={t('undo')}
          color="base-light"
        >
          <ToolbarUndoIcon />
        </IconButton>
        <IconButton
          disabled={!canRedo || !isEditable}
          onClick={() => {
            activeEditor.dispatchCommand(REDO_COMMAND, undefined);
          }}
          title={IS_APPLE ? `${t('redo')} (⌘Y)` : `${t('redo')} (Ctrl+Y)`}
          className={iconButtonClassName}
          aria-label={t('redo')}
          color="base-light"
        >
          <ToolbarRedoIcon />
        </IconButton>
        {activeEditor === editor && (
          <BlockFormatDropDown disabled={!isEditable} blockType={blockType} editor={editor} />
        )}
        <IconButton
          disabled={!isEditable}
          onClick={() => {
            activeEditor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');
          }}
          className={cx(iconButtonClassName, { 'bg-content-extra-lite': isBold })}
          color={isBold ? 'highlight' : 'base-light'}
          title={IS_APPLE ? `${t('bold')} (⌘B)` : `${t('bold')} (Ctrl+B)`}
          aria-label={`${t('boldDesc')}. ${t('shortcut')}: ${IS_APPLE ? '⌘B' : 'Ctrl+B'}`}
        >
          <ToolbarBoldIcon />
        </IconButton>
        <IconButton
          disabled={!isEditable}
          onClick={() => {
            activeEditor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline');
          }}
          className={cx(iconButtonClassName, { 'bg-content-extra-lite': isUnderline })}
          color={isUnderline ? 'highlight' : 'base-light'}
          title={IS_APPLE ? `${t('underline')} (⌘U)` : `${t('underline')} (Ctrl+U)`}
          aria-label={`${t('underlineDesc')}. ${t('shortcut')}: ${IS_APPLE ? '⌘U' : 'Ctrl+U'}`}
        >
          <ToolbarUnderlineIcon />
        </IconButton>
        <IconButton
          disabled={!isEditable}
          onClick={() => {
            activeEditor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');
          }}
          className={cx(iconButtonClassName, { 'bg-content-extra-lite': isItalic })}
          color={isItalic ? 'highlight' : 'base-light'}
          title={IS_APPLE ? `${t('italic')} (⌘I)` : `${t('italic')} (Ctrl+I)`}
          aria-label={`${t('italicDesc')}. ${t('shortcut')}: ${IS_APPLE ? '⌘I' : 'Ctrl+I'}`}
        >
          <ToolbarItalicIcon />
        </IconButton>
        <IconButton
          disabled={!isEditable}
          onClick={formatBulletList}
          className={iconButtonClassName}
          color="base-light"
          title={t('bullet')}
          aria-label={`${t('italicDesc')}. ${t('shortcut')}: ${IS_APPLE ? '⌘I' : 'Ctrl+I'}`}
        >
          <ToolbarBulletListIcon />
        </IconButton>
        <IconButton
          disabled={!isEditable}
          onClick={formatNumberedList}
          className={iconButtonClassName}
          color="base-light"
          title={t('number')}
          aria-label={`${t('italicDesc')}. ${t('shortcut')}: ${IS_APPLE ? '⌘I' : 'Ctrl+I'}`}
        >
          <ToolbarNumberListIcon />
        </IconButton>
        <IconButton
          disabled={!isEditable}
          onClick={() => addLink(false)}
          className={cx(iconButtonClassName, { 'bg-content-extra-lite': isLink })}
          color={isLink ? 'highlight' : 'base-light'}
          aria-label={t('insertLink')}
          title={t('insertLink')}
        >
          <ToolbarLinkIcon />
        </IconButton>
        {enableSuperlink && (
          <IconButton
            disabled={!isEditable}
            onClick={() => addLink(true)}
            className={cx(iconButtonClassName, { 'bg-secondary-lighter': isSuperLink })}
            color={isSuperLink ? 'highlight' : 'base-light'}
            aria-label={t('insertSuperLink')}
            title={t('insertSuperLink')}
          >
            <ToolbarSuperLinkIcon />
          </IconButton>
        )}
        <IconButton
          disabled={!isEditable}
          onClick={() => setAddTabelModalOpen(!addTabelModalOpen)}
          className={iconButtonClassName}
          color="base-light"
          aria-label={t('insertTable')}
          title={t('insertTable')}
        >
          <TableCellsIcon className="text-base-00 h-5 w-5" />
        </IconButton>
      </div>

      <LinkEditor
        isOpen={urlEditorOpen}
        {...urlEditorLinkAttr}
        onClose={handleEditorClose}
        onSubmit={handleEditorClose}
        onRemoveLink={handleRemoveLink}
      />

      <InsertTableModal
        isOpen={addTabelModalOpen}
        activeEditor={activeEditor}
        onClose={() => setAddTabelModalOpen(false)}
      />

      <NodeEventPlugin eventListener={handleLinkClick} eventType="click" nodeType={LinkNode} />
      <NodeEventPlugin eventListener={handleLinkClick} eventType="click" nodeType={SuperLinkNode} />
    </>
  );
}

import request from 'supertest';

import { apiURL, colors } from '../config';
import { Method } from '../enums/httpEnums';

export class BaseHelper {
  protected static async makeRequest<T>(
    method: Method,
    endpoint: string,
    requestObject?: Record<string, any>,
    bearerToken?: string
  ): Promise<T> {
    let req = request(apiURL)[method](endpoint);

    if (bearerToken) {
      req = req.set('Authorization', `Bearer ${bearerToken}`);
    }
    if (requestObject) {
      req = req.send(requestObject);
    }

    const response = await req;

    if (response.status >= 400 && response.status !== 404) {
      throw new Error(`Request failed with status code: ${response.status} and message: ${response.body.message}`);
    } else {
      console.log(
        colors.yellow,
        `${method} Request ${endpoint} ${response.status === 404 ? 'not found' : 'success'}\n
        with status code: ${response.status} and message: ${response.body.message}` + colors.reset
      );
    }

    return response as T;
  }
}

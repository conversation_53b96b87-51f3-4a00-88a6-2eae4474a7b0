{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@/iprox-open.interface": ["libs/iprox-open-api/generated/iprox-open.interface.ts"], "@iprox-open/shared": ["libs/shared/src/index.ts"], "@iprox-open/shared/server": ["libs/shared/src/server.ts"], "@iprox/react-ui": ["libs/react-ui/src/index.ts"], "@iprox/react-ui-i18n": ["libs/react-ui-i18n/src/index.ts"], "@iprox/react-ui-i18n/*": ["libs/react-ui-i18n/src/i18n/*"], "@iprox/react-ui-i18n/server": ["libs/react-ui-i18n/src/server.ts"], "@iprox/react-ui/server": ["libs/react-ui/src/server.ts"], "@iprox/shared-context": ["libs/shared-context/src/index.tsx"], "applications/workspace-plugin": ["tools/workspace-plugin/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}
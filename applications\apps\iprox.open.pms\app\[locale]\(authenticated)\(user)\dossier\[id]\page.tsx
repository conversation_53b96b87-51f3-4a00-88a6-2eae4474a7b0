import { Spinner } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';
import { Suspense } from 'react';

import { DossierLoader } from './dossier-loader';

export const dynamic = 'force-dynamic';

export default async function Page({ params }: { params: { id: string } }) {
  return (
    <Suspense fallback={<DossierLoading />}>
      <DossierLoader id={params.id} />
    </Suspense>
  );
}

const DossierLoading = () => {
  const t = useTranslations('dossier');

  return (
    <div className="grid h-full grid-cols-3 gap-8">
      <div className="col-span-2 grid place-items-center">
        <Spinner message={t('loading')} size="xl" />
      </div>
    </div>
  );
};

import { ReactNode, createContext, useContext, useReducer } from 'react';

import { StateAction } from '../../../../models/context.model';
import { ConfirmDialog } from '../confirm-dialog';
import { ConfirmDialogActionTypes, closeConfirmDialog, toggleConfirmDialog } from './confirm-dialog.actions';

interface ConfirmDialogProviderProps {
  children: ReactNode;
}

export interface ConfirmDialogState {
  isOpen?: boolean;
  message: string;
  onConfirm: () => void;
  onCancel?: () => void;
}

type ConfirmDialogActions =
  | StateAction<ConfirmDialogActionTypes, ConfirmDialogState>
  | StateAction<ConfirmDialogActionTypes, ConfirmDialogState, unknown>;

const ConfirmDialogContext = createContext<
  | {
      state: ConfirmDialogState;
      showDialog: (config: ConfirmDialogState) => void;
      closeDialog: () => void;
    }
  | undefined
>(undefined);

/**
 * Reducer for ConfirmDialog Context
 * @param state ConfirmDialogState
 * @param action ConfirmDialogActions
 * @returns ConfirmDialogState
 */
function ConfirmDialogReducer(state: ConfirmDialogState, action: ConfirmDialogActions): ConfirmDialogState {
  if (action.reducer) {
    if ('payload' in action) {
      return action.reducer(state, action.payload);
    }

    return action.reducer(state);
  }

  return state;
}

/**
 * ConfirmDialog Context provider
 * @param param ConfirmDialogProviderProps
 * @returns JSX.Element
 */
function ConfirmDialogProvider({ children }: ConfirmDialogProviderProps) {
  const [state, dispatch] = useReducer(ConfirmDialogReducer, {} as ConfirmDialogState);

  const showDialog = (config: ConfirmDialogState) => {
    dispatch(toggleConfirmDialog(config));
  };

  const closeDialog = () => {
    dispatch(closeConfirmDialog());
  };

  return (
    <ConfirmDialogContext.Provider value={{ state, showDialog, closeDialog }}>
      {children}
      <ConfirmDialog />
    </ConfirmDialogContext.Provider>
  );
}

/**
 * Confirm dialog Context hook
 * @returns ConfirmDialogState
 */
const useConfirmDialog = () => {
  const context = useContext(ConfirmDialogContext);

  if (!context) {
    throw new Error('useConfirmDialog must be used within a ConfirmDialogProvider');
  }

  return context;
};

export { ConfirmDialogProvider, useConfirmDialog };

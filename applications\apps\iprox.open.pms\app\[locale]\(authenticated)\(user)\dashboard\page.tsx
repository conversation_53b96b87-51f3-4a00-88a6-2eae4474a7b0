import { authOptions } from '@/auth/auth';
import { IPageProps } from '@/models/page-props.model';
import { getDossierCategories } from '@/services/dossier-category-service.server';
import { getDossierMetrics } from '@/services/dossier-file-structure-service.server';
import { getServerSession } from 'next-auth';

import { Greeting } from '@/components/greeting';
import { Statistics } from '@/components/statistics';
import { StatisticsFilterWrapper } from '@/components/statistics-filter-wrapper';

async function getDossierMetricsData(searchParams: Record<string, string>) {
  const { publicationType, publishedFrom, publishedTill, publishedBy } = searchParams;
  const filter = {
    'dossier.categoryId:eq:': publicationType,
    'dossier.published.dateTime:ge:': publishedFrom,
    'dossier.published.dateTime:le:': publishedTill,
    'dossier.published.userId:eq:': publishedBy,
    'dossier.published:': 'isnotnull',
  };
  const filterString = Object.entries(filter)
    .reduce<string[]>((acc, [key, value]) => {
      if (value) {
        return [...acc, `${key}${value}`];
      }
      return acc;
    }, [])
    .join(',');
  const params = new URLSearchParams();
  params.append('filter', filterString);

  return await getDossierMetrics(params.toString());
}

export default async function Page({ searchParams }: IPageProps) {
  const dossierCategoriesData = await getDossierCategories();
  const dossierMetricsData = await getDossierMetricsData(searchParams as Record<string, string>);
  const session = await getServerSession(authOptions);

  return (
    <>
      <div className="mb-12">
        <Greeting name={session?.user.name || ''} />
      </div>
      <div>
        <StatisticsFilterWrapper dossierCategories={dossierCategoriesData?.dossierCategories ?? []}>
          <Statistics dossierMetrics={dossierMetricsData.dossierMetrics} />
        </StatisticsFilterWrapper>
      </div>
    </>
  );
}

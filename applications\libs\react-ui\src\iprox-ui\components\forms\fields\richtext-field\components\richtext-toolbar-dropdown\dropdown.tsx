import cx from 'classnames';
import { ReactNode, useEffect, useRef, useState } from 'react';

import IconChevronDown from '../icons/chevron-down';
import { DropDownItems } from './dropdown-items';

export default function DropDown({
  disabled = false,
  buttonLabel,
  button<PERSON>riaLabel,
  children,
  className,
  stopCloseOnClickSelf,
}: {
  disabled?: boolean;
  buttonAriaLabel?: string;
  buttonLabel?: string;
  children: ReactNode;
  className?: string;
  stopCloseOnClickSelf?: boolean;
}): JSX.Element {
  const dropDownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [showDropDown, setShowDropDown] = useState(false);

  const handleClose = () => {
    setShowDropDown(false);
    if (buttonRef && buttonRef.current) {
      buttonRef.current.focus();
    }
  };

  useEffect(() => {
    const button = buttonRef.current;

    if (button !== null && showDropDown) {
      const handle = (event: MouseEvent) => {
        const target = event.target;
        if (stopCloseOnClickSelf) {
          if (dropDownRef.current && dropDownRef.current.contains(target as Node)) return;
        }
        if (!button.contains(target as Node)) {
          setShowDropDown(false);
        }
      };
      document.addEventListener('click', handle);

      return () => {
        document.removeEventListener('click', handle);
      };
    }
  }, [dropDownRef, buttonRef, showDropDown, stopCloseOnClickSelf]);

  return (
    <div className="relative">
      <button
        disabled={disabled}
        aria-label={buttonAriaLabel || buttonLabel}
        className={cx(
          'text-content-lite hover:bg-base-35 flex flex-row items-center gap-2 rounded-md px-3 py-1 text-sm ',
          className
        )}
        onClick={() => setShowDropDown(!showDropDown)}
        ref={buttonRef}
        type="button"
      >
        {buttonLabel && (
          <span className="text-base-00 flex-nowrap overflow-hidden overflow-ellipsis whitespace-nowrap">
            {buttonLabel}
          </span>
        )}

        <span className="text-base-00 mt-1">
          <IconChevronDown />
        </span>
      </button>

      {showDropDown && (
        <DropDownItems dropDownRef={dropDownRef} onClose={handleClose}>
          {children}
        </DropDownItems>
      )}
    </div>
  );
}

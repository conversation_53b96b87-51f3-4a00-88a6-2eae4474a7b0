'use client';

import { usePathname } from 'next/navigation';
import { useCallback, useMemo } from 'react';

import { MenuItem } from '../models/application-shell.models';
import { NavigationLink, NavigationLinkProps } from './navigation-link';

interface NavigationMenuProps {
  topItems: MenuItem[];
  bottomItems?: MenuItem[];
}

const isActive = (currentPathname: string, activeRoute: string, mode: 'exact' | 'starts-with' = 'starts-with') =>
  mode === 'exact' ? activeRoute === currentPathname : currentPathname.startsWith(activeRoute);

export function NavigationMenu(props: NavigationMenuProps) {
  const pathname = usePathname();

  const mapNavLinks = useCallback(
    (menuItem: MenuItem[], pathName: string): NavigationLinkProps[] =>
      menuItem.map((nav) => ({
        name: nav.name,
        icon: nav.icon,
        route: nav.route,
        isActive: nav.activeRouteMatch
          ? isActive(pathName, nav.activeRouteMatch.match, nav.activeRouteMatch.mode)
          : false,
        ...(nav.subMenu ? { subMenu: mapNavLinks(nav.subMenu, pathName) } : {}),
      })),
    []
  );

  const topMenuItems = useMemo<NavigationLinkProps[]>(
    () => mapNavLinks(props.topItems, pathname),
    [mapNavLinks, props.topItems, pathname]
  );

  const bottomMenuItems = useMemo<NavigationLinkProps[]>(
    () => (props.bottomItems ? mapNavLinks(props.bottomItems, pathname) : []),
    [props.bottomItems, mapNavLinks, pathname]
  );

  return (
    <nav className="flex flex-1 flex-col">
      <ul className="ipx-menu flex flex-1 flex-col gap-y-7">
        <li>
          <ul className="-mx-2 space-y-1">
            {topMenuItems.map((item) => (
              <NavigationLink
                key={item.name}
                name={item.name}
                icon={item.icon}
                route={item.route}
                isActive={item.isActive}
                subMenu={item?.subMenu}
              />
            ))}
          </ul>
        </li>
      </ul>
      {bottomMenuItems && (
        <ul className="ipx-menu mt-auto">
          {bottomMenuItems.map((item) => (
            <NavigationLink
              key={item.name}
              name={item.name}
              icon={item.icon}
              route={item.route}
              isActive={item.isActive}
            />
          ))}
        </ul>
      )}
    </nav>
  );
}

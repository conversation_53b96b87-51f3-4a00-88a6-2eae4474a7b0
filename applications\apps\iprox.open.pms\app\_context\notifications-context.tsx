'use client';

import { filesize } from 'filesize';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import React, { createContext, useCallback, useMemo } from 'react';

import { UploadState, useUploadState } from './dossier-upload-context';

type NotificationsContextType = {
  notifications: Notification[];
  removeNotification: (id: string) => void;
  removeAllNotifications: () => void;
  handleAction: (id: string) => void;
};

const NotificationsContext = createContext<NotificationsContextType>({
  notifications: [],
  removeNotification: () => null,
  removeAllNotifications: () => null,
  handleAction: () => null,
});

interface NotificationsProviderProps {
  children: React.ReactNode;
}

interface UploadData {
  progress: number;
  uploaded: string;
  total: string;
}

export interface Notification {
  id: string;
  title: string;
  description: string;
  status: 'queued' | 'uploading' | 'uploadComplete';
  data?: UploadData;
  error?: string;
}

export const NotificationsProvider = ({ children }: NotificationsProviderProps) => {
  const router = useRouter();
  const t = useTranslations('notifications');
  const { uploads, clearUpload } = useUploadState();

  const removeNotification = (id: string) => {
    clearUpload(id);
  };

  const removeAllNotifications = () => {
    uploads.forEach((upload) => {
      clearUpload(upload.key);
    });
  };

  const handleAction = (id: string) => {
    const uploadState = uploads.find((upload) => upload.key === id);

    if (uploadState?.value) {
      router.push(`/dossier/${uploadState.value.dossierId}`);
    }
  };

  const getNotificationDescription = useCallback(
    (status: 'queued' | 'uploading' | 'uploadComplete', fileName: string, error?: string): string => {
      if (error) {
        return t('fileUploadError', {
          fileName,
          error,
        });
      }

      if (status === 'queued') {
        return t('queuedFile', { fileName });
      }

      if (status === 'uploading') {
        return t('uploadingFile', { fileName });
      }

      if (status === 'uploadComplete') {
        return t('fileUploaded', { fileName });
      }

      return '';
    },
    [t]
  );

  const getNotificationData = useCallback((uploadData: UploadState): UploadData | undefined => {
    if (uploadData.status === 'uploading') {
      return {
        progress: uploadData.progress,
        uploaded: filesize(uploadData.loaded ?? 0, { base: 2, standard: 'jedec' }).toString(),
        total: filesize(uploadData.fileSize ?? 0, { base: 2, standard: 'jedec' }).toString(),
      };
    }
  }, []);

  const formattedNotifications: Notification[] = useMemo(() => {
    const notifications: Notification[] = uploads.map((upload) => {
      const uploadId = upload.key;
      const uploadData = upload.value as UploadState;

      return {
        id: uploadId,
        title: uploadData.error ? t('error') : t(uploadData.status),
        description: getNotificationDescription(uploadData.status, uploadData.fileName, uploadData.error),
        status: uploadData.status,
        error: uploadData.error,
        data: getNotificationData(uploadData),
      };
    });

    return notifications;
  }, [getNotificationData, getNotificationDescription, t, uploads]);

  return (
    <NotificationsContext.Provider
      value={{
        notifications: [...formattedNotifications],
        removeNotification,
        removeAllNotifications,
        handleAction,
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
};

export function useNotificationManager(): {
  notifications: Notification[];
  removeNotification: (id: string) => void;
  removeAllNotifications: () => void;
  handleAction: (id: string) => void;
} {
  const context = React.useContext(NotificationsContext);

  if (context === undefined) {
    throw new Error('useNotificationManager must be used within a NotificationsProvider');
  }

  return {
    notifications: context.notifications,
    removeNotification: context.removeNotification,
    removeAllNotifications: context.removeAllNotifications,
    handleAction: context.handleAction,
  };
}

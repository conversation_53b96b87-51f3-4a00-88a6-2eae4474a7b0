import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Button } from './button';

const meta: Meta<typeof Button> = {
  title: 'iprox-ui/components/button',
  component: Button,
  argTypes: {
    onClick: {
      action: 'onClick',
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {
  name: 'primary',
  args: {
    children: 'Login',
    type: 'button',
    variant: 'primary',
  },
};

export const PrimaryDisabled: Story = {
  name: 'primary disabled',
  args: {
    children: 'Login',
    type: 'button',
    variant: 'primary',
    disabled: true,
  },
};

export const PrimaryWithIcon: Story = {
  name: 'primary with icon',
  args: {
    children: 'Login',
    type: 'button',
    variant: 'primary',
    icon: 'PlusSmallIcon',
  },
};

export const Secondary: Story = {
  name: 'secondary',
  args: {
    children: 'Login',
    type: 'button',
    variant: 'secondary',
  },
};

export const SecondaryDisabled: Story = {
  name: 'secondary disabled',
  args: {
    children: 'Login',
    type: 'button',
    variant: 'secondary',
    disabled: true,
  },
};

export const SecondaryWithIcon: Story = {
  name: 'secondary with icon',
  args: {
    children: 'Login',
    type: 'button',
    variant: 'secondary',
    icon: 'PlusSmallIcon',
  },
};

export const Tertiary: Story = {
  name: 'tertiary',
  args: {
    children: 'Login',
    type: 'button',
    variant: 'tertiary',
  },
};

export const TertiaryDisabled: Story = {
  name: 'tertiary disabled',
  args: {
    children: 'Login',
    type: 'button',
    variant: 'tertiary',
    disabled: true,
  },
};

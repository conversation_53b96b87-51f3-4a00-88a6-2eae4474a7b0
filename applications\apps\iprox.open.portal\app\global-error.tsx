'use client';

import { ChevronDownIcon, ChevronUpIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { Button, Text } from '@iprox/react-ui';
import { useEffect, useState } from 'react';

export default function GlobalError({ error }: { error: Error & { digest?: string }; reset: () => void }) {
  const [openDetails, setOpenDetails] = useState(false);

  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <html lang="nl">
      <body>
        <div className="flex h-screen w-screen flex-col justify-center p-32">
          <div className="flex w-full flex-col items-center justify-center">
            <Text className="font-text text-error mb-2 block w-full break-words text-center text-lg">
              Er is iets mis gegaan, gebruikt de knop om het nogmaals te proberen.
            </Text>
            <Button
              type="button"
              onClick={() => (window ? window.location.reload() : null)}
              variant="secondary"
              icon="ArrowPathIcon"
              disabled={false}
            >
              {'Probeer het nog eens'}
            </Button>
          </div>
          <div className="relative w-full">
            <button
              type="button"
              className="text-error m-0 flex items-center p-0 ring-0"
              onClick={() => setOpenDetails(!openDetails)}
            >
              <ExclamationCircleIcon className="mr-1 h-5 w-5" />
              Klik hier voor meer technische details
              {openDetails ? <ChevronUpIcon className="mt-1 h-4 w-4" /> : <ChevronDownIcon className="mt-1 h-4 w-4" />}
            </button>
            {openDetails && (
              <div className="bg-base-25 top-100 absolute left-0 w-full rounded-lg p-4">
                <Text className="font-text text-error w-full break-words text-sm">{error.message}</Text>
              </div>
            )}
          </div>
        </div>
      </body>
    </html>
  );
}

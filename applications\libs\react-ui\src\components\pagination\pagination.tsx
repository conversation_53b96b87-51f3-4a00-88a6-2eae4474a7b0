import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { ParsedUrlQueryInput, parse } from 'querystring';

import { PaginationButton } from './parts/pagination-button';

export interface PaginationProps {
  count: number;
  start?: number;
  /** Maximum page links to show. Rounds down to nearest uneven number, defaults to `5` */
  maxVisiblePages?: number;
  totalCount: number;
  startingIndex?: number;
}

const safeProps = (props: PaginationProps) => ({
  count: props.count,
  start: props.start ?? 0,
  maxVisiblePages: props.maxVisiblePages ?? 5,
  totalCount: props.totalCount,
  startingIndex: props.startingIndex ?? 0,
});

export function Pagination(props: PaginationProps) {
  const { count, start, maxVisiblePages, totalCount, startingIndex } = safeProps(props);

  const t = useTranslations('components');

  const parsedUrlQueryInput = parse((useSearchParams() ?? new URLSearchParams()).toString());
  const pathname = usePathname();

  if (!count) {
    return null;
  }

  const currentPage = Math.floor(start / count) + 1;
  const maxUnevenVisiblePages = Math.max(1, maxVisiblePages % 2 === 0 ? maxVisiblePages - 1 : maxVisiblePages);
  const totalPages = count > 0 ? Math.ceil(totalCount / count) : 0;

  const getPageParams = (type: 'prev' | 'next' | 'page', pageIndex = 0): ParsedUrlQueryInput => {
    const pageParams = { ...parsedUrlQueryInput };

    pageParams['count'] = count.toString();

    if (type === 'prev') {
      pageParams['start'] = Math.max(0, start - count).toString();
    } else if (type === 'next') {
      pageParams['start'] = Math.min(start + count, totalCount).toString();
    } else {
      pageParams['start'] = (startingIndex + (pageIndex - 1) * count).toString();
    }

    return pageParams;
  };

  const pageButtons = () => {
    const maxVisibleButtons = Math.min(totalPages, maxUnevenVisiblePages);
    const pageOffset = Math.floor(maxVisibleButtons / 2);

    let start = Math.max(1, currentPage - pageOffset);
    let end = Math.min(start + maxVisibleButtons - 1, totalPages);

    if (end - start < maxVisibleButtons - 1) {
      start = Math.max(1, end - maxVisibleButtons + 1);
    }

    if (start > 1) {
      start++;
    }

    if (end < totalPages) {
      end--;
    }

    const buttons: {
      page: string;
      isCurrent: boolean;
      query?: ParsedUrlQueryInput;
      type: 'button' | 'ellipsis';
    }[] = [];

    buttons.push({
      page: '1',
      isCurrent: 1 === currentPage,
      query: getPageParams('page', 1),
      type: 'button',
    });

    if (start > 1) {
      buttons.push({
        page: '...',
        isCurrent: false,
        type: 'ellipsis',
      });
    }

    for (let index = 1; index < end - start; index++) {
      const page = index + start;
      const isCurrent = page === currentPage;

      buttons.push({
        page: page.toString(),
        isCurrent,
        query: getPageParams('page', page),
        type: 'button',
      });
    }

    if (end < totalPages) {
      buttons.push({
        page: '...',
        isCurrent: false,
        type: 'ellipsis',
      });
    }

    if (totalPages > 1) {
      buttons.push({
        page: totalPages.toString(),
        isCurrent: totalPages === currentPage,
        query: getPageParams('page', totalPages),
        type: 'button',
      });
    }

    return buttons;
  };

  const getIconColor = (type: 'prev' | 'next') => {
    if (type === 'prev') {
      return currentPage === 1 ? 'text-base-25' : 'text-base-0';
    }
    return currentPage === totalPages ? 'text-base-25' : 'text-base-0';
  };

  if (totalCount === 0) {
    return null;
  }

  return (
    <div className="flex w-fit flex-row items-center gap-x-4">
      <Link
        prefetch={false}
        href={{
          pathname,
          query: getPageParams('prev'),
        }}
        aria-label={t('pagination.prev')}
        aria-disabled={currentPage === 1 ? 'true' : 'false'}
        className={`flex h-12 w-12 items-center justify-center rounded-[1px] border ${
          currentPage === 1 ? 'border-base-25 cursor-default' : 'border-base-0'
        }`}
        onClick={(e) => {
          if (currentPage === 1) {
            e.preventDefault();
          }
        }}
      >
        <ChevronLeftIcon className={`${getIconColor('prev')} h-3 w-3`} />
      </Link>
      {pageButtons().map(({ page, query, isCurrent, type }, index) => (
        <PaginationButton key={index} page={page} query={query} isCurrent={isCurrent} type={type} />
      ))}
      <Link
        prefetch={false}
        href={{
          pathname,
          query: getPageParams('next'),
        }}
        aria-label={t('pagination.next')}
        aria-disabled={currentPage === totalPages ? 'true' : 'false'}
        className={`flex h-12 w-12 items-center justify-center rounded-[1px] border ${
          currentPage === totalPages ? 'border-base-25 cursor-default' : 'border-base-0'
        }`}
        onClick={(e) => {
          if (currentPage === totalPages) {
            e.preventDefault();
          }
        }}
      >
        <ChevronRightIcon className={`${getIconColor('next')} h-3 w-3`} />
      </Link>
    </div>
  );
}

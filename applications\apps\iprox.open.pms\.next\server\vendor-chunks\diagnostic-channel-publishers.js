"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/diagnostic-channel-publishers";
exports.ids = ["vendor-chunks/diagnostic-channel-publishers"];
exports.modules = {

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/azure-coretracing.pub.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/azure-coretracing.pub.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.azureCoreTracing = exports.AzureMonitorSymbol = void 0;\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nexports.AzureMonitorSymbol = \"Azure_Monitor_Tracer\";\r\nvar publisherName = \"azure-coretracing\";\r\nvar isPatched = false;\r\n/**\r\n * By default, @azure/core-tracing default tracer is a NoopTracer.\r\n * This patching changes the default tracer to a patched BasicTracer\r\n * which emits ended spans as diag-channel events.\r\n *\r\n * The @opentelemetry/tracing package must be installed to use these patches\r\n * https://www.npmjs.com/package/@opentelemetry/tracing\r\n * @param coreTracing\r\n */\r\nvar azureCoreTracingPatchFunction = function (coreTracing) {\r\n    if (isPatched) {\r\n        // tracer is already cached -- noop\r\n        return coreTracing;\r\n    }\r\n    try {\r\n        var tracing = __webpack_require__(/*! @opentelemetry/sdk-trace-base */ \"(instrument)/../../node_modules/@opentelemetry/sdk-trace-base/build/esm/index.js\");\r\n        var api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/@opentelemetry/api/build/esm/index.js\");\r\n        var defaultProvider = new tracing.BasicTracerProvider();\r\n        var defaultTracer = defaultProvider.getTracer(\"applicationinsights tracer\");\r\n        // Patch Azure SDK setTracer, @azure/core-tracing <= 1.0.0-preview.12\r\n        if (coreTracing.setTracer) {\r\n            var setTracerOriginal_1 = coreTracing.setTracer;\r\n            coreTracing.setTracer = function (tracer) {\r\n                // Patch startSpan instead of using spanProcessor.onStart because parentSpan must be\r\n                // set while the span is constructed\r\n                var startSpanOriginal = tracer.startSpan;\r\n                tracer.startSpan = function (name, options, context) {\r\n                    var span = startSpanOriginal.call(this, name, options, context);\r\n                    var originalEnd = span.end;\r\n                    span.end = function () {\r\n                        var result = originalEnd.apply(this, arguments);\r\n                        diagnostic_channel_1.channel.publish(publisherName, span);\r\n                        return result;\r\n                    };\r\n                    return span;\r\n                };\r\n                tracer[exports.AzureMonitorSymbol] = true;\r\n                setTracerOriginal_1.call(this, tracer);\r\n            };\r\n            api.trace.getSpan(api.context.active()); // seed OpenTelemetryScopeManagerWrapper with \"active\" symbol\r\n            coreTracing.setTracer(defaultTracer);\r\n        }\r\n        else { // Patch OpenTelemetry setGlobalTracerProvider  @azure/core-tracing > 1.0.0-preview.13\r\n            var setGlobalTracerProviderOriginal_1 = api.trace.setGlobalTracerProvider;\r\n            api.trace.setGlobalTracerProvider = function (tracerProvider) {\r\n                var getTracerOriginal = tracerProvider.getTracer;\r\n                tracerProvider.getTracer = function (tracerName, version) {\r\n                    var tracer = getTracerOriginal.call(this, tracerName, version);\r\n                    if (!tracer[exports.AzureMonitorSymbol]) { // Avoid patching multiple times\r\n                        var startSpanOriginal_1 = tracer.startSpan;\r\n                        tracer.startSpan = function (spanName, options, context) {\r\n                            var span = startSpanOriginal_1.call(this, spanName, options, context);\r\n                            var originalEnd = span.end;\r\n                            span.end = function () {\r\n                                var result = originalEnd.apply(this, arguments);\r\n                                diagnostic_channel_1.channel.publish(publisherName, span);\r\n                                return result;\r\n                            };\r\n                            return span;\r\n                        };\r\n                        tracer[exports.AzureMonitorSymbol] = true;\r\n                    }\r\n                    return tracer;\r\n                };\r\n                return setGlobalTracerProviderOriginal_1.call(this, tracerProvider);\r\n            };\r\n            defaultProvider.register();\r\n            api.trace.getSpan(api.context.active()); // seed OpenTelemetryScopeManagerWrapper with \"active\" symbol\r\n            // Register Azure SDK instrumentation\r\n            var openTelemetryInstr = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\r\n            var azureSdkInstr = __webpack_require__(/*! @azure/opentelemetry-instrumentation-azure-sdk */ \"(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/index.js\");\r\n            openTelemetryInstr.registerInstrumentations({\r\n                instrumentations: [\r\n                    azureSdkInstr.createAzureSdkInstrumentation()\r\n                ]\r\n            });\r\n        }\r\n        isPatched = true;\r\n    }\r\n    catch (e) { /* squash errors */ }\r\n    return coreTracing;\r\n};\r\nexports.azureCoreTracing = {\r\n    versionSpecifier: \">= 1.0.0 < 2.0.0\",\r\n    patch: azureCoreTracingPatchFunction,\r\n    publisherName: publisherName\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"@azure/core-tracing\", exports.azureCoreTracing);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=azure-coretracing.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/azure-coretracing.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/bunyan.pub.js":
/*!*******************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/bunyan.pub.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.bunyan = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nvar bunyanPatchFunction = function (originalBunyan) {\r\n    var originalEmit = originalBunyan.prototype._emit;\r\n    originalBunyan.prototype._emit = function (rec, noemit) {\r\n        var ret = originalEmit.apply(this, arguments);\r\n        if (!noemit) {\r\n            var str = ret;\r\n            if (!str) {\r\n                str = originalEmit.call(this, rec, true);\r\n            }\r\n            diagnostic_channel_1.channel.publish(\"bunyan\", { level: rec.level, result: str });\r\n        }\r\n        return ret;\r\n    };\r\n    return originalBunyan;\r\n};\r\nexports.bunyan = {\r\n    versionSpecifier: \">= 1.0.0 < 2.0.0\",\r\n    patch: bunyanPatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"bunyan\", exports.bunyan);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=bunyan.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/bunyan.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/console.pub.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/console.pub.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.console = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nvar stream_1 = __webpack_require__(/*! stream */ \"stream\");\r\nvar consolePatchFunction = function (originalConsole) {\r\n    var aiLoggingOutStream = new stream_1.Writable();\r\n    var aiLoggingErrStream = new stream_1.Writable();\r\n    // Default console is roughly equivalent to `new Console(process.stdout, process.stderr)`\r\n    // We create a version which publishes to the channel and also to stdout/stderr\r\n    aiLoggingOutStream.write = function (chunk) {\r\n        if (!chunk) {\r\n            return true;\r\n        }\r\n        var message = chunk.toString();\r\n        diagnostic_channel_1.channel.publish(\"console\", { message: message });\r\n        return true;\r\n    };\r\n    aiLoggingErrStream.write = function (chunk) {\r\n        if (!chunk) {\r\n            return true;\r\n        }\r\n        var message = chunk.toString();\r\n        diagnostic_channel_1.channel.publish(\"console\", { message: message, stderr: true });\r\n        return true;\r\n    };\r\n    var aiLoggingConsole = new originalConsole.Console(aiLoggingOutStream, aiLoggingErrStream);\r\n    var consoleMethods = [\"log\", \"info\", \"warn\", \"error\", \"dir\", \"time\", \"timeEnd\", \"trace\", \"assert\"];\r\n    var _loop_1 = function (method) {\r\n        var originalMethod = originalConsole[method];\r\n        if (originalMethod) {\r\n            originalConsole[method] = function () {\r\n                if (aiLoggingConsole[method]) {\r\n                    try {\r\n                        aiLoggingConsole[method].apply(aiLoggingConsole, arguments);\r\n                    }\r\n                    catch (e) {\r\n                        // Ignore errors; allow the original method to throw if necessary\r\n                    }\r\n                }\r\n                return originalMethod.apply(originalConsole, arguments);\r\n            };\r\n        }\r\n    };\r\n    for (var _i = 0, consoleMethods_1 = consoleMethods; _i < consoleMethods_1.length; _i++) {\r\n        var method = consoleMethods_1[_i];\r\n        _loop_1(method);\r\n    }\r\n    return originalConsole;\r\n};\r\nexports.console = {\r\n    versionSpecifier: \">= 4.0.0\",\r\n    patch: consolePatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"console\", exports.console);\r\n    // Force patching of console\r\n    /* tslint:disable-next-line:no-var-requires */\r\n    __webpack_require__(/*! console */ \"console\");\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=console.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/console.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/index.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.tedious = exports.pgPool = exports.pg = exports.winston = exports.redis = exports.mysql = exports.mongodb = exports.mongodbCore = exports.console = exports.bunyan = exports.azuresdk = void 0;\r\nvar azuresdk = __webpack_require__(/*! ./azure-coretracing.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/azure-coretracing.pub.js\");\r\nexports.azuresdk = azuresdk;\r\nvar bunyan = __webpack_require__(/*! ./bunyan.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/bunyan.pub.js\");\r\nexports.bunyan = bunyan;\r\nvar consolePub = __webpack_require__(/*! ./console.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/console.pub.js\");\r\nexports.console = consolePub;\r\nvar mongodbCore = __webpack_require__(/*! ./mongodb-core.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mongodb-core.pub.js\");\r\nexports.mongodbCore = mongodbCore;\r\nvar mongodb = __webpack_require__(/*! ./mongodb.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mongodb.pub.js\");\r\nexports.mongodb = mongodb;\r\nvar mysql = __webpack_require__(/*! ./mysql.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mysql.pub.js\");\r\nexports.mysql = mysql;\r\nvar pgPool = __webpack_require__(/*! ./pg-pool.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/pg-pool.pub.js\");\r\nexports.pgPool = pgPool;\r\nvar pg = __webpack_require__(/*! ./pg.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/pg.pub.js\");\r\nexports.pg = pg;\r\nvar redis = __webpack_require__(/*! ./redis.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/redis.pub.js\");\r\nexports.redis = redis;\r\nvar tedious = __webpack_require__(/*! ./tedious.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/tedious.pub.js\");\r\nexports.tedious = tedious;\r\nvar winston = __webpack_require__(/*! ./winston.pub */ \"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/winston.pub.js\");\r\nexports.winston = winston;\r\nfunction enable() {\r\n    bunyan.enable();\r\n    consolePub.enable();\r\n    mongodbCore.enable();\r\n    mongodb.enable();\r\n    mysql.enable();\r\n    pg.enable();\r\n    pgPool.enable();\r\n    redis.enable();\r\n    winston.enable();\r\n    azuresdk.enable();\r\n    tedious.enable();\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mongodb-core.pub.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/mongodb-core.pub.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.mongoCore = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nvar mongodbcorePatchFunction = function (originalMongoCore) {\r\n    var originalConnect = originalMongoCore.Server.prototype.connect;\r\n    originalMongoCore.Server.prototype.connect = function contextPreservingConnect() {\r\n        var ret = originalConnect.apply(this, arguments);\r\n        // Messages sent to mongo progress through a pool\r\n        // This can result in context getting mixed between different responses\r\n        // so we wrap the callbacks to restore appropriate state\r\n        var originalWrite = this.s.pool.write;\r\n        this.s.pool.write = function contextPreservingWrite() {\r\n            var cbidx = typeof arguments[1] === \"function\" ? 1 : 2;\r\n            if (typeof arguments[cbidx] === \"function\") {\r\n                arguments[cbidx] = diagnostic_channel_1.channel.bindToContext(arguments[cbidx]);\r\n            }\r\n            return originalWrite.apply(this, arguments);\r\n        };\r\n        // Logout is a special case, it doesn't call the write function but instead\r\n        // directly calls into connection.write\r\n        var originalLogout = this.s.pool.logout;\r\n        this.s.pool.logout = function contextPreservingLogout() {\r\n            if (typeof arguments[1] === \"function\") {\r\n                arguments[1] = diagnostic_channel_1.channel.bindToContext(arguments[1]);\r\n            }\r\n            return originalLogout.apply(this, arguments);\r\n        };\r\n        return ret;\r\n    };\r\n    return originalMongoCore;\r\n};\r\nexports.mongoCore = {\r\n    versionSpecifier: \">= 2.0.0 < 4.0.0\",\r\n    patch: mongodbcorePatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"mongodb-core\", exports.mongoCore);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=mongodb-core.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mongodb-core.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mongodb.pub.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/mongodb.pub.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.mongo330 = exports.mongo3 = exports.mongo2 = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nvar mongodbPatchFunction = function (originalMongo) {\r\n    var listener = originalMongo.instrument({\r\n        operationIdGenerator: {\r\n            next: function () {\r\n                return diagnostic_channel_1.channel.bindToContext(function (cb) { return cb(); });\r\n            }\r\n        }\r\n    });\r\n    var eventMap = {};\r\n    listener.on(\"started\", function (event) {\r\n        if (eventMap[event.requestId]) {\r\n            // Note: Mongo can generate 2 completely separate requests\r\n            // which share the same requestId, if a certain race condition is triggered.\r\n            // For now, we accept that this can happen and potentially miss or mislabel some events.\r\n            return;\r\n        }\r\n        eventMap[event.requestId] = __assign(__assign({}, event), { time: new Date() });\r\n    });\r\n    listener.on(\"succeeded\", function (event) {\r\n        var startedData = eventMap[event.requestId];\r\n        if (startedData) {\r\n            delete eventMap[event.requestId];\r\n        }\r\n        if (typeof event.operationId === \"function\") {\r\n            event.operationId(function () { return diagnostic_channel_1.channel.publish(\"mongodb\", { startedData: startedData, event: event, succeeded: true }); });\r\n        }\r\n        else {\r\n            // fallback -- correlation will not work here\r\n            diagnostic_channel_1.channel.publish(\"mongodb\", { startedData: startedData, event: event, succeeded: true });\r\n        }\r\n    });\r\n    listener.on(\"failed\", function (event) {\r\n        var startedData = eventMap[event.requestId];\r\n        if (startedData) {\r\n            delete eventMap[event.requestId];\r\n        }\r\n        if (typeof event.operationId === \"function\") {\r\n            event.operationId(function () { return diagnostic_channel_1.channel.publish(\"mongodb\", { startedData: startedData, event: event, succeeded: false }); });\r\n        }\r\n        else {\r\n            // fallback -- correlation will not work here\r\n            diagnostic_channel_1.channel.publish(\"mongodb\", { startedData: startedData, event: event, succeeded: false });\r\n        }\r\n    });\r\n    return originalMongo;\r\n};\r\nvar mongodb3PatchFunction = function (originalMongo) {\r\n    var listener = originalMongo.instrument();\r\n    var eventMap = {};\r\n    var contextMap = {};\r\n    listener.on(\"started\", function (event) {\r\n        if (eventMap[event.requestId]) {\r\n            // Note: Mongo can generate 2 completely separate requests\r\n            // which share the same requestId, if a certain race condition is triggered.\r\n            // For now, we accept that this can happen and potentially miss or mislabel some events.\r\n            return;\r\n        }\r\n        contextMap[event.requestId] = diagnostic_channel_1.channel.bindToContext(function (cb) { return cb(); });\r\n        eventMap[event.requestId] = __assign(__assign({}, event), { time: new Date() });\r\n    });\r\n    listener.on(\"succeeded\", function (event) {\r\n        var startedData = eventMap[event.requestId];\r\n        if (startedData) {\r\n            delete eventMap[event.requestId];\r\n        }\r\n        if (typeof event === \"object\" && typeof contextMap[event.requestId] === \"function\") {\r\n            contextMap[event.requestId](function () { return diagnostic_channel_1.channel.publish(\"mongodb\", { startedData: startedData, event: event, succeeded: true }); });\r\n            delete contextMap[event.requestId];\r\n        }\r\n    });\r\n    listener.on(\"failed\", function (event) {\r\n        var startedData = eventMap[event.requestId];\r\n        if (startedData) {\r\n            delete eventMap[event.requestId];\r\n        }\r\n        if (typeof event === \"object\" && typeof contextMap[event.requestId] === \"function\") {\r\n            contextMap[event.requestId](function () { return diagnostic_channel_1.channel.publish(\"mongodb\", { startedData: startedData, event: event, succeeded: false }); });\r\n            delete contextMap[event.requestId];\r\n        }\r\n    });\r\n    return originalMongo;\r\n};\r\n// In mongodb 3.3.0, mongodb-core was merged into mongodb, so the same patching\r\n// can be used here. this.s.pool was changed to this.s.coreTopology.s.pool\r\nvar mongodbcorePatchFunction = function (originalMongo) {\r\n    var originalConnect = originalMongo.Server.prototype.connect;\r\n    originalMongo.Server.prototype.connect = function contextPreservingConnect() {\r\n        var ret = originalConnect.apply(this, arguments);\r\n        // Messages sent to mongo progress through a pool\r\n        // This can result in context getting mixed between different responses\r\n        // so we wrap the callbacks to restore appropriate state\r\n        var originalWrite = this.s.coreTopology.s.pool.write;\r\n        this.s.coreTopology.s.pool.write = function contextPreservingWrite() {\r\n            var cbidx = typeof arguments[1] === \"function\" ? 1 : 2;\r\n            if (typeof arguments[cbidx] === \"function\") {\r\n                arguments[cbidx] = diagnostic_channel_1.channel.bindToContext(arguments[cbidx]);\r\n            }\r\n            return originalWrite.apply(this, arguments);\r\n        };\r\n        // Logout is a special case, it doesn't call the write function but instead\r\n        // directly calls into connection.write\r\n        var originalLogout = this.s.coreTopology.s.pool.logout;\r\n        this.s.coreTopology.s.pool.logout = function contextPreservingLogout() {\r\n            if (typeof arguments[1] === \"function\") {\r\n                arguments[1] = diagnostic_channel_1.channel.bindToContext(arguments[1]);\r\n            }\r\n            return originalLogout.apply(this, arguments);\r\n        };\r\n        return ret;\r\n    };\r\n    return originalMongo;\r\n};\r\nvar mongodb330PatchFunction = function (originalMongo) {\r\n    mongodbcorePatchFunction(originalMongo); // apply mongodb-core patches\r\n    var listener = originalMongo.instrument();\r\n    var eventMap = {};\r\n    var contextMap = {};\r\n    listener.on(\"started\", function (event) {\r\n        if (eventMap[event.requestId]) {\r\n            // Note: Mongo can generate 2 completely separate requests\r\n            // which share the same requestId, if a certain race condition is triggered.\r\n            // For now, we accept that this can happen and potentially miss or mislabel some events.\r\n            return;\r\n        }\r\n        contextMap[event.requestId] = diagnostic_channel_1.channel.bindToContext(function (cb) { return cb(); });\r\n        eventMap[event.requestId] = event;\r\n    });\r\n    listener.on(\"succeeded\", function (event) {\r\n        var startedData = eventMap[event.requestId];\r\n        if (startedData) {\r\n            delete eventMap[event.requestId];\r\n        }\r\n        if (typeof event === \"object\" && typeof contextMap[event.requestId] === \"function\") {\r\n            contextMap[event.requestId](function () { return diagnostic_channel_1.channel.publish(\"mongodb\", { startedData: startedData, event: event, succeeded: true }); });\r\n            delete contextMap[event.requestId];\r\n        }\r\n    });\r\n    listener.on(\"failed\", function (event) {\r\n        var startedData = eventMap[event.requestId];\r\n        if (startedData) {\r\n            delete eventMap[event.requestId];\r\n        }\r\n        if (typeof event === \"object\" && typeof contextMap[event.requestId] === \"function\") {\r\n            contextMap[event.requestId](function () { return diagnostic_channel_1.channel.publish(\"mongodb\", { startedData: startedData, event: event, succeeded: false }); });\r\n            delete contextMap[event.requestId];\r\n        }\r\n    });\r\n    return originalMongo;\r\n};\r\nexports.mongo2 = {\r\n    versionSpecifier: \">= 2.0.0 <= 3.0.5\",\r\n    patch: mongodbPatchFunction\r\n};\r\nexports.mongo3 = {\r\n    versionSpecifier: \"> 3.0.5 < 3.3.0\",\r\n    patch: mongodb3PatchFunction\r\n};\r\nexports.mongo330 = {\r\n    versionSpecifier: \">= 3.3.0 < 4.0.0\",\r\n    patch: mongodb330PatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"mongodb\", exports.mongo2);\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"mongodb\", exports.mongo3);\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"mongodb\", exports.mongo330);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=mongodb.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mongodb.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mysql.pub.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/mysql.pub.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.mysql = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nvar path = __webpack_require__(/*! path */ \"path\");\r\nvar mysqlPatchFunction = function (originalMysql, originalMysqlPath) {\r\n    // The `name` passed in here is for debugging purposes,\r\n    // to help distinguish which object is being patched.\r\n    var patchObjectFunction = function (obj, name) {\r\n        return function (func, cbWrapper) {\r\n            var originalFunc = obj[func];\r\n            if (originalFunc) {\r\n                obj[func] = function mysqlContextPreserver() {\r\n                    // Find the callback, if there is one\r\n                    var cbidx = arguments.length - 1;\r\n                    for (var i = arguments.length - 1; i >= 0; --i) {\r\n                        if (typeof arguments[i] === \"function\") {\r\n                            cbidx = i;\r\n                            break;\r\n                        }\r\n                        else if (typeof arguments[i] !== \"undefined\") {\r\n                            break;\r\n                        }\r\n                    }\r\n                    var cb = arguments[cbidx];\r\n                    var resultContainer = { result: null, startTime: null, startDate: null };\r\n                    if (typeof cb === \"function\") {\r\n                        // Preserve context on the callback.\r\n                        // If this is one of the functions that we want to track,\r\n                        // then wrap the callback with the tracking wrapper\r\n                        if (cbWrapper) {\r\n                            resultContainer.startTime = process.hrtime();\r\n                            resultContainer.startDate = new Date();\r\n                            arguments[cbidx] = diagnostic_channel_1.channel.bindToContext(cbWrapper(resultContainer, cb));\r\n                        }\r\n                        else {\r\n                            arguments[cbidx] = diagnostic_channel_1.channel.bindToContext(cb);\r\n                        }\r\n                    }\r\n                    var result = originalFunc.apply(this, arguments);\r\n                    resultContainer.result = result;\r\n                    return result;\r\n                };\r\n            }\r\n        };\r\n    };\r\n    var patchClassMemberFunction = function (classObject, name) {\r\n        return patchObjectFunction(classObject.prototype, name + \".prototype\");\r\n    };\r\n    var connectionCallbackFunctions = [\r\n        \"connect\", \"changeUser\",\r\n        \"ping\", \"statistics\", \"end\"\r\n    ];\r\n    var connectionClass = __webpack_require__(\"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src sync recursive ^.*\\\\/lib\\\\/Connection$\")(path.dirname(originalMysqlPath) + \"/lib/Connection\");\r\n    connectionCallbackFunctions.forEach(function (value) { return patchClassMemberFunction(connectionClass, \"Connection\")(value); });\r\n    // Connection.createQuery is a static method\r\n    patchObjectFunction(connectionClass, \"Connection\")(\"createQuery\", function (resultContainer, cb) {\r\n        return function (err) {\r\n            var hrDuration = process.hrtime(resultContainer.startTime);\r\n            /* tslint:disable-next-line:no-bitwise */\r\n            var duration = (hrDuration[0] * 1e3 + hrDuration[1] / 1e6) | 0;\r\n            diagnostic_channel_1.channel.publish(\"mysql\", { query: resultContainer.result, callbackArgs: arguments, err: err, duration: duration, time: resultContainer.startDate });\r\n            cb.apply(this, arguments);\r\n        };\r\n    });\r\n    var poolCallbackFunctions = [\r\n        \"_enqueueCallback\"\r\n    ];\r\n    var poolClass = __webpack_require__(\"(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src sync recursive ^.*\\\\/lib\\\\/Pool$\")(path.dirname(originalMysqlPath) + \"/lib/Pool\");\r\n    poolCallbackFunctions.forEach(function (value) { return patchClassMemberFunction(poolClass, \"Pool\")(value); });\r\n    return originalMysql;\r\n};\r\nexports.mysql = {\r\n    versionSpecifier: \">= 2.0.0 < 3.0.0\",\r\n    patch: mysqlPatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"mysql\", exports.mysql);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=mysql.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/mysql.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/pg-pool.pub.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/pg-pool.pub.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.postgresPool1 = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nfunction postgresPool1PatchFunction(originalPgPool) {\r\n    var originalConnect = originalPgPool.prototype.connect;\r\n    originalPgPool.prototype.connect = function connect(callback) {\r\n        if (callback) {\r\n            arguments[0] = diagnostic_channel_1.channel.bindToContext(callback);\r\n        }\r\n        return originalConnect.apply(this, arguments);\r\n    };\r\n    return originalPgPool;\r\n}\r\nexports.postgresPool1 = {\r\n    versionSpecifier: \">= 1.0.0 < 3.0.0\",\r\n    patch: postgresPool1PatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"pg-pool\", exports.postgresPool1);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=pg-pool.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/pg-pool.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/pg.pub.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/pg.pub.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.postgres = exports.postgres6 = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nvar events_1 = __webpack_require__(/*! events */ \"events\");\r\nvar publisherName = \"postgres\";\r\nfunction postgres6PatchFunction(originalPg, originalPgPath) {\r\n    var originalClientQuery = originalPg.Client.prototype.query;\r\n    var diagnosticOriginalFunc = \"__diagnosticOriginalFunc\";\r\n    // wherever the callback is passed, find it, save it, and remove it from the call\r\n    // to the the original .query() function\r\n    originalPg.Client.prototype.query = function query(config, values, callback) {\r\n        var data = {\r\n            query: {},\r\n            database: {\r\n                host: this.connectionParameters.host,\r\n                port: this.connectionParameters.port\r\n            },\r\n            result: null,\r\n            error: null,\r\n            duration: 0,\r\n            time: new Date()\r\n        };\r\n        var start = process.hrtime();\r\n        var queryResult;\r\n        function patchCallback(cb) {\r\n            if (cb && cb[diagnosticOriginalFunc]) {\r\n                cb = cb[diagnosticOriginalFunc];\r\n            }\r\n            var trackingCallback = diagnostic_channel_1.channel.bindToContext(function (err, res) {\r\n                var end = process.hrtime(start);\r\n                data.result = res && { rowCount: res.rowCount, command: res.command };\r\n                data.error = err;\r\n                data.duration = Math.ceil((end[0] * 1e3) + (end[1] / 1e6));\r\n                diagnostic_channel_1.channel.publish(publisherName, data);\r\n                // emulate weird internal behavior in pg@6\r\n                // on success, the callback is called *before* query events are emitted\r\n                // on failure, the callback is called *instead of* the query emitting events\r\n                // with no events, that means no promises (since the promise is resolved/rejected in an event handler)\r\n                // since we are always inserting ourselves as a callback, we have to restore the original\r\n                // behavior if the user didn't provide one themselves\r\n                if (err) {\r\n                    if (cb) {\r\n                        return cb.apply(this, arguments);\r\n                    }\r\n                    else if (queryResult && queryResult instanceof events_1.EventEmitter) {\r\n                        queryResult.emit(\"error\", err);\r\n                    }\r\n                }\r\n                else if (cb) {\r\n                    cb.apply(this, arguments);\r\n                }\r\n            });\r\n            try {\r\n                Object.defineProperty(trackingCallback, diagnosticOriginalFunc, { value: cb });\r\n                return trackingCallback;\r\n            }\r\n            catch (e) {\r\n                // this should never happen, but bailout in case it does\r\n                return cb;\r\n            }\r\n        }\r\n        // this function takes too many variations of arguments.\r\n        // this patches any provided callback or creates a new callback if one wasn't provided.\r\n        // since the callback is always called (if provided) in addition to always having a Promisified\r\n        // EventEmitter returned (well, sometimes -- see above), its safe to insert a callback if none was given\r\n        try {\r\n            if (typeof config === \"string\") {\r\n                if (values instanceof Array) {\r\n                    data.query.preparable = {\r\n                        text: config,\r\n                        args: values\r\n                    };\r\n                    callback = patchCallback(callback);\r\n                }\r\n                else {\r\n                    data.query.text = config;\r\n                    // pg v6 will, for some reason, accept both\r\n                    // client.query(\"...\", undefined, () => {...})\r\n                    // **and**\r\n                    // client.query(\"...\", () => {...});\r\n                    // Internally, precedence is given to the callback argument\r\n                    if (callback) {\r\n                        callback = patchCallback(callback);\r\n                    }\r\n                    else {\r\n                        values = patchCallback(values);\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                if (typeof config.name === \"string\") {\r\n                    data.query.plan = config.name;\r\n                }\r\n                else if (config.values instanceof Array) {\r\n                    data.query.preparable = {\r\n                        text: config.text,\r\n                        args: config.values\r\n                    };\r\n                }\r\n                else {\r\n                    data.query.text = config.text;\r\n                }\r\n                if (callback) {\r\n                    callback = patchCallback(callback);\r\n                }\r\n                else if (values) {\r\n                    values = patchCallback(values);\r\n                }\r\n                else {\r\n                    config.callback = patchCallback(config.callback);\r\n                }\r\n            }\r\n        }\r\n        catch (e) {\r\n            // if our logic here throws, bail out and just let pg do its thing\r\n            return originalClientQuery.apply(this, arguments);\r\n        }\r\n        arguments[0] = config;\r\n        arguments[1] = values;\r\n        arguments[2] = callback;\r\n        arguments.length = (arguments.length > 3) ? arguments.length : 3;\r\n        queryResult = originalClientQuery.apply(this, arguments);\r\n        return queryResult;\r\n    };\r\n    return originalPg;\r\n}\r\nfunction postgresLatestPatchFunction(originalPg, originalPgPath) {\r\n    var originalClientQuery = originalPg.Client.prototype.query;\r\n    var diagnosticOriginalFunc = \"__diagnosticOriginalFunc\";\r\n    // wherever the callback is passed, find it, save it, and remove it from the call\r\n    // to the the original .query() function\r\n    originalPg.Client.prototype.query = function query(config, values, callback) {\r\n        var _this = this;\r\n        var _a, _b;\r\n        var callbackProvided = !!callback; // Starting in pg@7.x+, Promise is returned only if !callbackProvided\r\n        var data = {\r\n            query: {},\r\n            database: {\r\n                host: this.connectionParameters.host,\r\n                port: this.connectionParameters.port\r\n            },\r\n            result: null,\r\n            error: null,\r\n            duration: 0,\r\n            time: new Date()\r\n        };\r\n        var queryResult;\r\n        var start = process.hrtime();\r\n        function patchCallback(cb) {\r\n            if (cb && cb[diagnosticOriginalFunc]) {\r\n                cb = cb[diagnosticOriginalFunc];\r\n            }\r\n            var trackingCallback = diagnostic_channel_1.channel.bindToContext(function (err, res) {\r\n                var end = process.hrtime(start);\r\n                data.result = res && { rowCount: res.rowCount, command: res.command };\r\n                data.error = err;\r\n                data.duration = Math.ceil((end[0] * 1e3) + (end[1] / 1e6));\r\n                diagnostic_channel_1.channel.publish(publisherName, data);\r\n                if (err) {\r\n                    if (cb) {\r\n                        return cb.apply(this, arguments);\r\n                    }\r\n                    else if (queryResult && queryResult instanceof events_1.EventEmitter) {\r\n                        queryResult.emit(\"error\", err);\r\n                    }\r\n                }\r\n                else if (cb) {\r\n                    cb.apply(this, arguments);\r\n                }\r\n            });\r\n            try {\r\n                Object.defineProperty(trackingCallback, diagnosticOriginalFunc, { value: cb });\r\n                return trackingCallback;\r\n            }\r\n            catch (e) {\r\n                // this should never happen, but bailout in case it does\r\n                return cb;\r\n            }\r\n        }\r\n        // Only try to wrap the callback if it is a function. We want to keep the same\r\n        // behavior of returning a promise only if no callback is provided. Wrapping\r\n        // a nonfunction makes it a function and pg will interpret it as a callback\r\n        try {\r\n            if (typeof config === \"string\") {\r\n                if (values instanceof Array) {\r\n                    data.query.preparable = {\r\n                        text: config,\r\n                        args: values\r\n                    };\r\n                    callbackProvided = typeof callback === \"function\";\r\n                    callback = callbackProvided ? patchCallback(callback) : callback;\r\n                }\r\n                else {\r\n                    data.query.text = config;\r\n                    if (callback) {\r\n                        callbackProvided = typeof callback === \"function\";\r\n                        callback = callbackProvided ? patchCallback(callback) : callback;\r\n                    }\r\n                    else {\r\n                        callbackProvided = typeof values === \"function\";\r\n                        values = callbackProvided ? patchCallback(values) : values;\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                if (typeof config.name === \"string\") {\r\n                    data.query.plan = config.name;\r\n                }\r\n                else if (config.values instanceof Array) {\r\n                    data.query.preparable = {\r\n                        text: config.text,\r\n                        args: config.values\r\n                    };\r\n                }\r\n                else if (config.cursor) {\r\n                    data.query.text = (_a = config.cursor) === null || _a === void 0 ? void 0 : _a.text;\r\n                }\r\n                else {\r\n                    data.query.text = config.text;\r\n                }\r\n                if (callback) {\r\n                    callbackProvided = typeof callback === \"function\";\r\n                    callback = patchCallback(callback);\r\n                }\r\n                else if (values) {\r\n                    callbackProvided = typeof values === \"function\";\r\n                    values = callbackProvided ? patchCallback(values) : values;\r\n                }\r\n                else {\r\n                    callbackProvided = typeof config.callback === \"function\";\r\n                    config.callback = callbackProvided ? patchCallback(config.callback) : config.callback;\r\n                }\r\n            }\r\n        }\r\n        catch (e) {\r\n            // if our logic here throws, bail out and just let pg do its thing\r\n            return originalClientQuery.apply(this, arguments);\r\n        }\r\n        arguments[0] = config;\r\n        arguments[1] = values;\r\n        arguments[2] = callback;\r\n        arguments.length = (arguments.length > 3) ? arguments.length : 3;\r\n        try {\r\n            queryResult = originalClientQuery.apply(this, arguments);\r\n        }\r\n        catch (err) {\r\n            patchCallback()(err, undefined);\r\n            throw err;\r\n        }\r\n        if (!callbackProvided) {\r\n            if ((queryResult instanceof Promise)) {\r\n                return queryResult\r\n                    // pass resolved promise after publishing the event\r\n                    .then(function (result) {\r\n                    patchCallback()(undefined, result);\r\n                    return new _this._Promise(function (resolve, reject) {\r\n                        resolve(result);\r\n                    });\r\n                })\r\n                    // pass along rejected promise after publishing the error\r\n                    .catch(function (error) {\r\n                    patchCallback()(error, undefined);\r\n                    return new _this._Promise(function (resolve, reject) {\r\n                        reject(error);\r\n                    });\r\n                });\r\n            }\r\n            // Result could be a Cursor, QueryStream or Readable Stream\r\n            else {\r\n                var command = queryResult.text ? queryResult.text : \"\";\r\n                if (queryResult.cursor) {\r\n                    command = (_b = queryResult.cursor) === null || _b === void 0 ? void 0 : _b.text;\r\n                }\r\n                if (command) {\r\n                    var res = {\r\n                        command: command,\r\n                        rowCount: 0,\r\n                    };\r\n                    patchCallback()(undefined, res);\r\n                }\r\n            }\r\n        }\r\n        return queryResult;\r\n    };\r\n    return originalPg;\r\n}\r\nexports.postgres6 = {\r\n    versionSpecifier: \"6.*\",\r\n    patch: postgres6PatchFunction\r\n};\r\nexports.postgres = {\r\n    versionSpecifier: \">=7.* <=8.*\",\r\n    patch: postgresLatestPatchFunction,\r\n    publisherName: publisherName\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"pg\", exports.postgres6);\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"pg\", exports.postgres);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=pg.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/pg.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/redis.pub.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/redis.pub.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.redis = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nvar redisPatchFunction = function (originalRedis) {\r\n    var originalSend = originalRedis.RedisClient.prototype.internal_send_command;\r\n    // Note: This is mixing together both context tracking and dependency tracking\r\n    originalRedis.RedisClient.prototype.internal_send_command = function (commandObj) {\r\n        if (commandObj) {\r\n            var cb_1 = commandObj.callback;\r\n            if (!cb_1 || !cb_1.pubsubBound) {\r\n                var address_1 = this.address;\r\n                var startTime_1 = process.hrtime();\r\n                var startDate_1 = new Date();\r\n                // Note: augmenting the callback on internal_send_command is correct for context\r\n                // tracking, but may be too low-level for dependency tracking. There are some 'errors'\r\n                // which higher levels expect in some cases\r\n                // However, the only other option is to intercept every individual command.\r\n                commandObj.callback = diagnostic_channel_1.channel.bindToContext(function (err, result) {\r\n                    var hrDuration = process.hrtime(startTime_1);\r\n                    /* tslint:disable-next-line:no-bitwise */\r\n                    var duration = (hrDuration[0] * 1e3 + hrDuration[1] / 1e6) | 0;\r\n                    diagnostic_channel_1.channel.publish(\"redis\", { duration: duration, address: address_1, commandObj: commandObj, err: err, result: result, time: startDate_1 });\r\n                    if (typeof cb_1 === \"function\") {\r\n                        cb_1.apply(this, arguments);\r\n                    }\r\n                });\r\n                commandObj.callback.pubsubBound = true;\r\n            }\r\n        }\r\n        return originalSend.call(this, commandObj);\r\n    };\r\n    return originalRedis;\r\n};\r\nexports.redis = {\r\n    versionSpecifier: \">= 2.0.0 < 4.0.0\",\r\n    patch: redisPatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"redis\", exports.redis);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=redis.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/redis.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/tedious.pub.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/tedious.pub.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.tedious = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\nvar tediousPatchFunction = function (originalTedious) {\r\n    var originalMakeRequest = originalTedious.Connection.prototype.makeRequest;\r\n    originalTedious.Connection.prototype.makeRequest = function makeRequest() {\r\n        function getPatchedCallback(origCallback) {\r\n            var start = process.hrtime();\r\n            var data = {\r\n                query: {},\r\n                database: {\r\n                    host: null,\r\n                    port: null\r\n                },\r\n                result: null,\r\n                error: null,\r\n                duration: 0\r\n            };\r\n            return diagnostic_channel_1.channel.bindToContext(function (err, rowCount, rows) {\r\n                var end = process.hrtime(start);\r\n                data = __assign(__assign({}, data), { database: {\r\n                        host: this.connection.config.server,\r\n                        port: this.connection.config.options.port\r\n                    }, result: !err && { rowCount: rowCount, rows: rows }, query: {\r\n                        text: this.parametersByName.statement.value\r\n                    }, error: err, duration: Math.ceil((end[0] * 1e3) + (end[1] / 1e6)) });\r\n                diagnostic_channel_1.channel.publish(\"tedious\", data);\r\n                origCallback.call(this, err, rowCount, rows);\r\n            });\r\n        }\r\n        var request = arguments[0];\r\n        arguments[0].callback = getPatchedCallback(request.callback);\r\n        originalMakeRequest.apply(this, arguments);\r\n    };\r\n    return originalTedious;\r\n};\r\nexports.tedious = {\r\n    versionSpecifier: \">= 6.0.0 < 9.0.0\",\r\n    patch: tediousPatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"tedious\", exports.tedious);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=tedious.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/tedious.pub.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/winston.pub.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/winston.pub.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __extends = (this && this.__extends) || (function () {\r\n    var extendStatics = function (d, b) {\r\n        extendStatics = Object.setPrototypeOf ||\r\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n        return extendStatics(d, b);\r\n    };\r\n    return function (d, b) {\r\n        extendStatics(d, b);\r\n        function __() { this.constructor = d; }\r\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n    };\r\n})();\r\nvar __rest = (this && this.__rest) || function (s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.enable = exports.winston2 = exports.winston3 = void 0;\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nvar diagnostic_channel_1 = __webpack_require__(/*! diagnostic-channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\n// register a \"filter\" with each logger that publishes the data about to be logged\r\nvar winston2PatchFunction = function (originalWinston) {\r\n    var originalLog = originalWinston.Logger.prototype.log;\r\n    var curLevels;\r\n    var loggingFilter = function (level, message, meta) {\r\n        var levelKind;\r\n        if (curLevels === originalWinston.config.npm.levels) {\r\n            levelKind = \"npm\";\r\n        }\r\n        else if (curLevels === originalWinston.config.syslog.levels) {\r\n            levelKind = \"syslog\";\r\n        }\r\n        else {\r\n            levelKind = \"unknown\";\r\n        }\r\n        diagnostic_channel_1.channel.publish(\"winston\", { level: level, message: message, meta: meta, levelKind: levelKind });\r\n        return message;\r\n    };\r\n    // whenever someone logs, ensure our filter comes last\r\n    originalWinston.Logger.prototype.log = function log() {\r\n        curLevels = this.levels;\r\n        if (!this.filters || this.filters.length === 0) {\r\n            this.filters = [loggingFilter];\r\n        }\r\n        else if (this.filters[this.filters.length - 1] !== loggingFilter) {\r\n            this.filters = this.filters.filter(function (f) { return f !== loggingFilter; });\r\n            this.filters.push(loggingFilter);\r\n        }\r\n        return originalLog.apply(this, arguments);\r\n    };\r\n    return originalWinston;\r\n};\r\nvar winston3PatchFunction = function (originalWinston) {\r\n    var mapLevelToKind = function (winston, level) {\r\n        var levelKind;\r\n        if (winston.config.npm.levels[level] != null) {\r\n            levelKind = \"npm\";\r\n        }\r\n        else if (winston.config.syslog.levels[level] != null) {\r\n            levelKind = \"syslog\";\r\n        }\r\n        else {\r\n            levelKind = \"unknown\";\r\n        }\r\n        return levelKind;\r\n    };\r\n    var AppInsightsTransport = /** @class */ (function (_super) {\r\n        __extends(AppInsightsTransport, _super);\r\n        function AppInsightsTransport(winston, opts) {\r\n            var _this = _super.call(this, opts) || this;\r\n            _this.winston = winston;\r\n            return _this;\r\n        }\r\n        AppInsightsTransport.prototype.log = function (info, callback) {\r\n            // tslint:disable-next-line:prefer-const - try to obtain level from Symbol(level) afterwards\r\n            var message = info.message, level = info.level, meta = info.meta, splat = __rest(info, [\"message\", \"level\", \"meta\"]);\r\n            level = typeof Symbol[\"for\"] === \"function\" ? info[Symbol[\"for\"](\"level\")] : level; // Symbol(level) is uncolorized, so prefer getting it from here\r\n            message = info instanceof Error ? info : message; // Winston places Errors at info, strings at info.message\r\n            var levelKind = mapLevelToKind(this.winston, level);\r\n            meta = meta || {}; // Winston _somtimes_ puts metadata inside meta, so start from here\r\n            for (var key in splat) {\r\n                if (splat.hasOwnProperty(key)) {\r\n                    meta[key] = splat[key];\r\n                }\r\n            }\r\n            diagnostic_channel_1.channel.publish(\"winston\", { message: message, level: level, levelKind: levelKind, meta: meta });\r\n            callback();\r\n        };\r\n        return AppInsightsTransport;\r\n    }(originalWinston.Transport));\r\n    // Patch this function\r\n    function patchedConfigure() {\r\n        // Grab highest sev logging level in case of custom logging levels\r\n        var levels = originalWinston.config.npm.levels;\r\n        if (arguments && arguments[0] && arguments[0].levels) {\r\n            levels = arguments[0].levels;\r\n        }\r\n        var lastLevel;\r\n        for (var level in levels) {\r\n            if (levels.hasOwnProperty(level)) {\r\n                lastLevel = lastLevel === undefined || levels[level] > levels[lastLevel] ? level : lastLevel;\r\n            }\r\n        }\r\n        this.add(new AppInsightsTransport(originalWinston, { level: lastLevel }));\r\n    }\r\n    var origCreate = originalWinston.createLogger;\r\n    originalWinston.createLogger = function patchedCreate() {\r\n        // Grab highest sev logging level in case of custom logging levels\r\n        var levels = originalWinston.config.npm.levels;\r\n        if (arguments && arguments[0] && arguments[0].levels) {\r\n            levels = arguments[0].levels;\r\n        }\r\n        var lastLevel;\r\n        for (var level in levels) {\r\n            if (levels.hasOwnProperty(level)) {\r\n                lastLevel = lastLevel === undefined || levels[level] > levels[lastLevel] ? level : lastLevel;\r\n            }\r\n        }\r\n        // Add custom app insights transport to the end\r\n        // Remark: Configure is not available until after createLogger()\r\n        // and the Logger prototype is not exported in winston 3.x, so\r\n        // patch both createLogger and configure. Could also call configure\r\n        // again after createLogger, but that would cause configure to be called\r\n        // twice per create.\r\n        var result = origCreate.apply(this, arguments);\r\n        result.add(new AppInsightsTransport(originalWinston, { level: lastLevel }));\r\n        var origConfigure = result.configure;\r\n        result.configure = function () {\r\n            origConfigure.apply(this, arguments);\r\n            patchedConfigure.apply(this, arguments);\r\n        };\r\n        return result;\r\n    };\r\n    var origRootConfigure = originalWinston.configure;\r\n    originalWinston.configure = function () {\r\n        origRootConfigure.apply(this, arguments);\r\n        patchedConfigure.apply(this, arguments);\r\n    };\r\n    originalWinston.add(new AppInsightsTransport(originalWinston));\r\n    return originalWinston;\r\n};\r\nexports.winston3 = {\r\n    versionSpecifier: \"3.x\",\r\n    patch: winston3PatchFunction\r\n};\r\nexports.winston2 = {\r\n    versionSpecifier: \"2.x\",\r\n    patch: winston2PatchFunction\r\n};\r\nfunction enable() {\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"winston\", exports.winston2);\r\n    diagnostic_channel_1.channel.registerMonkeyPatch(\"winston\", exports.winston3);\r\n}\r\nexports.enable = enable;\r\n//# sourceMappingURL=winston.pub.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src/winston.pub.js\n");

/***/ })

};
;
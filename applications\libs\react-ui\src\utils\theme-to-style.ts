import { DEFAULT_THEME } from '../constants';
import { ThemeParameter } from '../models/theme-parameter.model';

/** Generates the string required in the <style> tag for the portal theme. */
export const themeToStyle = (cssVariables: ThemeParameter[]) => {
  const cssVariablesMap = new Map(cssVariables.map((item) => [item.key, item.value]));

  const mergedArray = [...cssVariables, ...DEFAULT_THEME.filter((item) => !cssVariablesMap.has(item.key))];

  const cssVariablesString = mergedArray.map((item) => `--sys_${item.key}: ${item.value};`);

  return cssVariablesString;
};

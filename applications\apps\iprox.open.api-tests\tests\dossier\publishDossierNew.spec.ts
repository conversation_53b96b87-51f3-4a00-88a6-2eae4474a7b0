import { colors } from '../../config';
import * as http from '../../enums/httpEnums';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';

describe('Dossier Publish test', () => {
  let bearerToken: any;
  let dossierId: string;
  let dossierCategoryId: string | null;

  beforeAll(async () => {
    bearerToken = await loginAndGetToken();
    dossierCategoryId = await DossierHelpersNew.getRandomCategoryUuid(false, bearerToken);
    const response: any = await DossierHelpersNew.createDossier('Publish dossier', bearerToken, dossierCategoryId);
    dossierId = await response.body.dossier.dossierId;
    await DossierHelpersNew.updateDossier(dossierId, bearerToken);
  }, 90000);

  it('should publish a dossier', async () => {
    const response: any = await DossierHelpersNew.publishDossier(dossierId, bearerToken);
    expect(await response.status).toBe(http.StatusCode.OK_200);

    const publishedDossierResponse: any = await DossierHelpersNew.latestVersionOfDossier(dossierId, bearerToken);
    expect(await publishedDossierResponse.body.dossier.isPublished).toBe(true);
  }, 90000);

  afterAll(async () => {
    await DossierHelpersNew.deleteDossier(dossierId, bearerToken);
  });
});

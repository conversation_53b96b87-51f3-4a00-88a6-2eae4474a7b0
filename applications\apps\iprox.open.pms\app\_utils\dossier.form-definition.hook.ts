'use client';

import { components } from '@/iprox-open.interface';
import { FieldDefinition, FieldType, ValidationRuleType, ValueTypes } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { useDynamicFieldMapper } from './use-dynamic-field-mapper.hook';

export function useDossierFormDefinition(dossier: components['schemas']['DossierDto']) {
  const t = useTranslations('dossier');

  const defaultFields = useMemo<FieldDefinition<FieldType, ValueTypes>[]>(() => {
    const fields: FieldDefinition<FieldType, ValueTypes>[] = [
      {
        name: 'summary',
        label: t('summary'),
        fieldType: FieldType.RichText,
        value: dossier?.summary as string,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateStringLength,
            ruleValue: { maximumLength: 1000 },
          },
        ],
      },
    ];

    return fields;
  }, [dossier?.summary, t]);

  const dynamicFields = useDynamicFieldMapper(dossier?.category?.dynamicFields, dossier?.dynamicFieldValues);

  return [...defaultFields, ...dynamicFields];
}

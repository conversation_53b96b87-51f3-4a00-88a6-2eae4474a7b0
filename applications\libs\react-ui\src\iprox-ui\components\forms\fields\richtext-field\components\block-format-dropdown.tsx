import { $createHeadingNode, HeadingTagType } from '@lexical/rich-text';
import { $setBlocksType } from '@lexical/selection';
import {
  $createParagraphNode,
  $getSelection,
  $isRangeSelection,
  DEPRECATED_$isGridSelection,
  LexicalEditor,
} from 'lexical';
import { useTranslations } from 'next-intl';

import ToolbarTextH1Icon from '../components/icons/text-h1-icon';
import ToolbarTextH2Icon from '../components/icons/text-h2-icon';
import ToolbarTextH3Icon from '../components/icons/text-h3-icon';
import ToolbarTextParagraphIcon from '../components/icons/text-paragraph-icon';
import DropDown from '../components/richtext-toolbar-dropdown/dropdown';
import { DropDownItem } from '../components/richtext-toolbar-dropdown/dropdown-item';

export type BlockType =
  | 'normal'
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'number'
  | 'paragraph'
  | 'check'
  | 'bullet';

function dropDownActiveClass(active: boolean): string {
  if (active) {
    return 'active dropdown-item-active';
  }
  return '';
}

export function BlockFormatDropDown({
  editor,
  blockType,
  disabled = false,
}: {
  blockType: BlockType;
  editor: LexicalEditor;
  disabled?: boolean;
}): JSX.Element {
  const t = useTranslations('components.richtexteditor');

  const formatParagraph = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection) || DEPRECATED_$isGridSelection(selection)) {
        $setBlocksType(selection, () => $createParagraphNode());
      }
    });
  };

  const formatHeading = (headingSize: HeadingTagType) => {
    if (blockType !== headingSize) {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection) || DEPRECATED_$isGridSelection(selection)) {
          $setBlocksType(selection, () => $createHeadingNode(headingSize));
        }
      });
    }
  };

  return (
    <DropDown
      className="mx-2 h-11 w-[115px]"
      disabled={disabled}
      buttonLabel={t(blockType)}
      buttonAriaLabel={t('blockFormatLabel')}
    >
      <DropDownItem className={dropDownActiveClass(blockType === 'paragraph')} onClick={formatParagraph}>
        <ToolbarTextParagraphIcon />
        <span className="text">{t('normal')}</span>
      </DropDownItem>
      <DropDownItem className={dropDownActiveClass(blockType === 'h1')} onClick={() => formatHeading('h1')}>
        <ToolbarTextH1Icon />
        <span className="text">{t('h1')}</span>
      </DropDownItem>
      <DropDownItem className={dropDownActiveClass(blockType === 'h2')} onClick={() => formatHeading('h2')}>
        <ToolbarTextH2Icon />
        <span className="text">{t('h2')}</span>
      </DropDownItem>
      <DropDownItem className={dropDownActiveClass(blockType === 'h3')} onClick={() => formatHeading('h3')}>
        <ToolbarTextH3Icon />
        <span className="text">{t('h3')}</span>
      </DropDownItem>
    </DropDown>
  );
}

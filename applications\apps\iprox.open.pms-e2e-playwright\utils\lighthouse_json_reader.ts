import fs from 'fs/promises';
import path from 'path';

export async function readJsonFiles(outputFolder: string) {
  const jsonData: any[] = [];

  try {
    const files = await fs.readdir(outputFolder);
    for (const file of files) {
      if (path.extname(file) === '.json') {
        const filePath = path.join(outputFolder, file);
        const data = await fs.readFile(filePath, 'utf8');
        jsonData.push({ file: file.replace('.json', '.html'), data: JSON.parse(data) });
      }
    }
    return jsonData;
  } catch (error) {
    console.error('Error reading files', error);
  }
}

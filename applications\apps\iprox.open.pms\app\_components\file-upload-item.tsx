import { CheckCircleIcon, ExclamationTriangleIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Text } from '@iprox/react-ui';
import cx from 'classnames';
import { filesize } from 'filesize';
import { useTranslations } from 'next-intl';
import { FC, useMemo } from 'react';

interface FileUploadItemProps {
  fileId: string;
  progress: number;
  fileName: string;
  fileSize: number;
  loaded?: number;
  estimated?: number;
  error?: string;
  status?: 'queued' | 'uploading' | 'uploadComplete' | 'error';
  onClickRemove?: (fileId: string) => void;
}

export const FileUploadItem: FC<FileUploadItemProps> = ({
  fileId,
  fileName,
  fileSize,
  progress,
  loaded,
  estimated,
  error,
  status = 'queued',
  onClickRemove,
}) => {
  const t = useTranslations('dossier.fileUpload');

  const getSize = (size: number) => {
    return filesize(size, {
      base: 2,
      standard: 'jedec',
    }).toString();
  };

  const loadedSize = useMemo(() => {
    return getSize(loaded || 0);
  }, [loaded]);

  const totalSize = useMemo(() => {
    return getSize(fileSize || 0);
  }, [fileSize]);

  const getReadableTime = (seconds: number) => {
    if (seconds >= 60) {
      return Math.round(seconds / 60) + ' ' + t('minutes');
    }

    return seconds.toFixed(0) + ' ' + t('seconds');
  };

  const renderProgress = () => {
    if (error) {
      return (
        <div className="flex flex-row">
          <ExclamationTriangleIcon className="text-error h-5 w-5" />
          <Text className="font-text text-error mt-1 inline-block text-sm leading-none">{t('error', { error })}</Text>
        </div>
      );
    }

    if (status === 'queued' && !error) {
      return <Text className="text-sm">{t('queued')}</Text>;
    }

    if (status === 'uploading') {
      return (
        <div className="flex flex-col">
          <Text className="text-sm">
            {loadedSize} of {totalSize} - {getReadableTime(estimated || 0)} {t('left')}
          </Text>
          <div className="bg-progress-frame mt-1 h-[3px]" style={{ width: `${progress}%` }} />
        </div>
      );
    }

    if (status === 'uploadComplete' && !error)
      return (
        <div className="flex flex-row">
          <CheckCircleIcon className="text-published h-5 w-5" />
          <Text className="font-text text-published mt-1 inline-block text-sm leading-none">{t('success')}</Text>
        </div>
      );
  };

  return (
    <div
      className={cx(
        'bg-base-10 border-base-10 rounded-md border px-4 py-3',
        { 'border-error': error },
        { 'border-published': !error && status === 'uploadComplete' }
      )}
    >
      <Text className="font-heading text-heading mb-1 inline-block text-lg font-bold leading-none">{fileName}</Text>
      <div className="w-100 mt-2 flex items-center justify-between">
        {renderProgress()}
        {status !== 'uploading' && (
          <button
            className="hover:bg-base-25 flex h-min w-min rounded-full p-2"
            onClick={(e) => {
              e.stopPropagation();
              e.nativeEvent.stopImmediatePropagation();
              onClickRemove?.(fileId);
            }}
            aria-label={t('remove', { fileName })}
          >
            <TrashIcon className="text-error h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};

import { useCallback, useMemo } from 'react';

import { FieldType } from '../models/form.models';
import {
  NumberRangeRuleValue,
  StringLengthRuleValue,
  ValidationRule,
  ValidationRuleType,
} from '../models/validator.models';

export const useValidationRuleValues = (validationRules: ValidationRule[] = [], fieldType: FieldType) => {
  const getValidationRuleValue = useCallback(
    (ruleType: ValidationRuleType) => {
      const rule = validationRules.find((rule) => rule.ruleType === ruleType);

      return rule ?? undefined;
    },
    [validationRules]
  );

  const minMax = useMemo(() => {
    let min;
    let max;

    const stringLengthRule = getValidationRuleValue(ValidationRuleType.ValidateStringLength) as StringLengthRuleValue;
    min = stringLengthRule ? stringLengthRule.ruleValue.minimumLength : '';
    max = stringLengthRule ? stringLengthRule.ruleValue.maximumLength : '';

    if (fieldType === FieldType.Decimal || fieldType === FieldType.Integer) {
      const numberRangeRule = getValidationRuleValue(ValidationRuleType.ValidateNumberRange) as NumberRangeRuleValue;
      min = numberRangeRule ? numberRangeRule.ruleValue.minimumValue : '';
      max = numberRangeRule ? numberRangeRule.ruleValue.maximumValue : '';
    }

    return { min, max };
  }, [fieldType, getValidationRuleValue]);

  return minMax;
};

import { useField } from 'formik';
import { MultiValue, SingleValue } from 'react-select';

import { CustomSelect, CustomSelectOption } from '../../../custom-select/custom-select';
import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { MultiFieldDefinition } from '../../models/form.models';

export function SelectField(props: MultiFieldDefinition) {
  const [field, meta, helpers] = useField(props);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'select');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  const handleChange = (value: SingleValue<CustomSelectOption> | MultiValue<CustomSelectOption>) => {
    if (props.isMulti) {
      helpers.setValue((value as MultiValue<CustomSelectOption>)?.map((v) => v.value));
    } else {
      helpers.setValue((value as SingleValue<CustomSelectOption>)?.value);
    }
  };

  return (
    <FormField definition={props} errorMessage={meta.touched ? meta.error : undefined} {...formControlProps}>
      <CustomSelect
        id={inputProps.id}
        ariaDescribedbyId={inputProps['aria-describedby']}
        name={field.name}
        value={field.value ?? ''}
        options={props.options}
        onChange={!props.isMulti ? handleChange : () => null}
        onClose={handleChange}
        errorMessage={meta.touched ? meta.error : undefined}
        onBlur={() => helpers.setTouched(true, true)}
        isMulti={props.isMulti}
      />
    </FormField>
  );
}

'use client';

import { ALLOWED_IMAGE_FILE_TYPES } from '@/config/allowed-file-types';
import { useClientApi } from '@/http/fetcher-api.client';
import { updateFavicon } from '@/services/image-asset-service.client';
import { getPublicAssets } from '@/services/public-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { ImageUploader } from '@iprox/iprox-ui';
import { showToast } from '@iprox/react-ui';
import { useAppSettings } from '@iprox/shared-context';
import { AxiosProgressEvent } from 'axios';
import { useTranslations } from 'next-intl';

interface FaviconUploaderProps {
  faviconAssetPath?: string | null;
}

export function FaviconUploader({ faviconAssetPath }: FaviconUploaderProps) {
  const settings = useAppSettings();
  const clientApi = useClientApi();
  const t = useTranslations('site');

  const upload = async (image: File, handleProgress: (progressEvent: AxiosProgressEvent) => void) => {
    try {
      await updateFavicon(settings.apiUrl, image, handleProgress);
      showToast(t('faviconUpdated'), { type: 'success' });

      const publicAssets = await getPublicAssets(clientApi);
      return `${settings.apiUrl}/public/asset${publicAssets.siteAssets.favicon}`;
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    }
  };

  return (
    <ImageUploader
      label={t('faviconSettings')}
      allowedFileTypes={ALLOWED_IMAGE_FILE_TYPES}
      uploadPromise={upload}
      overflowText={t('changeFavicon')}
      initialImageSrc={faviconAssetPath ? `${settings.apiUrl}/public/asset${faviconAssetPath}` : undefined}
    />
  );
}

import { ChevronDownIcon, ChevronUpIcon, PencilSquareIcon, PlusIcon } from '@heroicons/react/24/outline';
import cx from 'classnames';
import { filesize } from 'filesize';
import { useTranslations } from 'next-intl';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Text } from '../../text/text';
import { FileNode, FolderNode, Node } from '../models/file-structure';
import { isNodeSelectedInHierarchy } from './../utils/file-structure-utils';
import { Checkbox } from './checkbox';
import { DisplayFileNode } from './file-node';
import { RenamingForm } from './renaming-form';

interface DisplayFolderNodeProps {
  item: FolderNode;
  level: number;
  editableNode?: boolean;
  /** Emits when a filenode is clicked. */
  onFileClick: (item: FileNode) => void;
  /** Emits when a node is expanded, and does not yet have its children available. */
  onLoadChildren: (item: FolderNode) => Promise<void>;
  /** Emits when a folder node is toggled */
  onToggleFolderNode: (item: FolderNode) => void;
  onSelectItem: (item: Node, isSelected: boolean) => void;
  onAddButtonClick?: (item: FolderNode) => void;
  onRenaming?: (item: Node, newName: string) => Promise<boolean>;
}

function DisplayFolderNodeComponent({
  item,
  level,
  editableNode,
  onSelectItem,
  onLoadChildren,
  onToggleFolderNode,
  onFileClick,
  onAddButtonClick = () => null,
  onRenaming,
}: DisplayFolderNodeProps) {
  const selectInputRef = useRef<HTMLInputElement>(null);
  const t = useTranslations('components.fileStructure');

  const [isUpdatingName, setIsUpdatingName] = useState(false);
  const [isRenaming, setIsRenaming] = useState(false);

  const isNodeSelectedInHierarchyCallback = useCallback((node: Node, currentItem: Node) => {
    return isNodeSelectedInHierarchy(node, currentItem);
  }, []);

  useEffect(() => {
    if (selectInputRef.current) {
      const isChildSelected =
        item.children.some((node) => isNodeSelectedInHierarchyCallback(node, item)) && !selectInputRef.current.checked;
      selectInputRef.current.indeterminate = isChildSelected;
    }
  }, [isNodeSelectedInHierarchyCallback, item, item.children]);

  const handleSelectItem = (item: Node, isSelected: boolean) => {
    onSelectItem?.(item, isSelected);
  };

  const handleItemClick = async (clickedItem: FolderNode) => {
    const childCount = (clickedItem.numberOfDescendantFiles ?? 0) + (clickedItem.numberOfDescendantFolders ?? 0);

    if (childCount > 0 && clickedItem.children.length === 0) {
      return await onLoadChildren(clickedItem);
    }

    return onToggleFolderNode(clickedItem);
  };

  const handleItemKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      event.nativeEvent.stopImmediatePropagation();
      handleItemClick(item);
    }
  };

  const itemStyle = {
    marginLeft: `${level * 24}px`,
  };

  const NodeItem = useMemo(() => {
    return (
      item.isExpanded &&
      item.children &&
      item.children.map((child) => (
        <div key={child.nodeId} role="listitem" className="mt-2">
          {child.nodeType === 'File' ? (
            <DisplayFileNode
              item={child}
              level={level + 1}
              editableNode={editableNode}
              onFileClick={onFileClick}
              onSelectItem={onSelectItem}
              onRenaming={onRenaming}
            />
          ) : (
            <DisplayFolderNode
              item={child}
              level={level + 1}
              onFileClick={onFileClick}
              onSelectItem={onSelectItem}
              onLoadChildren={onLoadChildren}
              onToggleFolderNode={onToggleFolderNode}
              editableNode={editableNode}
              onAddButtonClick={onAddButtonClick}
              onRenaming={onRenaming}
            />
          )}
        </div>
      ))
    );
  }, [
    item.isExpanded,
    item.children,
    level,
    editableNode,
    onFileClick,
    onSelectItem,
    onRenaming,
    onLoadChildren,
    onToggleFolderNode,
    onAddButtonClick,
  ]);

  return (
    <>
      <div
        className={cx('flex grid-cols-[max-content_3fr_minmax(0,_20%)] p-3 md:grid md:gap-4', {
          'bg-expanded-background': item.isExpanded,
        })}
        style={itemStyle}
      >
        <div className="w-100 flex cursor-pointer items-start md:items-center">
          <Checkbox
            inputRef={selectInputRef}
            ariaLabel={
              item.isSelected
                ? t('deselectNode', { nodeName: item.nodeName })
                : t('selectNode', { nodeName: item.nodeName })
            }
            value={item.nodeId}
            isSelected={item.isSelected}
            isDisabled={isUpdatingName}
            handleSelect={(e) => handleSelectItem(item, e.target.checked)}
          />
        </div>
        <div>
          {isUpdatingName ? (
            <RenamingForm
              label={item.nodeName}
              onLabelUpdate={async (values) => {
                const newName = typeof values.name === 'string' ? values.name : '';

                let updated: boolean | undefined;

                try {
                  setIsRenaming(true);
                  updated = await onRenaming?.(item, newName);
                } finally {
                  setTimeout(() => setIsRenaming(false), 1000);
                }

                if (updated) {
                  item.nodeName = newName;
                  setIsUpdatingName(false);
                }
              }}
              onCancel={() => setIsUpdatingName(false)}
              isSubmitting={isRenaming}
            />
          ) : (
            <div className="grid w-full grid-cols-[auto_auto_1fr] gap-2">
              <button
                className={`group flex items-start gap-2`}
                onClickCapture={(event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  event.nativeEvent.stopImmediatePropagation();
                  handleItemClick(item);
                }}
                onKeyDown={(e) => handleItemKeyDown(e)}
                role={item.nodeType === 'Folder' || item.nodeType === 'File' ? 'button' : 'listitem'}
                aria-expanded={item.nodeType === 'Folder' ? item.isExpanded : undefined}
                aria-label={t('folder', { folderName: item.nodeName ?? '' })}
                name={t('folder', { folderName: item.nodeName ?? '' })}
              >
                <Text className="font-heading text-body flex text-left text-base font-semibold group-hover:underline">
                  {item.nodeName}
                </Text>
                {item.isExpanded ? (
                  <ChevronUpIcon className="group-hover:bg-secondary text-primary mt-1 flex h-5 w-5 min-w-[20px]" />
                ) : (
                  <ChevronDownIcon className="group-hover:bg-secondary text-primary mt-1 flex h-5 w-5 min-w-[20px]" />
                )}
              </button>
              {editableNode && (
                <>
                  <button
                    className="hover:bg-secondary mt-1 flex h-min w-min"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.nativeEvent.stopImmediatePropagation();
                      onAddButtonClick(item);
                    }}
                    aria-label={t('addToThis', { nodeName: item.nodeName })}
                  >
                    <PlusIcon className="text-primary h-5 w-5" />
                  </button>
                  <button
                    className="hover:bg-secondary mt-1 flex h-min w-min"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.nativeEvent.stopImmediatePropagation();
                      setIsUpdatingName(!isUpdatingName);
                    }}
                    aria-label={t('rename', { nodeName: item.nodeName })}
                  >
                    <PencilSquareIcon className="text-primary h-5 w-5" />
                  </button>
                </>
              )}
            </div>
          )}
        </div>
        <Text className="font-heading text-body hidden text-left text-base md:flex md:items-center">{`${filesize(
          item.totalDescendantSize,
          {
            base: 2,
            standard: 'jedec',
          }
        ).toString()}`}</Text>
      </div>
      {NodeItem && <div role="list">{NodeItem}</div>}
    </>
  );
}

export const DisplayFolderNode = memo(DisplayFolderNodeComponent);

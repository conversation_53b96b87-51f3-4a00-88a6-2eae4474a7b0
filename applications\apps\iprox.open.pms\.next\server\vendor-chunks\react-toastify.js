"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-toastify";
exports.ids = ["vendor-chunks/react-toastify"];
exports.modules = {

/***/ "(ssr)/../../node_modules/react-toastify/dist/ReactToastify.css":
/*!****************************************************************!*\
  !*** ../../node_modules/react-toastify/dist/ReactToastify.css ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"791fe3d42eca\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LXRvYXN0aWZ5L2Rpc3QvUmVhY3RUb2FzdGlmeS5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LXRvYXN0aWZ5L2Rpc3QvUmVhY3RUb2FzdGlmeS5jc3M/MDdiOCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjc5MWZlM2Q0MmVjYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-toastify/dist/ReactToastify.css\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-toastify/dist/react-toastify.esm.mjs":
/*!*********************************************************************!*\
  !*** ../../node_modules/react-toastify/dist/react-toastify.esm.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Bounce: () => (/* binding */ H),
/* harmony export */   Flip: () => (/* binding */ Y),
/* harmony export */   Icons: () => (/* binding */ z),
/* harmony export */   Slide: () => (/* binding */ F),
/* harmony export */   ToastContainer: () => (/* binding */ Q),
/* harmony export */   Zoom: () => (/* binding */ X),
/* harmony export */   collapseToast: () => (/* binding */ f),
/* harmony export */   cssTransition: () => (/* binding */ g),
/* harmony export */   toast: () => (/* binding */ B),
/* harmony export */   useToast: () => (/* binding */ N),
/* harmony export */   useToastContainer: () => (/* binding */ L)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js");
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ "(ssr)/../../node_modules/clsx/dist/clsx.mjs");
/* __next_internal_client_entry_do_not_use__ Bounce,Flip,Icons,Slide,ToastContainer,Zoom,collapseToast,cssTransition,toast,useToast,useToastContainer auto */ 

const c = (e)=>"number" == typeof e && !isNaN(e), d = (e)=>"string" == typeof e, u = (e)=>"function" == typeof e, p = (e)=>d(e) || u(e) ? e : null, m = (e)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(e) || d(e) || u(e) || c(e);
function f(e, t, n) {
    void 0 === n && (n = 300);
    const { scrollHeight: o, style: s } = e;
    requestAnimationFrame(()=>{
        s.minHeight = "initial", s.height = o + "px", s.transition = `all ${n}ms`, requestAnimationFrame(()=>{
            s.height = "0", s.padding = "0", s.margin = "0", setTimeout(t, n);
        });
    });
}
function g(t) {
    let { enter: a, exit: r, appendPosition: i = !1, collapse: l = !0, collapseDuration: c = 300 } = t;
    return function(t) {
        let { children: d, position: u, preventExitTransition: p, done: m, nodeRef: g, isIn: y, playToast: v } = t;
        const h = i ? `${a}--${u}` : a, T = i ? `${r}--${u}` : r, E = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);
        return (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(()=>{
            const e = g.current, t = h.split(" "), n = (o)=>{
                o.target === g.current && (v(), e.removeEventListener("animationend", n), e.removeEventListener("animationcancel", n), 0 === E.current && "animationcancel" !== o.type && e.classList.remove(...t));
            };
            e.classList.add(...t), e.addEventListener("animationend", n), e.addEventListener("animationcancel", n);
        }, []), (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{
            const e = g.current, t = ()=>{
                e.removeEventListener("animationend", t), l ? f(e, m, c) : m();
            };
            y || (p ? t() : (E.current = 1, e.className += ` ${T}`, e.addEventListener("animationend", t)));
        }, [
            y
        ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, d);
    };
}
function y(e, t) {
    return null != e ? {
        content: e.content,
        containerId: e.props.containerId,
        id: e.props.toastId,
        theme: e.props.theme,
        type: e.props.type,
        data: e.props.data || {},
        isLoading: e.props.isLoading,
        icon: e.props.icon,
        status: t
    } : {};
}
const v = new Map;
let h = [];
const T = new Set, E = (e)=>T.forEach((t)=>t(e)), b = ()=>v.size > 0;
function I(e, t) {
    var n;
    if (t) return !(null == (n = v.get(t)) || !n.isToastActive(e));
    let o = !1;
    return v.forEach((t)=>{
        t.isToastActive(e) && (o = !0);
    }), o;
}
function _(e, t) {
    m(e) && (b() || h.push({
        content: e,
        options: t
    }), v.forEach((n)=>{
        n.buildToast(e, t);
    }));
}
function C(e, t) {
    v.forEach((n)=>{
        null != t && null != t && t.containerId ? (null == t ? void 0 : t.containerId) === n.id && n.toggle(e, null == t ? void 0 : t.id) : n.toggle(e, null == t ? void 0 : t.id);
    });
}
function L(e) {
    const { subscribe: o, getSnapshot: s, setProps: i } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(function(e) {
        const n = e.containerId || 1;
        return {
            subscribe (o) {
                const s = function(e, n, o) {
                    let s = 1, r = 0, i = [], l = [], f = [], g = n;
                    const v = new Map, h = new Set, T = ()=>{
                        f = Array.from(v.values()), h.forEach((e)=>e());
                    }, E = (e)=>{
                        l = null == e ? [] : l.filter((t)=>t !== e), T();
                    }, b = (e)=>{
                        const { toastId: n, onOpen: s, updateId: a, children: r } = e.props, i = null == a;
                        e.staleId && v.delete(e.staleId), v.set(n, e), l = [
                            ...l,
                            e.props.toastId
                        ].filter((t)=>t !== e.staleId), T(), o(y(e, i ? "added" : "updated")), i && u(s) && s(/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(r) && r.props);
                    };
                    return {
                        id: e,
                        props: g,
                        observe: (e)=>(h.add(e), ()=>h.delete(e)),
                        toggle: (e, t)=>{
                            v.forEach((n)=>{
                                null != t && t !== n.props.toastId || u(n.toggle) && n.toggle(e);
                            });
                        },
                        removeToast: E,
                        toasts: v,
                        clearQueue: ()=>{
                            r -= i.length, i = [];
                        },
                        buildToast: (n, l)=>{
                            if (((t)=>{
                                let { containerId: n, toastId: o, updateId: s } = t;
                                const a = n ? n !== e : 1 !== e, r = v.has(o) && null == s;
                                return a || r;
                            })(l)) return;
                            const { toastId: f, updateId: h, data: I, staleId: _, delay: C } = l, L = ()=>{
                                E(f);
                            }, N = null == h;
                            N && r++;
                            const $ = {
                                ...g,
                                style: g.toastStyle,
                                key: s++,
                                ...Object.fromEntries(Object.entries(l).filter((e)=>{
                                    let [t, n] = e;
                                    return null != n;
                                })),
                                toastId: f,
                                updateId: h,
                                data: I,
                                closeToast: L,
                                isIn: !1,
                                className: p(l.className || g.toastClassName),
                                bodyClassName: p(l.bodyClassName || g.bodyClassName),
                                progressClassName: p(l.progressClassName || g.progressClassName),
                                autoClose: !l.isLoading && (w = l.autoClose, k = g.autoClose, !1 === w || c(w) && w > 0 ? w : k),
                                deleteToast () {
                                    const e = v.get(f), { onClose: n, children: s } = e.props;
                                    u(n) && n(/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(s) && s.props), o(y(e, "removed")), v.delete(f), r--, r < 0 && (r = 0), i.length > 0 ? b(i.shift()) : T();
                                }
                            };
                            var w, k;
                            $.closeButton = g.closeButton, !1 === l.closeButton || m(l.closeButton) ? $.closeButton = l.closeButton : !0 === l.closeButton && ($.closeButton = !m(g.closeButton) || g.closeButton);
                            let P = n;
                            /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(n) && !d(n.type) ? P = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(n, {
                                closeToast: L,
                                toastProps: $,
                                data: I
                            }) : u(n) && (P = n({
                                closeToast: L,
                                toastProps: $,
                                data: I
                            }));
                            const M = {
                                content: P,
                                props: $,
                                staleId: _
                            };
                            g.limit && g.limit > 0 && r > g.limit && N ? i.push(M) : c(C) ? setTimeout(()=>{
                                b(M);
                            }, C) : b(M);
                        },
                        setProps (e) {
                            g = e;
                        },
                        setToggle: (e, t)=>{
                            v.get(e).toggle = t;
                        },
                        isToastActive: (e)=>l.some((t)=>t === e),
                        getSnapshot: ()=>f
                    };
                }(n, e, E);
                v.set(n, s);
                const r = s.observe(o);
                return h.forEach((e)=>_(e.content, e.options)), h = [], ()=>{
                    r(), v.delete(n);
                };
            },
            setProps (e) {
                var t;
                null == (t = v.get(n)) || t.setProps(e);
            },
            getSnapshot () {
                var e;
                return null == (e = v.get(n)) ? void 0 : e.getSnapshot();
            }
        };
    }(e)).current;
    i(e);
    const l = (0,react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)(o, s, s);
    return {
        getToastToRender: function(t) {
            if (!l) return [];
            const n = new Map;
            return e.newestOnTop && l.reverse(), l.forEach((e)=>{
                const { position: t } = e.props;
                n.has(t) || n.set(t, []), n.get(t).push(e);
            }), Array.from(n, (e)=>t(e[0], e[1]));
        },
        isToastActive: I,
        count: null == l ? void 0 : l.length
    };
}
function N(e) {
    const [t, o] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!1), [a, r] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!1), l = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null), c = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({
        start: 0,
        delta: 0,
        removalDistance: 0,
        canCloseOnClick: !0,
        canDrag: !1,
        didMove: !1
    }).current, { autoClose: d, pauseOnHover: u, closeToast: p, onClick: m, closeOnClick: f } = e;
    var g, y;
    function h() {
        o(!0);
    }
    function T() {
        o(!1);
    }
    function E(n) {
        const o = l.current;
        c.canDrag && o && (c.didMove = !0, t && T(), c.delta = "x" === e.draggableDirection ? n.clientX - c.start : n.clientY - c.start, c.start !== n.clientX && (c.canCloseOnClick = !1), o.style.transform = `translate3d(${"x" === e.draggableDirection ? `${c.delta}px, var(--y)` : `0, calc(${c.delta}px + var(--y))`},0)`, o.style.opacity = "" + (1 - Math.abs(c.delta / c.removalDistance)));
    }
    function b() {
        document.removeEventListener("pointermove", E), document.removeEventListener("pointerup", b);
        const t = l.current;
        if (c.canDrag && c.didMove && t) {
            if (c.canDrag = !1, Math.abs(c.delta) > c.removalDistance) return r(!0), e.closeToast(), void e.collapseAll();
            t.style.transition = "transform 0.2s, opacity 0.2s", t.style.removeProperty("transform"), t.style.removeProperty("opacity");
        }
    }
    null == (y = v.get((g = {
        id: e.toastId,
        containerId: e.containerId,
        fn: o
    }).containerId || 1)) || y.setToggle(g.id, g.fn), (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{
        if (e.pauseOnFocusLoss) return document.hasFocus() || T(), window.addEventListener("focus", h), window.addEventListener("blur", T), ()=>{
            window.removeEventListener("focus", h), window.removeEventListener("blur", T);
        };
    }, [
        e.pauseOnFocusLoss
    ]);
    const I = {
        onPointerDown: function(t) {
            if (!0 === e.draggable || e.draggable === t.pointerType) {
                c.didMove = !1, document.addEventListener("pointermove", E), document.addEventListener("pointerup", b);
                const n = l.current;
                c.canCloseOnClick = !0, c.canDrag = !0, n.style.transition = "none", "x" === e.draggableDirection ? (c.start = t.clientX, c.removalDistance = n.offsetWidth * (e.draggablePercent / 100)) : (c.start = t.clientY, c.removalDistance = n.offsetHeight * (80 === e.draggablePercent ? 1.5 * e.draggablePercent : e.draggablePercent) / 100);
            }
        },
        onPointerUp: function(t) {
            const { top: n, bottom: o, left: s, right: a } = l.current.getBoundingClientRect();
            "touchend" !== t.nativeEvent.type && e.pauseOnHover && t.clientX >= s && t.clientX <= a && t.clientY >= n && t.clientY <= o ? T() : h();
        }
    };
    return d && u && (I.onMouseEnter = T, e.stacked || (I.onMouseLeave = h)), f && (I.onClick = (e)=>{
        m && m(e), c.canCloseOnClick && p();
    }), {
        playToast: h,
        pauseToast: T,
        isRunning: t,
        preventExitTransition: a,
        toastRef: l,
        eventHandlers: I
    };
}
function $(t) {
    let { delay: n, isRunning: o, closeToast: s, type: a = "default", hide: r, className: i, style: c, controlledProgress: d, progress: p, rtl: m, isIn: f, theme: g } = t;
    const y = r || d && 0 === p, v = {
        ...c,
        animationDuration: `${n}ms`,
        animationPlayState: o ? "running" : "paused"
    };
    d && (v.transform = `scaleX(${p})`);
    const h = (0,clsx__WEBPACK_IMPORTED_MODULE_1__["default"])("Toastify__progress-bar", d ? "Toastify__progress-bar--controlled" : "Toastify__progress-bar--animated", `Toastify__progress-bar-theme--${g}`, `Toastify__progress-bar--${a}`, {
        "Toastify__progress-bar--rtl": m
    }), T = u(i) ? i({
        rtl: m,
        type: a,
        defaultClassName: h
    }) : (0,clsx__WEBPACK_IMPORTED_MODULE_1__["default"])(h, i), E = {
        [d && p >= 1 ? "onTransitionEnd" : "onAnimationEnd"]: d && p < 1 ? null : ()=>{
            f && s();
        }
    };
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        className: "Toastify__progress-bar--wrp",
        "data-hidden": y
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        className: `Toastify__progress-bar--bg Toastify__progress-bar-theme--${g} Toastify__progress-bar--${a}`
    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        role: "progressbar",
        "aria-hidden": y ? "true" : "false",
        "aria-label": "notification timer",
        className: T,
        style: v,
        ...E
    }));
}
let w = 1;
const k = ()=>"" + w++;
function P(e) {
    return e && (d(e.toastId) || c(e.toastId)) ? e.toastId : k();
}
function M(e, t) {
    return _(e, t), t.toastId;
}
function x(e, t) {
    return {
        ...t,
        type: t && t.type || e,
        toastId: P(t)
    };
}
function A(e) {
    return (t, n)=>M(t, x(e, n));
}
function B(e, t) {
    return M(e, x("default", t));
}
B.loading = (e, t)=>M(e, x("default", {
        isLoading: !0,
        autoClose: !1,
        closeOnClick: !1,
        closeButton: !1,
        draggable: !1,
        ...t
    })), B.promise = function(e, t, n) {
    let o, { pending: s, error: a, success: r } = t;
    s && (o = d(s) ? B.loading(s, n) : B.loading(s.render, {
        ...n,
        ...s
    }));
    const i = {
        isLoading: null,
        autoClose: null,
        closeOnClick: null,
        closeButton: null,
        draggable: null
    }, l = (e, t, s)=>{
        if (null == t) return void B.dismiss(o);
        const a = {
            type: e,
            ...i,
            ...n,
            data: s
        }, r = d(t) ? {
            render: t
        } : t;
        return o ? B.update(o, {
            ...a,
            ...r
        }) : B(r.render, {
            ...a,
            ...r
        }), s;
    }, c = u(e) ? e() : e;
    return c.then((e)=>l("success", r, e)).catch((e)=>l("error", a, e)), c;
}, B.success = A("success"), B.info = A("info"), B.error = A("error"), B.warning = A("warning"), B.warn = B.warning, B.dark = (e, t)=>M(e, x("default", {
        theme: "dark",
        ...t
    })), B.dismiss = function(e) {
    !function(e) {
        var t;
        if (b()) {
            if (null == e || d(t = e) || c(t)) v.forEach((t)=>{
                t.removeToast(e);
            });
            else if (e && ("containerId" in e || "id" in e)) {
                const t = v.get(e.containerId);
                t ? t.removeToast(e.id) : v.forEach((t)=>{
                    t.removeToast(e.id);
                });
            }
        } else h = h.filter((t)=>null != e && t.options.toastId !== e);
    }(e);
}, B.clearWaitingQueue = function(e) {
    void 0 === e && (e = {}), v.forEach((t)=>{
        !t.props.limit || e.containerId && t.id !== e.containerId || t.clearQueue();
    });
}, B.isActive = I, B.update = function(e, t) {
    void 0 === t && (t = {});
    const n = ((e, t)=>{
        var n;
        let { containerId: o } = t;
        return null == (n = v.get(o || 1)) ? void 0 : n.toasts.get(e);
    })(e, t);
    if (n) {
        const { props: o, content: s } = n, a = {
            delay: 100,
            ...o,
            ...t,
            toastId: t.toastId || e,
            updateId: k()
        };
        a.toastId !== e && (a.staleId = e);
        const r = a.render || s;
        delete a.render, M(r, a);
    }
}, B.done = (e)=>{
    B.update(e, {
        progress: 1
    });
}, B.onChange = function(e) {
    return T.add(e), ()=>{
        T.delete(e);
    };
}, B.play = (e)=>C(!0, e), B.pause = (e)=>C(!1, e);
const O =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect, D = (t)=>{
    let { theme: n, type: o, isLoading: s, ...a } = t;
    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("svg", {
        viewBox: "0 0 24 24",
        width: "100%",
        height: "100%",
        fill: "colored" === n ? "currentColor" : `var(--toastify-icon-color-${o})`,
        ...a
    });
}, z = {
    info: function(t) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(D, {
            ...t
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("path", {
            d: "M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"
        }));
    },
    warning: function(t) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(D, {
            ...t
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("path", {
            d: "M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"
        }));
    },
    success: function(t) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(D, {
            ...t
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("path", {
            d: "M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"
        }));
    },
    error: function(t) {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(D, {
            ...t
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("path", {
            d: "M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"
        }));
    },
    spinner: function() {
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
            className: "Toastify__spinner"
        });
    }
}, R = (n)=>{
    const { isRunning: o, preventExitTransition: s, toastRef: r, eventHandlers: i, playToast: c } = N(n), { closeButton: d, children: p, autoClose: m, onClick: f, type: g, hideProgressBar: y, closeToast: v, transition: h, position: T, className: E, style: b, bodyClassName: I, bodyStyle: _, progressClassName: C, progressStyle: L, updateId: w, role: k, progress: P, rtl: M, toastId: x, deleteToast: A, isIn: B, isLoading: O, closeOnClick: D, theme: R } = n, S = (0,clsx__WEBPACK_IMPORTED_MODULE_1__["default"])("Toastify__toast", `Toastify__toast-theme--${R}`, `Toastify__toast--${g}`, {
        "Toastify__toast--rtl": M
    }, {
        "Toastify__toast--close-on-click": D
    }), H = u(E) ? E({
        rtl: M,
        position: T,
        type: g,
        defaultClassName: S
    }) : (0,clsx__WEBPACK_IMPORTED_MODULE_1__["default"])(S, E), F = function(e) {
        let { theme: n, type: o, isLoading: s, icon: r } = e, i = null;
        const l = {
            theme: n,
            type: o
        };
        return !1 === r || (u(r) ? i = r({
            ...l,
            isLoading: s
        }) : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(r) ? i = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(r, l) : s ? i = z.spinner() : ((e)=>e in z)(o) && (i = z[o](l))), i;
    }(n), X = !!P || !m, Y = {
        closeToast: v,
        type: g,
        theme: R
    };
    let q = null;
    return !1 === d || (q = u(d) ? d(Y) : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(d) ? /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(d, Y) : function(t) {
        let { closeToast: n, theme: o, ariaLabel: s = "close" } = t;
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("button", {
            className: `Toastify__close-button Toastify__close-button--${o}`,
            type: "button",
            onClick: (e)=>{
                e.stopPropagation(), n(e);
            },
            "aria-label": s
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("svg", {
            "aria-hidden": "true",
            viewBox: "0 0 14 16"
        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("path", {
            fillRule: "evenodd",
            d: "M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"
        })));
    }(Y)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(h, {
        isIn: B,
        done: A,
        position: T,
        preventExitTransition: s,
        nodeRef: r,
        playToast: c
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        id: x,
        onClick: f,
        "data-in": B,
        className: H,
        ...i,
        style: b,
        ref: r
    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        ...B && {
            role: k
        },
        className: u(I) ? I({
            type: g
        }) : (0,clsx__WEBPACK_IMPORTED_MODULE_1__["default"])("Toastify__toast-body", I),
        style: _
    }, null != F && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__["default"])("Toastify__toast-icon", {
            "Toastify--animate-icon Toastify__zoom-enter": !O
        })
    }, F), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", null, p)), q, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement($, {
        ...w && !X ? {
            key: `pb-${w}`
        } : {},
        rtl: M,
        theme: R,
        delay: m,
        isRunning: o,
        isIn: B,
        closeToast: v,
        hide: y,
        type: g,
        style: L,
        className: C,
        controlledProgress: X,
        progress: P || 0
    })));
}, S = function(e, t) {
    return void 0 === t && (t = !1), {
        enter: `Toastify--animate Toastify__${e}-enter`,
        exit: `Toastify--animate Toastify__${e}-exit`,
        appendPosition: t
    };
}, H = g(S("bounce", !0)), F = g(S("slide", !0)), X = g(S("zoom")), Y = g(S("flip")), q = {
    position: "top-right",
    transition: H,
    autoClose: 5e3,
    closeButton: !0,
    pauseOnHover: !0,
    pauseOnFocusLoss: !0,
    draggable: "touch",
    draggablePercent: 80,
    draggableDirection: "x",
    role: "alert",
    theme: "light"
};
function Q(t) {
    let o = {
        ...q,
        ...t
    };
    const s = t.stacked, [a, r] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!0), c = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null), { getToastToRender: d, isToastActive: m, count: f } = L(o), { className: g, style: y, rtl: v, containerId: h } = o;
    function T(e) {
        const t = (0,clsx__WEBPACK_IMPORTED_MODULE_1__["default"])("Toastify__toast-container", `Toastify__toast-container--${e}`, {
            "Toastify__toast-container--rtl": v
        });
        return u(g) ? g({
            position: e,
            rtl: v,
            defaultClassName: t
        }) : (0,clsx__WEBPACK_IMPORTED_MODULE_1__["default"])(t, p(g));
    }
    function E() {
        s && (r(!0), B.play());
    }
    return O(()=>{
        if (s) {
            var e;
            const t = c.current.querySelectorAll('[data-in="true"]'), n = 12, s = null == (e = o.position) ? void 0 : e.includes("top");
            let r = 0, i = 0;
            Array.from(t).reverse().forEach((e, t)=>{
                const o = e;
                o.classList.add("Toastify__toast--stacked"), t > 0 && (o.dataset.collapsed = `${a}`), o.dataset.pos || (o.dataset.pos = s ? "top" : "bot");
                const l = r * (a ? .2 : 1) + (a ? 0 : n * t);
                o.style.setProperty("--y", `${s ? l : -1 * l}px`), o.style.setProperty("--g", `${n}`), o.style.setProperty("--s", "" + (1 - (a ? i : 0))), r += o.offsetHeight, i += .025;
            });
        }
    }, [
        a,
        f,
        s
    ]), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
        ref: c,
        className: "Toastify",
        id: h,
        onMouseEnter: ()=>{
            s && (r(!1), B.pause());
        },
        onMouseLeave: E
    }, d((t, n)=>{
        const o = n.length ? {
            ...y
        } : {
            ...y,
            pointerEvents: "none"
        };
        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement("div", {
            className: T(t),
            style: o,
            key: `container-${t}`
        }, n.map((t)=>{
            let { content: n, props: o } = t;
            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(R, {
                ...o,
                stacked: s,
                collapseAll: E,
                isIn: m(o.toastId, o.containerId),
                style: o.style,
                key: `toast-${o.key}`
            }, n);
        }));
    }));
}
 //# sourceMappingURL=react-toastify.esm.mjs.map


/***/ })

};
;
import { sanitize } from 'isomorphic-dompurify';

import { ValidationRule, ValidationRuleType, ValidatorFn, ValidatorFnFactory } from '../models/validator.models';

export interface RequiredValidationRule extends ValidationRule<ValidationRuleType.RequiredProperty> {
  ruleValue: 'Required';
}

export const isRequiredRule = (rule: ValidationRule): rule is RequiredValidationRule => {
  return rule.ruleType === ValidationRuleType.RequiredProperty;
};

export const requiredValidatorFactory: ValidatorFnFactory<string> = (_ruleValue): ValidatorFn => {
  return ({ value }) => {
    if (Array.isArray(value) && value.length === 0) {
      return { required: true };
    }

    if (typeof value === 'string') {
      const sanitizedValue = sanitize(value, {
        ALLOWED_ATTR: [],
        ALLOWED_TAGS: [],
      }).trim();

      if (sanitizedValue.length === 0) {
        return { required: true };
      }
    }

    return null;
  };
};

import { cachedServerApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { HTTPError } from 'ky';

export async function getSiteParameters(): Promise<
  components['schemas']['GetSiteParametersSettingsResponse'] | undefined
> {
  try {
    return await cachedServerApi
      .get(`site-parameters`)
      .json<components['schemas']['GetSiteParametersSettingsResponse']>();
  } catch (error) {
    if (error instanceof HTTPError && error.response.status === 404) {
      return undefined;
    }
    return Promise.reject(error);
  }
}

import { useTranslations } from 'next-intl';
import { useFormatter } from 'next-intl';
import { FC } from 'react';

interface PublicReviewPeriodContainerProps {
  fromDate: Date | null;
  toDate: Date | null;
}

export const PublicReviewPeriodContainer: FC<PublicReviewPeriodContainerProps> = ({ fromDate, toDate }) => {
  const format = useFormatter();
  const t = useTranslations('components.publishDateContainer');
  const today = new Date();
  let differenceInDays = 0; // Default to 0 if toDate is not available

  if (toDate) {
    const differenceInTime = toDate.getTime() - today.getTime();
    differenceInDays = Math.ceil(differenceInTime / (1000 * 3600 * 24));
  }

  const hasPublicReviewPeriodStarted = fromDate && fromDate.getTime() >= today.getTime();

  const formattedFromDate = fromDate ? format.dateTime(fromDate, { dateStyle: 'long' }) : null;
  const formattedToDate = toDate ? format.dateTime(toDate, { dateStyle: 'long' }) : null;

  return (
    <div className="border-primary mb-3 flex w-full flex-col border-2 sm:w-1/2 sm:flex-row md:w-2/3">
      {!hasPublicReviewPeriodStarted && differenceInDays >= 0 && (
        <div className="bg-primary flex items-center p-3">
          <span className="text-primary-content font-bold">
            {t(differenceInDays > 1 ? 'daysRemaining' : 'dayRemaining', { days: differenceInDays })}
          </span>
        </div>
      )}
      <div className="flex items-center p-3">
        <h2 className="font-bold">{t('dateRange', { fromDate: formattedFromDate, toDate: formattedToDate })}</h2>
      </div>
    </div>
  );
};

import { components } from '@/iprox-open.interface';
import { saveAs } from 'file-saver';
import { KyInstance } from 'ky/distribution/types/ky';

export function getNodeChildren(
  clientApi: KyInstance,
  nodeId: string
): Promise<components['schemas']['GetPublicDossierFileStructureResponse']> {
  return clientApi
    .get(`Node/${nodeId}/Children`)
    .json<components['schemas']['GetPublicDossierFileStructureResponse']>();
}

export async function downloadZip(
  clientApi: KyInstance,
  dossierId: string,
  body: components['schemas']['PublicNodesDownloadQuery']
): Promise<components['schemas']['PublicNodesDownloadResponse']> {
  const response = await clientApi
    .post(`download/${dossierId}`, { json: body })
    .json<components['schemas']['PublicNodesDownloadResponse']>();

  saveAs(`/public/download-zip/${response.zipId}`);

  return response;
}

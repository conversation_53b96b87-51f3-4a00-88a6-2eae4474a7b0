import type { Meta, StoryObj } from '@storybook/react';

import { MultipleFileUpload } from './multiple-file-upload';

const meta: Meta<typeof MultipleFileUpload> = {
  title: 'iprox-ui/components/multiple-file-upload',
  component: MultipleFileUpload,
  argTypes: {
    onFilesSelect: {
      action: 'onFilesSelect',
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof MultipleFileUpload>;

export const Default: Story = {
  name: 'default',
};

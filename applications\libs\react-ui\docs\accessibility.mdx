import { Meta } from '@storybook/blocks';

<Meta title="a11y - Accessibility" />

# Accessibility

Applications at **iprox.** have to be accessible. Our clients are government agencies and are required by law to

In today's digital age, web applications serve as vital tools for the Dutch Government in delivering services and information to citizens. However, to ensure equal access for all individuals, including those with disabilities, it is crucial that these applications adhere to accessibility standards.

## Legal Considerations:

The Dutch Government has recognized the importance of web accessibility and has implemented laws and regulations to ensure that public sector websites and web applications are accessible to individuals with disabilities. These legal requirements aim to promote equality, non-discrimination, and inclusivity for all citizens.
Under the Dutch Equal Treatment Act and the UN Convention on the Rights of Persons with Disabilities (which the Netherlands has ratified), government agencies are obliged to provide equal access to information and services. Failure to comply with these regulations can result in legal repercussions, including fines and legal challenges.

## WCAG Standards:

The Web Content Accessibility Guidelines (WCAG) are internationally recognized standards developed by the World Wide Web Consortium (W3C) to guide the creation of accessible web content. These guidelines provide a comprehensive framework for developers to ensure their web applications are perceivable, operable, understandable, and robust for individuals with disabilities.
By adhering to WCAG standards, web developers can enhance the accessibility of their applications and align with the legal requirements set forth by the Dutch Government. The current version, WCAG 2.1, consists of three conformance levels: A, AA, and AAA, with AA being the recommended level for most websites and applications.

**iprox.** applications have to be **WCAG2.1 AA** Accessible.

WCAG guidelines cover a wide range of accessibility aspects, including but not limited to:

- Providing alternative text for images and non-text content.
- Ensuring proper heading structure and hierarchical organization of content.
- Using descriptive and meaningful link text.
- Ensuring sufficient color contrast for text and graphical elements.
- Enabling keyboard accessibility for navigation and form input.
- Providing captions and transcripts for multimedia content.
- Ensuring compatibility with assistive technologies, such as screen readers and magnifiers.

Accessibility is not only a legal requirement but also an ethical responsibility for web applications developed for the Dutch Government. Adhering to the WCAG standards not only helps government agencies comply with legal obligations but also provides a more inclusive and equitable online experience for all citizens. By prioritizing accessibility, the Dutch Government can ensure that its web applications are accessible to individuals with disabilities, fostering equal access to information, services, and civic engagement.

## Resources:

[React-aria](https://react-spectrum.adobe.com/react-aria/) is a library that showcases many accessible components.

import cx from 'classnames';
import { useTranslations } from 'next-intl';

import { DescriptionAttributes, ErrorMessageAttributes, LabelAttributes } from '../hooks/use-form-field.hook';
import { FieldDefinition, FieldType, ValueTypes } from '../models/form.models';
import { ValidationMessageTypes } from '../models/validator.models';
import { borderClassname } from '../utils/border-classname';

export interface FormFieldGroupProps {
  children: React.ReactNode;
  definition: FieldDefinition<FieldType, ValueTypes>;
  labelProps: LabelAttributes;
  displayModeRow?: boolean;
  value?: string | boolean;
  descriptionProps?: DescriptionAttributes;
  errorMessage?: string;
  errorMessageProps?: ErrorMessageAttributes;
}

export function FormFieldGroup({
  children,
  definition,
  labelProps,
  displayModeRow = false,
  value,
  descriptionProps,
  errorMessage,
  errorMessageProps,
}: FormFieldGroupProps) {
  const t = useTranslations('components.validation');

  return (
    <fieldset className="min-w-0">
      <legend
        className="font-heading text-heading mb-1 inline-block pt-1 text-lg font-bold leading-none"
        {...labelProps}
      >
        {definition.label}
      </legend>
      {definition.description && (
        <div {...descriptionProps} className="font-text text-base-75 mb-3 text-sm">
          {definition.description}
        </div>
      )}
      <div
        className={cx(
          { 'rounded-input h-input flex flex-wrap items-center gap-x-4 gap-y-2 border p-3': displayModeRow },
          displayModeRow ? borderClassname(errorMessage, value) : null,
          'relative'
        )}
      >
        {children}
      </div>
      {errorMessage && (
        <p className="font-text text-error mt-1.5 text-sm" {...errorMessageProps}>
          {t(errorMessage as ValidationMessageTypes, { fieldName: definition.label })}
        </p>
      )}
    </fieldset>
  );
}

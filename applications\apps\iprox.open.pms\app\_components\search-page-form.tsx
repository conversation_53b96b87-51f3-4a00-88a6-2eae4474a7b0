'use client';

import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { updateSearchPage } from '@/services/page-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { usePageAction } from '@/utils/page-action-utils';
import { pageZoneMapper } from '@/utils/page-zone-mapper';
import { getSortedPageZones, reorderZones } from '@/utils/page-zone-utils';
import {
  Button,
  ButtonGroup,
  FieldType,
  FormBuilder,
  FormSubmitValues,
  PageZone,
  ValidationRuleType,
  showToast,
  useConfirmDialog,
} from '@iprox/iprox-ui';
import { Text } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';

interface SearchPageFormProps {
  page: components['schemas']['SearchPageDto'];
  dossierCategories: components['schemas']['DossierCategoryDto'][];
}

export function SearchPageForm({ page, dossierCategories }: SearchPageFormProps) {
  const t = useTranslations('pages');
  const clientApi = useClientApi();
  const { showDialog } = useConfirmDialog();
  const { getConfirmationMessage, performPageAction, isActionProcessing } = usePageAction();

  const [isSaving, setIsSaving] = useState(false);
  const [updatedPage, setUpdatedPage] = useState(page);
  const [isSaveAndPublish, setIsSaveAndPublish] = useState(false);

  const formFields = useMemo(() => {
    const minimumLength = 1;
    const maximumLength = 60;

    return [
      {
        name: 'label',
        label: t('title'),
        description: t('titleHelperText', { min: minimumLength, max: maximumLength }),
        fieldType: FieldType.Text,
        value: updatedPage.label,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateStringLength,
            ruleValue: { minimumLength, maximumLength },
          },
        ],
      },
      {
        name: 'slug',
        label: t('slug'),
        description: t('slugHelperText'),
        fieldType: FieldType.Text,
        value: updatedPage.slug,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateStringLength,
            ruleValue: { minimumLength, maximumLength },
          },
        ],
      },
      {
        name: 'categories',
        label: t('categories'),
        fieldType: FieldType.Select,
        options: dossierCategories.map((item) => ({ label: item.label, value: item.id })),
        value: updatedPage.categories.map((item) => item.id),
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
        ],
        isMulti: true,
        style: {
          customClasses: 'border-b border-content-lite pb-[72px]',
        },
      },
      {
        name: 'introduction',
        label: t('introductionText'),
        fieldType: FieldType.PageZonesField,
        value: getSortedPageZones(updatedPage.pageZones),
        validationRules: [],
        style: {
          customClasses: 'pt-[40px]',
        },
      },
    ];
  }, [dossierCategories, t, updatedPage.categories, updatedPage.label, updatedPage.pageZones, updatedPage.slug]);

  const handleSave = async (values: FormSubmitValues, disableToast?: boolean) => {
    try {
      setIsSaving(true);

      const response = await updateSearchPage(clientApi, updatedPage.id, {
        id: null,
        label: typeof values.label === 'string' ? values.label.trim() : '',
        slug: typeof values.slug === 'string' ? values.slug.trim() : '',
        categoryIds: Array.isArray(values.categories)
          ? values.categories.map((category) => (typeof category === 'string' ? category : ''))
          : [],
        zones: pageZoneMapper(
          Array.isArray(values.introduction) ? reorderZones(values.introduction as PageZone[]) : []
        ),
      });

      if (!disableToast) {
        showToast(t('pageUpdated'), { type: 'success' });
      }

      setUpdatedPage(response.page);
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = async (values: FormSubmitValues) => {
    if (isSaveAndPublish) {
      handleAction('publish', values);
    } else {
      await handleSave(values);
    }
  };

  const handleAction = async (action: 'delete' | 'publish' | 'unpublish', formValues?: FormSubmitValues) => {
    showDialog({
      message: getConfirmationMessage(action),
      onConfirm: async () => {
        if (action === 'publish' && formValues) {
          await handleSave(formValues, true);
        }
        const pageData = await performPageAction(updatedPage.id, updatedPage.pageType as 'Search', action);
        if (pageData) {
          setUpdatedPage(pageData.page as components['schemas']['SearchPageDto']);
        }
      },
    });
  };

  return (
    <div>
      <div className="mb-[72px]">
        <Text className="font-text mb-1 text-xl font-bold">{t('searchPage')}</Text>
        <Text className="font-text text-6xl font-bold">
          {updatedPage.label}
          <Text className="font-heading ml-12 inline text-sm font-bold">
            {updatedPage.pageState === 'Published' ? t('published') : t('unpublished')}
          </Text>
        </Text>
      </div>

      <FormBuilder
        fields={formFields}
        onSubmit={handleSubmit}
        formId="search-page-form"
        buttons={
          <div className="mb-32 mt-6 flex items-center gap-x-5">
            <Button
              variant="primary"
              type="submit"
              disabled={isSaving}
              onClick={() => {
                setIsSaveAndPublish(false);
              }}
            >
              {t('save')}
            </Button>
            <ButtonGroup
              label={t('action')}
              options={[
                {
                  text: t('delete'),
                  type: 'button',
                  onClick: () => handleAction('delete'),
                },
                {
                  text: t('publish'),
                  type: 'submit',
                  form: 'search-page-form',
                  onClick: () => {
                    setIsSaveAndPublish(true);
                  },
                  disabled: updatedPage.pageState === 'Published',
                },
                {
                  text: t('hide'),
                  type: 'button',
                  onClick: () => handleAction('unpublish'),
                  disabled: updatedPage.pageState === 'Unpublished',
                },
              ]}
              disabled={isActionProcessing}
            />
          </div>
        }
      />
    </div>
  );
}

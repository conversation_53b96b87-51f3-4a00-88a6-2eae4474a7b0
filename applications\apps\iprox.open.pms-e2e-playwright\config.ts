import { Flags } from 'lighthouse';

export const baseUrl = 'https://app-iprox-open-pms-dev.azurewebsites.net';
export const basePortalUrl = 'https://app-iprox-open-portal-dev.azurewebsites.net';
export const API_ENDPOINT = 'https://app-iprox-open-dev.azurewebsites.net';
export const getUrlsEndpoint = '/api/v1/public/page/list';

export const apiLoginUrl = '/api/auth/signin '; // for api auth login
export const loginUrl = baseUrl + apiLoginUrl;

export const root_with_sub_dirs_FileCount = 3;

export const thresholds = {
  accessibility: 100,
};

export const options: Flags = {
  logLevel: 'info',
};

# Iprox.Open

This is the root directory of nx workspace which all the projects related to iprox.open are maintained. Please look into the [Nx Official Documentation](https://nx.dev/getting-started/intro) for more info.

## Folder Structure

The `apps` directory contains following applications and the end to end tests.

1. Portal - Portal for viewing published dossiers for public users
2. PMS - Admin panel to manage the portal and dossiers

View each of these project's Readme.md files to get a better understanding.

The `libs` directory contains the libraries that is shared between those projects.

1. iprox-open-api: Contains the generated typescript interface for api endpoints
2. react-ui: contains the react ui components shared between projects
3. react-ui-i18n: Contains the translations for the react-ui components

## Frequently Used Commands

`yarn nx serve iprox.open.pms` - Starts the PMS application in development mode
`yarn nx serve iprox.open.portal` - Starts the Portal application in development mode
`yarn format` - Format files according to prettier config
`yarn lint` - Check for formatting errors in the projects
`yarn nx storybook react-ui` - Open react-ui component in storybook

**Note**: As a practice run `yarn format` and then `yarn lint` to make sure your code is formatted before pushing changes to git. This will make sure you won't get any lint error during the pipeline lint check.

## Generate Latest API typescript interfaces

If the latest interface changes in api is not already available you can use `yarn generate:openapi` to generate them. See `package.json` in application directory for more commands.

## Specials

### CSP Headers

Inside `middleware.ts` for portal and PMS we add content security policies and other security headers to all requests.

## Understand this workspace

Run `yarn nx graph` to see a diagram of the dependencies of the projects.

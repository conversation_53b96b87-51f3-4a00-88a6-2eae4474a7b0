import { Cog6ToothIcon, DocumentTextIcon, Square3Stack3DIcon, Squares2X2Icon } from '@heroicons/react/24/outline';
import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Default } from './../user-menu/user-menu.stories';
import { ApplicationShell } from './application-shell';
import { ApplicationShellProvider } from './context/application-shell.context';
import { MenuItem } from './models/application-shell.models';
import { NotificationButton } from './parts/notification-button';
import { IproxOpenLogo } from './parts/temp_logo';

const Navigation: MenuItem[] = [
  {
    name: 'Dashboard',
    route: '/Dashboard',
    activeRouteMatch: {
      match: '/Dashboard',
      mode: 'exact',
    },
    icon: <Squares2X2Icon />,
  },
  {
    name: 'Dossiers',
    route: '/Dossiers',
    activeRouteMatch: {
      match: '/Dossiers',
      mode: 'exact',
    },
    icon: <Square3Stack3DIcon />,
  },
  {
    name: 'Parameter',
    route: '/Parameter',
    activeRouteMatch: {
      match: '/Parameter',
      mode: 'exact',
    },
    icon: <DocumentTextIcon />,
  },
];

const BottomNavigation: MenuItem[] = [
  {
    name: 'Settings',
    route: '/Settings',
    icon: <Cog6ToothIcon />,
    activeRouteMatch: {
      match: '/Settings',
      mode: 'exact',
    },
  },
];

const QuickBar = () => (
  <div className="flex flex-row items-center">
    <NotificationButton />
    <Default {...Default.args} />
  </div>
);

const Story: Meta<typeof ApplicationShell> = {
  component: ApplicationShell,
  title: 'components/application-shell',
  argTypes: {
    children: { control: 'text' },
    quickbar: {
      table: {
        disable: true,
      },
    },
    sidePanel: {
      table: {
        disable: true,
      },
    },
    menuItems: {
      table: {
        disable: true,
      },
    },
    bottomMenuItems: {
      table: {
        disable: true,
      },
    },
    loggedIn: { control: 'boolean' },
  },
  args: {
    children: 'Content',
    loggedIn: false,
  },
  parameters: {
    layout: 'fullscreen',
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/Dashboard',
      },
    },
  },
  render: ({ children, loggedIn }) => {
    return (
      <ApplicationShellProvider>
        <ApplicationShell
          menuItems={Navigation}
          quickbar={<QuickBar />}
          bottomMenuItems={BottomNavigation}
          logo={<IproxOpenLogo />}
          sidePanel={<>Notifications</>}
          loggedIn={loggedIn}
        >
          {children}
        </ApplicationShell>
      </ApplicationShellProvider>
    );
  },
};

export default Story;

export const Base: StoryObj = {
  name: 'default',
  args: {},
};

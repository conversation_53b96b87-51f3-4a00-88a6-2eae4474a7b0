import { ValidationRuleType, ValidatorFnFactory } from '../models/validator.models';
import { dateTimeRangeValidatorFactory } from './date-time-range.validator';
import { disallowedCharsValidatorFactory } from './disallowed-chars.validator';
import { integerOnlyValidatorFactory } from './integer.validator';
import { numberRangeValidatorFactory } from './number-range.validator';
import { pastDateValidatorFactory } from './past-date.validator';
import { requiredValidatorFactory } from './required.validator';
import { stringLengthValidator } from './string-length.validator';

export class Validators {
  private static factories = new Map<ValidationRuleType, ValidatorFnFactory<unknown>>();

  static getValidatorFactory(ruleType: ValidationRuleType): ValidatorFnFactory<unknown> | undefined {
    return Validators.factories.get(ruleType);
  }

  static registerValidatorFactory(type: ValidationRuleType, factory: ValidatorFnFactory<unknown>) {
    Validators.factories.set(type, factory);
  }
}

Validators.registerValidatorFactory(
  ValidationRuleType.RequiredProperty,
  requiredValidatorFactory as ValidatorFnFactory<unknown>
);
Validators.registerValidatorFactory(
  ValidationRuleType.ValidateNumberRange,
  numberRangeValidatorFactory as ValidatorFnFactory<unknown>
);
Validators.registerValidatorFactory(
  ValidationRuleType.ValidateStringLength,
  stringLengthValidator as ValidatorFnFactory<unknown>
);
Validators.registerValidatorFactory(
  ValidationRuleType.ValidatePastDate,
  pastDateValidatorFactory as ValidatorFnFactory<unknown>
);
Validators.registerValidatorFactory(
  ValidationRuleType.IntegerOnly,
  integerOnlyValidatorFactory as ValidatorFnFactory<unknown>
);
Validators.registerValidatorFactory(
  ValidationRuleType.ValidateDisallowedCharacters,
  disallowedCharsValidatorFactory as ValidatorFnFactory<unknown>
);
Validators.registerValidatorFactory(
  ValidationRuleType.ValidateDateTimeRange,
  dateTimeRangeValidatorFactory as ValidatorFnFactory<unknown>
);

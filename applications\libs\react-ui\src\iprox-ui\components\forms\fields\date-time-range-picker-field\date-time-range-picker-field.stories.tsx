import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { DateTimeRangePickerField } from './date-time-range-picker-field';

const meta: Meta<typeof DateTimeRangePickerField> = {
  title: 'iprox-ui/forms/fields/date-time-range-picker-field',
  component: DateTimeRangePickerField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof DateTimeRangePickerField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'date-time-range-picker-field',
    fieldType: FieldType.DateTimeRange,
  },
};

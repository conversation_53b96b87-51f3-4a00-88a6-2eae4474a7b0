import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';

export async function getDossierCategories(): Promise<components['schemas']['GetAllDossierCategoriesResponse']> {
  try {
    const response = await serverApi
      .get('dossier-category')
      .json<components['schemas']['GetAllDossierCategoriesResponse']>();

    return response;
  } catch (error) {
    return Promise.reject(error);
  }
}

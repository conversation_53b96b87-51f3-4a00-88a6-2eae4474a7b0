import { components } from '@/iprox-open.interface';
import { ValidationRuleType } from '@iprox/iprox-ui';

export const getValidationRulesMapping = (
  isRequired: boolean,
  validationRules: components['schemas']['DynamicFieldValidationRuleDto'][] | null
) => {
  if (!validationRules) {
    return [];
  }

  // Check if the RequiredProperty rule already exists
  const requiredRuleExists = validationRules.some((rule) => rule.ruleType === ValidationRuleType.RequiredProperty);

  // Map the rules to the desired format
  const mappedValidationRules = validationRules.map((rule) => ({
    ruleType: rule.ruleType as unknown as ValidationRuleType,
    ruleValue: rule?.fieldRuleValue,
  }));

  if (!requiredRuleExists && isRequired) {
    mappedValidationRules.push({
      ruleType: ValidationRuleType.RequiredProperty,
      ruleValue: {},
    });
  }

  return mappedValidationRules;
};

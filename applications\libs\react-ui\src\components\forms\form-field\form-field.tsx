import { useTranslations } from 'next-intl';

import { DescriptionAttributes, ErrorMessageAttributes, LabelAttributes } from '../hooks/use-form-field.hook';
import { FieldDefinition, FieldType, ValueTypes } from '../models/form.models';
import { ValidationMessageTypes } from '../models/validator.models';
import { useValidationRuleValues } from '../utils/get-validation-rule-values.hook';

export interface FormFieldProps {
  children: React.ReactNode;
  definition: FieldDefinition<FieldType, ValueTypes>;
  labelProps: LabelAttributes;
  descriptionProps?: DescriptionAttributes;
  errorMessage?: string;
  errorMessageProps?: ErrorMessageAttributes;
}

export function FormField({
  children,
  definition,
  labelProps,
  descriptionProps,
  errorMessage,
  errorMessageProps,
}: FormFieldProps) {
  const t = useTranslations('components.validation');

  const { min, max } = useValidationRuleValues(definition.validationRules, definition.fieldType);

  return (
    <>
      <label {...labelProps} className="font-heading text-heading mb-1 inline-block text-lg font-bold leading-none">
        {definition.label}
      </label>
      {definition.description && (
        <div {...descriptionProps} className="font-text text-base-75 text-sm">
          {definition.description}
        </div>
      )}
      <div className="relative mt-3">{children}</div>
      {errorMessage && (
        <p className="font-text text-error mt-1.5 text-sm" {...errorMessageProps}>
          {t(errorMessage as ValidationMessageTypes, {
            fieldName: definition.label,
            min,
            max,
          })}
        </p>
      )}
    </>
  );
}

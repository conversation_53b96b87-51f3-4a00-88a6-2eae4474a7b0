import { ChevronDownIcon, ChevronUpIcon, XCircleIcon } from '@heroicons/react/24/outline';
import cx from 'classnames';
import { useId, useState } from 'react';
import Select, {
  ActionMeta,
  ControlProps,
  DropdownIndicatorProps,
  MultiValue,
  MultiValueRemoveProps,
  OptionProps,
  SingleValue,
  components,
} from 'react-select';

import { useUuid } from '../../../hooks/uuid.hook';
import './custom-select.scss';

interface SelectControllerProps extends ControlProps {
  hasError: boolean;
  filledIn: boolean;
  isMenuOpen?: boolean;
  ariaDescribedbyId?: string;
}

export interface CustomSelectOption {
  value: string;
  label: string;
}

export interface CustomSelectProps {
  id?: string;
  name: string;
  value?: string | string[];
  ariaDescribedbyId?: string;
  options?: CustomSelectOption[];
  errorMessage?: string;
  onChange?: (value: SingleValue<CustomSelectOption> | MultiValue<CustomSelectOption>) => void;
  onBlur?: () => void;
  isMulti?: boolean;
  onClose: (value: SingleValue<CustomSelectOption> | MultiValue<CustomSelectOption>) => void;
}

const SelectController = ({ children, ...props }: SelectControllerProps) => {
  const getBorderColor = () => {
    if (props.hasError) {
      return '!border-error hover:!shadow-input-error !shadow-none';
    }
    if (props.filledIn) {
      return '!border-highlight !shadow-none';
    }
    if (props.isMenuOpen) {
      return '!border-highlight !shadow-none';
    }
    if (props.isFocused) {
      return 'focus:!border-highlight focus:!outline-2 focus:!outline focus:!outline-blue-600 focus:!shadow-none';
    }

    return '!border-content-extra-lite hover:!border-base-75 focus:!border-base-75 focus:!outline-2 focus:!outline focus:!outline-blue-600';
  };

  return (
    <components.Control {...props} className={cx('react-select-container', getBorderColor())}>
      {children}
    </components.Control>
  );
};

const Option = ({ children, ...props }: OptionProps) => {
  return (
    <components.Option {...props}>
      <div
        className={cx(
          'outl text-body font-text rounded-input flex cursor-pointer flex-row items-center px-4 py-2 font-medium',
          {
            '!text-base-00 !bg-highlight': props.isFocused,
          },
          { 'bg-light-grey text-body': props.isSelected }
        )}
      >
        {props.isMulti && (
          <input
            type="checkbox"
            id={props.label}
            className="checked:bg-highlight hover:cheked:!bg-scondary hover:border-highlight relative z-10 mr-10 h-5 w-5 cursor-pointer rounded-md align-top focus:ring-0 focus:ring-offset-0"
            value={props.label}
            checked={props.isSelected}
            readOnly
          />
        )}
        {children}
      </div>
    </components.Option>
  );
};

const MultiValueRemove = ({ ...props }: MultiValueRemoveProps) => (
  <components.MultiValueRemove {...props}>
    <div className="flex flex-row items-center justify-between">
      <XCircleIcon className="h-5 w-5" strokeWidth={2} fill="#fff" />
    </div>
  </components.MultiValueRemove>
);

const DropdownIndicator = ({ isFocused, ...props }: DropdownIndicatorProps) => (
  <components.DropdownIndicator isFocused={isFocused} {...props} className="!p-0">
    {isFocused ? (
      <ChevronUpIcon className={cx('h-5 w-5', { 'color-highlight': isFocused })} strokeWidth={3} />
    ) : (
      <ChevronDownIcon className={cx('h-5 w-5', { 'color-highlight': isFocused })} strokeWidth={3} />
    )}
  </components.DropdownIndicator>
);

export function CustomSelect({
  id,
  name,
  value,
  options,
  ariaDescribedbyId,
  errorMessage,
  onChange,
  onBlur,
  isMulti,
  onClose,
}: CustomSelectProps) {
  const inputId = useUuid(id);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const instanceId = useId();
  const [selectedValues, setSelectedValues] = useState(
    isMulti
      ? options?.filter((option) => value?.includes(option.value))
      : options?.find((option) => option.value === value)
  );

  const handleOnClose = (newValue: unknown) => {
    if (isMulti) {
      onClose?.(newValue as SingleValue<CustomSelectOption> | MultiValue<CustomSelectOption>);
    }
    setIsMenuOpen(false);
  };

  const handleChange = (newValue: unknown, _actionMeta: ActionMeta<unknown>) => {
    setSelectedValues(newValue as CustomSelectOption | CustomSelectOption);
    onChange?.(newValue as SingleValue<CustomSelectOption> | MultiValue<CustomSelectOption>);
  };

  return (
    <div key={instanceId}>
      <Select
        key={`${inputId}-${JSON.stringify(value)}`} /* this is need to reset the select field on restForm */
        inputId={inputId}
        instanceId={instanceId}
        hideSelectedOptions={false}
        closeMenuOnSelect={!isMulti}
        name={name}
        aria-label={ariaDescribedbyId}
        aria-labelledby={ariaDescribedbyId}
        options={options}
        tabSelectsValue={false}
        value={selectedValues as unknown}
        components={{
          Control: (props) => (
            <SelectController
              hasError={errorMessage ? true : false}
              filledIn={!!value}
              isMenuOpen={isMenuOpen}
              {...props}
              isFocused={isMenuOpen}
            />
          ),
          Option: (props) => <Option {...props} isMulti={isMulti ?? false} />,
          MultiValueRemove,
          DropdownIndicator: (props) => <DropdownIndicator {...props} isFocused={isMenuOpen} />,
        }}
        onFocus={() => setIsMenuOpen(true)}
        onBlur={onBlur}
        onChange={handleChange}
        classNamePrefix="custom-select"
        menuShouldScrollIntoView
        menuIsOpen={isMenuOpen}
        onMenuOpen={() => setIsMenuOpen(true)}
        onMenuClose={() => {
          handleOnClose(selectedValues);
        }}
        placeholder={null}
        isMulti={isMulti}
      />
    </div>
  );
}

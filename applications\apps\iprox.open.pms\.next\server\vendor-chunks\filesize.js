"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/filesize";
exports.ids = ["vendor-chunks/filesize"];
exports.modules = {

/***/ "(ssr)/../../node_modules/filesize/dist/filesize.esm.js":
/*!********************************************************!*\
  !*** ../../node_modules/filesize/dist/filesize.esm.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filesize: () => (/* binding */ filesize),\n/* harmony export */   partial: () => (/* binding */ partial)\n/* harmony export */ });\n/**\n * filesize\n *\n * @copyright 2023 Jason Mulligan <<EMAIL>>\n * @license BSD-3-Clause\n * @version 10.0.8\n */\nconst ARRAY = \"array\";\r\nconst BIT = \"bit\";\r\nconst BITS = \"bits\";\r\nconst BYTE = \"byte\";\r\nconst BYTES = \"bytes\";\r\nconst EMPTY = \"\";\r\nconst EXPONENT = \"exponent\";\r\nconst FUNCTION = \"function\";\r\nconst IEC = \"iec\";\r\nconst INVALID_NUMBER = \"Invalid number\";\r\nconst INVALID_ROUND = \"Invalid rounding method\";\r\nconst JEDEC = \"jedec\";\r\nconst OBJECT = \"object\";\r\nconst PERIOD = \".\";\r\nconst ROUND = \"round\";\r\nconst S = \"s\";\r\nconst SI_KBIT = \"kbit\";\r\nconst SI_KBYTE = \"kB\";\r\nconst SPACE = \" \";\r\nconst STRING = \"string\";\r\nconst ZERO = \"0\";\r\nconst STRINGS = {\r\n\tsymbol: {\r\n\t\tiec: {\r\n\t\t\tbits: [\"bit\", \"Kibit\", \"Mibit\", \"Gibit\", \"Tibit\", \"Pibit\", \"Eibit\", \"Zibit\", \"Yibit\"],\r\n\t\t\tbytes: [\"B\", \"KiB\", \"MiB\", \"GiB\", \"TiB\", \"PiB\", \"EiB\", \"ZiB\", \"YiB\"]\r\n\t\t},\r\n\t\tjedec: {\r\n\t\t\tbits: [\"bit\", \"Kbit\", \"Mbit\", \"Gbit\", \"Tbit\", \"Pbit\", \"Ebit\", \"Zbit\", \"Ybit\"],\r\n\t\t\tbytes: [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\", \"ZB\", \"YB\"]\r\n\t\t}\r\n\t},\r\n\tfullform: {\r\n\t\tiec: [\"\", \"kibi\", \"mebi\", \"gibi\", \"tebi\", \"pebi\", \"exbi\", \"zebi\", \"yobi\"],\r\n\t\tjedec: [\"\", \"kilo\", \"mega\", \"giga\", \"tera\", \"peta\", \"exa\", \"zetta\", \"yotta\"]\r\n\t}\r\n};function filesize (arg, {\r\n\tbits = false,\r\n\tpad = false,\r\n\tbase = -1,\r\n\tround = 2,\r\n\tlocale = EMPTY,\r\n\tlocaleOptions = {},\r\n\tseparator = EMPTY,\r\n\tspacer = SPACE,\r\n\tsymbols = {},\r\n\tstandard = EMPTY,\r\n\toutput = STRING,\r\n\tfullform = false,\r\n\tfullforms = [],\r\n\texponent = -1,\r\n\troundingMethod = ROUND,\r\n\tprecision = 0\r\n} = {}) {\r\n\tlet e = exponent,\r\n\t\tnum = Number(arg),\r\n\t\tresult = [],\r\n\t\tval = 0,\r\n\t\tu = EMPTY;\r\n\r\n\t// Sync base & standard\r\n\tif (base === -1 && standard.length === 0) {\r\n\t\tbase = 10;\r\n\t\tstandard = JEDEC;\r\n\t} else if (base === -1 && standard.length > 0) {\r\n\t\tstandard = standard === IEC ? IEC : JEDEC;\r\n\t\tbase = standard === IEC ? 2 : 10;\r\n\t} else {\r\n\t\tbase = base === 2 ? 2 : 10;\r\n\t\tstandard = base === 10 ? JEDEC : standard === JEDEC ? JEDEC : IEC;\r\n\t}\r\n\r\n\tconst ceil = base === 10 ? 1000 : 1024,\r\n\t\tfull = fullform === true,\r\n\t\tneg = num < 0,\r\n\t\troundingFunc = Math[roundingMethod];\r\n\r\n\tif (typeof arg !== \"bigint\" && isNaN(arg)) {\r\n\t\tthrow new TypeError(INVALID_NUMBER);\r\n\t}\r\n\r\n\tif (typeof roundingFunc !== FUNCTION) {\r\n\t\tthrow new TypeError(INVALID_ROUND);\r\n\t}\r\n\r\n\t// Flipping a negative number to determine the size\r\n\tif (neg) {\r\n\t\tnum = -num;\r\n\t}\r\n\r\n\t// Determining the exponent\r\n\tif (e === -1 || isNaN(e)) {\r\n\t\te = Math.floor(Math.log(num) / Math.log(ceil));\r\n\r\n\t\tif (e < 0) {\r\n\t\t\te = 0;\r\n\t\t}\r\n\t}\r\n\r\n\t// Exceeding supported length, time to reduce & multiply\r\n\tif (e > 8) {\r\n\t\tif (precision > 0) {\r\n\t\t\tprecision += 8 - e;\r\n\t\t}\r\n\r\n\t\te = 8;\r\n\t}\r\n\r\n\tif (output === EXPONENT) {\r\n\t\treturn e;\r\n\t}\r\n\r\n\t// Zero is now a special case because bytes divide by 1\r\n\tif (num === 0) {\r\n\t\tresult[0] = 0;\r\n\t\tu = result[1] = STRINGS.symbol[standard][bits ? BITS : BYTES][e];\r\n\t} else {\r\n\t\tval = num / (base === 2 ? Math.pow(2, e * 10) : Math.pow(1000, e));\r\n\r\n\t\tif (bits) {\r\n\t\t\tval = val * 8;\r\n\r\n\t\t\tif (val >= ceil && e < 8) {\r\n\t\t\t\tval = val / ceil;\r\n\t\t\t\te++;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst p = Math.pow(10, e > 0 ? round : 0);\r\n\t\tresult[0] = roundingFunc(val * p) / p;\r\n\r\n\t\tif (result[0] === ceil && e < 8 && exponent === -1) {\r\n\t\t\tresult[0] = 1;\r\n\t\t\te++;\r\n\t\t}\r\n\r\n\t\tu = result[1] = base === 10 && e === 1 ? bits ? SI_KBIT : SI_KBYTE : STRINGS.symbol[standard][bits ? BITS : BYTES][e];\r\n\t}\r\n\r\n\t// Decorating a 'diff'\r\n\tif (neg) {\r\n\t\tresult[0] = -result[0];\r\n\t}\r\n\r\n\t// Setting optional precision\r\n\tif (precision > 0) {\r\n\t\tresult[0] = result[0].toPrecision(precision);\r\n\t}\r\n\r\n\t// Applying custom symbol\r\n\tresult[1] = symbols[result[1]] || result[1];\r\n\r\n\tif (locale === true) {\r\n\t\tresult[0] = result[0].toLocaleString();\r\n\t} else if (locale.length > 0) {\r\n\t\tresult[0] = result[0].toLocaleString(locale, localeOptions);\r\n\t} else if (separator.length > 0) {\r\n\t\tresult[0] = result[0].toString().replace(PERIOD, separator);\r\n\t}\r\n\r\n\tif (pad && Number.isInteger(result[0]) === false && round > 0) {\r\n\t\tconst x = separator || PERIOD,\r\n\t\t\ttmp = result[0].toString().split(x),\r\n\t\t\ts = tmp[1] || EMPTY,\r\n\t\t\tl = s.length,\r\n\t\t\tn = round - l;\r\n\r\n\t\tresult[0] = `${tmp[0]}${x}${s.padEnd(l + n, ZERO)}`;\r\n\t}\r\n\r\n\tif (full) {\r\n\t\tresult[1] = fullforms[e] ? fullforms[e] : STRINGS.fullform[standard][e] + (bits ? BIT : BYTE) + (result[0] === 1 ? EMPTY : S);\r\n\t}\r\n\r\n\t// Returning Array, Object, or String (default)\r\n\treturn output === ARRAY ? result : output === OBJECT ? {\r\n\t\tvalue: result[0],\r\n\t\tsymbol: result[1],\r\n\t\texponent: e,\r\n\t\tunit: u\r\n\t} : result.join(spacer);\r\n}\r\n\r\n// Partial application for functional programming\r\nfunction partial ({\r\n\tbits = false,\r\n\tpad = false,\r\n\tbase = -1,\r\n\tround = 2,\r\n\tlocale = EMPTY,\r\n\tlocaleOptions = {},\r\n\tseparator = EMPTY,\r\n\tspacer = SPACE,\r\n\tsymbols = {},\r\n\tstandard = EMPTY,\r\n\toutput = STRING,\r\n\tfullform = false,\r\n\tfullforms = [],\r\n\texponent = -1,\r\n\troundingMethod = ROUND,\r\n\tprecision = 0\r\n} = {}) {\r\n\treturn arg => filesize(arg, {\r\n\t\tbits,\r\n\t\tpad,\r\n\t\tbase,\r\n\t\tround,\r\n\t\tlocale,\r\n\t\tlocaleOptions,\r\n\t\tseparator,\r\n\t\tspacer,\r\n\t\tsymbols,\r\n\t\tstandard,\r\n\t\toutput,\r\n\t\tfullform,\r\n\t\tfullforms,\r\n\t\texponent,\r\n\t\troundingMethod,\r\n\t\tprecision\r\n\t});\r\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/filesize/dist/filesize.esm.js\n");

/***/ })

};
;
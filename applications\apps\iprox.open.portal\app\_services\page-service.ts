import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { HTTPError } from 'ky';

export async function getPageList(): Promise<components['schemas']['GetPublishedPageListResponse']> {
  try {
    return await serverApi.get(`page/list`).json<components['schemas']['GetPublishedPageListResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function getPage(
  slug: string
): Promise<components['schemas']['GetPublishedPageBySlugResponse'] | undefined> {
  try {
    return await serverApi.get(`page/${slug}`).json<components['schemas']['GetPublishedPageBySlugResponse']>();
  } catch (error) {
    if (error instanceof HTTPError && error.response.status === 400) {
      return undefined;
    }
    return Promise.reject(error);
  }
}

export async function getSearchPages(dossierId: string): Promise<components['schemas']['SearchPageListItemDto'][]> {
  try {
    return await serverApi
      .get(`dossier/${dossierId}/search-pages`)
      .json<components['schemas']['SearchPageListItemDto'][]>();
  } catch (error) {
    return Promise.reject(error);
  }
}

'use client';

import { useClient<PERSON><PERSON> } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { updateNavigation } from '@/services/settings-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { CheckIcon, EyeIcon, EyeSlashIcon, FolderIcon, PencilIcon } from '@heroicons/react/24/outline';
import { Button, FieldType, Text, TextField, showToast } from '@iprox/iprox-ui';
import { Form, Formik } from 'formik';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { RefObject, useEffect, useMemo, useRef, useState } from 'react';
import { NodeApi, NodeRendererProps, Tree, TreeApi, useSimpleTree } from 'react-arborist';

import { PageHeader } from '@/components/page-header';

import './site-navigation-editor.scss';

export interface NavigationPage {
  id: string;
  title: string;
  slug: string;
  pageState: 'Unpublished' | 'Published';
}

export interface TreeBaseItem {
  readonly?: boolean;
  id: string;
  name: string;
  children: TreePageItem[] | null;
}

/** This is used for TreeItems, can be pages assigned to the navigation structure, but also pages not yet added to it. */
export interface TreePageItem extends TreeBaseItem {
  page: NavigationPage | null;
}

let globalTree: TreeApi<TreePageItem | TreeBaseItem> | null | undefined;

export interface SiteNavigationEditorProps {
  navigation: components['schemas']['NavigationItemDto'][];
  pages: components['schemas']['NavigationPageDto'][];
}

const ROW_HEIGHT = 112;

export const SiteNavigationEditor = ({ navigation, pages }: SiteNavigationEditorProps) => {
  const clientApi = useClientApi();
  const t = useTranslations('siteNavigation');

  const [loading, setLoading] = useState(false);

  const treeRef = (tree?: TreeApi<TreePageItem | TreeBaseItem> | null) => {
    globalTree = tree;
  };

  /** This object will be mutated by the react-arborist tree component. */
  const initialData = useMemo(() => {
    const unusedPagesHolder: TreeBaseItem = {
      readonly: true,
      id: 'unusedPages',
      name: t('unusedPages'),
      children: pages.map((page) => ({ id: page.id, name: page.title, page, children: null })),
    };

    return [...navigation, unusedPagesHolder];
  }, [navigation, pages, t]);

  const [data, controller] = useSimpleTree<TreePageItem | TreeBaseItem>(initialData);

  const disableDrop = (_args: {
    parentNode: NodeApi<TreeBaseItem>;
    dragNodes: NodeApi<TreeBaseItem>[];
    index: number;
  }): boolean => {
    if (_args.dragNodes[0].children?.length && !_args.parentNode.isRoot) {
      return true;
    }

    if (_args.parentNode.isRoot && _args.index === _args.parentNode.children?.length) {
      return true;
    }

    return false;
  };

  const disableDrag = (data: TreeBaseItem): boolean => {
    return !!data.readonly;
  };

  const save = () => {
    if (!globalTree) return;
    const navigation = data.filter((item): item is TreePageItem => item.id !== 'unusedPages');

    setLoading(true);
    updateNavigation(clientApi, {
      navigation,
    })
      .then(() => {
        showToast(t('navigationStructureUpdated'), { type: 'success' });
      })
      .catch(async (error) => {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      })
      .finally(() => setLoading(false));
  };

  return (
    <>
      <PageHeader title={t('navigation')} />
      <div>
        <Tree
          data={data}
          {...controller}
          width={'100%'}
          height={ROW_HEIGHT * 6}
          ref={treeRef}
          disableEdit={(data) => !!data.readonly}
          disableDrop={disableDrop}
          disableDrag={disableDrag}
          rowHeight={ROW_HEIGHT}
          onCreate={() => {
            return null;
          }}
          rowClassName="ipx-tree-row"
        >
          {TreeNode}
        </Tree>
      </div>
      <div>
        <Button type="button" variant="primary" className="mt-10" onClick={() => save()} disabled={loading}>
          {t('save')}
        </Button>
      </div>
    </>
  );
};

const TreeNode = ({ node, style, dragHandle }: NodeRendererProps<TreeBaseItem | TreePageItem>) => {
  const t = useTranslations('siteNavigation');
  const router = useRouter();

  const isUnusedPage = node.parent?.id === 'unusedPages';

  if (isUnusedPage) {
    node.children = null;
  } else if (node.parent?.data.name) {
    node.children = null;
  } else if (!node.parent?.data.name && !node.children) {
    node.children = [];
  }

  if ('page' in node.data) {
    return (
      <div
        ref={dragHandle}
        style={{
          ...style,
        }}
      >
        <div
          className="border-base-25 flex flex-row items-center border-b pr-2.5"
          style={{
            height: ROW_HEIGHT,
          }}
        >
          {isUnusedPage ? (
            <FolderIcon className="text-unused mr-7 h-6 w-6" />
          ) : node.data.page?.pageState === 'Published' ? (
            <EyeIcon className="text-published mr-7 h-6 w-6" />
          ) : (
            <EyeSlashIcon className="text-not-published mr-7 h-6 w-6" />
          )}

          <div className="flex w-3/5 flex-col">
            {node.isEditing ? (
              <TreeInput node={node} />
            ) : (
              <div className="flex flex-row items-center gap-x-2.5">
                <span
                  className="font-heading text-primary-content truncate text-lg font-bold"
                  style={{
                    maxWidth: '95%',
                  }}
                >
                  {node.data.name}
                </span>
                {!isUnusedPage && (
                  <PencilIcon
                    className="text-base-100 h-4 w-4 cursor-pointer"
                    onClick={() => {
                      node.edit();
                    }}
                  />
                )}
              </div>
            )}
            {!node.isEditing ? (
              <Text className="font-heading text-primary-content truncate text-sm">{node.data.page?.title}</Text>
            ) : null}
          </div>

          <Button
            variant="secondary"
            className="ml-auto"
            onClick={() => {
              router.push(`page/${node.data.id}`);
            }}
          >
            {t('editPage')}
          </Button>
        </div>
      </div>
    );
  }

  return <Text className="font-heading text-base-100 text-2xl font-bold">{t('outsideNavigation')}</Text>;
};

function useOutsideClick<T extends HTMLElement>(ref: RefObject<T>, callback: () => void): void {
  useEffect(() => {
    function handleClickOutside(event: MouseEvent): void {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        callback();
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, callback]);
}

const TreeInput = ({ node }: { node: NodeApi<TreeBaseItem | TreePageItem> }) => {
  const wrapperRef = useRef(null);

  useOutsideClick(wrapperRef, () => {
    node.reset();
  });

  return (
    <Formik
      initialValues={{
        navigationTitle: node.data.name ?? ('page' in node.data ? node.data.page?.title : ''),
      }}
      onSubmit={(values) => {
        node.data.name = values.navigationTitle;
        node.submit(values.navigationTitle);
      }}
    >
      <Form ref={wrapperRef} className="relative flex w-fit flex-row items-center">
        <div className="w-96">
          <TextField name="navigationTitle" label="" fieldType={FieldType.Text} validationRules={[]} />
        </div>
        <button
          type="submit"
          className="bg-light-grey absolute right-4 flex h-6 w-6 items-center justify-center rounded"
        >
          <CheckIcon className="h-4 w-4" />
        </button>
      </Form>
    </Formik>
  );
};

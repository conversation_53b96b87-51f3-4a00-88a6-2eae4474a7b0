import { faker } from '@faker-js/faker';

import * as http from '../../enums/httpEnums';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';
import { PortalHelpers } from '../../helpers/portalHelpers';

describe('Latest Published dossier test', () => {
  let dossierCategoryId: string | null;
  let bearerToken: any;
  let dossierTitle: string;
  let dossierId: string;
  let dossierResponse: any;

  beforeAll(async () => {
    dossierTitle = faker.lorem.word();
    bearerToken = await loginAndGetToken();

    dossierCategoryId = await DossierHelpersNew.getRandomCategoryUuid(false, await bearerToken);
    dossierResponse = await DossierHelpersNew.createDossier(dossierTitle, bearerToken, dossierCategoryId);
    dossierId = await dossierResponse.body.dossier.dossierId;
    await DossierHelpersNew.publishDossier(dossierId, bearerToken);
  });

  it('PORTAL - should get latest published dossier', async () => {
    const response: any = await PortalHelpers.getPublicDossier(dossierId);

    expect(response.status).toBe(http.StatusCode.OK_200);
    expect(response.body.dossier.version).toBe(1);

    await DossierHelpersNew.unpublishDossier(dossierId, bearerToken);
    await DossierHelpersNew.updateDossier(dossierId, bearerToken);
    await DossierHelpersNew.publishDossier(dossierId, bearerToken);

    const updatedResponse: any = await PortalHelpers.getPublicDossier(dossierId);

    expect(updatedResponse.status).toBe(http.StatusCode.OK_200);
    expect(updatedResponse.body.dossier.version).toBe(2);
  });
});

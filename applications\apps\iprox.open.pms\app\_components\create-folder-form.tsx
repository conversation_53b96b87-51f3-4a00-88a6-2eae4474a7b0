import { Button, FieldType, FormBuilder, FormSubmitValues, ValidationRuleType } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import React, { useMemo } from 'react';

interface CreateFolderFormProps {
  createLoading: boolean;
  onSubmit: (folderName: string) => void;
  onCancel: () => void;
}

export function CreateFolderForm({ createLoading, onSubmit, onCancel }: CreateFolderFormProps) {
  const t = useTranslations('dossier');

  const formFields = useMemo(() => {
    return [
      {
        name: 'folderName',
        label: t('folderName'),
        description: t('folderNameHelperText'),
        fieldType: FieldType.Text,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateStringLength,
            ruleValue: { minimumLength: 1, maximumLength: 256 },
          },
          {
            ruleType: ValidationRuleType.ValidateDisallowedCharacters,
            ruleValue: {
              regExp: new RegExp(
                '[<>:"/\\\\|?*]' + '|' + '^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\\.|$)' + '|' + '[ .]$'
              ),
            },
          },
        ],
      },
    ];
  }, [t]);

  return (
    <div>
      <h3 className="font-heading text-heading mb-5 block text-2xl font-bold">{t('createNewFolder')}</h3>
      <FormBuilder
        fields={formFields}
        onSubmit={(values: FormSubmitValues) => {
          const folderName = typeof values.folderName === 'string' ? values.folderName : '';
          onSubmit(folderName);
        }}
        buttons={
          <div className="mt-8 flex flex-row gap-8">
            <Button variant="primary" type="submit" disabled={createLoading}>
              {t('createFolder')}
            </Button>
            <Button variant="secondary" onClick={onCancel}>
              {t('cancel')}
            </Button>
          </div>
        }
      />
    </div>
  );
}

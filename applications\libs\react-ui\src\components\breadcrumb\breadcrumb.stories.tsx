import { Meta, StoryObj } from '@storybook/react';

import { Breadcrumb } from './breadcrumb';

const meta: Meta<typeof Breadcrumb> = {
  title: 'components/breadcrumb',
  component: Breadcrumb,
};

export default meta;

type Story = StoryObj<typeof Breadcrumb>;

export const Default: Story = {
  name: 'default',
  parameters: {
    nextjs: {
      appDirectory: true,
      navigation: {
        pathname: '/products/product-1',
      },
    },
  },
  args: {
    items: [
      { label: 'Products', slug: '/products' },
      { label: 'Product 1', slug: '/products/product-1' },
    ],
  },
};

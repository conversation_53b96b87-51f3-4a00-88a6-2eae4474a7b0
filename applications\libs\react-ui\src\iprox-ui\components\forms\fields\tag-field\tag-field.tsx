import { XMarkIcon } from '@heroicons/react/24/outline';
import cx from 'classnames';
import { useField } from 'formik';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { IconButton } from '../../../icon-button/icon-button';
import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { StringListFieldDefinition } from '../../models/form.models';
import { borderClassname } from '../../utils/border-classname';

export function TagField(props: StringListFieldDefinition) {
  const t = useTranslations('components.multipeTextField');
  const [field, meta, helpers] = useField<string[]>(props);
  const [value, setValue] = useState('');
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'text');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddClick();
    }
  };

  const handleAddClick = () => {
    if (value && value.trim() !== '') {
      const newChip = value.trim();

      helpers.setValue([...field.value, newChip]);
      setValue('');
    }
  };

  const handleRemoveClick = (index: number) => {
    const updatedChips: string[] = [...field.value];
    updatedChips.splice(index, 1);

    helpers.setValue([...updatedChips]);
  };

  return (
    <FormField definition={props} errorMessage={meta.touched ? meta.error : undefined} {...formControlProps}>
      <div className="relative">
        <input {...field} className="invisible absolute inset-0 opacity-0" />
        <input
          {...inputProps}
          value={value}
          onChange={(event) => setValue(event.target.value)}
          onKeyDown={handleKeyDown}
          className={cx(
            'rounded-input font-text text-body h-input w-100 w-full p-3 pr-20 text-sm',
            borderClassname(meta.touched ? meta.error : undefined, field.value)
          )}
        />
        <button
          type="button"
          onClick={handleAddClick}
          className={`text-base-00 absolute bottom-0 right-0 top-0 m-1 flex items-center rounded-lg px-5 ${
            value.trim() === '' ? 'bg-content-lite' : 'bg-highlight'
          }`}
          disabled={value.trim() === ''}
        >
          {t('add')}
        </button>
      </div>
      <div className="flex flex-wrap">
        {field.value.map((chip: string, index: number) => (
          <div key={`${chip}-${index}`} className="bg-base-10 mr-1 mt-2 flex max-w-full rounded-lg p-2">
            <span className="overflow-hidden overflow-ellipsis whitespace-nowrap leading-snug">{chip}</span>
            <IconButton
              onClick={() => handleRemoveClick(index)}
              aria-label={t('removeItem', { item: chip })}
              className="ml-2 leading-normal"
            >
              <XMarkIcon className="text-body h-4 w-4" />
            </IconButton>
          </div>
        ))}
      </div>
    </FormField>
  );
}

//@ts-check

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');

const nextIntl = 'next-intl/plugin';
const i18nPath = './i18n.ts';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const withNextIntl = require(nextIntl)(i18nPath);

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  experimental: {
    esmExternals: true,
  },
  typescript: {
    tsconfigPath: process.env.NODE_ENV === 'production' ? './tsconfig.prod.json' : './tsconfig.json',
  },
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  output: 'standalone',
  images: {
    domains: [process.env.IPROX_OPEN_API_URL ? new URL(process.env.IPROX_OPEN_API_URL).hostname : '127.0.0.1'],
  },
  async rewrites() {
    return [
      {
        source: '/robots.txt',
        destination: '/app/robots.txt',
      },
      {
        source: '/.well-known/security.txt',
        destination: '/app/.well-known/security.txt',
      },
    ];
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
  withNextIntl,
];

module.exports = composePlugins(...plugins)(nextConfig);

import { Text } from '@iprox/react-ui';
import { useFormatter, useTranslations } from 'next-intl';
import Link from 'next/link';

import { SortableColumn } from './sortable-column';

interface Dossier {
  id: string;
  dossierId: string;
  name: string;
  type?: string;
  modifiedDate?: Date;
  modifiedBy?: string;
  publishedDate?: Date;
  publishFromDate?: Date;
  author?: string;
  dossierPublishedDate?: Date;
}

interface DossiersListProps {
  dossiers: Dossier[];
}

export default function DossiersList({ dossiers }: DossiersListProps) {
  const t = useTranslations('dossiersList');
  const format = useFormatter();

  if (dossiers.length === 0) {
    return null;
  }

  const getDateString = (date: Date | undefined): string => {
    if (date) {
      return format.dateTime(date, { dateStyle: 'short' });
    }

    return '-';
  };

  return (
    <div className="border-base-25 mt-5 overflow-hidden border">
      <table className="w-full">
        <thead>
          <tr className="bg-base-10 border-base-25 grid grid-cols-8 gap-x-4 border-b py-5">
            <th className="col-span-2 pl-6 text-left">
              <Text className="font-text text-heading text-sm font-bold">{t('headerName')}</Text>
            </th>
            <th className="text-left">
              <Text className="font-text text-heading text-sm font-bold">{t('headerType')}</Text>
            </th>
            <th className="text-left">
              <SortableColumn sortField="modified.dateTime">
                <Text className="font-text text-heading text-sm font-bold">{t('headerModified')}</Text>
              </SortableColumn>
            </th>
            <th className="text-left">
              <Text className="font-text text-heading text-sm font-bold">{t('headerBy')}</Text>
            </th>
            <th className="text-left">
              <SortableColumn sortField="dossierPublished.dateTime">
                <Text className="font-text text-heading text-sm font-bold">{t('headerDossierDate')}</Text>
              </SortableColumn>
            </th>
            <th className="text-left">
              <SortableColumn sortField="published.dateTime">
                <Text className="font-text text-heading text-sm font-bold">{t('headerPublished')}</Text>
              </SortableColumn>
            </th>
            <th className="pr-6 text-left">
              <Text className="font-text text-heading text-sm font-bold">{t('headerAuthor')}</Text>
            </th>
          </tr>
        </thead>
        <tbody>
          {dossiers.map((dossier, index) => (
            <tr
              key={dossier.id}
              className={`grid grid-cols-8 gap-x-4 pb-3 pt-4 ${index % 2 === 0 ? 'bg-base-00' : 'bg-base-10'}`}
            >
              <td className="col-span-2 pl-6">
                <Text className="font-text text-heading line-clamp-1 break-words text-sm underline underline-offset-2">
                  <Link prefetch={false} href={`/dossier/${dossier.dossierId}`}>
                    {dossier.name || '-'}
                  </Link>
                </Text>
              </td>
              <td>
                <Text className="font-text text-heading line-clamp-1 break-words text-sm">{dossier.type || '-'}</Text>
              </td>
              <td>
                <Text className="font-text text-heading line-clamp-1 break-words text-sm">
                  {getDateString(dossier.modifiedDate)}
                </Text>
              </td>
              <td>
                <Text className="font-text text-heading line-clamp-1 break-words text-sm">
                  {dossier.modifiedBy || '-'}
                </Text>
              </td>
              <td>
                <Text className="font-text text-heading line-clamp-1 break-words text-sm">
                  {getDateString(dossier.publishFromDate)}
                </Text>
              </td>
              <td>
                <Text className="font-text text-heading line-clamp-1 break-words text-sm">
                  {getDateString(dossier.publishedDate)}
                </Text>
              </td>
              <td className="pr-6">
                <Text className="font-text text-heading line-clamp-1 break-words text-sm">{dossier.author || '-'}</Text>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

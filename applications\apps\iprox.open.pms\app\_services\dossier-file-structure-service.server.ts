import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';

export async function getDossierMetrics(params: string): Promise<components['schemas']['GetDossierMetricsResponse']> {
  try {
    return await serverApi
      .get(`dossier-file-structure/dossier-metrics?${params}`)
      .json<components['schemas']['GetDossierMetricsResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

import { getSecuritySettings } from '@/services/settings-service';

// Change the dynamic behavior of a layout or page to fully dynamic. Cache will be handled by ky
export const dynamic = 'force-dynamic';

const generateSecurityFile = async () => {
  const securitySettings = await getSecuritySettings();
  const { settings, expirationDate } = securitySettings;

  const lines: string[] = [];

  // Common function to generate section lines
  const generateSectionLines = (label: string, array: string[] | null) => {
    if (array && array.length > 0) {
      array.forEach((item) => {
        lines.push(`${label}: ${item}`);
      });
    }
  };

  generateSectionLines('Contact', settings.contact);
  generateSectionLines('Encryption', settings.openPgpKey);
  generateSectionLines('Acknowledgments', settings.acknowledgements);

  // Preferred Language
  if (settings.preferredLanguages && settings.preferredLanguages.length > 0) {
    lines.push(`Preferred-Languages: ${settings.preferredLanguages.join(', ')}`);
  }

  generateSectionLines('Policy', settings.policy);
  generateSectionLines('Canonical', settings.canonical);
  generateSectionLines('Hiring', settings.hiring);
  generateSectionLines('CSAF', settings.csaf);

  // Expires
  if (expirationDate) {
    lines.push(`Expires: ${expirationDate}`);
  }

  return lines.join('\n');
};

export async function GET() {
  try {
    const fileContent = await generateSecurityFile();

    return new Response(fileContent, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Content-Disposition': 'inline; filename="security.txt"',
        'X-Frame-Options': 'DENY',
      },
    });
  } catch (e) {
    return new Response('File not found', {
      status: 404,
    });
  }
}

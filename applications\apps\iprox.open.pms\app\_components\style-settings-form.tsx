'use client';

import { updateCssVariables } from '@/context/portal-configuration.actions';
import { usePortalConfigurationContext } from '@/context/portal-configuration.context';
import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { updateSiteParameters } from '@/services/settings-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { FieldType, FormBuilder, FormSubmitValues, Text, showToast } from '@iprox/iprox-ui';
import { ThemeParameter } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';

import { DEFAULT_THEME } from '@iprox/react-ui/server';

import { ThemePreview } from './theme-preview';

interface StyleSettingsFormProps {
  tenantName: string;
  cssVariables: components['schemas']['StringStringKeyValuePair'][];
}

export function StyleSettingsForm({ tenantName, cssVariables }: StyleSettingsFormProps) {
  const clientApi = useClientApi();
  const t = useTranslations('site');

  const [loading, setLoading] = useState(false);
  const { dispatch } = usePortalConfigurationContext();

  const getNumericValue = (value: string) => {
    return parseInt(value, 10);
  };

  const generateFormLabel = (key: string) => {
    const words = key.split('_');

    return words.map((word, index) => (index === 0 ? word.charAt(0).toUpperCase() + word.slice(1) : word)).join(' ');
  };

  const formFields = useMemo(() => {
    return DEFAULT_THEME.map((item) => {
      return {
        id: item.key,
        name: item.key,
        label: generateFormLabel(item.key),
        description: item.type === 'number' ? t('unit') : undefined,
        fieldType:
          item.type === 'color' ? FieldType.Color : item.type === 'number' ? FieldType.Integer : FieldType.Text,
        validationRules: [],
        value:
          item.type === 'number'
            ? getNumericValue(cssVariables.find((x) => x.key === item.key)?.value ?? item.value).toString()
            : cssVariables.find((x) => x.key === item.key)?.value ?? item.value,
      };
    });
  }, [cssVariables, t]);

  const handleSubmit = (values: FormSubmitValues) => {
    setLoading(true);

    const themeParameters = Object.entries(values)
      .filter(([_key, value]) => value || value === 0)
      .map(
        ([key, value]): ThemeParameter => ({
          key,
          value: DEFAULT_THEME.find((x) => x.key === key)?.type === 'number' ? `${value}px` : (value as string),
        })
      );

    updateSiteParameters(clientApi, {
      cssVariables: themeParameters,
      tenantName,
    })
      .then(() => {
        showToast(t('stylesUpdated'), { type: 'success' });
        dispatch(updateCssVariables(themeParameters));
      })
      .catch(async (error) => {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      })
      .finally(() => setLoading(false));
  };

  return (
    <div>
      <Text className="font-heading text-heading mb-6 text-2xl font-bold">{t('styleSettings')}</Text>
      <div className="grid grid-cols-[24rem_1fr] gap-10">
        <div>
          <FormBuilder fields={formFields} onSubmit={handleSubmit} buttonText={t('save')} disableButton={loading} />
        </div>
        <div className="ipx-portal-component">
          <ThemePreview />
        </div>
      </div>
    </div>
  );
}

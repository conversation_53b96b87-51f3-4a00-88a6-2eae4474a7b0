@mixin background-color($cssVar) {
  --tw-bg-opacity: 1;
  background-color: rgb($cssVar / var(--tw-bg-opacity));
}

/** Applies text-color and background-colors. using $cssVars, selects '-content' and '-hover' */
@mixin apply-colors($cssVar) {
  @include background-color(var($cssVar));

  color: rgb(var($cssVar + '-content'));

  &:hover {
    @include background-color(var($cssVar + '-hover'));
  }
}

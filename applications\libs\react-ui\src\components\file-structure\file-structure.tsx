import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useMemo, useRef } from 'react';

import { Button } from '../../iprox-ui/components/button/button';
import { Text } from '../text/text';
import { FileNode, FolderNode, Node } from './models/file-structure';
import { Checkbox } from './parts/checkbox';
import { DisplayFileNode } from './parts/file-node';
import { DisplayFolderNode } from './parts/folder-node';
import {
  isNodeSelectedInHierarchy,
  toggleSelectionForAllNodes,
  updateAncestorsRecursively,
} from './utils/file-structure-utils';

interface FileStructureProps {
  rootNode: FolderNode;
  /** Emits when a filenode is clicked. */
  onFileClick: (item: FileNode) => void;
  /** Emits when a node is expanded, and does not yet have its children available. */
  onLoadChildren: (item: FolderNode) => Promise<void>;
  /** Emits when a folder node is toggled */
  onToggleFolderNode: (item: FolderNode) => void;
  onSelectItem: (updatedRoonLoadChildrenotNode: FolderNode, selectedNodes: Node[]) => void;
  setRootFolderNode: React.Dispatch<React.SetStateAction<FolderNode | null>>;
  onAddButtonClick?: (item: FolderNode) => void;
  onAddFolderButtonClick?: () => void;
  onRenaming?: (item: Node, newName: string) => Promise<boolean>;
  selectedNodes: Node[];
  editableNodes?: boolean;
  enableAddFolder?: boolean;
  enableUploadFiles?: boolean;
  onUploadFilesButtonClick?: () => void;
}

export function FileStructure({
  rootNode,
  onFileClick,
  onLoadChildren,
  onToggleFolderNode,
  onSelectItem,
  onAddButtonClick = () => null,
  onAddFolderButtonClick = () => null,
  onRenaming,
  selectedNodes,
  editableNodes = false,
  enableAddFolder,
  enableUploadFiles,
  onUploadFilesButtonClick,
}: FileStructureProps) {
  const t = useTranslations('components.fileStructure');

  const selectAllInputRef = useRef<HTMLInputElement>(null);

  const isNodeSelectedInHierarchyCallback = useCallback((node: Node, currentItem: Node) => {
    return isNodeSelectedInHierarchy(node, currentItem);
  }, []);

  useEffect(() => {
    if (selectAllInputRef.current) {
      const isChildSelected =
        rootNode.children.some((node) => isNodeSelectedInHierarchyCallback(node, rootNode)) &&
        !selectAllInputRef.current.checked;
      selectAllInputRef.current.indeterminate = isChildSelected;
    }
  }, [isNodeSelectedInHierarchyCallback, rootNode, rootNode.children]);

  const handleSelectItems = useCallback(
    (item: Node, isSelected: boolean): void => {
      const updatedNode: Node = {
        ...item,
        isSelected,
      };

      const { updatedRoot, selectedNodes: selected } = updateAncestorsRecursively(rootNode, updatedNode, selectedNodes);

      onSelectItem(updatedRoot as FolderNode, selected);
    },
    [onSelectItem, selectedNodes, rootNode]
  );

  const handleToggleSelectAllItems = useCallback(
    (isSelected: boolean): void => {
      const { updatedRoot, selectedNodes } = toggleSelectionForAllNodes(rootNode, isSelected);

      onSelectItem(updatedRoot as FolderNode, selectedNodes);
    },
    [onSelectItem, rootNode]
  );

  const handleOnLoadChildren = useCallback(
    (item: FolderNode): Promise<void> => {
      const updatedNode: Node = {
        ...item,
        isExpanded: true,
      };

      return onLoadChildren(updatedNode);
    },
    [onLoadChildren]
  );

  const rootNodeItems = useMemo(() => {
    return rootNode.children.map((child) => (
      <div key={child.nodeId} className="border-base-10 border-t" role="listitem" aria-label="listitem">
        {child.nodeType === 'Folder' ? (
          <DisplayFolderNode
            item={child}
            level={0}
            editableNode={editableNodes}
            onFileClick={onFileClick}
            onSelectItem={handleSelectItems}
            onLoadChildren={handleOnLoadChildren}
            onToggleFolderNode={onToggleFolderNode}
            onAddButtonClick={onAddButtonClick}
            onRenaming={onRenaming}
          />
        ) : (
          <DisplayFileNode
            item={child}
            level={0}
            editableNode={editableNodes}
            onFileClick={onFileClick}
            onSelectItem={handleSelectItems}
            onRenaming={onRenaming}
          />
        )}
      </div>
    ));
  }, [
    editableNodes,
    handleOnLoadChildren,
    handleSelectItems,
    onAddButtonClick,
    onFileClick,
    onRenaming,
    onToggleFolderNode,
    rootNode.children,
  ]);

  return (
    <>
      {rootNode.numberOfDescendantFiles ? (
        <Text className="font-text text-body mb-3 text-sm">
          {t('amountOfFiles', { fileCount: rootNode.numberOfDescendantFiles })}
        </Text>
      ) : null}
      <div className="my-3 flex flex-row gap-x-6">
        {enableAddFolder ? (
          <Button type="button" variant="tertiary" onClick={onAddFolderButtonClick}>
            {t('addFolder')}
          </Button>
        ) : null}
        {enableUploadFiles ? (
          <Button type="button" variant="tertiary" onClick={onUploadFilesButtonClick}>
            {t('uploadFiles')}
          </Button>
        ) : null}
      </div>
      <div className="mb-1 gap-4 p-3">
        <label className="flex cursor-pointer items-center">
          <Checkbox
            inputRef={selectAllInputRef}
            isSelected={rootNode.isSelected}
            handleSelect={(e) => handleToggleSelectAllItems(e.target.checked)}
          />
          <span className="font-heading text-body text-base">{t('selectAllNodes')}</span>
        </label>
      </div>
      <div role="list" aria-label={t('fileListLabel')}>
        <div role="listitem" className="mb-1 hidden grid-cols-[max-content_3fr_minmax(0,_20%)] gap-4 p-3 md:grid">
          <Text className="font-heading text-body text-left text-base font-bold">{t('type')}</Text>
          <Text className="font-heading text-body text-left text-base font-bold">{t('fileName')}</Text>
          <Text className="font-heading text-body text-left text-base font-bold">{t('size')}</Text>
        </div>
        {rootNodeItems}
      </div>
    </>
  );
}

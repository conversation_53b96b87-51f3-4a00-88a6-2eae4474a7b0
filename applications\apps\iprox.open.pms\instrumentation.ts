export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    const applicationinsights = await import('applicationinsights');
    applicationinsights
      .setup(process.env['APPLICATIONINSIGHTS_CONNECTION_STRING'])
      .setAutoCollectConsole(true, true)
      .setAutoDependencyCorrelation(true)
      .setAutoCollectRequests(true)
      .setAutoCollectPerformance(true, true)
      .setAutoCollectExceptions(true)
      .setAutoCollectDependencies(true)
      .setUseDiskRetryCaching(true)
      .setAutoCollectPreAggregatedMetrics(true)
      .setSendLiveMetrics(true)
      .setAutoCollectHeartbeat(true)
      .setAutoCollectIncomingRequestAzureFunctions(true)
      .setInternalLogging(false, true)
      .setDistributedTracingMode(applicationinsights.DistributedTracingModes.AI_AND_W3C)
      .enableWebInstrumentation(true)
      .start();
  }
}

'use client';

import { components } from '@/iprox-open.interface';
import {
  Button,
  FieldDefinition,
  FieldType,
  FormBuilder,
  FormSubmitValues,
  MultiFieldDefinition,
  ValidationRuleType,
  ValueTypes,
} from '@iprox/iprox-ui';
import { FontCategory, FontStyle, FontWeight, enumToOptionsArray } from '@iprox/react-ui';
import { useMemo } from 'react';
import { useTranslations } from 'use-intl';

type ButtonText = 'createFont' | 'saveFont';

interface FontCreateUpdateFormProps {
  initialValues?: components['schemas']['FontAssetDto'];
  submitButtonText?: ButtonText;
  disableButton: boolean;
  onSubmitForm: (values: FormSubmitValues) => void;
  onCancel?: () => void;
}

export function FontCreateUpdateForm({
  initialValues,
  submitButtonText = 'createFont',
  disableButton,
  onSubmitForm,
  onCancel,
}: FontCreateUpdateFormProps) {
  const t = useTranslations('fonts');

  const fontForm: FieldDefinition<FieldType, ValueTypes>[] = useMemo(
    () => [
      {
        name: 'fontFamily',
        label: t('fontFamily'),
        fieldType: FieldType.Text,
        value: initialValues?.fontFamily ?? '',
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
        ],
        style: { colSpan: 'col-span-2', gridRow: 'grid-row-1' },
      },
      {
        name: 'fontCategory',
        label: t('fontCategory'),
        fieldType: FieldType.Select,
        value: initialValues?.fontCategory.toString().toLowerCase() ?? '',
        options: enumToOptionsArray(FontCategory).map(({ label, value }) => ({
          label: t(label),
          value,
        })),
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
        ],
        style: { colSpan: 'col-span-2', gridRow: 'grid-row-1' },
      } as MultiFieldDefinition,
      {
        name: 'fontWeight',
        label: t('fontWeight'),
        fieldType: FieldType.Select,
        value: initialValues?.fontWeight.toString().toLowerCase() ?? '',
        options: enumToOptionsArray(FontWeight).map(({ label, value }) => ({
          label: t(label),
          value,
        })),
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
        ],
        style: { colSpan: 'col-span-2', gridRow: 'grid-row-1' },
      } as MultiFieldDefinition,
      {
        name: 'fontStyle',
        label: t('fontStyle'),
        fieldType: FieldType.RadioButton,
        value: initialValues?.fontStyle.toString().toLowerCase() ?? '',
        options: enumToOptionsArray(FontStyle).map(({ label, value }) => ({
          label: t(label),
          value,
        })),
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
        ],
        displayModeRow: true,
        style: { colSpan: 'col-span-2', gridRow: 'grid-row-1' },
      } as MultiFieldDefinition,
    ],
    [initialValues?.fontCategory, initialValues?.fontFamily, initialValues?.fontStyle, initialValues?.fontWeight, t]
  );

  return (
    <div className="mb-5 rounded-lg p-4 shadow-md">
      <FormBuilder
        fields={fontForm}
        gridContainerClasses="grid grid-cols-8 gap-2"
        onSubmit={onSubmitForm}
        onCancel={onCancel}
        buttons={
          <FormButtons submitButtonText={t(submitButtonText)} cancelButtonText={t('cancel')} disabled={disableButton} />
        }
      />
    </div>
  );
}

type formButtonProps = {
  submitButtonText: string;
  cancelButtonText: string;
  disabled: boolean;
};

const FormButtons = ({ submitButtonText, cancelButtonText, disabled }: formButtonProps) => (
  <div className="col-span-8 mb-5 grid grid-flow-col justify-end gap-2">
    <Button type="submit" variant="primary" disabled={disabled}>
      {submitButtonText}
    </Button>
    <Button type="reset" variant="secondary" disabled={disabled}>
      {cancelButtonText}
    </Button>
  </div>
);

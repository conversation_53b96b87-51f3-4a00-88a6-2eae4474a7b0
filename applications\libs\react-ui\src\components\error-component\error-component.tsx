import { ChevronDownIcon, ChevronUpIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useState } from 'react';

import { Button } from '../button/button';
import { Text } from '../text/text';

type ErrorComponentProps = {
  error: Error & { digest?: string };
  reset: () => void;
  serviceDeskLink: string;
};

export function ErrorComponent({ reset, error, serviceDeskLink }: ErrorComponentProps) {
  const t = useTranslations('components.error');

  const [openDetails, setOpenDetails] = useState(false);

  return (
    <div className="flex h-full w-full flex-col justify-center">
      <div className="flex w-full flex-col items-center justify-center">
        <Text className="font-text text-error mb-2 block w-full whitespace-pre-line break-words text-center text-lg">
          {t('message')}{' '}
          <Link href={serviceDeskLink} target="_blank" className="text-anchor">
            {t('serviceDesk')}
          </Link>
        </Text>
        <Button type="button" onClick={() => reset()} variant="secondary" icon="ArrowPathIcon" disabled={false}>
          {t('tryAgain')}
        </Button>
      </div>
      <div className="relative w-full">
        <button
          type="button"
          className="text-error m-0 flex items-center p-0 ring-0"
          onClick={() => setOpenDetails(!openDetails)}
        >
          <ExclamationCircleIcon className="mr-1 h-5 w-5" />
          {t('moreDetails')}
          {openDetails ? <ChevronUpIcon className="mt-1 h-4 w-4" /> : <ChevronDownIcon className="mt-1 h-4 w-4" />}
        </button>
        {openDetails && (
          <div className="bg-base-25 top-100 absolute left-0 w-full rounded-lg p-4">
            <Text className="font-text text-error w-full break-words text-sm">{error.message}</Text>
          </div>
        )}
      </div>
    </div>
  );
}

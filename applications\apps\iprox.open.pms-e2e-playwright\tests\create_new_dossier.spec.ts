import { Locator, expect, test } from '@playwright/test';

import { baseUrl } from '../config';
import { DashboardPage } from '../pages/dashboard_page';
import { EditDossierPage } from '../pages/edit_dossier_page';
import { CreateNewDossierPage } from '../pages/new_dossier_page';
import { DateTimeFormatter } from '../utils/date_time_formatter';

let dashboardPage: DashboardPage;
let newDossierPage: CreateNewDossierPage;
let editDossierPage: EditDossierPage;
let titleValidationMessage: Locator;
let categoryValidationMessage: Locator;

test.beforeEach(async ({ page }) => {
  await page.goto(baseUrl);
  dashboardPage = new DashboardPage(page);
  newDossierPage = new CreateNewDossierPage(page);
  editDossierPage = new EditDossierPage(page);
});

test.describe('Create new dossier test verifications', () => {
  test.slow();
  test('Check the UI validations', async ({ page }) => {
    await dashboardPage.clickNewDossierButton();
    await newDossierPage.clickAddNewDossier();
    titleValidationMessage = page.locator(`//p[contains(text(),"Het veld 'Titel' is verplicht")]`);
    await expect(titleValidationMessage).toBeVisible();
    categoryValidationMessage = page.locator(`//p[contains(text(),"Het veld 'Categorie' is verplicht")]`);
    await expect(titleValidationMessage).toBeVisible();
  });

  test('Verify Create dossier flow - with publish dates', async ({ page }) => {
    const title = `Playwright dossier with publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;
    const category = newDossierPage.categoryWithPublishDates;

    await dashboardPage.goToDashboardPage();
    await dashboardPage.clickNewDossierButton();
    await newDossierPage.createNewDossier(title, true);
    await expect(page).toHaveURL(new RegExp('/dossier/.*'));

    expect(await editDossierPage.dossierTitle.textContent()).toBe(title);
    expect(await editDossierPage.categoryElement.textContent()).toBe(category);

    // TODO: @HASH Do we need to lookup newly created dossier in the list?
  });

  test('Verify Create dossier flow - without publish dates', async ({ page }) => {
    const title = `Playwright dossier without publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;
    const category = newDossierPage.categoryWithoutPublishDates;

    await dashboardPage.goToDashboardPage();
    await dashboardPage.clickNewDossierButton();
    await newDossierPage.createNewDossier(title, false);
    await expect(page).toHaveURL(new RegExp('/dossier/.*'));

    expect(await editDossierPage.dossierTitle.textContent()).toBe(title);
    expect(await editDossierPage.categoryElement.textContent()).toBe(category);
  });
});

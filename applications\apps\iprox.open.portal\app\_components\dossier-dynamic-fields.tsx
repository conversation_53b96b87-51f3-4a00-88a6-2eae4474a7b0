import { components } from '@/iprox-open.interface';
import { FieldType } from '@iprox/react-ui';
import { useFormatter } from 'next-intl';
import { Fragment } from 'react';

interface DossierDynamicFieldsProps {
  dynamicFields: components['schemas']['PublicDynamicFieldDto'][];
  dynamicFieldValues: components['schemas']['PublicDossierDynamicFieldValueDto'][];
}

export function DossierDynamicFields({ dynamicFields, dynamicFieldValues }: DossierDynamicFieldsProps) {
  const format = useFormatter();

  const getDynamicFieldType = (dynamicFieldId: string): FieldType | undefined => {
    const dynamicField = dynamicFields?.find((field) => field.id === dynamicFieldId);

    if (dynamicField) {
      return FieldType[dynamicField?.dynamicFieldType];
    }
  };

  const hasDynamicFieldValue = (dynamicFieldValue: components['schemas']['PublicDossierDynamicFieldValueDto']) =>
    !!dynamicFieldValue.value && Object.keys(dynamicFieldValue.value).length > 0;

  const renderDynamicFieldValue = (
    dynamicFieldId: string,
    value?: Record<string, unknown> | null | undefined
  ): React.ReactNode => {
    const fieldType = getDynamicFieldType(dynamicFieldId);

    if (fieldType === FieldType.Date) {
      const date = new Date(String(value));
      return <dd className="font-text">{format.dateTime(date, { dateStyle: 'short' })}</dd>;
    }

    if (fieldType === FieldType.StringList && Array.isArray(value)) {
      return value.map((item, index) => (
        <dd key={`${dynamicFieldId}_${item}`} className="font-text mr-1 inline">
          {item}
          {index < value.length - 1 && ', '}
        </dd>
      ));
    }

    if (fieldType === FieldType.RichText && typeof value === 'string') {
      return <div className="font-text mb-4" dangerouslySetInnerHTML={{ __html: value }}></div>;
    }

    return <dd className="font-text">{String(value)}</dd>;
  };

  const renderGroups = (dynamicFieldValues ?? []).reduce<
    Array<components['schemas']['PublicDossierDynamicFieldValueDto'][]>
  >((groups, field) => {
    const fieldType = getDynamicFieldType(field.dynamicFieldId);
    const previousGroup = groups[groups.length - 1] ?? [];
    const previousFieldType = previousGroup[0]?.dynamicFieldId
      ? getDynamicFieldType(previousGroup[0].dynamicFieldId)
      : undefined;

    if (!fieldType) {
      return groups;
    }

    if (groups.length === 0) {
      return [[field]];
    }

    // Start a new group if it's a RichText field and the previous field was not a RichText field
    if (fieldType !== previousFieldType && fieldType === FieldType.RichText) {
      groups.push([field]);
      return groups;
    }

    // Start a new group if the previousFieldType is a RichText field and the current one is not.
    if (fieldType !== previousFieldType && previousFieldType === FieldType.RichText) {
      groups.push([field]);
      return groups;
    }

    // Add to the last group if we shouldn't start a new group.
    groups[groups.length - 1].push(field);

    return groups;
  }, []);

  return renderGroups.map((group, index) => (
    <Fragment key={`group_${index}`}>
      {getDynamicFieldType(group[0].dynamicFieldId) === FieldType.RichText ? (
        group.map((field) =>
          hasDynamicFieldValue(field) ? (
            <Fragment key={field.dynamicFieldId}>{renderDynamicFieldValue(field.dynamicFieldId, field.value)}</Fragment>
          ) : null
        )
      ) : (
        <dl key={index}>
          {group.map((field) =>
            hasDynamicFieldValue(field) ? (
              <div key={field.dynamicFieldId} className="mb-4 flex flex-row">
                <dt className="font-text mr-4 font-bold">{`${field.label}:`}</dt>
                <>{renderDynamicFieldValue(field.dynamicFieldId, field.value)}</>
              </div>
            ) : null
          )}
        </dl>
      )}
    </Fragment>
  ));
}

{"name": "react-ui-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/react-ui-e2e/src", "projectType": "application", "targets": {"e2e": {"executor": "nx:run-commands", "options": {"commands": ["playwright test"], "cwd": "apps/react-ui-e2e"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": [], "implicitDependencies": ["react-ui"]}
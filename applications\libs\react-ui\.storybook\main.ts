import type { StorybookConfig } from '@storybook/nextjs';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');

const config: StorybookConfig = {
  stories: ['../docs/**/*.mdx', '../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx|mdx)'],

  addons: ['@storybook/addon-essentials', '@bbbtech/storybook-formik/register', '@storybook/addon-a11y'],

  staticDirs: ['./../../../apps/iprox.open.pms/public'],

  framework: {
    name: '@storybook/nextjs',
    options: {
      builder: {},
    },
  },

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  webpackFinal: async (config: any) => {
    config.resolve = config.resolve || {};
    config.resolve.plugins = config.resolve.plugins || [];
    config.resolve.plugins = [new TsconfigPathsPlugin({ configFile: './tsconfig.base.json' })];
    return config;
  },

  docs: {
    autodocs: true,
  },
};

export default config;

// To customize your Vite configuration you can use the viteFinal field.
// Check https://storybook.js.org/docs/react/builders/vite#configuration
// and https://nx.dev/packages/storybook/documents/custom-builder-configs

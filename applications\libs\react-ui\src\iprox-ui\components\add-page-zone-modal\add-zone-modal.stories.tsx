import { <PERSON>a, StoryObj } from '@storybook/react';
import { useEffect, useState } from 'react';

import { AddZoneModal } from './add-zone-modal';

const meta: Meta<typeof AddZoneModal> = {
  title: 'components/add-zone-modal',
  component: AddZoneModal,
};

export default meta;

type Story = StoryObj<typeof AddZoneModal>;

const TemplateModal = ({ open }: { open: boolean }) => {
  const [isOpen, setIsOpen] = useState(open);

  useEffect(() => {
    setIsOpen(open);
  }, [open]);

  return (
    <>
      <button data-testid="open-modal" onClick={() => setIsOpen(true)}>
        Click to open modal
      </button>
      <AddZoneModal isOpen={isOpen} onClose={() => setIsOpen(false)} onCreateZone={() => setIsOpen(false)} />
    </>
  );
};

export const Default: Story = {
  name: 'default',
  args: {
    isOpen: false,
  },
  render: (args) => <TemplateModal open={args.isOpen} />,
  decorators: [
    (Story) => (
      <div className="w-100 flex flex-row justify-center">
        <Story />
      </div>
    ),
  ],
};

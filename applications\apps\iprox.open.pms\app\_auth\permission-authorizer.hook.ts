'use server';

import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';

import { authOptions } from './auth';

const onAuthenticationFail = () => {
  redirect('/login');
};

type ApiPermissions = components['schemas']['PermissionType'];
type UIPermissions = 'admin:access';

type ApplicationPermissions = ApiPermissions | UIPermissions;

const adminAccessPermission: ApiPermissions[] = [
  'Admin:UserRoles:View',
  'Settings:UpdateSecuritySettings',
  'Settings:UpdateSiteParameters',
  'Settings:UpdateHomePageContent',
  'Page:CreatePage',
  'Page:UpdatePage',
  'Page:DeletePage',
  'ImageAsset:UpdateSiteLogo',
  'ImageAsset:UpdateSiteFavicon',
  'ImageAsset:UpdateSiteHomePageImage',
  'FontAsset:GetFontAsset',
  'FontAsset:GetAllFontAssets',
  'FontAsset:CreateFontAsset',
  'FontAsset:UpdateFontAsset',
  'FontAsset:UploadFonts',
  'FontAsset:DeleteFonts',
  'FontAsset:DeleteFontAsset',
];

const adminAccessAuthorizer = (userPermissions: ApiPermissions[]) =>
  adminAccessPermission.find((p) => userPermissions.includes(p)) !== undefined;

let userRolesCache: Promise<components['schemas']['GetUserRolesWithPermissionsResponse']> | undefined;
let userRolesCacheTime: number | undefined = undefined;

const getUserRoles = (): Promise<components['schemas']['GetUserRolesWithPermissionsResponse']> => {
  // Fetch the userroles only every 60 seconds.
  if (!userRolesCache || (userRolesCacheTime && userRolesCacheTime > Date.now() + 1000 * 60)) {
    userRolesCache = serverApi.get('user/roles').json<components['schemas']['GetUserRolesWithPermissionsResponse']>();

    userRolesCacheTime = Date.now();

    return userRolesCache;
  }

  return userRolesCache;
};

const getPermissionsForUserRoles = async (userRoles: string[]): Promise<ApplicationPermissions[]> => {
  const { roles } = await getUserRoles();

  const userApiPermissions: ApiPermissions[] = [
    ...new Set(
      roles
        .filter((role) => userRoles.includes(role.name))
        .map((role) => role.permissions)
        .flat()
    ),
  ] as ApiPermissions[];

  const uiPermissionAuthorizer: Record<UIPermissions, boolean> = {
    'admin:access': adminAccessAuthorizer(userApiPermissions),
  };

  const userUiPermissions = Object.entries(uiPermissionAuthorizer).reduce<UIPermissions[]>(
    (uiPermissions, [key, value]) => (value ? [...uiPermissions, key as UIPermissions] : uiPermissions),
    []
  );

  return [...userApiPermissions, ...userUiPermissions];
};

export const usePermissionAuthorizer = async <T extends ApplicationPermissions>(
  permissions: T[]
): Promise<Record<T, boolean>> => {
  const session = await getServerSession(authOptions);

  if (!session || !session.user) {
    return onAuthenticationFail();
  }

  const userPermissions = await getPermissionsForUserRoles(session.user.userRoles ?? []);

  return permissions.reduce<Record<T, boolean>>(
    (acc, permission) => ({
      ...acc,
      [permission]: userPermissions.includes(permission),
    }),
    {} as Record<T, boolean>
  );
};

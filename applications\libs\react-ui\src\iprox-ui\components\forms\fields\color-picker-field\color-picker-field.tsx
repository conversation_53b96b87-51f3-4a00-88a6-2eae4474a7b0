import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { Sketch } from '@uiw/react-color';
import cx from 'classnames';
import { debounce } from 'debounce';
import { useField } from 'formik';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';

import { Text } from '../../../text/text';
import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { ColorPickerFieldDefinition } from '../../models/form.models';
import { borderClassname } from '../../utils/border-classname';

const debouncedSetValue = debounce((value: string, setValueFn: (value: string) => void) => {
  setValueFn(value);
}, 300);

export function ColorPickerField(props: ColorPickerFieldDefinition) {
  const [field, meta, helpers] = useField(props);
  const [hex, setHex] = useState(field.value || '#fff');
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'color');

  const [showColorPicker, setShowColorPicker] = useState(false);
  const popperRef = useRef<HTMLDivElement>(null);
  const t = useTranslations('components');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  const handleChangeComplete = (color: string) => {
    setHex(color);
    debouncedSetValue(color, helpers.setValue);
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (popperRef.current && !popperRef.current.contains(event.target as Node)) {
        setShowColorPicker(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <FormField definition={props} errorMessage={meta.touched ? meta.error : undefined} {...formControlProps}>
      <div
        className={cx(
          'rounded-input h-input flex w-full flex-row items-center p-3',
          borderClassname(meta.touched ? meta.error : undefined, field.value)
        )}
        role="button"
        onClick={() => setShowColorPicker(!showColorPicker)}
        id={inputProps.id}
        aria-label={t('colorPickerField.ariaLabel')}
      >
        <div
          className={`h-5 w-5 rounded ${field.value ? 'border-base-25 border' : ''}`}
          style={{
            backgroundColor: field.value || 'transparent',
          }}
        ></div>
        <Text className="font-text text-body ml-2 text-sm">{field.value}</Text>
        <ChevronDownIcon className="text-body ml-auto h-4 w-4" />
      </div>
      {showColorPicker ? (
        <div ref={popperRef} className="absolute z-50 mt-1">
          <Sketch
            style={{ marginLeft: 20 }}
            color={hex}
            onChange={(color) => {
              handleChangeComplete(color.hex);
            }}
          />
        </div>
      ) : null}
    </FormField>
  );
}

import { ValidationRule, ValidationRuleType, ValidatorFn, ValidatorFnFactory } from '../models/validator.models';

export interface NumberRangeValidationRule extends ValidationRule<ValidationRuleType.ValidateNumberRange> {
  ruleValue: {
    minimumValue: number | string;
    maximumValue: number | string;
  };
}

export const isNumberRangeRule = (rule: ValidationRule): rule is NumberRangeValidationRule => {
  return rule.ruleType === ValidationRuleType.ValidateNumberRange;
};

export const numberRangeValidatorFactory: ValidatorFnFactory<{
  minimumValue: number | string;
  maximumValue: number | string;
}> = (ruleValue): ValidatorFn => {
  return ({ value }) => {
    if (typeof value !== 'number') {
      return null;
    }

    if (value < Number(ruleValue.minimumValue) || value > Number(ruleValue.maximumValue)) {
      return { numberRange: true };
    }

    return null;
  };
};

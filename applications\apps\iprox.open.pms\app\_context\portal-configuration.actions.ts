import { StateAction, ThemeParameter } from '@iprox/react-ui';

import { PortalConfigurationState } from './portal-configuration.context';

export type PortalConfigurationActionTypes = 'UPDATE_CSS_VARIABLES';

type UpdateCssVariablesAction = StateAction<'UPDATE_CSS_VARIABLES', PortalConfigurationState>;

export const updateCssVariables = (cssVariables: ThemeParameter[]): UpdateCssVariablesAction => ({
  type: 'UPDATE_CSS_VARIABLES',
  reducer: (state) => ({ ...state, cssVariables }),
});

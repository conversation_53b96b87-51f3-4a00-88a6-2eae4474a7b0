"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(authenticated)/(user)/dossier/[id]/page",{

/***/ "(app-pages-browser)/./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx":
/*!***************************************************************************!*\
  !*** ./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx ***!
  \***************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/http/fetcher-api.client */ \"(app-pages-browser)/./app/_http/fetcher-api.client.ts\");\n/* harmony import */ var _services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/dossier-service.client */ \"(app-pages-browser)/./app/_services/dossier-service.client.ts\");\n/* harmony import */ var _utils_dossier_form_changed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/dossier.form-changed */ \"(app-pages-browser)/./app/_utils/dossier.form-changed.ts\");\n/* harmony import */ var _utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dossier.form-definition.hook */ \"(app-pages-browser)/./app/_utils/dossier.form-definition.hook.ts\");\n/* harmony import */ var _utils_error_handler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/error-handler */ \"(app-pages-browser)/./app/_utils/error-handler.ts\");\n/* harmony import */ var _iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @iprox/iprox-ui */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/index.ts\");\n/* harmony import */ var _iprox_react_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @iprox/react-ui */ \"(app-pages-browser)/../../libs/react-ui/src/index.ts\");\n/* harmony import */ var _iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @iprox/shared-context */ \"(app-pages-browser)/../../libs/shared-context/src/index.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _components_dossier_file_manager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dossier-file-manager */ \"(app-pages-browser)/./app/_components/dossier-file-manager.tsx\");\n/* harmony import */ var _components_dossier_image_field__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dossier-image-field */ \"(app-pages-browser)/./app/_components/dossier-image-field.tsx\");\n/* harmony import */ var _components_dossier_status_controls__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dossier-status-controls */ \"(app-pages-browser)/./app/_components/dossier-status-controls.tsx\");\n/* harmony import */ var _components_dossier_title__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dossier-title */ \"(app-pages-browser)/./app/_components/dossier-title.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PageContent(param) {\n    let { dossier: initialDossier, versions: initialVersions, pages } = param;\n    var _dossier_created, _dossier_published, _dossier_unpublished, _dossier_modified, _dossier_category, _dossier_decorativeImageNode;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations)(\"dossier\");\n    const settings = (0,_iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__.useAppSettings)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const clientApi = (0,_http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__.useClientApi)();\n    const formId = \"dossier-creation-form\";\n    const { showDialog } = (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.useConfirmDialog)();\n    const [dossier, setDossier] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(initialDossier);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(initialDossier.status);\n    const [isSaveAndPublish, setIsSaveAndPublish] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(initialVersions);\n    const [lastSavedValues, setLastSavedValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [formValues, setFormValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [dateFormValues, setDateFormValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(dossier.publishDates);\n    const [isNew, setIsNew] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(dossier.version === 1);\n    const formDefinition = (0,_utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__.useDossierFormDefinition)(dossier);\n    const editStatus = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        switch(true){\n            case status === \"Published\":\n                var _dossier_published;\n                return {\n                    status: \"published\",\n                    date: (_dossier_published = dossier.published) === null || _dossier_published === void 0 ? void 0 : _dossier_published.dateTime\n                };\n            case status === \"Unpublished\":\n                var _dossier_unpublished;\n                return {\n                    status: \"unpublished\",\n                    date: (_dossier_unpublished = dossier.unpublished) === null || _dossier_unpublished === void 0 ? void 0 : _dossier_unpublished.dateTime\n                };\n            case isNew:\n                var _dossier_created;\n                return {\n                    status: \"new\",\n                    date: (_dossier_created = dossier.created) === null || _dossier_created === void 0 ? void 0 : _dossier_created.dateTime\n                };\n            default:\n                var _versions__modified, _versions_;\n                return {\n                    status: \"modified\",\n                    date: (_versions_ = versions[0]) === null || _versions_ === void 0 ? void 0 : (_versions__modified = _versions_.modified) === null || _versions__modified === void 0 ? void 0 : _versions__modified.dateTime\n                };\n        }\n    }, [\n        status,\n        isNew,\n        (_dossier_created = dossier.created) === null || _dossier_created === void 0 ? void 0 : _dossier_created.dateTime,\n        (_dossier_published = dossier.published) === null || _dossier_published === void 0 ? void 0 : _dossier_published.dateTime,\n        (_dossier_unpublished = dossier.unpublished) === null || _dossier_unpublished === void 0 ? void 0 : _dossier_unpublished.dateTime,\n        versions\n    ]);\n    const dossierLiveStatus = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _version_published, _version_unpublished, _version_deleted;\n        const version = versions.find((version)=>version.status !== \"Draft\");\n        if (!version) {\n            var _dossier_modified;\n            return {\n                status: \"Draft\",\n                date: (_dossier_modified = dossier.modified) === null || _dossier_modified === void 0 ? void 0 : _dossier_modified.dateTime\n            };\n        }\n        const dates = [\n            {\n                status: \"Published\",\n                date: (_version_published = version.published) === null || _version_published === void 0 ? void 0 : _version_published.dateTime\n            },\n            {\n                status: \"Unpublished\",\n                date: (_version_unpublished = version.unpublished) === null || _version_unpublished === void 0 ? void 0 : _version_unpublished.dateTime\n            },\n            {\n                status: \"Deleted\",\n                date: (_version_deleted = version.deleted) === null || _version_deleted === void 0 ? void 0 : _version_deleted.dateTime\n            }\n        ].filter((item)=>item.date !== null);\n        const mostRecent = dates.reduce((latest, current)=>{\n            if (!latest || current.date && new Date(current.date) > new Date(latest.date)) {\n                return current;\n            }\n            return latest;\n        }, null);\n        return mostRecent;\n    }, [\n        versions,\n        (_dossier_modified = dossier.modified) === null || _dossier_modified === void 0 ? void 0 : _dossier_modified.dateTime\n    ]);\n    const refetchVersions = async ()=>{\n        try {\n            const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.getDossierVersions)(clientApi, dossier.dossierId);\n            if (response === null || response === void 0 ? void 0 : response.dossiers) {\n                setVersions(response.dossiers);\n            }\n        } catch (error) {\n            console.error(\"Failed to refetch versions:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if ((0,_utils_dossier_form_changed__WEBPACK_IMPORTED_MODULE_3__.hasFormChanged)(formDefinition, formValues, lastSavedValues)) {\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        t,\n        formDefinition,\n        formValues,\n        lastSavedValues\n    ]);\n    /**\r\n   * format form value object to match the API request body required to save form data\r\n   * @param values form values\r\n   * @returns formatted form values object\r\n   */ const formatValues = (values)=>{\n        return Object.keys(values).filter((key)=>key !== \"summary\" && key !== \"fromDate\" && key !== \"toDate\").reduce((acc, value)=>{\n            var _formDefinition_find;\n            return {\n                ...acc,\n                [(_formDefinition_find = formDefinition.find((field)=>field.name === value)) === null || _formDefinition_find === void 0 ? void 0 : _formDefinition_find.id]: values[value]\n            };\n        }, {});\n    };\n    const handleFormChange = (values)=>{\n        setFormValues(values);\n    };\n    const handleDateFormChange = (values)=>{\n        setDateFormValues(values);\n    };\n    /**\r\n   * handle save form data\r\n   * @param values form values\r\n   */ const handleSaveForm = async (values)=>{\n        showDialog({\n            message: isSaveAndPublish ? t(\"confirmation.publish.message\") : t(\"confirmation.save.message\"),\n            onConfirm: async ()=>{\n                setIsLoading(true);\n                const updateDossierRequest = {\n                    dossierId: dossier.dossierId,\n                    summary: typeof values.summary === \"string\" ? values.summary : \"\",\n                    dynamicFieldValues: formatValues(values),\n                    publishFromDate: (dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.fromDate) ? dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.fromDate : new Date().toISOString(),\n                    publishToDate: (dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.toDate) ? dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.toDate : null\n                };\n                try {\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.updateDossier)(clientApi, updateDossierRequest);\n                    if (response) {\n                        setIsNew(response.dossier.version === 1);\n                        setLastSavedValues(formValues);\n                        await refetchVersions();\n                        if (isSaveAndPublish) {\n                            handlePublishDossier();\n                        } else {\n                            setStatus(\"Draft\");\n                            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.save\"), {\n                                type: \"success\"\n                            });\n                        }\n                    }\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    const handleDeleteDossier = ()=>{\n        showDialog({\n            message: t(\"confirmation.delete.message\"),\n            onConfirm: async ()=>{\n                try {\n                    setIsLoading(true);\n                    const reqBody = {\n                        dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n                    };\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.deleteDossier)(clientApi, reqBody);\n                    if (response && response.success) {\n                        // Refetch versions to get updated delete timestamp (though we're navigating away)\n                        await refetchVersions();\n                        router.push(\"/dossier/list\");\n                    }\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.delete\"), {\n                        type: \"success\"\n                    });\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    /**\r\n   * handle publish dossier functionality\r\n   */ const handlePublishDossier = async ()=>{\n        try {\n            const reqBody = {\n                dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n            };\n            const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.publishDossier)(clientApi, reqBody);\n            if (response && response.success) {\n                setStatus(\"Published\");\n                // Refetch versions to get updated publish timestamp\n                await refetchVersions();\n            }\n            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.publish\"), {\n                type: \"success\"\n            });\n        } catch (error) {\n            const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                type: \"error\"\n            });\n        }\n    };\n    const handleUnpublishDossier = ()=>{\n        showDialog({\n            message: t(\"confirmation.unpublish.message\"),\n            onConfirm: async ()=>{\n                try {\n                    setIsLoading(true);\n                    const reqBody = {\n                        dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n                    };\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.unpublishDossier)(clientApi, reqBody);\n                    if (response) {\n                        setStatus(\"Draft\");\n                        setIsNew(false);\n                        // Refetch versions to get updated unpublish timestamp\n                        await refetchVersions();\n                    }\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.unpublish\"), {\n                        type: \"success\"\n                    });\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    if (!dossier.rootFolderNode) {\n        console.error(\"Root folder node is missing\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid h-full grid-cols-3 gap-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        className: \"font-heading text-heading mb-9 truncate text-5xl\",\n                        children: dossier.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_title__WEBPACK_IMPORTED_MODULE_14__.DossierTitle, {\n                            title: dossier.title,\n                            dossierId: dossier.dossierId,\n                            onTitleUpdate: (updatedDossier)=>setDossier(updatedDossier)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                className: \"font-heading text-heading mb-2 text-lg font-bold\",\n                                children: t(\"category\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                className: \"font-text-regular text-content-lite text-sm\",\n                                children: (dossier === null || dossier === void 0 ? void 0 : (_dossier_category = dossier.category) === null || _dossier_category === void 0 ? void 0 : _dossier_category.label) || \"\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b-highlight mb-10 border-b\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 mt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_image_field__WEBPACK_IMPORTED_MODULE_12__.DossierImageField, {\n                                    dossierId: dossier.dossierId,\n                                    imagePath: (_dossier_decorativeImageNode = dossier.decorativeImageNode) === null || _dossier_decorativeImageNode === void 0 ? void 0 : _dossier_decorativeImageNode.fullName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.FormBuilder, {\n                                    fields: formDefinition,\n                                    onChange: handleFormChange,\n                                    onSubmit: handleSaveForm,\n                                    formId: formId,\n                                    buttons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            dossier.rootFolderNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_file_manager__WEBPACK_IMPORTED_MODULE_11__.DossierFileManager, {\n                                dossierId: dossier.dossierId,\n                                dossierName: dossier.title,\n                                apiRootNode: dossier.rootFolderNode\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_react_ui__WEBPACK_IMPORTED_MODULE_7__.StatusBox, {\n                    editStatus: editStatus,\n                    dossierLiveStatus: dossierLiveStatus,\n                    publishDates: dateFormValues,\n                    pages: pages,\n                    dossierId: dossier.dossierId,\n                    categoryId: dossier.categoryId,\n                    portalUrl: settings.portalUrl,\n                    submitForm: handleDateFormChange,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_status_controls__WEBPACK_IMPORTED_MODULE_13__.DossierStatusControls, {\n                        dossierId: dossier.dossierId,\n                        formId: formId,\n                        disabled: isLoading,\n                        status: status,\n                        handleUnpublishDossier: handleUnpublishDossier,\n                        handleDeleteDossier: handleDeleteDossier,\n                        setIsSaveAndPublish: setIsSaveAndPublish\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, this);\n}\n_s(PageContent, \"/lC7UfztqxrJSzmMMERWsRc0Evk=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations,\n        _iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__.useAppSettings,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__.useClientApi,\n        _iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.useConfirmDialog,\n        _utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__.useDossierFormDefinition\n    ];\n});\n_c = PageContent;\nvar _c;\n$RefreshReg$(_c, \"PageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/_services/dossier-service.client.ts":
/*!*************************************************!*\
  !*** ./app/_services/dossier-service.client.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDossier: function() { return /* binding */ createDossier; },\n/* harmony export */   deleteDossier: function() { return /* binding */ deleteDossier; },\n/* harmony export */   getDossierLatestVersion: function() { return /* binding */ getDossierLatestVersion; },\n/* harmony export */   getDossierVersions: function() { return /* binding */ getDossierVersions; },\n/* harmony export */   publishDossier: function() { return /* binding */ publishDossier; },\n/* harmony export */   unpublishDossier: function() { return /* binding */ unpublishDossier; },\n/* harmony export */   updateDossier: function() { return /* binding */ updateDossier; },\n/* harmony export */   updateDossierImage: function() { return /* binding */ updateDossierImage; },\n/* harmony export */   updateDossierTitle: function() { return /* binding */ updateDossierTitle; }\n/* harmony export */ });\n/* harmony import */ var _http_fetcher_axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/http/fetcher-axios */ \"(app-pages-browser)/./app/_http/fetcher-axios.ts\");\n\nasync function createDossier(clientApi, dossier) {\n    try {\n        return await clientApi.post(\"dossier\", {\n            json: dossier\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function updateDossier(clientApi, updatedDossier) {\n    try {\n        return await clientApi.put(\"dossier\", {\n            json: updatedDossier\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function publishDossier(clientApi, body) {\n    try {\n        return await clientApi.post(\"dossier/publish-dossier\", {\n            json: body\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function deleteDossier(clientApi, body) {\n    try {\n        return await clientApi.delete(\"dossier\", {\n            json: body\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function unpublishDossier(clientApi, body) {\n    try {\n        return await clientApi.post(\"dossier/unpublish-dossier\", {\n            json: body\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function updateDossierTitle(clientApi, body) {\n    try {\n        return await clientApi.put(\"dossier/dossier-title\", {\n            json: body\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function updateDossierImage(apiUrl, id, file, onUploadProgress) {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n    return await (0,_http_fetcher_axios__WEBPACK_IMPORTED_MODULE_0__.put)(\"\".concat(apiUrl, \"/dossier/\").concat(id, \"/image\"), formData, {\n        onUploadProgress,\n        headers: {\n            \"Content-Type\": \"multipart/form-data\"\n        }\n    });\n}\nasync function getDossierLatestVersion(clientApi, dossierId) {\n    return await clientApi.get(\"dossier/\".concat(dossierId, \"/latest-version\")).json();\n}\nasync function getDossierVersions(clientApi, dossierId) {\n    return await clientApi.get(\"dossier/\".concat(dossierId, \"/versions\")).json();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/_services/dossier-service.client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx":
/*!**********************************************************************************!*\
  !*** ../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx ***!
  \**********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DossierViewLink: function() { return /* binding */ DossierViewLink; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nfunction DossierViewLink(param) {\n    let { url, label } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)(\"components.dossierViewLink\");\n    if (url) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            target: \"_blank\",\n            rel: \"noreferrer\",\n            href: url,\n            className: \"text-base-00 font-text-regular underline\",\n            children: label ? label : t(\"view\")\n        }, void 0, false, {\n            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\dossier-view-link\\\\dossier-view-link.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: \"font-text-regular text-base-25 text-sm\",\n        children: t(\"noAssociatedPage\")\n    }, void 0, false, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\dossier-view-link\\\\dossier-view-link.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\n_s(DossierViewLink, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations\n    ];\n});\n_c = DossierViewLink;\nvar _c;\n$RefreshReg$(_c, \"DossierViewLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9saWJzL3JlYWN0LXVpL3NyYy9jb21wb25lbnRzL2Rvc3NpZXItdmlldy1saW5rL2Rvc3NpZXItdmlldy1saW5rLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEM7QUFPckMsU0FBU0MsZ0JBQWdCLEtBQW9DO1FBQXBDLEVBQUVDLEdBQUcsRUFBRUMsS0FBSyxFQUF3QixHQUFwQzs7SUFDOUIsTUFBTUMsSUFBSUosMERBQWVBLENBQUM7SUFFMUIsSUFBSUUsS0FBSztRQUNQLHFCQUNFLDhEQUFDRztZQUFFQyxRQUFPO1lBQVNDLEtBQUk7WUFBYUMsTUFBTU47WUFBS08sV0FBVTtzQkFDdEROLFFBQVFBLFFBQVFDLEVBQUU7Ozs7OztJQUd6QjtJQUNBLHFCQUFPLDhEQUFDTTtRQUFFRCxXQUFVO2tCQUEwQ0wsRUFBRTs7Ozs7O0FBQ2xFO0dBWGdCSDs7UUFDSkQsc0RBQWVBOzs7S0FEWEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL2xpYnMvcmVhY3QtdWkvc3JjL2NvbXBvbmVudHMvZG9zc2llci12aWV3LWxpbmsvZG9zc2llci12aWV3LWxpbmsudHN4PzAxNzIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSAnbmV4dC1pbnRsJztcclxuXHJcbmludGVyZmFjZSBEb3NzaWVyVmlld0xpbmtQcm9wcyB7XHJcbiAgdXJsOiBzdHJpbmc7XHJcbiAgbGFiZWw6IHN0cmluZyB8IG51bGw7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBEb3NzaWVyVmlld0xpbmsoeyB1cmwsIGxhYmVsIH06IERvc3NpZXJWaWV3TGlua1Byb3BzKSB7XHJcbiAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucygnY29tcG9uZW50cy5kb3NzaWVyVmlld0xpbmsnKTtcclxuXHJcbiAgaWYgKHVybCkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGEgdGFyZ2V0PVwiX2JsYW5rXCIgcmVsPVwibm9yZWZlcnJlclwiIGhyZWY9e3VybH0gY2xhc3NOYW1lPVwidGV4dC1iYXNlLTAwIGZvbnQtdGV4dC1yZWd1bGFyIHVuZGVybGluZVwiPlxyXG4gICAgICAgIHtsYWJlbCA/IGxhYmVsIDogdCgndmlldycpfVxyXG4gICAgICA8L2E+XHJcbiAgICApO1xyXG4gIH1cclxuICByZXR1cm4gPHAgY2xhc3NOYW1lPVwiZm9udC10ZXh0LXJlZ3VsYXIgdGV4dC1iYXNlLTI1IHRleHQtc21cIj57dCgnbm9Bc3NvY2lhdGVkUGFnZScpfTwvcD47XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVRyYW5zbGF0aW9ucyIsIkRvc3NpZXJWaWV3TGluayIsInVybCIsImxhYmVsIiwidCIsImEiLCJ0YXJnZXQiLCJyZWwiLCJocmVmIiwiY2xhc3NOYW1lIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../libs/react-ui/src/components/status-box/status-box.tsx":
/*!********************************************************************!*\
  !*** ../../libs/react-ui/src/components/status-box/status-box.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatusBox: function() { return /* binding */ StatusBox; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/../../node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _iprox_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../iprox-ui */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/index.ts\");\n/* harmony import */ var _iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../iprox-ui/components/forms/fields/date-time-picker/date-time-picker */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/components/forms/fields/date-time-picker/date-time-picker.tsx\");\n/* harmony import */ var _dossier_view_link_dossier_view_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dossier-view-link/dossier-view-link */ \"(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst AutoSubmitForm = ()=>{\n    _s();\n    const { values, submitForm } = (0,formik__WEBPACK_IMPORTED_MODULE_1__.useFormikContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (values.fromDate) {\n            submitForm();\n        }\n    }, [\n        values,\n        submitForm\n    ]);\n    return null;\n};\n_s(AutoSubmitForm, \"eluiyIPyaFuYIzYXLcNy1h5e6gs=\", false, function() {\n    return [\n        formik__WEBPACK_IMPORTED_MODULE_1__.useFormikContext\n    ];\n});\n_c = AutoSubmitForm;\nfunction StatusBox(param) {\n    let { children, editStatus, dossierLiveStatus, publishDates, dossierId, categoryId, pages, portalUrl, submitForm } = param;\n    _s1();\n    const format = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useFormatter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"components.statusBox\");\n    const getStatusTranslationKey = (status)=>{\n        switch(status){\n            case \"Published\":\n                return \"lastPublished\";\n            case \"Unpublished\":\n                return \"lastUnpublished\";\n            default:\n                return null;\n        }\n    };\n    const initialValues = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            fromDate: (publishDates === null || publishDates === void 0 ? void 0 : publishDates.fromDate) || new Date(),\n            toDate: (publishDates === null || publishDates === void 0 ? void 0 : publishDates.toDate) || null\n        };\n    }, [\n        publishDates\n    ]);\n    const pageSlug = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const pagesWithCategory = pages.filter((page)=>page.pageState === \"Published\").filter((page)=>{\n            var _page_categories;\n            return (_page_categories = page.categories) === null || _page_categories === void 0 ? void 0 : _page_categories.some((category)=>category.id === categoryId);\n        }).sort((a, b)=>a.categories.length - b.categories.length);\n        return pagesWithCategory === null || pagesWithCategory === void 0 ? void 0 : pagesWithCategory.map((page)=>({\n                label: page.label,\n                slug: page.slug\n            }));\n    }, [\n        categoryId,\n        pages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sticky top-[110px] h-fit max-w-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-input bg-highlight p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-base-00 font-heading mb-2 text-2xl font-semibold\",\n                            children: t(\"dossier\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_1__.Formik, {\n                        initialValues: initialValues,\n                        onSubmit: (values)=>{\n                            submitForm === null || submitForm === void 0 ? void 0 : submitForm(values);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_1__.Form, {\n                            className: \"my-5 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AutoSubmitForm, {}, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__.DateTimePickerField, {\n                                        name: \"fromDate\",\n                                        label: t(\"dossierDate\"),\n                                        fieldType: _iprox_ui__WEBPACK_IMPORTED_MODULE_3__.FieldType.DateTime,\n                                        labelColor: \"text-base-00\",\n                                        descriptionColor: \"text-base-25\",\n                                        validationRules: [],\n                                        popperPlacement: \"left\",\n                                        description: t(\"dossierDateDescription\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__.DateTimePickerField, {\n                                        name: \"toDate\",\n                                        label: t(\"expirationDate\"),\n                                        fieldType: _iprox_ui__WEBPACK_IMPORTED_MODULE_3__.FieldType.DateTime,\n                                        labelColor: \"text-base-00\",\n                                        descriptionColor: \"text-base-25\",\n                                        validationRules: [],\n                                        popperPlacement: \"left\",\n                                        minDate: initialValues.fromDate,\n                                        isClearable: true,\n                                        description: t(\"expirationDateDescription\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"rounded-input bg-highlight mt-6 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-base-00 font-heading mb-2 text-2xl font-semibold\",\n                                children: t(\"title\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base-00 font-text capitalize\",\n                                children: t(editStatus.status)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            editStatus.status === \"modified\" && editStatus.date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base-00 font-text\",\n                                children: [\n                                    format.dateTime(new Date(editStatus.date), {\n                                        dateStyle: \"short\"\n                                    }),\n                                    \" \",\n                                    format.dateTime(new Date(editStatus.date), {\n                                        timeStyle: \"short\"\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            getStatusTranslationKey(dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-base-00 font-heading text-lg font-bold\",\n                                children: t(getStatusTranslationKey(dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.status))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            (dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.date) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base-00 font-text\",\n                                children: [\n                                    format.dateTime(new Date(dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.date), {\n                                        dateStyle: \"short\"\n                                    }),\n                                    \" \",\n                                    format.dateTime(new Date(dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.date), {\n                                        timeStyle: \"short\"\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    editStatus.status === \"published\" || (dossierLiveStatus === null || dossierLiveStatus === void 0 ? void 0 : dossierLiveStatus.status) === \"Published\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-base-00 font-heading text-lg font-bold\",\n                                children: t(\"visibility\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            pageSlug === null || pageSlug === void 0 ? void 0 : pageSlug.map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dossier_view_link_dossier_view_link__WEBPACK_IMPORTED_MODULE_5__.DossierViewLink, {\n                                        url: \"\".concat(portalUrl, \"/\").concat(page.slug, \"/\").concat(dossierId),\n                                        label: page.label\n                                    }, page.slug, false, {\n                                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this) : null\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s1(StatusBox, \"2JmU5KFwTFr6cstnOZ61+Ev0kEA=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useFormatter,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations\n    ];\n});\n_c1 = StatusBox;\nvar _c, _c1;\n$RefreshReg$(_c, \"AutoSubmitForm\");\n$RefreshReg$(_c1, \"StatusBox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../libs/react-ui/src/components/status-box/status-box.tsx\n"));

/***/ })

});
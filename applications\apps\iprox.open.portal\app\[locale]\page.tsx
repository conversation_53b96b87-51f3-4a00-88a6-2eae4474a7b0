import { getHomepageContent } from '@/services/homepage-content-service';
import { getPublicAssets } from '@/services/public-assets-service';
import { getSiteParameters } from '@/services/site-parameters-service';
import { Image } from '@iprox/react-ui';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

import { ContentWrapper } from '@/components/content-wrapper';
import { ContentZones } from '@/components/content-zones';

type Props = {
  params: { locale: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'page' });
  const siteParameters = await getSiteParameters();

  return {
    title: t('homeTitle', {
      tenantTitle: siteParameters?.siteParametersSettings.tenantName,
    }),
  };
}

export default async function Index() {
  const publicAssets = await getPublicAssets();
  const homepageContent = await getHomepageContent();

  return (
    <div className="flex w-full flex-col items-center justify-center">
      {publicAssets?.siteAssets.homePageImage ? (
        <div className="relative h-96 w-full">
          <Image
            src={`${process.env.IPROX_OPEN_API_URL}/asset${publicAssets.siteAssets.homePageImage}`}
            fill
            alt="Home page image"
            className="object-cover"
            blurDataURL={`${process.env.IPROX_OPEN_API_URL}/asset${publicAssets.siteAssets.homePageImage}`}
          />
        </div>
      ) : null}
      <ContentWrapper>
        <div className="py-6 lg:py-12">
          <ContentZones pageZones={homepageContent.homePage.pageZones} />
        </div>
      </ContentWrapper>
    </div>
  );
}

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@storybook/blocks';

<Meta title="Design tokens" />

# Introduction to Design Tokens

Design tokens are a set of pre-defined variables that represent the visual language of a design system. They are used to define and communicate design properties such as colors, typography, spacing, and more. Design tokens provide a centralized source of truth for design properties that can be easily reused across different applications.

# Structure of Design Tokens

Design tokens are structured in three layers: reference, system, and component tokens. Reference tokens define global design properties that are used across the entire design system. System tokens define specific design properties for the system, such as colors, typography or spacing. Component tokens define specific design properties for each component, such as the color of a primary button or the border-radius of cards.

## CSS-variables and Naming Convention

Our design tokens are defined as css-variables. This allows applications to be themed without having to transpile the css. Naming conventions are important for keeping design tokens organized and easy to understand. Each token type has their own naming convention. The naming convention is based on [Material Design system](https://m3.material.io/foundations/design-tokens/how-to-read-tokens)

- **Reference Tokens:** These tokens define global design properties that are used across the entire design system.
- **System Tokens:** These tokens are used to theme the application on a high level.
- **Component Tokens:** These tokens define specific design properties for each component type, such as button, progress bar or card.

## Themes

We develop two default themes. "iprox-admin" and "iprox-site". For iprox.open each tenant should be able to theme their portal using _only_ the system design tokens. Themes consist of reference and system tokens. Components and application code **never** refer to reference tokens directly.

# Reference Tokens

Reference tokens define global design properties that are used across the entire design system. These include palletes, fonts, and other properties that are commonly used throughout the system. Reference tokens are defined in a separate css-variable and can be referenced by system tokens. Reference tokens are raw values without context. They will be named using all lowercase letters, start with `--ref_` with components separated by underscores (\_).

```
:root {
  --ref_color_blue100: #6c757d;
  --ref_border-radius: 4px;
  --ref_font-family: 'Open Sans';
}
```

# System Tokens

System tokens define specific decisions and roles that give the design system its character, from color and typography, to spacing and shape. System tokens are scoped to containers, viewports and other high-level structures. They will be named using all lowercase letters, start with `--sys_` with parts separated by underscores (\_).

```
--sys_color_primary: var(--ref_color_blue100);
--sys_color_content_background: #f3f3f3;
--sys_display-large_font-family: var(--ref_font-family);
--sys_display-small_font-size: 0.875rem;


--sys_color_base_00 : wit
--sys_color_base_05 : lichtergrijs
--sys_color_base_10 : lichtgrijs

--sys_color_base_90 : donkergrijs
--sys_color_base_95 : donkerdergrijs
--sys_color_base_100 : zwart
```

# Component Tokens (currently not implemented)

Component tokens define specific design properties for each instance of a component, such as the color of the primary button. These properties may are filled with references to the system tokens. In extreme theming cases a theme is allowed to directly set the component tokens. Component tokens are defined inside the `component-tokens.scss` file. They will be named using all lowercase letters, start with `--comp_<component>` with properties separated by underscores (\_).

```
--comp_button_primary_background-color: var(--sys_color_primary);
--comp_button_large_padding: var(--sys_large-sizing);
```

```
// button-tokens.scss
$button_primary_background-color: var(--comp_button_primary_background-color);
```

```
@use "./button-tokens";

.btn-primary {
  background-color: button-tokens.$button_primary_background-color,
}
```

# Conclusion

Design tokens provide a powerful tool for defining and communicating the visual language of a design system. By structuring design tokens using reference, system, and component tokens, you can create a centralized source of truth for design properties that can be easily reused across different applications.

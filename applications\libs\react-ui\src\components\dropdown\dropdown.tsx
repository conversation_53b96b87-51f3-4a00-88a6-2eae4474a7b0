import { Menu, Transition } from '@headlessui/react';
import Link from 'next/link';
import { Fragment, ReactNode } from 'react';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export interface DropdownMenuItem {
  label: string;
  icon?: string;
  onClick?: () => void;
  route?: string;
}

export interface DropdownProps {
  menu: DropdownMenuItem[];
  children: ReactNode;
}

export function Dropdown({ children, menu }: DropdownProps) {
  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button className="inline-flex w-full justify-center gap-x-1.5 bg-transparent px-3 py-2">
          {children}
        </Menu.Button>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="bg-base-00 absolute right-0 z-10 w-56 origin-top-right overflow-hidden rounded-md shadow-lg focus:outline-none">
          {menu.map(({ icon, label, route, onClick }) => (
            <Menu.Item key={`menu-item-${label}`}>
              {({ active }) =>
                route ? (
                  <Link
                    href={route}
                    className={classNames(
                      active ? 'bg-highlight text-base-00' : 'text-base-35 bg-transparent',
                      'flex w-full flex-row px-4 py-3 text-left text-sm leading-none'
                    )}
                  >
                    {label}
                  </Link>
                ) : (
                  <button
                    type="button"
                    className={classNames(
                      active ? 'bg-highlight text-base-00' : 'text-base-35 bg-transparent',
                      'flex w-full flex-row px-4 py-3 text-left text-sm leading-none'
                    )}
                    onClick={onClick}
                  >
                    {icon}
                    <span>{label}</span>
                  </button>
                )
              }
            </Menu.Item>
          ))}
        </Menu.Items>
      </Transition>
    </Menu>
  );
}

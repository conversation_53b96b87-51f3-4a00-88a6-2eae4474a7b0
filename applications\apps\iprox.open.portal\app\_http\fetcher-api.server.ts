import ky from 'ky';

export const serverApi = ky.extend({
  prefixUrl: process.env.IPROX_OPEN_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  cache: 'no-store',
});

export const cachedServerApi = ky.extend({
  prefixUrl: process.env.IPROX_OPEN_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  next: {
    revalidate: Number(process.env.CACHE_DURATION) || 600, // default to 10 minutes if CACHE_DURATION is not set
  },
});

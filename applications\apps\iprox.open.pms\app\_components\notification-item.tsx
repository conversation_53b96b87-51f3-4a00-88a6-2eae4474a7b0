import { Notification } from '@/context/notifications-context';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button, Text } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';

interface NotificationItemProps {
  notification: Notification;
  onDismiss: (id: string) => void;
  onAction: (id: string) => void;
}

export function NotificationItem({ notification, onDismiss, onAction }: NotificationItemProps) {
  const t = useTranslations('notifications');

  const renderContent = () => {
    if (notification.error) {
      return (
        <>
          <div className="mb-2.5 flex flex-row items-center">
            <Text className="font-text mr-2">❌</Text>
            <Text className="font-text text-sm font-bold">{notification.title}</Text>
            <XMarkIcon className="ml-auto h-4 w-4" role="button" onClick={() => onDismiss(notification.id)} />
          </div>

          <Text className="font-text mb-4 whitespace-pre-wrap text-base">{notification.description}</Text>

          <div className="flex flex-row justify-center">
            <Button variant="tertiary" onClick={() => onAction(notification.id)}>
              {t('view')}
            </Button>
          </div>
        </>
      );
    }
    if (notification.status === 'queued') {
      return (
        <>
          <div className="mb-2.5 flex flex-row items-center">
            <Text className="font-text mr-2">🕘</Text>
            <Text className="font-text text-sm font-bold">{notification.title}</Text>
          </div>

          <Text className="font-text mb-4 whitespace-pre-wrap text-base">{notification.description}</Text>

          <div className="flex flex-row justify-center">
            <Button variant="tertiary" onClick={() => onAction(notification.id)}>
              {t('view')}
            </Button>
          </div>
        </>
      );
    }
    if (notification.status === 'uploading') {
      return (
        <>
          <div className="mb-2.5 flex flex-row items-center">
            <Text className="font-text mr-2">⬆️</Text>
            <Text className="font-text text-sm font-bold">{notification.title}</Text>
          </div>

          <Text className="font-text mb-3 whitespace-pre-wrap text-base">{notification.description}</Text>

          <div className="border-highlight mb-2.5 h-2 w-full rounded-[40px] border">
            <div className="bg-highlight h-full rounded-[40px]" style={{ width: `${notification.data?.progress}%` }} />
          </div>

          <Text className="font-text text-content-lite mb-4 text-center text-xs">{`(${notification.data?.uploaded}/${notification.data?.total})`}</Text>

          <div className="flex flex-row justify-center">
            <Button variant="tertiary" onClick={() => onAction(notification.id)}>
              {t('view')}
            </Button>
          </div>
        </>
      );
    }
    if (notification.status === 'uploadComplete') {
      return (
        <>
          <div className="mb-2.5 flex flex-row items-center">
            <Text className="font-text mr-2">✅</Text>
            <Text className="font-text text-sm font-bold">{notification.title}</Text>
            <XMarkIcon className="ml-auto h-4 w-4" role="button" onClick={() => onDismiss(notification.id)} />
          </div>

          <Text className="font-text mb-4 whitespace-pre-wrap text-base">{notification.description}</Text>

          <div className="flex flex-row justify-end">
            <Button variant="tertiary" onClick={() => onAction(notification.id)}>
              {t('view')}
            </Button>
          </div>
        </>
      );
    }
  };

  return <div className="rounded-medium bg-base-10 p-5">{renderContent()}</div>;
}

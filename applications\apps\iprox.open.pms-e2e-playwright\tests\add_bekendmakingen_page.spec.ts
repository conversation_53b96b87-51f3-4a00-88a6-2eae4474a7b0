import { Locator, expect, test } from '@playwright/test';

import { DashboardPage } from '../pages/dashboard_page';
import { Menu } from '../pages/menu';
import { BekendmakingenPage } from '../pages/pages_bekendmakingen';
import { PageList } from '../pages/pages_list_page';
import { SearchPage } from '../pages/pages_searchpage';
import { DateTimeFormatter } from '../utils/date_time_formatter';
import { BekendmakingenPageFields, DossierCatergory, ModalPageTypeButton, PageType, Status } from '../utils/enums';

let dashboard: DashboardPage;
let menu: Menu;
let pagelist: PageList;
let searchpage: SearchPage;
let bekendmakingenpage: BekendmakingenPage;

test.beforeEach(async ({ page }) => {
  dashboard = new DashboardPage(page);
  menu = new Menu(page);
  pagelist = new PageList(page);
  searchpage = new SearchPage(page);
  bekendmakingenpage = new BekendmakingenPage(page);
});
test.afterEach(async ({ page }) => {
  await page.close();
});

const newPageTitle = `bekendmakingen-${DateTimeFormatter.getFormattedDateTime()}`;

test(`should create and publish a bekendmakingen page`, async ({ page }) => {
  await dashboard.goToDashboardPage();
  await menu.clickBeheerMenuButton();
  await menu.clickPagesMenuButton();
  await pagelist.clickAddNewPageButton();
  await pagelist.clickModalPageTypeButton(ModalPageTypeButton.AnnouncementsButton);
  await pagelist.inputNewPageTitle(newPageTitle);
  await pagelist.clickNewPageFurthermoreButton();

  await bekendmakingenpage.inputText(BekendmakingenPageFields.OrganizationName, 'Waterschap Vechtstromen');
  await bekendmakingenpage.inputText(BekendmakingenPageFields.OrganizationType, '-');
  await bekendmakingenpage.inputText(BekendmakingenPageFields.PublicationType, 'officielepublicaties');

  await bekendmakingenpage.assertPageStatus(Status.Unpublished);
  await bekendmakingenpage.clickDropdownOptions();
  await bekendmakingenpage.clickDropdownPublishAction();
  await bekendmakingenpage.clickModalConfirmationButton();
  await bekendmakingenpage.assertPublishButtonIsDisabled();
  await bekendmakingenpage.assertPageStatus(Status.Published);
  await menu.clickPagesMenuButton();
  await pagelist.assertPageInList(newPageTitle);
});

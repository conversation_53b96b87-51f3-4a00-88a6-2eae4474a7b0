import { Meta, StoryObj } from '@storybook/react';

import { StatisticsCard } from './statistics-card';

const meta: Meta<typeof StatisticsCard> = {
  title: 'components/statistics-card',
  component: StatisticsCard,
};

export default meta;

type Story = StoryObj<typeof StatisticsCard>;

export const Default: Story = {
  name: 'default',
  args: {
    icon: 'FolderIcon',
    label: 'Number of dossiers',
    value: 20521,
    formatterType: 'filesize',
  },
  render: ({ icon, label, value, formatterType }) => (
    <div className="max-w-xs">
      <StatisticsCard icon={icon} label={label} value={value} formatterType={formatterType} />
    </div>
  ),
};

import { components } from '@/iprox-open.interface';
import { Text } from '@iprox/react-ui';

import { ContentWrapper } from '@/components/content-wrapper';
import { ContentZones } from '@/components/content-zones';
import { PageBreadcrumb } from '@/components/page-breadcrumb';

export const SimplePage = async ({ page }: { page: components['schemas']['SimplePageDto'] }) => {
  return (
    <ContentWrapper>
      <PageBreadcrumb page={page} />
      <div className="flex-1 py-12">
        <Text className="font-heading text-heading mb-4 hyphens-auto break-words text-3xl font-bold">{page.label}</Text>
        <ContentZones pageZones={page.pageZones} />
      </div>
    </ContentWrapper>
  );
};

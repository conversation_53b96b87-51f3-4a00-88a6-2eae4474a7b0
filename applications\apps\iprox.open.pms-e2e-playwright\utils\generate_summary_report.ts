import fs from 'fs/promises';
import path from 'path';

import { generateHtml } from '../utils/html_report_generator';
import { readJsonFiles } from '../utils/lighthouse_json_reader';

export async function generateSummaryReport(outputFolder: string) {
  const jsonData = await readJsonFiles(outputFolder);
  const html = await generateHtml(jsonData || []);

  const directoryPath = path.join(outputFolder);
  const filePath = path.join(directoryPath, '_SummaryReport.html');

  try {
    await fs.mkdir(directoryPath, { recursive: true });
    await fs.writeFile(filePath, html);
    console.log(`Report generated successfully: ${filePath}`);
  } catch (error) {
    console.error('Error writing file:', error);
  }
}

import { $generateNodesFromDOM } from '@lexical/html';
import LexicalComposerContext from '@lexical/react/LexicalComposerContext';
import { $getSelection, COMMAND_PRIORITY_EDITOR, PASTE_COMMAND } from 'lexical';

import { sanitizeHtml } from '../../../utils/html-sanitizer';

export class InvalidClipboardError extends Error {
  constructor(msg: string) {
    super(msg);
    Object.setPrototypeOf(this, InvalidClipboardError.prototype);
  }
}

const PastePlugin = () => {
  const [editor] = LexicalComposerContext.useLexicalComposerContext();

  editor.registerCommand<ClipboardEvent>(
    PASTE_COMMAND,
    (event) => {
      try {
        const htmlText = event.clipboardData?.getData('text/html');

        if (htmlText) {
          const sanitizedHtml = sanitizeHtml(htmlText);

          const parser = new DOMParser();
          const dom = parser.parseFromString(sanitizedHtml, 'text/html');
          const nodes = $generateNodesFromDOM(editor, dom);
          const selection = $getSelection();

          if (selection) {
            selection.insertNodes(nodes);
          }

          return true;
        }
      } catch (e) {
        throw new InvalidClipboardError('Invalid Clipboard Content');
      }

      return false;
    },
    COMMAND_PRIORITY_EDITOR
  );

  return null;
};

export default PastePlugin;

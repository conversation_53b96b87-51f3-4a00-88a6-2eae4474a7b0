import { put } from '@/http/fetcher-axios';
import { AxiosProgressEvent } from 'axios';

const uploadNodeAsset = async (
  url: string,
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
) => {
  const formData = new FormData();
  formData.append('file', file);

  return await put<void, FormData>(url, formData, {
    onUploadProgress,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

export async function updateLogo(
  apiUrl: string,
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<void> {
  return uploadNodeAsset(`${apiUrl}/image-asset/logo`, file, onUploadProgress);
}

export async function updateFavicon(
  apiUrl: string,
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<void> {
  return uploadNodeAsset(`${apiUrl}/image-asset/favicon`, file, onUploadProgress);
}

export async function updateHomePageImage(
  apiUrl: string,
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<void> {
  return uploadNodeAsset(`${apiUrl}/image-asset/home-page-image`, file, onUploadProgress);
}

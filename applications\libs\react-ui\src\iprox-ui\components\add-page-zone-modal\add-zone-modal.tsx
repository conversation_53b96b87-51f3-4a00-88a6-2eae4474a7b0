import { useEffect, useId, useState } from 'react';
import { useTranslations } from 'use-intl';

import { Button } from '../button/button';
import { Modal } from '../modal/modal';
import { Text } from '../text/text';

export type ZoneLayout = '12' | '6-6' | '8-4' | '4-8' | '4-4-4';

const ZONE_LAYOUTS: ZoneLayout[] = ['12', '6-6', '8-4', '4-8', '4-4-4'];

type AddZoneModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onCreateZone: (selectedLayout: ZoneLayout) => void;
};

export function AddZoneModal({ isOpen, onClose, onCreateZone }: AddZoneModalProps) {
  const t = useTranslations('components.AddZoneModal');
  const [disabled, setDisabled] = useState(false);
  const [selectedLayout, setSelectedLayout] = useState<ZoneLayout | undefined>();

  useEffect(() => {
    if (isOpen) {
      setDisabled(false);
    }
  }, [isOpen]);

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="flex flex-col items-center justify-center px-8">
        <div className="grid grid-cols-3 items-center justify-center gap-9">
          {ZONE_LAYOUTS.map((layout: ZoneLayout) => (
            <OptionCard
              key={`column-${layout}`}
              isActive={selectedLayout === layout}
              layout={layout}
              onClick={() => setSelectedLayout(layout)}
            />
          ))}
        </div>
        <Button
          variant="primary"
          type="button"
          className="mt-14"
          onClick={() => {
            setDisabled(true);
            selectedLayout && onCreateZone(selectedLayout);
          }}
          disabled={!selectedLayout || disabled}
        >
          {t('further')}
        </Button>
      </div>
    </Modal>
  );
}

type OptionCardProps = {
  layout: string;
  isActive: boolean;
  onClick: () => void;
};

const OptionCard = ({ layout, isActive, onClick }: OptionCardProps) => {
  const t = useTranslations('components.AddZoneModal');
  const id = useId();

  return (
    <label
      htmlFor={id}
      className={`bg-light-grey rounded-medium hover:bg-base-25 relative flex h-[200px] w-[200px] cursor-pointer flex-col items-center justify-center gap-y-5 border-2 focus-visible:ring-1 ${
        isActive ? 'border-primary-content' : 'border-transparent'
      }`}
      aria-checked={isActive}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === ' ' || e.key === 'Enter') {
          onClick();
        }
      }}
    >
      <input
        id={id}
        type="checkbox"
        checked={isActive}
        className="invisible absolute inset-0 opacity-0"
        onChange={onClick}
      />
      <div className="item-center grid w-full grid-cols-12 justify-center gap-2 px-2">
        {layout.split('-').map((width, index) => (
          <div
            key={`${id}-${index}`}
            className="bg-base-10 grid h-24 rounded-sm"
            style={{ gridColumn: `span ${width} / span ${width}` }}
          >
            <Text className="text-primary-content font-heading place-self-center">{t('column', { count: width })}</Text>
          </div>
        ))}
      </div>
    </label>
  );
};

import { Locator, expect, test } from '@playwright/test';

import { DashboardPage } from '../pages/dashboard_page';
import { Menu } from '../pages/menu';
import { PageList } from '../pages/pages_list_page';
import { SearchPage } from '../pages/pages_searchpage';
import { DateTimeFormatter } from '../utils/date_time_formatter';
import { DossierCatergory, PageType, Status } from '../utils/enums';

let dashboard: DashboardPage;
let menu: Menu;
let pagelist: PageList;
let searchpage: SearchPage;

test.beforeEach(async ({ page }) => {
  dashboard = new DashboardPage(page);
  menu = new Menu(page);
  pagelist = new PageList(page);
  searchpage = new SearchPage(page);
});
test.afterEach(async ({ page }) => {
  await menu.clickLogoutButton();
  await page.close();
});

const newPageTitle = `simplePage-${DateTimeFormatter.getFormattedDateTime()}`;

test(`should create a Simple page and Publish`, async ({ page }) => {
  await dashboard.goToDashboardPage();
  await menu.clickBeheerMenuButton();
  await menu.clickPagesMenuButton();
  await pagelist.clickAddNewPageButton();
  await pagelist.clickModalSimplePageButton();
  await pagelist.inputNewPageTitle(newPageTitle);
  await pagelist.clickNewPageFurthermoreButton();
  await searchpage.assertSimplePageStatus(Status.Unpublished);
  await searchpage.clickDropdownOptions();
  await searchpage.clickDropdownPublishAction();
  await searchpage.clickModalConfirmationButton();
  await searchpage.assertPublishButtonIsDisabled();
  await searchpage.assertSimplePageStatus(Status.Published);
  await menu.clickPagesMenuButton(); // go back to pages list
  await pagelist.assertPageInList(newPageTitle);
});

import { useTranslations } from 'next-intl';

interface DossierViewLinkProps {
  url: string;
  label: string | null;
}

export function DossierViewLink({ url, label }: DossierViewLinkProps) {
  const t = useTranslations('components.dossierViewLink');

  if (url) {
    return (
      <a target="_blank" rel="noreferrer" href={url} className="text-base-00 font-text-regular underline">
        {label ? label : t('view')}
      </a>
    );
  }
  return <p className="font-text-regular text-base-25 text-sm">{t('noAssociatedPage')}</p>;
}

import { expect, test } from '@playwright/test';

import { baseUrl } from '../config';
import { DashboardPage } from '../pages/dashboard_page';
import { EditDossierPage } from '../pages/edit_dossier_page';
import { CreateNewDossierPage } from '../pages/new_dossier_page';
import { DateTimeFormatter } from '../utils/date_time_formatter';

let dashboardPage: DashboardPage;
let newDossierPage: CreateNewDossierPage;
let editDossierPage: EditDossierPage;
let updatedText = 'updated text';

test.beforeEach(async ({ page }) => {
  await page.goto(baseUrl);
  dashboardPage = new DashboardPage(page);
  newDossierPage = new CreateNewDossierPage(page);
  editDossierPage = new EditDossierPage(page);
});

test('Verify the dossier publishing flow - with publish dates', async ({ page }) => {
  test.slow();
  const title = `Playwright dossier with publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;

  // Create new dossier
  await dashboardPage.clickNewDossierButton();

  // create a dossier with publish From To dates.
  await newDossierPage.createNewDossier(title, true);
  await expect(page).toHaveURL(new RegExp('/dossier/.*'));

  // Input dynamic fields
  await editDossierPage.inputDynamicFields();

  // uploadImageFile
  // await editDossierPage.uploadImage('iprox_1.png');

  // save dossier
  await editDossierPage.clickActionButton('Opslaan');

  // click publish
  await editDossierPage.clickActionButton('Publiceren');

  // verify the publish link and publish status
  await editDossierPage.checkStatusBoxStatus('Gepubliceerd');
  await editDossierPage.checkPublishedDossierViewLink();
});

test('Verify the dossier edit for dossiers without publish `From` `To` dates', async ({ page }) => {
  test.slow();
  const title = `Playwright dossier without publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;

  // Create new dossier
  await dashboardPage.clickNewDossierButton();

  // create a dossier with publish From To dates.
  await newDossierPage.createNewDossier(title, false);
  await expect(page).toHaveURL(new RegExp('/dossier/.*'));

  // Input dynamic fields
  await editDossierPage.inputDynamicFields();

  // uploadImageFile
  // await editDossierPage.uploadImage('iprox_1.png');

  // save dossier
  await editDossierPage.clickActionButton('Opslaan');

  // click publish
  await editDossierPage.clickActionButton('Publiceren');

  // verify the publish link and publish status
  await editDossierPage.checkStatusBoxStatus('Gepubliceerd');
  await editDossierPage.checkPublishedDossierViewLink();
});

import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { Button } from '../../iprox-ui';
import { Modal } from '../modal/modal';
import { useConfirmDialog } from './context/confirm-dialog.context';

export function ConfirmDialog() {
  const [disabled, setDisabled] = useState(false);
  const t = useTranslations('components.confirmModal');

  const { state, closeDialog } = useConfirmDialog();

  const handleConfirm = () => {
    setDisabled(true);
    state.onConfirm();
    closeDialog();
  };

  const handleCancel = () => {
    setDisabled(true);
    state.onCancel?.();
    closeDialog();
  };

  useEffect(() => {
    if (state.isOpen) {
      setDisabled(false);
    }
  }, [state.isOpen]);

  return (
    <Modal isOpen={state.isOpen ?? false} onClose={handleCancel}>
      <div className="sm:flex sm:items-start">
        <div className="mt-3 text-center sm:mt-0 sm:text-left">
          <h3 className="font-heading text-heading mb-5 block text-2xl font-bold">{state.title}</h3>
          <p className="font-text text-body block text-lg">{state.message}</p>
        </div>
      </div>
      <div className="mt-4 grid grid-flow-col justify-end gap-2">
        <Button type="button" variant="primary" onClick={handleConfirm} disabled={disabled}>
          {t('confirm')}
        </Button>
        <Button type="button" variant="secondary" onClick={handleCancel} disabled={disabled}>
          {t('cancel')}
        </Button>
      </div>
    </Modal>
  );
}

'use client';

import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { updateSimplePage } from '@/services/page-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { usePageAction } from '@/utils/page-action-utils';
import { pageZoneMapper } from '@/utils/page-zone-mapper';
import { getSortedPageZones, reorderZones } from '@/utils/page-zone-utils';
import {
  Button,
  ButtonGroup,
  FieldType,
  FormBuilder,
  FormSubmitValues,
  PageZone,
  ValidationRuleType,
  showToast,
  useConfirmDialog,
} from '@iprox/iprox-ui';
import { Text } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';

interface SimplePageFormProps {
  page: components['schemas']['SimplePageDto'];
}

export function SimplePageForm({ page }: SimplePageFormProps) {
  const t = useTranslations('pages');
  const clientApi = useClientApi();
  const { showDialog } = useConfirmDialog();
  const { getConfirmationMessage, performPageAction, isActionProcessing } = usePageAction();

  const [isSaving, setIsSaving] = useState(false);
  const [updatedPage, setUpdatedPage] = useState(page);
  const [isSaveAndPublish, setIsSaveAndPublish] = useState(false);

  const formFields = useMemo(() => {
    const minimumLength = 1;
    const maximumLength = 60;

    return [
      {
        name: 'label',
        label: t('title'),
        description: t('titleHelperText', { min: minimumLength, max: maximumLength }),
        fieldType: FieldType.Text,
        value: updatedPage.label,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateStringLength,
            ruleValue: { minimumLength, maximumLength },
          },
        ],
      },
      {
        name: 'slug',
        label: t('slug'),
        description: t('slugHelperText'),
        fieldType: FieldType.Text,
        value: updatedPage.slug,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateStringLength,
            ruleValue: { minimumLength, maximumLength },
          },
        ],
      },
      {
        name: 'pageContent',
        label: t('pageContent'),
        fieldType: FieldType.PageZonesField,
        value: getSortedPageZones(updatedPage.pageZones),
        validationRules: [],
      },
    ];
  }, [t, updatedPage.label, updatedPage.pageZones, updatedPage.slug]);

  const handleSave = async (values: FormSubmitValues, disableToast?: boolean) => {
    try {
      setIsSaving(true);
      const response = await updateSimplePage(clientApi, updatedPage.id, {
        id: null,
        label: typeof values.label === 'string' ? values.label.trim() : '',
        slug: typeof values.slug === 'string' ? values.slug.trim() : '',
        zones: pageZoneMapper(Array.isArray(values.pageContent) ? reorderZones(values.pageContent as PageZone[]) : []),
      });
      if (!disableToast) {
        showToast(t('pageUpdated'), { type: 'success' });
      }
      setUpdatedPage(response.page);
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmit = async (values: FormSubmitValues) => {
    if (isSaveAndPublish) {
      handleAction('publish', values);
    } else {
      await handleSave(values);
    }
  };

  const handleAction = async (action: 'delete' | 'publish' | 'unpublish', formValues?: FormSubmitValues) => {
    showDialog({
      message: getConfirmationMessage(action),
      onConfirm: async () => {
        if (action === 'publish' && formValues) {
          await handleSave(formValues, true);
        }
        const pageData = await performPageAction(updatedPage.id, updatedPage.pageType as 'Simple', action);
        if (pageData) {
          setUpdatedPage(pageData.page as components['schemas']['SimplePageDto']);
        }
      },
    });
  };

  return (
    <div>
      <div className="mb-[72px]">
        <Text className="font-text mb-1 text-xl font-bold">{t('simplePage')}</Text>
        <Text className="font-text text-6xl font-bold">
          {updatedPage.label}
          <Text className="font-heading ml-12 inline text-sm font-bold">
            {updatedPage.pageState === 'Published' ? t('published') : t('unpublished')}
          </Text>
        </Text>
      </div>

      <FormBuilder
        fields={formFields}
        onSubmit={handleSubmit}
        formId="simple-page-form"
        buttons={
          <div className="mb-32 mt-6 flex items-center gap-x-5">
            <Button
              variant="primary"
              type="submit"
              disabled={isSaving}
              onClick={() => {
                setIsSaveAndPublish(false);
              }}
            >
              {t('save')}
            </Button>
            <ButtonGroup
              label={t('action')}
              options={[
                {
                  text: t('delete'),
                  type: 'button',
                  onClick: () => handleAction('delete'),
                },
                {
                  text: t('publish'),
                  type: 'submit',
                  form: 'simple-page-form',
                  onClick: () => {
                    setIsSaveAndPublish(true);
                  },
                  disabled: updatedPage.pageState === 'Published',
                },
                {
                  text: t('hide'),
                  type: 'button',
                  onClick: () => handleAction('unpublish'),
                  disabled: updatedPage.pageState === 'Unpublished',
                },
              ]}
              disabled={isActionProcessing}
            />
          </div>
        }
      />
    </div>
  );
}

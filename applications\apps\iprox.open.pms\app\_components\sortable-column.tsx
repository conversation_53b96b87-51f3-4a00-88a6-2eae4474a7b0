'use client';

import { SortDirection, safeSortDirection } from '@/models/sort-direction.model';
import { ArrowDownIcon, ArrowUpIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

interface SortableColumnProps<T extends string> {
  sortField: T;
  children: React.ReactNode;
}

export function SortableColumn<T extends string>({ children, sortField }: SortableColumnProps<T>) {
  const searchParams = useSearchParams();

  const isCurrentSortField = searchParams.get('sortField') === sortField;
  const currentSortDirection = safeSortDirection(searchParams.get('sortDirection'));

  const getNextSortDirection = (sortDirection: SortDirection): SortDirection => {
    if (sortDirection === 'desc') {
      return 'asc';
    }

    if (sortDirection === 'asc') {
      return null;
    }

    return 'desc';
  };

  const sortParams = () => {
    const nextSortDirection: SortDirection = isCurrentSortField ? getNextSortDirection(currentSortDirection) : 'desc';

    const params = new URLSearchParams(searchParams);

    if (nextSortDirection) {
      params.set('sortField', sortField);
      params.set('sortDirection', nextSortDirection);

      return `/dossier/list?${params.toString()}`;
    }

    params.set('sortField', '');
    params.set('sortDirection', '');

    return `/dossier/list?${params.toString()}`;
  };

  return (
    <Link prefetch={false} className="flex w-fit flex-row" href={sortParams()}>
      {children}
      {isCurrentSortField && currentSortDirection === 'asc' && <ArrowUpIcon className="text-heading ml-2 h-4 w-4" />}
      {isCurrentSortField && currentSortDirection === 'desc' && <ArrowDownIcon className="text-heading ml-2 h-4 w-4" />}
    </Link>
  );
}

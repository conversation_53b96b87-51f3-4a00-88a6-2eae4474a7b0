import { Meta, StoryObj } from '@storybook/react';

import { PublicReviewPeriodContainer } from './public-review-period-container';

const meta: Meta<typeof PublicReviewPeriodContainer> = {
  title: 'components/PublicReviewPeriodContainer',
  component: PublicReviewPeriodContainer,
  argTypes: {
    fromDate: {
      control: 'date',
    },
    toDate: {
      control: 'date',
    },
  },
};

export default meta;

type Story = StoryObj<typeof PublicReviewPeriodContainer>;

export const Default: Story = {
  name: 'default',
  args: {
    fromDate: null,
    toDate: null,
  },
};

export const WithDates: Story = {
  name: 'withDates',
  args: {
    fromDate: new Date('2023-01-01'),
    toDate: new Date('2026-12-31'),
  },
};

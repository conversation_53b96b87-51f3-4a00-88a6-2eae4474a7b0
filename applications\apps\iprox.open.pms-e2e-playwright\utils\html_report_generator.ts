export async function generateHtml(data: any[]) {
  let tableRows = data
    .map(
      (item: { file: string; data: { requestedUrl: any; categories: { accessibility: { score: any } } } }) => `
        <tr>
          <td><a href="${item.data.requestedUrl}">${item.data.requestedUrl}</></td>
          <td><a href="${item.file}">${item.file}</></td>
          <td>${item.data.categories.accessibility.score * 100} %</td>
        </tr>
      `
    )
    .join('');

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Accessibility Scores</title>
      <style>
        table {
          border-collapse: collapse;
          width: 100%;
        }
        th, td {
          border: 1px solid black;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f2f2f2;
        }
      </style>
    </head>
    <body>
      <table>
        <thead>
          <tr>
            <th>Requested URL</th>
            <th>Report</th>
            <th>Accessibility Score(%)</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    </body>
    </html>
  `;
}

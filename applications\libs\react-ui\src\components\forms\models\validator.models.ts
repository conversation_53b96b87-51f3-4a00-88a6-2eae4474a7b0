export type ValidatorFn = (params: { value: unknown }) => ValidationErrors | null;

export declare type ValidationErrors = {
  [key: string]: boolean;
};

export type ObjectValidationErrors = Record<string, ValidationErrors>;

export enum ValidationRuleType {
  ValidateGuid = 'ValidateGuid',
  RequiredProperty = 'RequiredProperty',
  ValidateNumberRange = 'ValidateNumberRange',
  ValidateStringLength = 'ValidateStringLength',
  ValidateDate = 'ValidateDate',
  ValidatePastDate = 'ValidatePastDate', // RequiredPastDate?
  ValidateFutureDate = 'ValidateFutureDate',
  ValidateDisallowedCharacters = 'ValidateDisallowedCharacters',
}

export type ValidationMessageTypes =
  | 'required'
  | 'pastDate'
  | 'futureDate'
  | 'numberRange'
  | 'stringRangeLength'
  | 'stringMaxLength';

export interface ValidationRule<T extends ValidationRuleType | unknown = unknown> {
  ruleType: T;
  ruleValue: unknown;
}
export interface StringLengthRuleValue extends ValidationRule<ValidationRuleType.ValidateStringLength> {
  ruleValue: { minimumLength: number | string; maximumLength: number | string };
}

export interface NumberRangeRuleValue extends ValidationRule<ValidationRuleType.ValidateStringLength> {
  ruleValue: { minimumValue: number | string; maximumValue: number | string };
}

export type ValidatorFnFactory<T> = (ruleValue: T) => ValidatorFn;

export type ValidatorSchema = Record<string, ValidatorFn[]>;

import { fetcher } from '@/http/fetcher-public.server';
import { BekendmakingenApiResponse } from '@/models/announcements.api-models';
import xml2js from 'xml2js';

export const fetchAnnouncements = async (
  start: string,
  count: string,
  query: string,
  filter: string,
  sort: string
): Promise<BekendmakingenApiResponse | null> => {
  // Split the query into words, keeping quoted text blocks as a whole
  const queryWords = query.match(/"([^"]+)"|\S+/g) || [];

  // Step 2: Sanitize each word by removing quotes that are not at both the start and the end
  const sanitizedWords = queryWords.map((word) => {
    if (word.startsWith('"') && word.endsWith('"')) {
      return word;
    }
    return word.replace(/"/g, '');
  });

  const updatedQuery =
    queryWords.length > 0 ? `${filter} and cql.textAndIndexes=${sanitizedWords.join(' and ')}` : filter;

  try {
    const response = await fetcher.get(
      `https://repository.overheid.nl/sru?startRecord=${start}&maximumRecords=${count}&query=(${updatedQuery}) ${sort}`
    );
    const responseText = await response.text();
    return await xml2js.parseStringPromise(responseText, {
      tagNameProcessors: [xml2js.processors.stripPrefix],
      explicitArray: false,
    });
  } catch (error) {
    console.error('Error fetching announcements:', error);
    return null;
  }
};

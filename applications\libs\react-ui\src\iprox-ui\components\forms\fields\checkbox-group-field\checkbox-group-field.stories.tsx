import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { CheckboxGroup } from './checkbox-group-field';

const Story: Meta<typeof CheckboxGroup> = {
  component: CheckboxGroup,
  title: 'iprox-ui/forms/fields/checkboxgroup',
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default Story;

export const Base: StoryObj = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'checkbox--field',
    options: [
      {
        label: 'Label 01',
        value: 'label-1',
      },
      {
        label: 'Label 02',
        value: 'label-2',
      },
      {
        label: 'Label 03',
        value: 'label-3',
      },
    ],
    fieldType: FieldType.CheckboxGroup,
  },
};

'use client';

import { components } from '@/iprox-open.interface';
import { uploadSingleFile, uploadZipToDossier } from '@/services/file-structure-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { useTaskQueue } from '@/utils/task-queue.hook';
import { FileWithDisallowed } from '@iprox/iprox-ui';
import { calculateUploadProgress, showToast } from '@iprox/react-ui';
import { useAppSettings } from '@iprox/shared-context';
import { AxiosProgressEvent } from 'axios';
import { throttle } from 'lodash';
import React, { createContext, useState } from 'react';
import { v4 as uuid } from 'uuid';

interface DossierUploadProviderContext {
  uploadZip: (dossierId: string, dossierName: string, file: File, callbackFunc: () => void) => void;
  uploadFile: (
    uploadId: string,
    dossierName: string,
    file: FileWithDisallowed,
    callbackFunc: () => void,
    folderId?: string
  ) => void;
  clearUpload: (uploadId: string) => void;
  resetNotUploadedFiles: (uploadId: string) => void;
  uploadState: Record<string, UploadState>;
  startStopProcess: (start: boolean) => void;
}

export const DossierUploadContext = createContext<DossierUploadProviderContext>({
  uploadZip: () => null,
  uploadFile: () => null,
  resetNotUploadedFiles: () => null,
  clearUpload: () => null,
  uploadState: {},
  startStopProcess: () => null,
});

interface DossierUploadProviderProps {
  children: React.ReactNode;
}

export interface UploadState {
  status: 'queued' | 'uploading' | 'uploadComplete';
  fileName: string;
  fileSize: number;
  loaded?: number;
  estimated?: number;
  progress: number;
  uploadResponse: components['schemas']['UploadDossierZipResponse'] | null;
  dossierName: string;
  dossierId: string;
  isZipUpload?: boolean;
  disallowed?: boolean;
  error?: string;
}

export const DossierUploadProvider = ({ children }: DossierUploadProviderProps) => {
  const { apiUrl } = useAppSettings();

  const [uploadState, setUploadState] = useState<Record<string, UploadState>>({});
  const [shouldProcess, setShouldProcess] = useState(false);
  const { addTask, removeTask } = useTaskQueue({ shouldProcess, maxProcessingCount: 3 });

  const updateUploadProgress = (uploadId: string, progress: number, loaded = 0, estimated = 0) => {
    setUploadState((prevState) => ({
      ...prevState,
      [uploadId]: {
        ...(prevState[uploadId] as UploadState),
        progress,
        estimated,
        loaded,
      },
    }));
  };

  const updateUploadStateThrottled = throttle((uploadId: string, progressEvent: AxiosProgressEvent) => {
    const percentage = calculateUploadProgress(progressEvent);
    updateUploadProgress(uploadId, percentage, progressEvent.loaded, progressEvent.estimated);
  }, 1000);

  const uploadZip = (dossierId: string, dossierName: string, file: File, callbackFn: () => void) => {
    const uploadId = `${dossierId}||${uuid()}`;

    setUploadState((prevState) => ({
      ...prevState,
      [uploadId]: {
        status: 'queued', // Replace uploading and uploadComplete with status property. And this is status: 'queued' | 'uploading' | 'uploadComplete';
        progress: 0,
        fileName: file.name,
        fileSize: file.size,
        uploadResponse: null,
        dossierName,
        dossierId,
        isZipUpload: true,
      },
    }));

    addTask(uploadId, async () => {
      setUploadState((prevState) => ({
        ...prevState,
        [uploadId]: {
          status: 'uploading',
          progress: 0,
          fileName: file.name,
          fileSize: file.size,
          uploadResponse: null,
          dossierName,
          dossierId,
          isZipUpload: true,
        },
      }));

      try {
        const response = await uploadZipToDossier(apiUrl, dossierId, file, (progressEvent) =>
          updateUploadStateThrottled(uploadId, progressEvent)
        );

        setUploadState((prevState_1) => ({
          ...prevState_1,
          [uploadId]: {
            ...(prevState_1[uploadId] as UploadState),
            uploadResponse: response,
          },
        }));

        callbackFn();
      } catch (error) {
        const errorMessages = await getErrorMessages(error);

        showToast(errorMessages, { type: 'error' });
      } finally {
        setUploadState((prevState_2) => ({
          ...prevState_2,
          [uploadId]: {
            ...(prevState_2[uploadId] as UploadState),
            status: 'uploadComplete',
          },
        }));
      }
    });
  };

  const uploadFile = (
    dossierId: string,
    dossierName: string,
    file: FileWithDisallowed,
    callbackFn: () => void,
    folderId?: string
  ) => {
    const uploadId = `${dossierId}||${uuid()}`;
    setUploadState((prevState) => ({
      ...prevState,
      [uploadId]: {
        status: 'queued',
        progress: 0,
        fileName: file.name,
        fileSize: file.size,
        uploadResponse: null,
        dossierName,
        dossierId,
        isZipUpload: false,
        disallowed: file.disallowed,
      },
    }));

    addTask(uploadId, async () => {
      setUploadState((prevState) => ({
        ...prevState,
        [uploadId]: {
          status: 'uploading',
          progress: 0,
          fileName: file.name,
          fileSize: file.size,
          uploadResponse: null,
          dossierName,
          dossierId,
          isZipUpload: false,
        },
      }));

      try {
        await uploadSingleFile(
          apiUrl,
          dossierId,
          file,
          (progressEvent) => updateUploadStateThrottled(uploadId, progressEvent),
          folderId
        );
        setUploadState((prevState) => ({
          ...prevState,
          [uploadId]: {
            ...(prevState[uploadId] as UploadState),
            status: 'uploadComplete',
            uploadResponse: null,
          },
        }));
        callbackFn();
      } catch (error) {
        const errorMessages = await getErrorMessages(error);

        setUploadState((prevState) => ({
          ...prevState,
          [uploadId]: {
            ...(prevState[uploadId] as UploadState),
            status: 'uploadComplete',
            error: typeof errorMessages === 'string' ? errorMessages : errorMessages[0],
          },
        }));

        showToast(errorMessages, { type: 'error' });
      } finally {
        setUploadState((prevState) => ({
          ...prevState,
          [uploadId]: {
            ...(prevState[uploadId] as UploadState),
            status: 'uploadComplete',
          },
        }));
      }
    });
  };

  const clearUpload = (key: string) => {
    setUploadState((prevState) => {
      const newState = { ...prevState };
      delete newState[key];
      return newState;
    });
    removeTask(key);
  };

  const resetNotUploadedFiles = (uploadId: string) => {
    setUploadState((prevState) => {
      const dossierState = prevState[uploadId];
      return dossierState
        ? ({
            ...prevState,
            [uploadId]: {
              ...dossierState,
              uploadResponse: {
                ...dossierState.uploadResponse,
                notUploadedFiles: [],
              },
            },
          } as Record<string, UploadState>)
        : prevState;
    });
  };

  const startStopProcess = (start: boolean) => {
    setShouldProcess(start);
  };

  return (
    <DossierUploadContext.Provider
      value={{ uploadZip, uploadFile, uploadState, startStopProcess, resetNotUploadedFiles, clearUpload }}
    >
      {children}
    </DossierUploadContext.Provider>
  );
};

interface DossierUploadServiceProps {
  uploadZip: (dossierName: string, file: File, callbackFunc: () => void) => void;
  uploadFiles: (files: FileWithDisallowed[], callbackFunc: () => void, folderId?: string) => void;
  resetNotUploadedFiles: (uploadId: string) => void;
  clearUpload: (key: string) => void;
  dossierUploads: { [uploadId: string]: UploadState }[];
  startStopProcess: (start: boolean) => void;
}

/** Use this in the in the dossier file manager to get only the dossier realated uploads. */
export function useDossierUploadService(dossierId: string): DossierUploadServiceProps {
  const context = React.useContext(DossierUploadContext);

  if (context === undefined) {
    throw new Error('useDossierUpload must be used within a DossierUploadProvider');
  }

  return {
    uploadZip: (dossierName, file, callback) => {
      context.uploadZip(dossierId, dossierName, file, callback);
    },
    uploadFiles: (files, callback, folderId) => {
      files.forEach((file) => context.uploadFile(dossierId, file.name, file, callback, folderId));
    },
    resetNotUploadedFiles: (uploadId) => {
      context.resetNotUploadedFiles(uploadId);
    },
    clearUpload: (key: string) => {
      context.clearUpload(key);
    },
    startStopProcess: (start: boolean) => {
      context.startStopProcess(start);
    },
    dossierUploads: Object.entries(context.uploadState)
      .filter(([_key, value]) => value.dossierId === dossierId)
      .map(([key, value]) => ({ [key]: value })),
    // And then map this to something that's useful. If array > 0 then you should not show the Upload buttons anymore in the interface of the dossier. And after clearing it array is empty again.
  };
}

interface UploadServiceProps {
  uploads: { key: string; value: UploadState | null }[];
  clearUpload: (key: string) => void;
}

/** Use this in the navigationtray to show all uploads. */
export function useUploadState(): UploadServiceProps {
  const context = React.useContext(DossierUploadContext);

  if (context === undefined) {
    throw new Error('useUploadState must be used within a DossierUploadProvider');
  }

  return {
    uploads: Object.entries(context.uploadState).map(([key, value]) => ({ key, value })),
    clearUpload: (key: string) => {
      context.clearUpload(key);
    },
  };
}

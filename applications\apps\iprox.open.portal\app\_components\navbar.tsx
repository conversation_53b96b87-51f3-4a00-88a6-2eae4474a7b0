'use client';

import { useAppSettings } from '@/services/settings.context';
import { getCurrentRoute } from '@/utils/get-current-route';
import { Disclosure } from '@headlessui/react';
import { Bars3Icon, ChevronDownIcon, ChevronUpIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Image, Text } from '@iprox/react-ui';
import cx from 'classnames';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslations } from 'use-intl';

import { NavbarDropdown } from './navbar-dropdown';

const MAX_LOGO_WIDTH = 284;
const MARGIN_RIGHT = 36;

export interface NavOptions {
  name: string;
  href: string;
  children: NavOptions[];
}
export interface NavbarProps {
  logoAssetPath: string;
  navOptions: NavOptions[];
}

export function Navbar({ navOptions, logoAssetPath }: NavbarProps) {
  const t = useTranslations('navbar');

  const { apiUrl } = useAppSettings();
  const pathname = usePathname();
  const [openSubmenu, setOpenSubmenu] = useState<string | undefined>(undefined);
  const navRef = useRef<HTMLDivElement>(null);
  const [visibleNavOptions, setVisibleNavOptions] = useState<NavOptions[]>([]);
  const [hiddenNavOptions, setHiddenNavOptions] = useState<NavOptions[]>([]);

  const calculateLinkWidth = (link: NavOptions) => {
    const tempLink = document.createElement('div');
    tempLink.style.position = 'absolute';
    tempLink.style.visibility = 'hidden';
    tempLink.style.whiteSpace = 'nowrap';
    tempLink.style.fontSize = '1.125rem';
    tempLink.innerText = link.name;

    document.body.appendChild(tempLink);
    const linkWidth = tempLink.offsetWidth;
    document.body.removeChild(tempLink);

    return linkWidth + MARGIN_RIGHT;
  };

  const calculateVisibleLinksCount = useCallback((links: NavOptions[], maxWidth: number) => {
    let totalWidth = 0;
    let visibleLinksCount = 0;

    for (let i = 0; i < links.length; i++) {
      const calculatedLinkWidth = calculateLinkWidth(links[i]);
      const linkWidth = calculatedLinkWidth;

      if (totalWidth + linkWidth >= maxWidth) {
        break;
      } else {
        totalWidth += linkWidth;
        visibleLinksCount++;
      }
    }

    return visibleLinksCount;
  }, []);

  const checkForOverflow = useCallback(() => {
    if (navRef.current) {
      const navBarWidth = navRef.current.offsetWidth - MAX_LOGO_WIDTH;
      const navOptionsWithoutOther = navOptions.filter((navOption) => navOption.name !== 'Other');

      const visibleLinksCount = calculateVisibleLinksCount(navOptionsWithoutOther, navBarWidth);

      const isOverflow = navOptions.length > visibleLinksCount;

      setVisibleNavOptions(navOptionsWithoutOther.slice(0, !isOverflow ? visibleLinksCount : visibleLinksCount - 1));
      setHiddenNavOptions(navOptionsWithoutOther.slice(!isOverflow ? visibleLinksCount : visibleLinksCount - 1));
    }
  }, [calculateVisibleLinksCount, navOptions]);

  useEffect(() => {
    checkForOverflow();
    window.addEventListener('resize', checkForOverflow);

    return () => {
      window.removeEventListener('resize', checkForOverflow);
    };
  }, [checkForOverflow]);

  return (
    <Disclosure
      as="nav"
      className="bg-navigation-background shadow-navbar h-navbar-sm lg:h-navbar lg:min-h-navbar fixed left-0 right-0 top-0 z-10 w-screen lg:relative lg:w-full"
    >
      {({ open, close }) => {
        return (
          <>
            <div className="relative mx-auto flex h-full justify-center px-4 sm:px-6 lg:px-16">
              <div
                ref={navRef}
                className="max-w-content flex h-full w-full items-center justify-between lg:justify-start"
              >
                <div className="relative flex flex-shrink-0 items-center lg:mr-16">
                  <Link href="/" className="w-logo max-w-logo-sm lg:max-w-logo h-navbar-sm lg:h-navbar relative">
                    <Image
                      src={`${apiUrl}/asset${logoAssetPath}`}
                      fill
                      width={0}
                      height={0}
                      alt="logo"
                      sizes="(max-width: 640px) 100vw, 300px"
                      className="h-auto object-contain"
                      blurDataURL={`${apiUrl}/asset${logoAssetPath}`}
                    />
                  </Link>
                </div>
                <div className="hidden h-full lg:flex lg:space-x-8">
                  <ul className="flex h-full flex-col items-center gap-y-6 lg:flex-row lg:gap-y-4">
                    {visibleNavOptions.map((navOption, index) => {
                      const isCurrent = getCurrentRoute(pathname) === navOption.href;
                      return (
                        <li className="group relative h-full lg:mb-0 lg:mr-9" key={index}>
                          {navOption.children.length > 0 ? (
                            <NavbarDropdown options={navOption.children}>
                              <span className="sr-only">Open more navigation</span>
                              {navOption.href ? (
                                <Link
                                  tabIndex={-1}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                  }}
                                  href={navOption.href}
                                  className={cx(
                                    'font-text nav-link text-navigation-text relative z-[1] flex h-full items-center whitespace-nowrap align-middle text-lg font-bold after:scale-x-0',
                                    {
                                      'after:border-b-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-b-4 after:transition-transform hover:after:scale-x-100':
                                        isCurrent,
                                    }
                                  )}
                                >
                                  {navOption.name}
                                </Link>
                              ) : (
                                <Text
                                  className={cx(
                                    'font-text nav-link text-navigation-text relative flex h-full items-center whitespace-nowrap align-middle text-lg font-bold after:scale-x-0',
                                    {
                                      'after:border-b-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-b-4 after:transition-transform hover:after:scale-x-100':
                                        isCurrent,
                                    }
                                  )}
                                >
                                  {navOption.name}
                                </Text>
                              )}
                              <ChevronDownIcon className="ml-1 mt-[2px] h-5 w-5" aria-hidden="true" />
                            </NavbarDropdown>
                          ) : (
                            <Link
                              href={navOption.href}
                              className={cx(
                                'font-text nav-link text-navigation-text relative flex h-full items-center whitespace-nowrap align-middle text-lg font-bold after:scale-x-0',
                                {
                                  'after:border-b-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-b-4 after:transition-transform hover:after:scale-x-100':
                                    isCurrent,
                                }
                              )}
                            >
                              {navOption.name}
                            </Link>
                          )}
                        </li>
                      );
                    })}
                    {hiddenNavOptions.length > 0 && (
                      <li className="h-full lg:mb-0">
                        <NavbarDropdown options={hiddenNavOptions} dropDownSide="right">
                          <span className="absolute -inset-1.5" />
                          <span className="sr-only">Open more navigation</span>
                          {t('more')}
                          <ChevronDownIcon className="ml-1 mt-[2px] h-5 w-5" aria-hidden="true" />
                        </NavbarDropdown>
                      </li>
                    )}
                  </ul>
                </div>
                <div className="-mr-2 flex items-center lg:hidden">
                  {/* Mobile menu button */}
                  <Disclosure.Button className="relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500">
                    <span className="absolute -inset-0.5" />
                    <span className="sr-only">Open main menu</span>
                    {open ? (
                      <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                    ) : (
                      <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                    )}
                  </Disclosure.Button>
                </div>
              </div>
            </div>

            {/* Mobile menu */}
            <Disclosure.Panel className="bg-navigation-background shadow-navbar-side-panel absolute left-0 right-[10%] z-10 h-[calc(100vh-theme(height.navbar-sm))] overflow-y-auto overflow-x-hidden overscroll-none md:right-[60%] lg:hidden">
              <div className="space-y-1 pb-3 pt-2">
                {
                  <ul className="flex h-full flex-col lg:flex-row lg:gap-y-4">
                    {navOptions.map((navOption, index) => {
                      const isCurrent = getCurrentRoute(pathname) === navOption.href;
                      return (
                        <li className="h-full lg:mb-0 lg:mr-9" key={index}>
                          <div className="flex w-full items-center justify-between">
                            {navOption.href ? (
                              <Link
                                href={navOption.href}
                                onClick={() => close()}
                                className={cx(
                                  'font-text text-navigation-text relative block overflow-hidden text-ellipsis whitespace-nowrap border-l-4 py-2 pl-3 pr-4 text-lg font-medium after:scale-0',
                                  {
                                    'border-l-primary after:border-l-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-l-4 after:transition-transform hover:after:scale-x-100':
                                      isCurrent,
                                  },
                                  {
                                    'border-l-transparent': !isCurrent,
                                  }
                                )}
                              >
                                {navOption.name}
                              </Link>
                            ) : (
                              <Text
                                className={cx(
                                  'font-text text-navigation-text relative block overflow-hidden text-ellipsis whitespace-nowrap border-l-4 py-2 pl-3 pr-4 text-lg font-medium after:scale-0',
                                  {
                                    'border-l-primary after:border-l-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-l-4 after:transition-transform hover:after:scale-x-100':
                                      isCurrent,
                                  },
                                  {
                                    'border-l-transparent': !isCurrent,
                                  }
                                )}
                              >
                                {navOption.name}
                              </Text>
                            )}
                            {navOption.children.length > 0 && (
                              <button
                                type="button"
                                className="flex items-center justify-center bg-transparent p-2"
                                onClick={() =>
                                  setOpenSubmenu(openSubmenu !== navOption.name ? navOption.name : undefined)
                                }
                              >
                                {openSubmenu !== navOption.name ? (
                                  <ChevronDownIcon className="h-5 w-5" aria-hidden="true" />
                                ) : (
                                  <ChevronUpIcon className="h-5 w-5" aria-hidden="true" />
                                )}
                              </button>
                            )}
                          </div>
                          {openSubmenu === navOption.name && (
                            <div className="bg-base-15 pl-3">
                              {navOption.children.map((child, index) => (
                                <Link
                                  key={`${child.name}-${index}`}
                                  href={child.href}
                                  onClick={() => close()}
                                  className={cx(
                                    'font-text text-navigation-text relative block overflow-hidden text-ellipsis whitespace-nowrap border-l-4 py-2 pl-3 pr-4 text-lg font-medium after:scale-0',
                                    {
                                      'border-l-primary after:border-l-primary after:absolute after:inset-0 after:block after:scale-x-100 after:border-l-4 after:transition-transform hover:after:scale-x-100':
                                        isCurrent,
                                    },
                                    {
                                      'border-l-transparent': !isCurrent,
                                    }
                                  )}
                                >
                                  {child.name}
                                </Link>
                              ))}
                            </div>
                          )}
                        </li>
                      );
                    })}
                  </ul>
                }
              </div>
            </Disclosure.Panel>
          </>
        );
      }}
    </Disclosure>
  );
}

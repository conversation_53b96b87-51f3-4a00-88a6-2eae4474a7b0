import { addClassNamesToElement } from '@lexical/utils';
import type {
  DOMConversionMap,
  DOMConversionOutput,
  EditorConfig,
  LexicalCommand,
  LexicalNode,
  NodeKey,
  RangeSelection,
  SerializedElementNode,
  SerializedLexicalNode,
} from 'lexical';
import { $createTextNode, $getNodeByKey, $getSelection, $isElementNode, ElementNode, createCommand } from 'lexical';

import { sanitizeUrl } from '../utils/url';

interface SerializedLinkNode extends SerializedElementNode<SerializedLexicalNode> {
  link?: string;
}

export class LinkNode extends ElementNode {
  __url: string;
  __target?: string;
  __rel?: string;

  static portalUrl: string;

  static getType(): string {
    return 'link';
  }

  static clone(node: LinkNode): LinkNode {
    return new LinkNode(node.__url, node.__key, node.__target, node.__rel);
  }

  constructor(url: string, key?: Node<PERSON><PERSON>, target?: string, rel?: string) {
    super(key);
    this.__url = url;
    this.__target = target;
    this.__rel = rel;
  }

  createDOM(config: EditorConfig): HTMLElement {
    const element = document.createElement('a');
    element.href = this.__url;

    if (this.__target) {
      element.target = this.__target;
    }

    if (this.__rel) {
      element.rel = this.__rel;
    }

    if (this.__title) {
      element.innerText = this.__title;
    }

    addClassNamesToElement(element, config.theme.link);
    return element;
  }

  updateDOM(prevNode: LinkNode, dom: HTMLElement): boolean {
    if (dom instanceof HTMLAnchorElement) {
      const anchor: HTMLAnchorElement = dom;
      const url = this.__url;
      const target = this.__target;

      if (url !== prevNode.__url) {
        anchor.href = url;
      }

      if (target) {
        anchor.target = target;
      } else {
        anchor.removeAttribute('target');
      }
    }

    return false;
  }

  static importDOM(): DOMConversionMap | null {
    return {
      a: () => ({
        conversion: convertAnchorElement,
        priority: 1,
      }),
    };
  }

  static importJSON(jsonNode: SerializedLinkNode): LinkNode {
    const node = $createLinkNode(jsonNode.link || '');
    node.setFormat(jsonNode.format);
    node.setIndent(jsonNode.indent);
    node.setDirection(jsonNode.direction);
    return node;
  }

  exportJSON(): SerializedLinkNode {
    return {
      ...super.exportJSON(),
      type: 'link',
      version: 1,
      link: this.__url,
    };
  }

  getURL(): string {
    return this.getLatest().__url;
  }

  setURL(url: string): void {
    const writable = this.getWritable();
    writable.__url = url;
  }

  getTarget(): string | undefined {
    return this.getLatest().__target;
  }

  setTarget(target: string): void {
    const writable = this.getWritable();
    writable.__target = target ?? undefined;
  }

  insertNewAfter(selection: RangeSelection): null | ElementNode {
    const element = this.getParentOrThrow().insertNewAfter(selection);
    if ($isElementNode(element)) {
      const linkNode = $createLinkNode(this.__url);
      element.append(linkNode);
      return linkNode;
    }
    return null;
  }

  canInsertTextBefore(): false {
    return false;
  }

  canInsertTextAfter(): false {
    return false;
  }

  canBeEmpty(): boolean {
    return false;
  }

  isInline(): true {
    return true;
  }

  static setConfig({ portalUrl }: { portalUrl: string }) {
    LinkNode.portalUrl = portalUrl;
  }
}

function convertAnchorElement(domNode: Node): DOMConversionOutput {
  let node = null;
  if (domNode instanceof HTMLAnchorElement) {
    if (sanitizeUrl(domNode.href, LinkNode.portalUrl)) {
      node = $createLinkNode(domNode.href);
    } else {
      node = $createLinkNode('');
    }
  }
  return { node };
}

export function $createLinkNode(url: string, target?: string, rel?: string): LinkNode {
  return new LinkNode(url, undefined, target, rel);
}

export function $isLinkNode(node?: LexicalNode): boolean {
  return node instanceof LinkNode;
}

export type LinkAttributes = {
  rel?: string;
  target?: string;
  title?: null | string;
};

export interface ToggleLinkParams extends LinkAttributes {
  url: string;
}

export interface ChangeLinkParams extends ToggleLinkParams {
  nodeKey: string;
}

export interface RemoveLinkParams {
  nodeKey: string;
}

export const TOGGLE_LINK_COMMAND: LexicalCommand<ToggleLinkParams> = createCommand();
export const CHANGE_LINK_COMMAND: LexicalCommand<ChangeLinkParams> = createCommand();
export const REMOVE_LINK_COMMAND: LexicalCommand<RemoveLinkParams> = createCommand();

export function toggleLink({ url, title, target, rel }: ToggleLinkParams) {
  if (url && title) {
    const linkItem = $createLinkNode(url, target, rel);
    const textNode = $createTextNode(title);

    linkItem.append(textNode);

    const selection = $getSelection();

    if (selection) {
      selection.insertNodes([$createTextNode(''), linkItem, $createTextNode('')]);
    }

    $getSelection();
  }

  return null;
}

export function editLink(props: ChangeLinkParams) {
  const linkNode = $getNodeByKey(props.nodeKey) as LinkNode;
  linkNode.setURL(props.url);
  linkNode.setTarget(props.target ?? '');

  const [textNode] = linkNode.getAllTextNodes();
  textNode.setTextContent(props.title || '');
}

export function removeLink(props: RemoveLinkParams) {
  const linkNode = $getNodeByKey(props.nodeKey) as LinkNode;
  const [textNode] = linkNode.getAllTextNodes();
  linkNode.remove();

  const selection = $getSelection();

  if (selection) {
    selection.insertNodes([textNode]);
  }

  $getSelection();
}

import ChevronDownIcon from '@heroicons/react/24/outline/ChevronDownIcon';
import { useMemo } from 'react';

import { Image } from '../../image/image';

interface UserPorps {
  displayName: string;
  avatar?: string;
}

export function User({ displayName, avatar }: UserPorps) {
  const initials = useMemo(() => {
    const names = displayName.split(' ');

    if (names.length > 1) {
      return names[0].substring(0, 1).toUpperCase() + names[names.length - 1].substring(0, 1).toUpperCase();
    }

    return names[0].substring(0, 2).toUpperCase();
  }, [displayName]);

  return (
    <div className="flex">
      {avatar ? (
        <Image className="rounded-full bg-gray-50" src={avatar} alt="avatar" height={36} width={36} />
      ) : (
        <div className="bg-base-100 relative inline-flex h-9 w-9 items-center justify-center overflow-hidden rounded-full">
          <span className="text-base-10 font-medium text-gray-600">{initials}</span>
        </div>
      )}
      <span className="hidden lg:flex lg:items-center">
        <span className="ml-3 text-sm font-semibold leading-6 text-gray-900">{displayName}</span>
        <ChevronDownIcon className="ml-1.5 mt-0.5 h-4 w-4 text-gray-400" aria-hidden="true" />
      </span>
    </div>
  );
}

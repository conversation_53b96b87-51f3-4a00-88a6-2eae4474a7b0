import { getDossier } from '@/services/dossier-service';
import { getPage } from '@/services/page-service';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';

import { DossierInfo } from '@/components/dossier-info';
import { PageBreadcrumb } from '@/components/page-breadcrumb';

type Props = {
  params: {
    page: string;
    id: string;
    locale: string;
  };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'page' });
  const data = await getDossier(params.id);

  return {
    title: t('dossierTitle', {
      dossierTitle: data?.dossier?.title ?? '',
    }),
    alternates: {
      canonical: `${process.env.BASE_URL}/dossier/${params.id}`,
    },
  };
}

export default async function Page({ params }: Props) {
  const dossierData = await getDossier(params.id);
  const pageData = await getPage(params.page);

  if (!dossierData) {
    notFound();
  }

  return (
    <div className="max-w-content flex w-full flex-col justify-center px-4 md:px-8 xl:px-0">
      <PageBreadcrumb page={pageData?.page} dossierLabel={dossierData.dossier.title} />
      <div className="flex-1 py-6">
        <DossierInfo dossier={dossierData.dossier} />
      </div>
    </div>
  );
}

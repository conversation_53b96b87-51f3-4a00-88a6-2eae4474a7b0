import { StateAction } from '../../../models/context.model';
import { ApplicationShellState } from './application-shell.context';

export type ApplicationShellActionTypes =
  | 'TOGGLE_SIDE_PANEL'
  | 'CLOSE_SIDE_PANEL'
  | 'TOGGLE_SIDE_NAVIGATION'
  | 'CLOSE_SIDE_NAVIGATION';

type ToggleSidePanelAction = StateAction<'TOGGLE_SIDE_PANEL', ApplicationShellState>;
type CloseSidePanelAction = StateAction<'CLOSE_SIDE_PANEL', ApplicationShellState>;

type ToggleSideNavigationAction = StateAction<'TOGGLE_SIDE_NAVIGATION', ApplicationShellState>;
type CloseSideNavigationAction = StateAction<'CLOSE_SIDE_NAVIGATION', ApplicationShellState>;

export const toggleSidePanel = (): ToggleSidePanelAction => ({
  type: 'TOGGLE_SIDE_PANEL',
  reducer: (state) => ({ ...state, isNotificationTrayOpen: !state.isNotificationTrayOpen }),
});

export const closeSidePanel = (): CloseSidePanelAction => ({
  type: 'CLOSE_SIDE_PANEL',
  reducer: (state) => ({ ...state, isNotificationTrayOpen: false }),
});

export const toggleSideNavigation = (): ToggleSideNavigationAction => ({
  type: 'TOGGLE_SIDE_NAVIGATION',
  reducer: (state) => ({ ...state, isSideNavigationOpen: !state.isSideNavigationOpen }),
});

export const closeSideNavigation = (): CloseSideNavigationAction => ({
  type: 'CLOSE_SIDE_NAVIGATION',
  reducer: (state) => ({ ...state, isSideNavigationOpen: false }),
});

import { Preview } from '@storybook/react';
import { NextIntlClientProvider } from 'next-intl';
import React from 'react';

import '../src/scss/tailwind-imports.scss';

const preview: Preview = {
  globalTypes: {
    locale: {
      description: 'Internationalization locale',
      defaultValue: 'en',
      toolbar: {
        title: 'Locale',
        icon: 'globe',
        items: [
          { value: 'en', right: '🇺🇸', title: 'English' },
          { value: 'nl', right: '🇳🇱', title: 'Nederlands' },
        ],
      },
    },
  },
  loaders: [
    async ({ globals }) => {
      return {
        messages:
          globals.locale === 'en'
            ? {
                ...(await import('@iprox/react-ui-i18n/en.json')).default,
              }
            : {
                ...(await import('@iprox/react-ui-i18n/nl.json')).default,
              },
      };
    },
  ],
  decorators: [
    (Story, context) => {
      return (
        <NextIntlClientProvider locale={context.globals.locale} messages={context.loaded.messages}>
          <main>
            <h1 className="sr-only">component</h1>
            <Story />
          </main>
        </NextIntlClientProvider>
      );
    },
  ],
};

export default preview;

import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { DateTimePickerField } from './date-time-picker';

const meta: Meta<typeof DateTimePickerField> = {
  title: 'iprox-ui/forms/fields/date-time-picker-field',
  component: DateTimePickerField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof DateTimePickerField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'date-time-field',
    fieldType: FieldType.Date,
  },
};

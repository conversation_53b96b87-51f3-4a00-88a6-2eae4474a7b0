import AxeBuilder from '@axe-core/playwright';
import { expect, test } from '@playwright/test';

import { loadStory } from '../../../../utils/common-utils';
import {
  addHeading,
  createTable,
  iterateThroughTableCells,
  performActionOnCell,
  richTextProps,
} from './rich-text-field-test-utils';

const storyId = 'iprox-ui-forms-fields-richtext--default';

export type RichTextFieldArgs = {
  label?: string;
  description?: string;
  name?: string;
  defaultValue?: string;
};

test.describe('<RichTextField />', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await loadStory<RichTextFieldArgs>(page, storyId);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should display the richtext field', async ({ page }) => {
    await loadStory<RichTextFieldArgs>(page, storyId, richTextProps);

    await expect(page.getByText(richTextProps.label)).toBeVisible();
    await expect(page.getByText(richTextProps.description)).toBeVisible();
  });

  test('should be able to add header', async ({ page }) => {
    await loadStory<RichTextFieldArgs>(page, storyId, richTextProps);

    await addHeading(page, 'Heading 1', 'Header 1 text');
    await addHeading(page, 'Heading 2', 'Header 2 text');
    await addHeading(page, 'Heading 3', 'Header 3 text');

    expect(page.getByRole('heading', { name: 'Header 1 text' })).toBeVisible();
    expect(page.getByRole('heading', { name: 'Header 1 text' })).toBeVisible();
    expect(page.getByRole('heading', { name: 'Header 1 text' })).toBeVisible();
  });

  test('should be able to create and edit a link', async ({ page }) => {
    await loadStory<RichTextFieldArgs>(page, storyId, richTextProps);

    const linkTitle = 'Sample Link';
    const linkUrl = 'https://www.example.com/example-url';
    const editedLinkTitle = 'Sample Link Edited';
    const editedLInkUrl = 'https://www.example.com/example-url/updated';

    await page.getByTitle('Insert Link').click();

    await page.getByLabel('Enter URL').type(linkUrl);
    await page.getByLabel('Title').type(linkTitle);

    await page.getByText('Insert Link').click();

    const linkElement = page.getByRole('link', { name: linkTitle });

    await expect(page.getByText(linkTitle)).toBeVisible();
    await expect(linkElement).toHaveAttribute('href', linkUrl);

    linkElement.click();

    await expect(page.getByLabel('Enter URL')).toHaveValue(linkUrl);
    await expect(page.getByLabel('Title')).toHaveValue(linkTitle);

    await page.getByLabel('Enter URL').clear();
    await page.getByLabel('Enter URL').type(editedLInkUrl);

    await page.getByLabel('Title').clear();
    await page.getByLabel('Title').type(editedLinkTitle);

    await page.getByText('Save').click();

    const editedlinkElement = page.getByRole('link', { name: editedLinkTitle });
    await expect(page.getByText(editedLinkTitle)).toBeVisible();
    await expect(editedlinkElement).toHaveAttribute('href', editedLInkUrl);
  });

  test('should be able to create and edit a super link', async ({ page }) => {
    await loadStory<RichTextFieldArgs>(page, storyId, richTextProps);

    const linkTitle = 'Sample Super Link';
    const linkUrl = 'https://www.example.com/example-url';
    const editedLinkTitle = 'Sample Super Link Edited';
    const editedLInkUrl = 'https://www.example.com/example-url/updated';

    await page.getByLabel('Insert Super Link').click();

    await page.getByLabel('Enter URL').type(linkUrl);
    await page.getByLabel('Title').type(linkTitle);

    await page.getByText('Insert Link').click();

    const linkElement = page.getByRole('link', { name: linkTitle });

    await expect(page.getByText(linkTitle)).toBeVisible();
    await expect(linkElement).toHaveAttribute('href', linkUrl);

    linkElement.click();

    await expect(page.getByLabel('Enter URL')).toHaveValue(linkUrl);
    await expect(page.getByLabel('Title')).toHaveValue(linkTitle);

    await page.getByLabel('Enter URL').clear();
    await page.getByLabel('Enter URL').type(editedLInkUrl);

    await page.getByLabel('Title').clear();
    await page.getByLabel('Title').type(editedLinkTitle);

    await page.getByText('Save').click();

    const editedlinkElement = page.getByRole('link', { name: editedLinkTitle });
    await expect(page.getByText(editedLinkTitle)).toBeVisible();
    await expect(editedlinkElement).toHaveAttribute('href', editedLInkUrl);
  });

  test('should be able to add a table', async ({ page }) => {
    const columns = 3;
    const rows = 4;
    const table = await createTable(page, rows, columns);

    await expect(table).toBeVisible();

    const tableRowCount = await table.locator('tr').count();

    const tableRows = await table.locator('tr').all();

    const cellCount = await tableRows.reduce(async (accPromise, row) => {
      const acc = await accPromise;
      const colCount = (await row.locator('td').count()) + (await row.locator('th').count());
      return acc + colCount;
    }, Promise.resolve(0));

    expect(tableRowCount).toBe(rows);
    expect(cellCount).toBe(columns * rows);
  });

  test('should be able fill up the cells in the table', async ({ page }) => {
    const rows = 2;
    const columns = 2;
    const table = await createTable(page, rows, columns);

    await iterateThroughTableCells(table, 3, 3, async (cell, _rowIndex) => {
      await cell.click();
      await page.keyboard.type('test');

      const cellContent = await cell.textContent();
      expect(cellContent.trim()).toBe('test');
    });

    await expect(table).toBeVisible();
  });

  test('should be able to delete the table from the action menu', async ({ page }) => {
    const table = await createTable(page, 3, 2);
    await table.isVisible();

    await page.getByRole('button', { name: 'action-menu' }).click();

    await page.getByRole('button', { name: 'Delete table' }).click();

    const isTableVisible = await table.isVisible();
    expect(isTableVisible).toBeFalsy();
  });

  test('should be able to add row above the clicked row from the action menu', async ({ page }) => {
    const columns = 3;
    const rows = 2;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const cellText = 'test cell content';
    const clickedRowIndex = 0;
    const clickedCellIndex = 0;

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Insert row above', cellText);

    await page.waitForTimeout(1000);

    let rowIndexWithText = -1;

    await iterateThroughTableCells(table, rows + 1, columns + 1, async (cell, rowIndex) => {
      await cell.click();
      const cellContent = await cell.innerText();

      if (cellContent.includes(cellText)) {
        rowIndexWithText = rowIndex;
      }
    });

    expect(rowIndexWithText).toBe(clickedRowIndex + 1);
  });

  test('should be able to add row below the clicked row from the action menu', async ({ page }) => {
    const columns = 2;
    const rows = 3;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 1;
    const clickedCellIndex = 0;

    const initialRowCount = await table.locator('tr').count();

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Insert row below');

    await page.waitForTimeout(1000);

    const newRowCount = await table.locator('tr').count();

    expect(newRowCount).toBe(initialRowCount + 1);
  });

  test('should be able to add column right to the clicked column from the action menu', async ({ page }) => {
    const columns = 3;
    const rows = 2;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 1;

    const initialCellCount = await table.locator('tr').nth(clickedRowIndex).locator('td').count();

    await performActionOnCell(page, table, clickedRowIndex, 0, 'Insert column right');

    const newCellCount = await table.locator('tr').nth(clickedRowIndex).locator('td').count();

    expect(newCellCount).toBe(initialCellCount + 1);
  });

  test('should be able to add column left to the clicked column from the action menu', async ({ page }) => {
    const rows = 2;
    const columns = 3;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 1;
    const clickedCellIndex = 1;
    const cellText = 'test cell content';

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Insert column left', cellText);

    let cellIndexWithText = -1;

    await iterateThroughTableCells(table, rows, columns + 1, async (cell, _rowIndex, cellIndex) => {
      await cell.click();
      const cellContent = await cell.innerText();

      if (cellContent.includes(cellText)) {
        cellIndexWithText = cellIndex;
      }
    });

    expect(cellIndexWithText).toBe(clickedCellIndex + 1);
  });

  test('should be able to remove the selected row from the action menu', async ({ page }) => {
    const rows = 2;
    const columns = 3;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 1;
    const clickedCellIndex = 1;
    const cellText = 'test cell content';

    const initialRowCount = await table.locator('tr').count();

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Delete row', cellText);

    let rowIndexWithText = -1;
    const newRowCount = await table.locator('tr').count();

    await iterateThroughTableCells(table, rows, columns + 1, async (cell, rowIndex) => {
      await cell.click();
      const cellContent = await cell.innerText();

      if (cellContent.includes(cellText)) {
        rowIndexWithText = rowIndex;
      }
    });

    expect(rowIndexWithText).toBe(-1);
    expect(newRowCount).toBe(initialRowCount - 1);
  });

  test('should be able to remove the selected column from the action menu', async ({ page }) => {
    const rows = 2;
    const columns = 3;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 1;
    const clickedCellIndex = 1;
    const cellText = 'test cell content';
    const initialColCount = await table.locator('tr').nth(clickedRowIndex).locator('td').count();

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Delete column', cellText);

    let colIndexWithText = -1;
    const newColCount = await table.locator('tr').nth(clickedRowIndex).locator('td').count();

    await iterateThroughTableCells(table, rows, columns + 1, async (cell, _rowIndex, columnIndex) => {
      await cell.click();
      const cellContent = await cell.innerText();

      if (cellContent.includes(cellText)) {
        colIndexWithText = columnIndex;
      }
    });

    expect(colIndexWithText).toBe(-1);
    expect(newColCount).toBe(initialColCount - 1);
  });

  test('should be able to remove the row header from the action menu', async ({ page }) => {
    const rows = 2;
    const columns = 3;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 0;
    const clickedCellIndex = 0;
    const initialThColCount = await table.locator('tr').nth(clickedRowIndex).locator('th').count();

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Remove row header');

    const newThColCount = await table.locator('tr').nth(clickedRowIndex).locator('th').count();

    expect(newThColCount).toBe(initialThColCount - columns);
  });

  test('should be able to make a row, a row-header from the action menu', async ({ page }) => {
    const rows = 2;
    const columns = 3;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 1;
    const clickedCellIndex = 0;

    let initialThCells = 0;
    let newThColCount = 0;

    const tableRows = await table.locator('tr').all();

    for (let i = 0; i < tableRows.length; i++) {
      initialThCells = await tableRows[i].locator('th').count();
    }

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Add row header');

    for (let i = 0; i < tableRows.length; i++) {
      newThColCount = await tableRows[i].locator('th').count();
    }

    expect(newThColCount).toBe(initialThCells + columns);
  });

  test('should be able to make a column, a column-header from the action menu', async ({ page }) => {
    const rows = 2;
    const columns = 3;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 0;
    const clickedCellIndex = 0;

    const tableRows = await table.locator('tr').all();

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Remove row header');
    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Add column header');

    for (let i = 0; i < tableRows.length; i++) {
      expect(await tableRows[clickedCellIndex].locator('th').count()).toBe(1);
    }
  });

  test('should be able to remove column header from the action menu', async ({ page }) => {
    const rows = 2;
    const columns = 3;
    const table = await createTable(page, rows, columns);
    await table.isVisible();

    const clickedRowIndex = 0;
    const clickedCellIndex = 0;

    const tableRows = await table.locator('tr').all();

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Remove row header');
    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Add column header');

    for (let i = 0; i < tableRows.length; i++) {
      expect(await tableRows[clickedCellIndex].locator('th').count()).toBe(1);
    }

    await performActionOnCell(page, table, clickedRowIndex, clickedCellIndex, 'Remove column header');

    for (let i = 0; i < tableRows.length; i++) {
      expect(await tableRows[clickedCellIndex].locator('th').count()).toBe(0);
    }
  });
});

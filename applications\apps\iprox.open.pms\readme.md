# PMS Application Developer Guide

The PMS application serves as the administrative hub for modifying content within the portal. Through this platform, admins and moderators can seamlessly alter the homepage's content, manage dossiers, and adjust the overall look and feel.

This application is built using Next 14 and requires specific prerequisites and setup steps.

## Prerequisites

Ensure the following software is installed:

1. Node.js 20+
2. Yarn (Package Manager)

## Setup Guide

### 01. Installing NPM Packages

Navigate to the application directory and execute `yarn` to install the necessary libraries, as this project utilizes Yarn as its package manager.

### 02. Configuring Environment Variables

Create a .env file on your local machine and copy all variables from the sample.env file. Due to limitations in Next's environment variable handling, certain configurations from env.development.local have been moved. Create this .env file to support the Prisma adapter with NextAuth. Ensure proper configuration in the .env.development.local file to run the application.

### 03. Configuring Prisma

To generate the Prisma client, navigate to `applications/apps/iprox.open.pms` and execute `npx prisma generate`.

To preview the session database using prisma studio in the same directory, run `npx prisma studio`.

**Note:** Configure your IP address to be allowed in the database, pointing to Prisma in Azure.

### 04. Running the Project

From the application directory, execute `yarn nx server iprox.open.pms`.

Alternatively, consider using the "NX Console" Visual Studio Code extension. Find the "NX Console" extension in the marketplace or [install it here](https://marketplace.visualstudio.com/items?itemName=nrwl.angular-console).

## Installing Additional Dependencies

To include new npm libraries, access the `application` directory. Once there, employ `yarn add` to install the desired libraries.

For ex..

`yarn add react-dom`

## Pointing to Locally Hosted Backend API

Backend developers need to create a temporary database. Once created, modify the connection string in `iprox.open/api/appsettings.Development.json` as shown below:

**Example:**

```json
"ConnectionStrings": {
    "IproxOpenSqlDb": "Server=tcp:iprox-open-dev.database.windows.net,1433;Initial Catalog=iprox-temp-db;Persist Security Info=False;User ID=iproxopenadmin;Password=**$t9P4i&WcpfxU9;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  },
```

Next, navigate to the iprox.open/api directory using the command line. Run `dotnet run --project ./Iprox.Open.Api --launch-profile "Iprox.Open"`.

In the `.env.development.local` file, set the frontend API URL to:

IPROX_OPEN_API_URL=http://127.0.0.1:5113/api/v1

import { colors } from '../../config';
import * as http from '../../enums/httpEnums';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';

describe('Dossier latest published version test', () => {
  let dossierId: string;
  let bearerToken: any;
  let dossierCategoryId: string | null;

  beforeAll(async () => {
    bearerToken = await loginAndGetToken();
    dossierCategoryId = await DossierHelpersNew.getRandomCategoryUuid(false, bearerToken);
    const response: any = await DossierHelpersNew.createDossier(
      'Latest Publish version of dossier',
      bearerToken,
      dossierCategoryId
    );
    dossierId = await response.body.dossier.dossierId;
    await DossierHelpersNew.updateDossier(dossierId, bearerToken);
  }, 90000);

  it('should return latest publish version', async () => {
    // initial version
    const response: any = await DossierHelpersNew.latestPublishedVersionOfDossier(dossierId, bearerToken);
    expect(await response.status).toBe(http.StatusCode.NOT_FOUND_404);
    expect(await response.body.detail).toBe('Dossier not found');

    // publish the dossier
    await DossierHelpersNew.publishDossier(dossierId, bearerToken);

    // latest version should updated
    const dossierLatestPublishedVersionResponse = await DossierHelpersNew.latestPublishedVersionOfDossier(
      dossierId,
      bearerToken
    );
    expect(await dossierLatestPublishedVersionResponse.status).toBe(http.StatusCode.OK_200);
    expect(await dossierLatestPublishedVersionResponse.body.dossier.isPublished).toBe(true);
  }, 90000);

  afterAll(async () => {
    await DossierHelpersNew.deleteDossier(dossierId, bearerToken);
  });
});

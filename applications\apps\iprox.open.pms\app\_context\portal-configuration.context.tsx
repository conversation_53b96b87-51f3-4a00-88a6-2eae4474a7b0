'use client';

import { StateAction, StateContext, ThemeParameter, themeToStyle } from '@iprox/react-ui';
import { ReactNode, createContext, useContext, useEffect, useReducer, useRef } from 'react';

import { PortalConfigurationActionTypes } from './portal-configuration.actions';

export interface PortalConfigurationState {
  cssVariables: ThemeParameter[];
}

export type PortalConfigurationProviderProps = {
  children: ReactNode;
  initialPortalConfiguration: PortalConfigurationState;
};

type PortalConfigurationActions =
  | StateAction<PortalConfigurationActionTypes, PortalConfigurationState>
  | StateAction<PortalConfigurationActionTypes, PortalConfigurationState, unknown>;

export const PortalConfigurationContext = createContext<
  StateContext<PortalConfigurationState, PortalConfigurationActions>
>({
  state: {
    cssVariables: [],
  },
  dispatch: () => null,
});

function PortalConfigurationReducer(state: PortalConfigurationState, action: PortalConfigurationActions) {
  if (action.reducer) {
    if ('payload' in action) {
      return action.reducer(state, action.payload);
    }

    return action.reducer(state);
  }

  return state;
}

function PortalConfigurationProvider({ children, initialPortalConfiguration }: PortalConfigurationProviderProps) {
  const [state, dispatch] = useReducer(PortalConfigurationReducer, {
    cssVariables: initialPortalConfiguration.cssVariables,
  });

  const styleSheet = useRef(typeof CSSStyleSheet !== 'undefined' ? new CSSStyleSheet() : undefined);

  useEffect(() => {
    if (styleSheet.current) {
      document.adoptedStyleSheets.push(styleSheet.current);
      styleSheet.current.replaceSync(`.ipx-portal-component { ${themeToStyle(state.cssVariables).join('\n')} }`);

      console.info('PortalConfigurationProvider', themeToStyle(state.cssVariables).join('\n'));
    }
  }, [state.cssVariables]);

  return (
    <PortalConfigurationContext.Provider value={{ state, dispatch }}>{children}</PortalConfigurationContext.Provider>
  );
}

/**
 * PortalConfiguration Context hook
 * @returns PortalConfigurationContext
 */
function usePortalConfigurationContext() {
  const context = useContext(PortalConfigurationContext);

  if (context === undefined) {
    throw new Error('usePortalConfigurationContext must be used within a PortalConfigurationProvider');
  }

  return context;
}

export { PortalConfigurationProvider, usePortalConfigurationContext };

import * as http from '../../enums/httpEnums';
import { PortalHelpers } from '../../helpers/portalHelpers';

describe('woo-categories test', () => {
  it('PORTAL - should get the woo-categories', async () => {
    const response: any = await PortalHelpers.getWooCategories();

    expect(response.status).toBe(http.StatusCode.OK_200);
    expect(response.body.wooCategories.length).toBeGreaterThan(0);
  });
});

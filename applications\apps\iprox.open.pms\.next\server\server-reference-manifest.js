self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"dadb187e522d878e5332da053f875c265d685112\": {\n      \"workers\": {\n        \"app/[locale]/(authenticated)/(user)/dashboard/page\": \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Capps%5C%5Ciprox.open.pms%5C%5Capp%5C%5C_auth%5C%5Cpermission-authorizer.hook.ts%22%2C%5B%22usePermissionAuthorizer%22%2C%22%24%24ACTION_0%22%5D%5D%5D&__client_imported__=!\",\n        \"app/[locale]/(authenticated)/(user)/dossier/list/page\": \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Capps%5C%5Ciprox.open.pms%5C%5Capp%5C%5C_auth%5C%5Cpermission-authorizer.hook.ts%22%2C%5B%22usePermissionAuthorizer%22%2C%22%24%24ACTION_0%22%5D%5D%5D&__client_imported__=!\",\n        \"app/[locale]/(authenticated)/(user)/dossier/[id]/page\": \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Capps%5C%5Ciprox.open.pms%5C%5Capp%5C%5C_auth%5C%5Cpermission-authorizer.hook.ts%22%2C%5B%22usePermissionAuthorizer%22%2C%22%24%24ACTION_0%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/[locale]/(authenticated)/(user)/dashboard/page\": \"rsc\",\n        \"app/[locale]/(authenticated)/(user)/dossier/list/page\": \"rsc\",\n        \"app/[locale]/(authenticated)/(user)/dossier/[id]/page\": \"rsc\"\n      }\n    },\n    \"e20919bdc552c612145fb8335fe9b7b8b7ad9a14\": {\n      \"workers\": {\n        \"app/[locale]/(authenticated)/(user)/dashboard/page\": \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Capps%5C%5Ciprox.open.pms%5C%5Capp%5C%5C_auth%5C%5Cpermission-authorizer.hook.ts%22%2C%5B%22usePermissionAuthorizer%22%2C%22%24%24ACTION_0%22%5D%5D%5D&__client_imported__=!\",\n        \"app/[locale]/(authenticated)/(user)/dossier/list/page\": \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Capps%5C%5Ciprox.open.pms%5C%5Capp%5C%5C_auth%5C%5Cpermission-authorizer.hook.ts%22%2C%5B%22usePermissionAuthorizer%22%2C%22%24%24ACTION_0%22%5D%5D%5D&__client_imported__=!\",\n        \"app/[locale]/(authenticated)/(user)/dossier/[id]/page\": \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProducten%5C%5Ciprox-open%5C%5Cprojects%5C%5Ciprox.open%5C%5Capplications%5C%5Capps%5C%5Ciprox.open.pms%5C%5Capp%5C%5C_auth%5C%5Cpermission-authorizer.hook.ts%22%2C%5B%22usePermissionAuthorizer%22%2C%22%24%24ACTION_0%22%5D%5D%5D&__client_imported__=!\"\n      },\n      \"layer\": {\n        \"app/[locale]/(authenticated)/(user)/dashboard/page\": \"rsc\",\n        \"app/[locale]/(authenticated)/(user)/dossier/list/page\": \"rsc\",\n        \"app/[locale]/(authenticated)/(user)/dossier/[id]/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"
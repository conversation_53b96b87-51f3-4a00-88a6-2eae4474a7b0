{"name": "iprox.open.pms", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/iprox.open.pms", "projectType": "application", "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/iprox.open.pms"}, "configurations": {"development": {"outputPath": "apps/iprox.open.pms"}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "iprox.open.pms:build", "dev": true}, "configurations": {"development": {"buildTarget": "iprox.open.pms:build:development", "dev": true}, "production": {"buildTarget": "iprox.open.pms:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "iprox.open.pms:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/iprox.open.pms/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "options": {"maxWarnings": 0}}}, "tags": []}
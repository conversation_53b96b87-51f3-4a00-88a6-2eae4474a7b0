import AxeBuilder from '@axe-core/playwright';
import { expect, test } from '@playwright/test';

import { loadStory } from '../../../../utils/common-utils';

type ColorPickerFieldArgs = {
  id?: string;
  name: string;
  label: string;
  value?: string;
  description?: string;
};

function rgbToHex(rgb: string): string {
  // Extract the RGB components using a regular expression
  const match = rgb.match(/rgb\((\d+), (\d+), (\d+)\)/);
  if (!match) {
    throw new Error('Invalid RGB format. Expected format: rgb(r, g, b)');
  }

  const [, r, g, b] = match.map(Number);

  // Convert each RGB component to its two-digit hexadecimal form
  const toHex = (value: number) => value.toString(16).padStart(2, '0');

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

const storyId = 'iprox-ui-forms-fields-color-picker-field--default';

test.describe('<ColorPickerField />', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await loadStory<ColorPickerFieldArgs>(page, storyId);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should display the color picker field', async ({ page }) => {
    const props: ColorPickerFieldArgs = {
      id: 'color-picker',
      name: 'color',
      label: 'Select Color',
      description: 'Primary color',
    };

    await loadStory<ColorPickerFieldArgs>(page, storyId, props);

    await expect(page.getByText(props.label)).toBeVisible();
    await expect(page.getByText(props.description)).toBeVisible();

    const colorPicker = await page.$(`#${props.id}`);
    expect(colorPicker).not.toBeNull();
  });

  test('should be able to select a color', async ({ page }) => {
    const props: ColorPickerFieldArgs = {
      id: 'color-picker',
      name: 'color',
      label: 'Select Color',
      description: 'Primary color',
    };

    await loadStory<ColorPickerFieldArgs>(page, storyId, props);
    const colorPicker = await page.$(`#${props.id}`);
    await colorPicker.click();

    const colorPickerPopup = await page.$('.w-color-sketch ');
    expect(colorPickerPopup).not.toBeNull();

    const box = await colorPickerPopup.boundingBox();
    if (!box) throw new Error('Failed to get bounding box of the color picker popup');

    // Calculate a target position within the bounding box
    const targetX = box.x + box.width * 0.5;
    const targetY = box.y + box.height * 0.5;

    await page.mouse.move(targetX, targetY);
    await page.mouse.click(targetX, targetY);

    const colorSwatch = await page.waitForSelector('.w-color-saturation-fill', { state: 'visible' });
    expect(colorSwatch).not.toBeNull();

    // Retrieve the background color using `evaluate` to run JS in the browser
    const backgroundColor = await page.evaluate((swatch) => {
      const style = window.getComputedStyle(swatch);
      return style.backgroundColor;
    }, colorSwatch);

    const hexValue = rgbToHex(backgroundColor);

    await page.click('label[for="color-picker"]');
    expect(await page.isVisible('.w-color-sketch ')).toBe(false);

    await page.waitForTimeout(1000);

    await expect(page.getByText(hexValue)).toBeVisible();
  });
});

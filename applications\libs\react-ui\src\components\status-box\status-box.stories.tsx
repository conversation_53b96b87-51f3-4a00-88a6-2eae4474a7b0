import { Meta, StoryObj } from '@storybook/react';

import { StatusBox } from './status-box';

const meta: Meta<typeof StatusBox> = {
  title: 'components/statusbox',
  component: StatusBox,
  argTypes: {
    onClick: {
      action: 'onClick',
      table: {
        disable: true,
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof StatusBox>;

export const Default: Story = {
  name: 'default',
  args: {
    editStatus: {
      status: 'new',
      date: '2025-07-02T07:23:00.6949905Z',
    },
    dossierLiveStatus: {
      status: 'Published',
      date: '2025-07-01T15:30:00.0000000Z',
    },
    publishDates: {
      fromDate: new Date(),
      toDate: null,
    },
    pages: [],
    dossierId: 'test-dossier-id',
    categoryId: 'test-category-id',
    portalUrl: 'https://example.com',
  },
};

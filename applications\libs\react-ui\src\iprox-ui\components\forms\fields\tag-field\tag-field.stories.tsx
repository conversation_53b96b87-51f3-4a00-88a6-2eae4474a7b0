import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { TagField } from './tag-field';

const meta: Meta<typeof TagField> = {
  title: 'iprox-ui/forms/fields/multiple-text-field',
  component: TagField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof TagField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'string-list-field',
    fieldType: FieldType.StringList,
  },
};

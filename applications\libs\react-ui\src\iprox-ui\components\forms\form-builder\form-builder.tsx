/* eslint-disable @typescript-eslint/no-explicit-any */
import cx from 'classnames';
import { Form, FormikProvider, useFormik } from 'formik';
import React, { ReactNode, useEffect, useMemo, useState } from 'react';

import { But<PERSON> } from '../../button/button';
import { CheckboxField } from '../fields/checkbox-field/checkbox-field';
import { CheckboxGroup } from '../fields/checkbox-group-field/checkbox-group-field';
import { ColorPickerField } from '../fields/color-picker-field/color-picker-field';
import { DatePickerField } from '../fields/date-picker-field/date-picker-field';
import { DateRangePickerField } from '../fields/date-range-picker-field/date-range-picker-field';
import { DateTimePickerField } from '../fields/date-time-picker/date-time-picker';
import { DateTimeRangePickerField } from '../fields/date-time-range-picker-field/date-time-range-picker-field';
import { PageZonesField } from '../fields/page-zones-field/page-zones-field';
import { RadioGroup } from '../fields/radio-field/radio-field';
import { RichTextField } from '../fields/richtext-field/richtext-field';
import { SelectField } from '../fields/select-field/select-field';
import { TagField } from '../fields/tag-field/tag-field';
import { TextField } from '../fields/text-field/text-field';
import { TextareaField } from '../fields/textarea-field/textarea-field';
import { FieldDefinition, FieldType, FieldValuesMap, ValueTypes } from '../models/form.models';
import { ValidatorSchema } from '../models/validator.models';
import { getFieldValue } from '../utils/get-field-value';
import { createValidatorSchema, formikValidation } from '../validators/utils';

// TODO: Figure out a way to remove "& { fieldType: any; } & { value?: any }"
type FieldComponent = React.ComponentType<
  FieldDefinition<FieldType, ValueTypes> & { fieldType: any } & { value?: any }
>;

const FieldComponents = new Map<FieldType, FieldComponent>();

FieldComponents.set(FieldType.Text, TextField);
FieldComponents.set(FieldType.Integer, TextField);
FieldComponents.set(FieldType.Decimal, TextField);
FieldComponents.set(FieldType.Email, TextField);
FieldComponents.set(FieldType.Password, TextField);
FieldComponents.set(FieldType.TextArea, TextareaField);
FieldComponents.set(FieldType.RadioButton, RadioGroup);
FieldComponents.set(FieldType.CheckBox, CheckboxField);
FieldComponents.set(FieldType.Date, DatePickerField);
FieldComponents.set(FieldType.Select, SelectField);
FieldComponents.set(FieldType.CheckboxGroup, CheckboxGroup);
FieldComponents.set(FieldType.RichText, RichTextField);
FieldComponents.set(FieldType.StringList, TagField);
FieldComponents.set(FieldType.Color, ColorPickerField);
FieldComponents.set(FieldType.DateTime, DateTimePickerField);
FieldComponents.set(FieldType.DateRange, DateRangePickerField);
FieldComponents.set(FieldType.PageZonesField, PageZonesField);
FieldComponents.set(FieldType.DateTimeRange, DateTimeRangePickerField);

interface FormBuilderProps<Fields extends FieldDefinition<FieldType, ValueTypes>[]> {
  fields: FieldDefinition<FieldType, ValueTypes>[];
  onSubmit: (values: FieldValuesMap<Fields>) => void;
  onChange?: (values: FieldValuesMap<Fields>, formik: any) => void;
  onCancel?: () => void;
  children?: ReactNode;
  buttonText?: string;
  disableButton?: boolean;
  errorMessage?: string;
  buttons?: ReactNode;
  formId?: string;
  gridContainerClasses?: string;
}

export function FormBuilder<Fields extends FieldDefinition<FieldType, ValueTypes>[]>({
  fields,
  onSubmit,
  onChange,
  onCancel,
  children,
  buttonText,
  disableButton,
  errorMessage,
  buttons,
  formId,
  gridContainerClasses = 'grid grid-cols-1 gap-5',
}: FormBuilderProps<Fields>) {
  const initialValues = useMemo<Record<string, string | number | boolean | Date>>(
    () =>
      fields.reduce(
        (acc, field) => ({
          ...acc,
          [field.name]: getFieldValue(field.fieldType, field.value),
        }),
        {}
      ),
    [fields]
  );

  const validationSchema = useMemo<ValidatorSchema>(() => createValidatorSchema(fields), [fields]);
  const [disabled, setDisabled] = useState(disableButton ?? false);

  const formik = useFormik<FieldValuesMap<Fields>>({
    initialValues: initialValues as FieldValuesMap<Fields>,
    validateOnMount: true,
    validate: (values) => {
      return formikValidation(values, validationSchema);
    },
    onSubmit: (values) => {
      onSubmit(values);
      if (!disabled) {
        setDisabled(true);
        setTimeout(function () {
          setDisabled(disableButton ?? false);
        }, 1000);
      }
    },
    onReset: onCancel,
  });

  useEffect(() => {
    if (onChange) {
      onChange(formik.values, formik);
    }
  }, [formik, formik.values, onChange]);

  const renderFields = (fields: FieldDefinition<FieldType, ValueTypes>[]): Array<JSX.Element | null> =>
    fields.map((field) => {
      const FieldComponent = FieldComponents.get(field.fieldType);

      const gridColumnClass = field.style?.colSpan ?? 'col-span-1';
      const gridRowClass = field.style?.gridRow ?? null;
      const fieldCustomClass = field.style?.customClasses ?? null;

      return FieldComponent ? (
        <React.Fragment key={field.name}>
          {field.sectionTitle ?? null}
          <div className={cx(gridColumnClass, gridRowClass, fieldCustomClass)}>
            <FieldComponent
              {...field}
              {...(field.fieldType === FieldType.Date || field.fieldType === FieldType.DateTime
                ? { formik }
                : undefined)}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                formik.setFieldValue(field.name, e.target.value);
                field.onChange && field.onChange(e);
              }}
            />
          </div>
        </React.Fragment>
      ) : null;
    });

  return (
    <FormikProvider value={formik}>
      <Form id={formId} onSubmit={formik.handleSubmit} onReset={formik.handleReset}>
        <div className={gridContainerClasses}>
          {renderFields(fields)}
          {children}
          {errorMessage && <p className="text-error">{errorMessage}</p>}
          {buttons ? (
            buttons
          ) : (
            <div className="flex justify-end">
              <Button type="submit" variant="primary" disabled={disabled}>
                {buttonText}
              </Button>
            </div>
          )}
        </div>
      </Form>
    </FormikProvider>
  );
}

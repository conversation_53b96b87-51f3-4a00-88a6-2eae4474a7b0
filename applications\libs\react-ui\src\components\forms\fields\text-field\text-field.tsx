import { ExclamationCircleIcon } from '@heroicons/react/24/outline';
import cx from 'classnames';
import { useField } from 'formik';

import { FormField } from '../../form-field/form-field';
import { FormFieldTypes, useFormField } from '../../hooks/use-form-field.hook';
import { FieldType, TextFieldDefinition } from '../../models/form.models';
import { borderClassname } from '../../utils/border-classname';

const defineFieldType = (type: FieldType): FormFieldTypes => {
  switch (type) {
    case FieldType.Integer:
      return 'number';
    case FieldType.Decimal:
      return 'number';
    case FieldType.Password:
      return 'password';
    case FieldType.Email:
      return 'email';
    default:
      return 'text';
  }
};

export function TextField(props: TextFieldDefinition) {
  const [field, meta, _helpers] = useField(props);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(
    props,
    field,
    meta,
    defineFieldType(props.fieldType)
  );

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  return (
    <FormField definition={props} errorMessage={meta.touched ? meta.error : undefined} {...formControlProps}>
      <input
        {...inputProps}
        {...field}
        onChange={(e) => {
          field.onChange(e);
        }}
        className={cx(
          'rounded-input font-text text-body h-input focus:!border-highlight disabled:bg-light-grey disabled:!border-base-100 disabled:text-base-75 w-full p-3 text-sm focus:ring-0 focus:ring-offset-0 disabled:font-bold',
          { 'pr-12': meta.touched && meta.error },
          borderClassname(meta.touched ? meta.error : undefined, field.value)
        )}
      />
      {meta.touched && meta.error && (
        <ExclamationCircleIcon className="text-error absolute right-4 top-1/2 h-6 w-6 -translate-y-1/2" />
      )}
    </FormField>
  );
}

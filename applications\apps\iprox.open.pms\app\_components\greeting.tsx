import { Text } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';
import React from 'react';

import NewDossierCta from '@/components/new-dossier-cta';

type GreetingProps = {
  name: string;
};

export function Greeting({ name }: GreetingProps) {
  const t = useTranslations('dashboard');

  return (
    <div>
      <Text className="font-heading text-heading mb-5 text-4xl font-bold">{t('greeting', { name })}</Text>
      <Text className="font-text text-body mb-10 text-lg">{t('intro')}</Text>
      <NewDossierCta />
    </div>
  );
}

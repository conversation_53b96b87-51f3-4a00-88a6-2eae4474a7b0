"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CProducten%5Ciprox-open%5Cprojects%5Ciprox.open%5Capplications%5Capps%5Ciprox.open.pms%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProducten%5Ciprox-open%5Cprojects%5Ciprox.open%5Capplications%5Capps%5Ciprox.open.pms&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CProducten%5Ciprox-open%5Cprojects%5Ciprox.open%5Capplications%5Capps%5Ciprox.open.pms%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProducten%5Ciprox-open%5Cprojects%5Ciprox.open%5Capplications%5Capps%5Ciprox.open.pms&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Producten_iprox_open_projects_iprox_open_applications_apps_iprox_open_pms_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Producten_iprox_open_projects_iprox_open_applications_apps_iprox_open_pms_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CProducten%5Ciprox-open%5Cprojects%5Ciprox.open%5Capplications%5Capps%5Ciprox.open.pms%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProducten%5Ciprox-open%5Cprojects%5Ciprox.open%5Capplications%5Capps%5Ciprox.open.pms&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/_auth/auth.ts":
/*!***************************!*\
  !*** ./app/_auth/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   azureAdProviders: () => (/* binding */ azureAdProviders)\n/* harmony export */ });\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jwt-decode */ \"(rsc)/../../node_modules/jwt-decode/build/jwt-decode.esm.js\");\n/* harmony import */ var _azure_ad_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./azure-ad.provider */ \"(rsc)/./app/_auth/azure-ad.provider.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./app/_auth/prisma.ts\");\n\n\n\nconst session = {\n    strategy: \"database\",\n    maxAge: 30 * 24 * 60 * 60,\n    updateAge: 24 * 60 * 60\n};\nif (_azure_ad_provider__WEBPACK_IMPORTED_MODULE_1__.azureAdProvidersConfig.length === 0) {\n    console.info(\"No Azure AD providers configured\");\n}\nconst azureAdProviders = _azure_ad_provider__WEBPACK_IMPORTED_MODULE_1__.azureAdProvidersConfig.map((provider)=>{\n    return (0,_azure_ad_provider__WEBPACK_IMPORTED_MODULE_1__.azureAuthProviderFactory)(provider);\n});\nconst authOptions = {\n    // Uncomment this to see nextauth debug logs\n    // debug: true,\n    session,\n    adapter: _prisma__WEBPACK_IMPORTED_MODULE_2__.prismaAdapter,\n    providers: azureAdProviders,\n    callbacks: {\n        async signIn ({ user }) {\n            return Boolean(user.email);\n        },\n        // Potential improvement: memoizing the fetch to the db.\n        async session ({ session, user }) {\n            const [storedUser] = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prismaClient.account.findMany({\n                where: {\n                    userId: user.id,\n                    provider: user.provider\n                }\n            });\n            if (_azure_ad_provider__WEBPACK_IMPORTED_MODULE_1__.AZUREAD_PROVIDERS.includes(storedUser.provider)) {\n                if ((storedUser.expires_at || 0) * 1000 < Date.now() && storedUser.access_token) {\n                    try {\n                        const tokens = await (0,_azure_ad_provider__WEBPACK_IMPORTED_MODULE_1__.azureAdRefreshToken)(storedUser.access_token, storedUser.refresh_token || \"\");\n                        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prismaClient.account.update({\n                            data: {\n                                access_token: tokens.access_token,\n                                expires_at: Math.floor(Date.now() / 1000 + tokens.expires_in),\n                                refresh_token: tokens.refresh_token || storedUser.refresh_token\n                            },\n                            where: {\n                                provider_providerAccountId: {\n                                    provider: storedUser.provider,\n                                    providerAccountId: storedUser.providerAccountId\n                                }\n                            }\n                        });\n                        session.user = {\n                            ...user,\n                            ...storedUser,\n                            userRoles: tokens.access_token !== null ? (0,jwt_decode__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(tokens.access_token).roles ?? [] : []\n                        };\n                    } catch (error) {\n                        console.error(\"Error refreshing access token\", error);\n                        // The error property will be used client-side to handle the refresh token error\n                        session.error = \"RefreshAccessTokenError\";\n                    }\n                } else {\n                    session.user = {\n                        ...user,\n                        ...storedUser,\n                        userRoles: storedUser.access_token !== null ? (0,jwt_decode__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(storedUser.access_token)?.roles ?? [] : []\n                    };\n                }\n            }\n            return session;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/_auth/auth.ts\n");

/***/ }),

/***/ "(rsc)/./app/_auth/azure-ad.provider.ts":
/*!****************************************!*\
  !*** ./app/_auth/azure-ad.provider.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AZUREAD_PROVIDERS: () => (/* binding */ AZUREAD_PROVIDERS),\n/* harmony export */   azureAdProvidersConfig: () => (/* binding */ azureAdProvidersConfig),\n/* harmony export */   azureAdRefreshToken: () => (/* binding */ azureAdRefreshToken),\n/* harmony export */   azureAuthProviderFactory: () => (/* binding */ azureAuthProviderFactory)\n/* harmony export */ });\n/* harmony import */ var _http_fetcher_public__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/http/fetcher-public */ \"(rsc)/./app/_http/fetcher-public.ts\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jwt-decode */ \"(rsc)/../../node_modules/jwt-decode/build/jwt-decode.esm.js\");\n/* harmony import */ var next_auth_providers_azure_ad__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/azure-ad */ \"(rsc)/../../node_modules/next-auth/providers/azure-ad.js\");\n\n\n\nconst AZUREAD_PROVIDERS = [\n    \"azure-ad\",\n    \"azure-ad-iprox\",\n    \"azure-ad-test\"\n];\nconst azureAdProvidersConfig = JSON.parse(process.env.AZURE_AD_PROVIDERS ?? \"[]\");\nconst accessScopes = \"IproxOpen.Access openid profile email offline_access\";\nconst azureAuthProviderFactory = ({ id, name, clientId, clientSecret, uri, tenantId })=>{\n    return (0,next_auth_providers_azure_ad__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        id,\n        name,\n        clientId,\n        clientSecret,\n        tenantId,\n        authorization: {\n            params: {\n                scope: `${uri}${accessScopes}`\n            }\n        }\n    });\n};\nasync function azureAdRefreshToken(accessToken, refreshToken) {\n    try {\n        const { aud, tid } = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(accessToken);\n        const provider = azureAdProvidersConfig.find((provider)=>provider.tenantId === tid && provider.uri.replace(/\\/+$/g, \"\") === aud);\n        if (!provider) {\n            throw new Error(\"No provider found\");\n        }\n        const response = await _http_fetcher_public__WEBPACK_IMPORTED_MODULE_0__.fetcher.post(`https://login.microsoftonline.com/${provider.tenantId}/oauth2/v2.0/token`, {\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            },\n            body: new URLSearchParams({\n                client_Id: provider.clientId,\n                scope: `${provider.uri}${accessScopes}`,\n                refresh_token: refreshToken,\n                grant_type: \"refresh_token\",\n                client_secret: provider.clientSecret\n            })\n        });\n        return await response.json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/_auth/azure-ad.provider.ts\n");

/***/ }),

/***/ "(rsc)/./app/_auth/prisma.ts":
/*!*****************************!*\
  !*** ./app/_auth/prisma.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prismaAdapter: () => (/* binding */ prismaAdapter),\n/* harmony export */   prismaClient: () => (/* binding */ prismaClient)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/../../node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst prismaClient = new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient({\n    datasourceUrl: process.env.PMS_NEXT_DATABASE_URL\n});\nconst prismaAdapter = (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(prismaClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvX2F1dGgvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBEO0FBQ1o7QUFHdkMsTUFBTUUsZUFBZSxJQUFJRCx3REFBWUEsQ0FBQztJQUMzQ0UsZUFBZUMsUUFBUUMsR0FBRyxDQUFDQyxxQkFBcUI7QUFDbEQsR0FBRztBQUVJLE1BQU1DLGdCQUF5QlAsd0VBQWFBLENBQUNFLGNBQWMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9hcHAvX2F1dGgvcHJpc21hLnRzP2NlZmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQWRhcHRlciB9IGZyb20gJ0BuZXh0LWF1dGgvcHJpc21hLWFkYXB0ZXInO1xyXG5pbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XHJcbmltcG9ydCB7IEFkYXB0ZXIgfSBmcm9tICduZXh0LWF1dGgvYWRhcHRlcnMnO1xyXG5cclxuZXhwb3J0IGNvbnN0IHByaXNtYUNsaWVudCA9IG5ldyBQcmlzbWFDbGllbnQoe1xyXG4gIGRhdGFzb3VyY2VVcmw6IHByb2Nlc3MuZW52LlBNU19ORVhUX0RBVEFCQVNFX1VSTCxcclxufSk7XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hQWRhcHRlcjogQWRhcHRlciA9IFByaXNtYUFkYXB0ZXIocHJpc21hQ2xpZW50KTtcclxuIl0sIm5hbWVzIjpbIlByaXNtYUFkYXB0ZXIiLCJQcmlzbWFDbGllbnQiLCJwcmlzbWFDbGllbnQiLCJkYXRhc291cmNlVXJsIiwicHJvY2VzcyIsImVudiIsIlBNU19ORVhUX0RBVEFCQVNFX1VSTCIsInByaXNtYUFkYXB0ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/_auth/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./app/_http/fetcher-public.ts":
/*!*************************************!*\
  !*** ./app/_http/fetcher-public.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetcher: () => (/* binding */ fetcher)\n/* harmony export */ });\n/* harmony import */ var ky__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ky */ \"(rsc)/../../node_modules/ky/distribution/index.js\");\n\nconst fetcher = ky__WEBPACK_IMPORTED_MODULE_0__[\"default\"].extend({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvX2h0dHAvZmV0Y2hlci1wdWJsaWMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0I7QUFFYixNQUFNQyxVQUFVRCwwQ0FBRUEsQ0FBQ0UsTUFBTSxDQUFDLENBQUMsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL2FwcC9faHR0cC9mZXRjaGVyLXB1YmxpYy50cz8zZGQyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBreSBmcm9tICdreSc7XHJcblxyXG5leHBvcnQgY29uc3QgZmV0Y2hlciA9IGt5LmV4dGVuZCh7fSk7XHJcbiJdLCJuYW1lcyI6WyJreSIsImZldGNoZXIiLCJleHRlbmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/_http/fetcher-public.ts\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _auth_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth/auth */ \"(rsc)/./app/_auth/auth.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/../../node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_1___default()(_auth_auth__WEBPACK_IMPORTED_MODULE_0__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQztBQUNUO0FBRWpDLE1BQU1FLFVBQVVELGdEQUFRQSxDQUFDRCxtREFBV0E7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovLy8uL2FwcC9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdL3JvdXRlLnRzP2M4YTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICdAL2F1dGgvYXV0aCc7XHJcbmltcG9ydCBOZXh0QXV0aCBmcm9tICduZXh0LWF1dGgnO1xyXG5cclxuY29uc3QgaGFuZGxlciA9IE5leHRBdXRoKGF1dGhPcHRpb25zKTtcclxuZXhwb3J0IHsgaGFuZGxlciBhcyBHRVQsIGhhbmRsZXIgYXMgUE9TVCB9O1xyXG4iXSwibmFtZXMiOlsiYXV0aE9wdGlvbnMiLCJOZXh0QXV0aCIsImhhbmRsZXIiLCJHRVQiLCJQT1NUIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@opentelemetry","vendor-chunks/yallist","vendor-chunks/uuid","vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/ky","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/jwt-decode"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CProducten%5Ciprox-open%5Cprojects%5Ciprox.open%5Capplications%5Capps%5Ciprox.open.pms%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProducten%5Ciprox-open%5Cprojects%5Ciprox.open%5Capplications%5Capps%5Ciprox.open.pms&isDev=true&tsconfigPath=.%2Ftsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
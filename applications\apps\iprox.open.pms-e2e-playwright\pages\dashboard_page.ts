import { type Locator, type Page, expect } from '@playwright/test';

export class DashboardPage {
  readonly page: Page;
  readonly newDossier: Locator;
  readonly pageUrl: RegExp;

  constructor(page: Page) {
    this.page = page;
    this.newDossier = page.getByRole('button', { name: 'Nieuw dossier' }).getByText('Nieuw dossier');
    this.pageUrl = /dashboard/;
  }

  async goToDashboardPage() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.goto('/dashboard');
  }

  async clickNewDossierButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.newDossier.focus();
    await expect(this.newDossier).toBeVisible();
    await this.newDossier.click();
  }

  async assertPageUrl() {
    await this.page.waitForLoadState('domcontentloaded');
    await expect(this.page).toHaveURL(this.pageUrl);
  }

  async getPublicationType() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForLoadState('load');
    return await this.page
      .locator('//*[contains(text(), "Kies type publicatie")]/following-sibling::div[1]')
      .innerText();
  }

  async getPublishedDossierWidgetText() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForTimeout(3000); // wait for the animation to complete
    return await this.page
      .locator('//span[contains(text(), "Aantal gepubliceerde dossiers")]/following-sibling::span[1]')
      .innerText();
  }

  async getPublishedDossierFileWidgetText() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForTimeout(3000); // wait for the animation to complete
    return await this.page
      .locator('//span[contains(text(), "Aantal gepubliceerde bestanden")]/following-sibling::span[1]')
      .innerText();
  }
}

export default DashboardPage;

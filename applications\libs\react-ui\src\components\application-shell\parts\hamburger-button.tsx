import { Bars3Icon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';

import { toggleSideNavigation } from '../context/application-shell.actions';
import { useApplicationShellContext } from '../context/application-shell.context';

export function HamburgerButton() {
  const { dispatch } = useApplicationShellContext();
  const t = useTranslations('components');

  return (
    <button
      type="button"
      className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
      onClick={() => dispatch(toggleSideNavigation())}
    >
      <span className="sr-only">{t('hamburgerCTA.hamburgerButtonAlt')}</span>
      <Bars3Icon className="h-6 w-6" aria-hidden="true" />
    </button>
  );
}

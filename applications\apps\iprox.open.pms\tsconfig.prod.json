{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "allowImportingTsExtensions": true, "types": ["jest", "node"], "paths": {"@iprox/react-ui": ["libs/react-ui/src/index.ts"], "@iprox/react-ui/server": ["libs/react-ui/src/server.ts"], "@iprox/react-ui-i18n/*": ["libs/react-ui-i18n/src/i18n/*"], "@iprox/iprox-ui": ["libs/react-ui/src/iprox-ui/index.ts"], "@iprox/shared-context": ["libs/shared-context/src/index.tsx"], "@/iprox-open.interface": ["libs/iprox-open-api/generated/iprox-open.interface.ts"], "@/auth/*": ["apps/iprox.open.pms/app/_auth/*"], "@/http/*": ["apps/iprox.open.pms/app/_http/*"], "@/components/*": ["apps/iprox.open.pms/app/_components/*"], "@/config/*": ["apps/iprox.open.pms/app/_config/*"], "@/services/*": ["apps/iprox.open.pms/app/_services/*"], "@/models/*": ["apps/iprox.open.pms/app/_models/*"], "@/utils/*": ["apps/iprox.open.pms/app/_utils/*"], "@/context/*": ["apps/iprox.open.pms/app/_context/*"]}, "plugins": [{"name": "next"}]}, "files": ["i18n.prod.d.ts", "types/next-auth.d.ts"], "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "next-env.d.ts", "../../apps/iprox.open.pms/.next/types/**/*.ts", "../../dist/apps/iprox.open.pms/.next/types/**/*.ts"], "exclude": ["node_modules", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", ".next"]}
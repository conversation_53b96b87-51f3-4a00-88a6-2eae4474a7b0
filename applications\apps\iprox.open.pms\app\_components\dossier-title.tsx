import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { updateDossierTitle } from '@/services/dossier-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { useTitleFormDefinition } from '@/utils/use-title-definition';
import { CheckIcon, PencilSquareIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { FormBuilder, FormSubmitValues, Text, showToast } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

interface DossierTitleProps {
  title: string;
  dossierId: string;
  onTitleUpdate: (dossier: components['schemas']['DossierDto']) => void;
}

export function DossierTitle({ title, dossierId, onTitleUpdate }: DossierTitleProps) {
  const t = useTranslations('dossier');
  const clientApi = useClientApi();

  const formDefinition = useTitleFormDefinition(title);
  const [isEditingTitle, setIsEditingTitle] = useState(false);

  const saveTitleChange = async (values: FormSubmitValues) => {
    try {
      const response = await updateDossierTitle(clientApi, {
        dossierId,
        title: values?.title as string,
      });

      onTitleUpdate(response.dossier);
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    }

    setIsEditingTitle(false);
  };

  if (isEditingTitle) {
    return (
      <FormBuilder
        fields={formDefinition}
        onSubmit={saveTitleChange}
        buttons={
          <div className="relative">
            <div className="absolute -top-3 right-0 flex flex-row justify-end">
              <button
                type="reset"
                className="rounded-input bg-base-25 flex items-center justify-center border p-1 outline-none"
                onClick={() => setIsEditingTitle(false)}
              >
                <span className="sr-only">{t('cancelEditTitle')}</span>
                <XMarkIcon aria-hidden="true" className="text-heading h-4 w-4" />
              </button>
              <button
                type="submit"
                className="rounded-input bg-base-25 ml-2 flex items-center justify-center border p-1 outline-none"
              >
                <span className="sr-only">{t('saveTitle')}</span>
                <CheckIcon aria-hidden="true" className="text-heading h-4 w-4" />
              </button>
            </div>
          </div>
        }
      />
    );
  }

  return (
    <>
      <Text className="font-heading text-heading mb-2 text-lg font-bold">{t('title')}</Text>
      <div className="flex flex-row">
        <Text className="font-text text-content-lite truncate text-sm">{title}</Text>
        <button
          type="button"
          className="border-none bg-transparent outline-none"
          onClick={() => setIsEditingTitle(true)}
        >
          <span className="sr-only">{t('changeTitle')}</span>
          <PencilSquareIcon aria-hidden="true" className="text-heading ml-4 h-4 w-4" />
        </button>
      </div>
    </>
  );
}

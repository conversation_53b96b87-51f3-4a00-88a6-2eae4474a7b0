'use client';

import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { createDossier } from '@/services/dossier-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import {
  FieldType,
  FormBuilder,
  FormSubmitValues,
  MultiFieldDefinition,
  Text,
  ValidationRuleType,
  showToast,
} from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';

interface PageContentProps {
  dossierCategories: components['schemas']['DossierCategoryDto'][];
}

export default function PageContent({ dossierCategories }: PageContentProps) {
  const t = useTranslations('dossier');
  const router = useRouter();
  const clientApi = useClientApi();

  const [isCreateDossierLoading, setIsCreateDossierLoading] = useState<boolean>(false);

  const newDossierForm = useMemo(() => {
    if (dossierCategories) {
      const options = dossierCategories
        .map((item) => ({ label: item.label, value: item.id }))
        .sort((a, b) => a.label.localeCompare(b.label)); // Sort by label name
      return [
        {
          name: 'title',
          label: t('title'),
          fieldType: FieldType.Text,
          validationRules: [
            {
              ruleType: ValidationRuleType.RequiredProperty,
              ruleValue: {},
            },
            {
              ruleType: ValidationRuleType.ValidateStringLength,
              ruleValue: { minimumLength: 0, maximumLength: 255 },
            },
          ],
        },
        {
          name: 'category',
          label: t('category'),
          fieldType: FieldType.Select,
          options,
          validationRules: [
            {
              ruleType: ValidationRuleType.RequiredProperty,
              ruleValue: {},
            },
          ],
        } as MultiFieldDefinition,
      ];
    }
    return [];
  }, [t, dossierCategories]);

  const handleSubmit = (values: FormSubmitValues) => {
    setIsCreateDossierLoading(true);
    createDossier(clientApi, { title: values.title as string, categoryId: values.category as string })
      .then((res) => {
        if (res?.dossier?.dossierId) {
          router.push(`/dossier/${res.dossier.dossierId}`);
        }
      })
      .catch(async (error) => {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      })
      .finally(() => {
        setIsCreateDossierLoading(false);
      });
  };

  return (
    <div className="h-full">
      <div className="mb-10 ml-0.5">
        <Text className="font-text text-content-lite text-lg">
          {'Dossiers > '}
          <Text className="font-heading text-heading inline text-lg font-bold">{t('newDossier')}</Text>
        </Text>
      </div>
      <Text className="font-heading text-heading mb-11 text-5xl font-bold">{t('newDossierTitle')}</Text>
      {newDossierForm && newDossierForm.length && (
        <div className="w-2/5 max-w-[534px]">
          <FormBuilder
            fields={newDossierForm}
            onSubmit={handleSubmit}
            buttonText={t('newDossierCta')}
            disableButton={isCreateDossierLoading}
          />
        </div>
      )}
    </div>
  );
}

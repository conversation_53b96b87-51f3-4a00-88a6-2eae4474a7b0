import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

import { Text } from '../../text/text';

interface PaginationButtonProps {
  page: string;
  query: Record<string, string | number> | undefined;
  isCurrent: boolean;
  type: 'button' | 'ellipsis';
}

export function PaginationButton({ page, query, isCurrent, type }: PaginationButtonProps) {
  const t = useTranslations('components');
  const pathname = usePathname();

  const renderButton = useMemo(() => {
    if (isCurrent) {
      return (
        <span
          key={page}
          className={`border-highlight font-text text-body flex h-12 w-12 items-center justify-center rounded-sm border text-lg ${
            isCurrent ? 'bg-highlight !text-base-00' : ''
          }`}
          aria-current={isCurrent ? 'page' : undefined}
        >
          {t('pagination.pageDisplayLabel', { page })}
        </span>
      );
    }
    if (type === 'button') {
      return (
        <Link
          prefetch={false}
          key={page}
          href={{
            pathname,
            query,
          }}
          className={`border-highlight font-text text-body flex h-12 w-12 items-center justify-center rounded-sm border text-lg ${
            isCurrent ? 'bg-highlight text-base-00' : ''
          }`}
          aria-current={isCurrent ? 'page' : undefined}
          aria-label={t('pagination.pageAriaLabel', { page })}
        >
          {t('pagination.pageDisplayLabel', { page })}
        </Link>
      );
    }
    if (type === 'ellipsis') {
      return (
        <Text className="font-text text-body flex h-12 w-12 items-center justify-center text-lg">
          {t('pagination.pageDisplayLabel', { page })}
        </Text>
      );
    }

    return null;
  }, [isCurrent, page, pathname, query, t, type]);

  return renderButton;
}

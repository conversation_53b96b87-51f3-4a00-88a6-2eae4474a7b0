import { CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';

import { FormBuilder } from '../../../iprox-ui/components/forms/form-builder/form-builder';
import { FormSubmitValues } from '../../../iprox-ui/components/forms/models/form.models';
import { useLabelFormDefinition } from '../utils/use-label-form-definition';

interface RenamingFormProps {
  label: string;
  onLabelUpdate: (value: FormSubmitValues) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

export function RenamingForm({ label, onLabelUpdate, onCancel, isSubmitting }: RenamingFormProps) {
  const t = useTranslations('components.fileStructure');

  const formDefinition = useLabelFormDefinition(label);

  return (
    <FormBuilder
      fields={formDefinition}
      onSubmit={onLabelUpdate}
      gridContainerClasses="grid grid-cols-[min(85%)_1fr] gap-2 items-baseline"
      buttons={
        <div className="flex flex-row justify-end">
          <button
            type="reset"
            className="rounded-input bg-base-25 flex items-center justify-center border p-1 outline-none"
            onClick={onCancel}
          >
            <span className="sr-only">{t('cancelRename')}</span>
            <XMarkIcon aria-hidden="true" className="text-heading h-4 w-4" />
          </button>
          <button
            type="submit"
            className="rounded-input bg-base-25 ml-2 flex items-center justify-center border p-1 outline-none"
            disabled={isSubmitting}
          >
            <span className="sr-only">{t('saveName')}</span>
            <CheckIcon aria-hidden="true" className="text-heading h-4 w-4" />
          </button>
        </div>
      }
    />
  );
}

import { ValidationRule, ValidationRuleType, ValidatorFn, ValidatorFnFactory } from '../models/validator.models';

export interface PastDateValidationRule extends ValidationRule<ValidationRuleType.ValidatePastDate> {
  ruleValue: {
    minDate: Date | string;
  };
}

export const isPastDateRule = (rule: ValidationRule): rule is PastDateValidationRule => {
  return rule.ruleType === ValidationRuleType.ValidatePastDate;
};

export const pastDateValidatorFactory: ValidatorFnFactory<{
  minDate: Date | string;
}> = (ruleValue): ValidatorFn => {
  return ({ value }) => {
    if (
      (value instanceof Date || typeof value === 'string' ? new Date(value).getTime() : new Date().getTime()) <=
      new Date(ruleValue.minDate).getTime()
    ) {
      return { pastDate: true };
    }

    return null;
  };
};

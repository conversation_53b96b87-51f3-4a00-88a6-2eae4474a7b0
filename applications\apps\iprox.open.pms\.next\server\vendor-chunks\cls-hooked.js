/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cls-hooked";
exports.ids = ["vendor-chunks/cls-hooked"];
exports.modules = {

/***/ "(instrument)/../../node_modules/cls-hooked/context-legacy.js":
/*!*******************************************************!*\
  !*** ../../node_modules/cls-hooked/context-legacy.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst util = __webpack_require__(/*! util */ \"util\");\nconst assert = __webpack_require__(/*! assert */ \"assert\");\nconst wrapEmitter = __webpack_require__(/*! emitter-listener */ \"(instrument)/../../node_modules/emitter-listener/listener.js\");\nconst asyncHook = __webpack_require__(/*! async-hook-jl */ \"(instrument)/../../node_modules/async-hook-jl/index.js\");\n\nconst CONTEXTS_SYMBOL = 'cls@contexts';\nconst ERROR_SYMBOL = 'error@context';\n\n//const trace = [];\n\nconst invertedProviders = [];\nfor (let key in asyncHook.providers) {\n  invertedProviders[asyncHook.providers[key]] = key;\n}\n\nconst DEBUG_CLS_HOOKED = process.env.DEBUG_CLS_HOOKED;\n\nlet currentUid = -1;\n\nmodule.exports = {\n  getNamespace: getNamespace,\n  createNamespace: createNamespace,\n  destroyNamespace: destroyNamespace,\n  reset: reset,\n  //trace: trace,\n  ERROR_SYMBOL: ERROR_SYMBOL\n};\n\nfunction Namespace(name) {\n  this.name = name;\n  // changed in 2.7: no default context\n  this.active = null;\n  this._set = [];\n  this.id = null;\n  this._contexts = new Map();\n}\n\nNamespace.prototype.set = function set(key, value) {\n  if (!this.active) {\n    throw new Error('No context available. ns.run() or ns.bind() must be called first.');\n  }\n\n  if (DEBUG_CLS_HOOKED) {\n    debug2('    SETTING KEY:' + key + '=' + value + ' in ns:' + this.name + ' uid:' + currentUid + ' active:' +\n      util.inspect(this.active, true));\n  }\n  this.active[key] = value;\n  return value;\n};\n\nNamespace.prototype.get = function get(key) {\n  if (!this.active) {\n    if (DEBUG_CLS_HOOKED) {\n      debug2('    GETTING KEY:' + key + '=undefined' + ' ' + this.name + ' uid:' + currentUid + ' active:' +\n        util.inspect(this.active, true));\n    }\n    return undefined;\n  }\n  if (DEBUG_CLS_HOOKED) {\n    debug2('    GETTING KEY:' + key + '=' + this.active[key] + ' ' + this.name + ' uid:' + currentUid + ' active:' +\n      util.inspect(this.active, true));\n  }\n  return this.active[key];\n};\n\nNamespace.prototype.createContext = function createContext() {\n  if (DEBUG_CLS_HOOKED) {\n    debug2('   CREATING Context: ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' ' + ' active:' +\n      util.inspect(this.active, true, 2, true));\n  }\n\n  let context = Object.create(this.active ? this.active : Object.prototype);\n  context._ns_name = this.name;\n  context.id = currentUid;\n\n  if (DEBUG_CLS_HOOKED) {\n    debug2('   CREATED Context: ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' ' + ' context:' +\n      util.inspect(context, true, 2, true));\n  }\n\n  return context;\n};\n\nNamespace.prototype.run = function run(fn) {\n  let context = this.createContext();\n  this.enter(context);\n  try {\n    if (DEBUG_CLS_HOOKED) {\n      debug2(' BEFORE RUN: ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' ' +\n        util.inspect(context));\n    }\n    fn(context);\n    return context;\n  }\n  catch (exception) {\n    if (exception) {\n      exception[ERROR_SYMBOL] = context;\n    }\n    throw exception;\n  }\n  finally {\n    if (DEBUG_CLS_HOOKED) {\n      debug2(' AFTER RUN: ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' ' +\n        util.inspect(context));\n    }\n    this.exit(context);\n  }\n};\n\nNamespace.prototype.runAndReturn = function runAndReturn(fn) {\n  var value;\n  this.run(function (context) {\n    value = fn(context);\n  });\n  return value;\n};\n\n/**\n * Uses global Promise and assumes Promise is cls friendly or wrapped already.\n * @param {function} fn\n * @returns {*}\n */\nNamespace.prototype.runPromise = function runPromise(fn) {\n  let context = this.createContext();\n  this.enter(context);\n\n  let promise = fn(context);\n  if (!promise || !promise.then || !promise.catch) {\n    throw new Error('fn must return a promise.');\n  }\n\n  if (DEBUG_CLS_HOOKED) {\n    debug2(' BEFORE runPromise: ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' ' +\n      util.inspect(context));\n  }\n\n  return promise\n    .then(result => {\n      if (DEBUG_CLS_HOOKED) {\n        debug2(' AFTER runPromise: ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' ' +\n          util.inspect(context));\n      }\n      this.exit(context);\n      return result;\n    })\n    .catch(err => {\n      err[ERROR_SYMBOL] = context;\n      if (DEBUG_CLS_HOOKED) {\n        debug2(' AFTER runPromise: ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' ' +\n          util.inspect(context));\n      }\n      this.exit(context);\n      throw err;\n    });\n};\n\nNamespace.prototype.bind = function bindFactory(fn, context) {\n  if (!context) {\n    if (!this.active) {\n      context = this.createContext();\n    }\n    else {\n      context = this.active;\n    }\n  }\n\n  let self = this;\n  return function clsBind() {\n    self.enter(context);\n    try {\n      return fn.apply(this, arguments);\n    }\n    catch (exception) {\n      if (exception) {\n        exception[ERROR_SYMBOL] = context;\n      }\n      throw exception;\n    }\n    finally {\n      self.exit(context);\n    }\n  };\n};\n\nNamespace.prototype.enter = function enter(context) {\n  assert.ok(context, 'context must be provided for entering');\n  if (DEBUG_CLS_HOOKED) {\n    debug2('  ENTER ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' context: ' +\n      util.inspect(context));\n  }\n\n  this._set.push(this.active);\n  this.active = context;\n};\n\nNamespace.prototype.exit = function exit(context) {\n  assert.ok(context, 'context must be provided for exiting');\n  if (DEBUG_CLS_HOOKED) {\n    debug2('  EXIT ' + this.name + ' uid:' + currentUid + ' len:' + this._set.length + ' context: ' +\n      util.inspect(context));\n  }\n\n  // Fast path for most exits that are at the top of the stack\n  if (this.active === context) {\n    assert.ok(this._set.length, 'can\\'t remove top context');\n    this.active = this._set.pop();\n    return;\n  }\n\n  // Fast search in the stack using lastIndexOf\n  let index = this._set.lastIndexOf(context);\n\n  if (index < 0) {\n    if (DEBUG_CLS_HOOKED) {\n      debug2('??ERROR?? context exiting but not entered - ignoring: ' + util.inspect(context));\n    }\n    assert.ok(index >= 0, 'context not currently entered; can\\'t exit. \\n' + util.inspect(this) + '\\n' +\n      util.inspect(context));\n  } else {\n    assert.ok(index, 'can\\'t remove top context');\n    this._set.splice(index, 1);\n  }\n};\n\nNamespace.prototype.bindEmitter = function bindEmitter(emitter) {\n  assert.ok(emitter.on && emitter.addListener && emitter.emit, 'can only bind real EEs');\n\n  let namespace = this;\n  let thisSymbol = 'context@' + this.name;\n\n  // Capture the context active at the time the emitter is bound.\n  function attach(listener) {\n    if (!listener) {\n      return;\n    }\n    if (!listener[CONTEXTS_SYMBOL]) {\n      listener[CONTEXTS_SYMBOL] = Object.create(null);\n    }\n\n    listener[CONTEXTS_SYMBOL][thisSymbol] = {\n      namespace: namespace,\n      context: namespace.active\n    };\n  }\n\n  // At emit time, bind the listener within the correct context.\n  function bind(unwrapped) {\n    if (!(unwrapped && unwrapped[CONTEXTS_SYMBOL])) {\n      return unwrapped;\n    }\n\n    let wrapped = unwrapped;\n    let unwrappedContexts = unwrapped[CONTEXTS_SYMBOL];\n    Object.keys(unwrappedContexts).forEach(function (name) {\n      let thunk = unwrappedContexts[name];\n      wrapped = thunk.namespace.bind(wrapped, thunk.context);\n    });\n    return wrapped;\n  }\n\n  wrapEmitter(emitter, attach, bind);\n};\n\n/**\n * If an error comes out of a namespace, it will have a context attached to it.\n * This function knows how to find it.\n *\n * @param {Error} exception Possibly annotated error.\n */\nNamespace.prototype.fromException = function fromException(exception) {\n  return exception[ERROR_SYMBOL];\n};\n\nfunction getNamespace(name) {\n  return process.namespaces[name];\n}\n\nfunction createNamespace(name) {\n  assert.ok(name, 'namespace must be given a name.');\n\n  if (DEBUG_CLS_HOOKED) {\n    debug2('CREATING NAMESPACE ' + name);\n  }\n  let namespace = new Namespace(name);\n  namespace.id = currentUid;\n\n  asyncHook.addHooks({\n    init(uid, handle, provider, parentUid, parentHandle) {\n      //parentUid = parentUid || currentUid;  // Suggested usage but appears to work better for tracing modules.\n      currentUid = uid;\n\n      //CHAIN Parent's Context onto child if none exists. This is needed to pass net-events.spec\n      if (parentUid) {\n        namespace._contexts.set(uid, namespace._contexts.get(parentUid));\n        if (DEBUG_CLS_HOOKED) {\n          debug2('PARENTID: ' + name + ' uid:' + uid + ' parent:' + parentUid + ' provider:' + provider);\n        }\n      } else {\n        namespace._contexts.set(currentUid, namespace.active);\n      }\n\n      if (DEBUG_CLS_HOOKED) {\n        debug2('INIT ' + name + ' uid:' + uid + ' parent:' + parentUid + ' provider:' + invertedProviders[provider]\n          + ' active:' + util.inspect(namespace.active, true));\n      }\n\n    },\n    pre(uid, handle) {\n      currentUid = uid;\n      let context = namespace._contexts.get(uid);\n      if (context) {\n        if (DEBUG_CLS_HOOKED) {\n          debug2(' PRE ' + name + ' uid:' + uid + ' handle:' + getFunctionName(handle) + ' context:' +\n            util.inspect(context));\n        }\n\n        namespace.enter(context);\n      } else {\n        if (DEBUG_CLS_HOOKED) {\n          debug2(' PRE MISSING CONTEXT ' + name + ' uid:' + uid + ' handle:' + getFunctionName(handle));\n        }\n      }\n    },\n    post(uid, handle) {\n      currentUid = uid;\n      let context = namespace._contexts.get(uid);\n      if (context) {\n        if (DEBUG_CLS_HOOKED) {\n          debug2(' POST ' + name + ' uid:' + uid + ' handle:' + getFunctionName(handle) + ' context:' +\n            util.inspect(context));\n        }\n\n        namespace.exit(context);\n      } else {\n        if (DEBUG_CLS_HOOKED) {\n          debug2(' POST MISSING CONTEXT ' + name + ' uid:' + uid + ' handle:' + getFunctionName(handle));\n        }\n      }\n    },\n    destroy(uid) {\n      currentUid = uid;\n\n      if (DEBUG_CLS_HOOKED) {\n        debug2('DESTROY ' + name + ' uid:' + uid + ' context:' + util.inspect(namespace._contexts.get(currentUid))\n          + ' active:' + util.inspect(namespace.active, true));\n      }\n\n      namespace._contexts.delete(uid);\n    }\n  });\n\n  process.namespaces[name] = namespace;\n  return namespace;\n}\n\nfunction destroyNamespace(name) {\n  let namespace = getNamespace(name);\n\n  assert.ok(namespace, 'can\\'t delete nonexistent namespace! \"' + name + '\"');\n  assert.ok(namespace.id, 'don\\'t assign to process.namespaces directly! ' + util.inspect(namespace));\n\n  process.namespaces[name] = null;\n}\n\nfunction reset() {\n  // must unregister async listeners\n  if (process.namespaces) {\n    Object.keys(process.namespaces).forEach(function (name) {\n      destroyNamespace(name);\n    });\n  }\n  process.namespaces = Object.create(null);\n}\n\nprocess.namespaces = {};\n\nif (asyncHook._state && !asyncHook._state.enabled) {\n  asyncHook.enable();\n}\n\nfunction debug2(msg) {\n  if (process.env.DEBUG) {\n    process._rawDebug(msg);\n  }\n}\n\n\n/*function debug(from, ns) {\n process._rawDebug('DEBUG: ' + util.inspect({\n from: from,\n currentUid: currentUid,\n context: ns ? ns._contexts.get(currentUid) : 'no ns'\n }, true, 2, true));\n }*/\n\n\nfunction getFunctionName(fn) {\n  if (!fn) {\n    return fn;\n  }\n  if (typeof fn === 'function') {\n    if (fn.name) {\n      return fn.name;\n    }\n    return (fn.toString().trim().match(/^function\\s*([^\\s(]+)/) || [])[1];\n  } else if (fn.constructor && fn.constructor.name) {\n    return fn.constructor.name;\n  }\n}\n\n\n// Add back to callstack\nif (DEBUG_CLS_HOOKED) {\n  var stackChain = __webpack_require__(/*! stack-chain */ \"(instrument)/../../node_modules/stack-chain/index.js\");\n  for (var modifier in stackChain.filter._modifiers) {\n    stackChain.filter.deattach(modifier);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/cls-hooked/context-legacy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/cls-hooked/context.js":
/*!************************************************!*\
  !*** ../../node_modules/cls-hooked/context.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/* eslint-disable max-len */\n\n\nconst util = __webpack_require__(/*! util */ \"util\");\nconst assert = __webpack_require__(/*! assert */ \"assert\");\nconst wrapEmitter = __webpack_require__(/*! emitter-listener */ \"(instrument)/../../node_modules/emitter-listener/listener.js\");\nconst async_hooks = __webpack_require__(/*! async_hooks */ \"async_hooks\");\n\nconst CONTEXTS_SYMBOL = 'cls@contexts';\nconst ERROR_SYMBOL = 'error@context';\n\nconst DEBUG_CLS_HOOKED = process.env.DEBUG_CLS_HOOKED;\n\nlet currentUid = -1;\n\nmodule.exports = {\n  getNamespace: getNamespace,\n  createNamespace: createNamespace,\n  destroyNamespace: destroyNamespace,\n  reset: reset,\n  ERROR_SYMBOL: ERROR_SYMBOL\n};\n\nfunction Namespace(name) {\n  this.name = name;\n  // changed in 2.7: no default context\n  this.active = null;\n  this._set = [];\n  this.id = null;\n  this._contexts = new Map();\n  this._indent = 0;\n}\n\nNamespace.prototype.set = function set(key, value) {\n  if (!this.active) {\n    throw new Error('No context available. ns.run() or ns.bind() must be called first.');\n  }\n\n  this.active[key] = value;\n\n  if (DEBUG_CLS_HOOKED) {\n    const indentStr = ' '.repeat(this._indent < 0 ? 0 : this._indent);\n    debug2(indentStr + 'CONTEXT-SET KEY:' + key + '=' + value + ' in ns:' + this.name + ' currentUid:' + currentUid + ' active:' + util.inspect(this.active, {showHidden:true, depth:2, colors:true}));\n  }\n\n  return value;\n};\n\nNamespace.prototype.get = function get(key) {\n  if (!this.active) {\n    if (DEBUG_CLS_HOOKED) {\n      const asyncHooksCurrentId = async_hooks.currentId();\n      const triggerId = async_hooks.triggerAsyncId();\n      const indentStr = ' '.repeat(this._indent < 0 ? 0 : this._indent);\n      //debug2(indentStr + 'CONTEXT-GETTING KEY NO ACTIVE NS:' + key + '=undefined' + ' (' + this.name + ') currentUid:' + currentUid + ' active:' + util.inspect(this.active, {showHidden:true, depth:2, colors:true}));\n      debug2(`${indentStr}CONTEXT-GETTING KEY NO ACTIVE NS: (${this.name}) ${key}=undefined currentUid:${currentUid} asyncHooksCurrentId:${asyncHooksCurrentId} triggerId:${triggerId} len:${this._set.length}`);\n    }\n    return undefined;\n  }\n  if (DEBUG_CLS_HOOKED) {\n    const asyncHooksCurrentId = async_hooks.executionAsyncId();\n    const triggerId = async_hooks.triggerAsyncId();\n    const indentStr = ' '.repeat(this._indent < 0 ? 0 : this._indent);\n    debug2(indentStr + 'CONTEXT-GETTING KEY:' + key + '=' + this.active[key] + ' (' + this.name + ') currentUid:' + currentUid + ' active:' + util.inspect(this.active, {showHidden:true, depth:2, colors:true}));\n    debug2(`${indentStr}CONTEXT-GETTING KEY: (${this.name}) ${key}=${this.active[key]} currentUid:${currentUid} asyncHooksCurrentId:${asyncHooksCurrentId} triggerId:${triggerId} len:${this._set.length} active:${util.inspect(this.active)}`);\n  }\n  return this.active[key];\n};\n\nNamespace.prototype.createContext = function createContext() {\n  // Prototype inherit existing context if created a new child context within existing context.\n  let context = Object.create(this.active ? this.active : Object.prototype);\n  context._ns_name = this.name;\n  context.id = currentUid;\n\n  if (DEBUG_CLS_HOOKED) {\n    const asyncHooksCurrentId = async_hooks.executionAsyncId();\n    const triggerId = async_hooks.triggerAsyncId();\n    const indentStr = ' '.repeat(this._indent < 0 ? 0 : this._indent);\n    debug2(`${indentStr}CONTEXT-CREATED Context: (${this.name}) currentUid:${currentUid} asyncHooksCurrentId:${asyncHooksCurrentId} triggerId:${triggerId} len:${this._set.length} context:${util.inspect(context, {showHidden:true, depth:2, colors:true})}`);\n  }\n\n  return context;\n};\n\nNamespace.prototype.run = function run(fn) {\n  let context = this.createContext();\n  this.enter(context);\n\n  try {\n    if (DEBUG_CLS_HOOKED) {\n      const triggerId = async_hooks.triggerAsyncId();\n      const asyncHooksCurrentId = async_hooks.executionAsyncId();\n      const indentStr = ' '.repeat(this._indent < 0 ? 0 : this._indent);\n      debug2(`${indentStr}CONTEXT-RUN BEGIN: (${this.name}) currentUid:${currentUid} triggerId:${triggerId} asyncHooksCurrentId:${asyncHooksCurrentId} len:${this._set.length} context:${util.inspect(context)}`);\n    }\n    fn(context);\n    return context;\n  } catch (exception) {\n    if (exception) {\n      exception[ERROR_SYMBOL] = context;\n    }\n    throw exception;\n  } finally {\n    if (DEBUG_CLS_HOOKED) {\n      const triggerId = async_hooks.triggerAsyncId();\n      const asyncHooksCurrentId = async_hooks.executionAsyncId();\n      const indentStr = ' '.repeat(this._indent < 0 ? 0 : this._indent);\n      debug2(`${indentStr}CONTEXT-RUN END: (${this.name}) currentUid:${currentUid} triggerId:${triggerId} asyncHooksCurrentId:${asyncHooksCurrentId} len:${this._set.length} ${util.inspect(context)}`);\n    }\n    this.exit(context);\n  }\n};\n\nNamespace.prototype.runAndReturn = function runAndReturn(fn) {\n  let value;\n  this.run(function (context) {\n    value = fn(context);\n  });\n  return value;\n};\n\n/**\n * Uses global Promise and assumes Promise is cls friendly or wrapped already.\n * @param {function} fn\n * @returns {*}\n */\nNamespace.prototype.runPromise = function runPromise(fn) {\n  let context = this.createContext();\n  this.enter(context);\n\n  let promise = fn(context);\n  if (!promise || !promise.then || !promise.catch) {\n    throw new Error('fn must return a promise.');\n  }\n\n  if (DEBUG_CLS_HOOKED) {\n    debug2('CONTEXT-runPromise BEFORE: (' + this.name + ') currentUid:' + currentUid + ' len:' + this._set.length + ' ' + util.inspect(context));\n  }\n\n  return promise\n    .then(result => {\n      if (DEBUG_CLS_HOOKED) {\n        debug2('CONTEXT-runPromise AFTER then: (' + this.name + ') currentUid:' + currentUid + ' len:' + this._set.length + ' ' + util.inspect(context));\n      }\n      this.exit(context);\n      return result;\n    })\n    .catch(err => {\n      err[ERROR_SYMBOL] = context;\n      if (DEBUG_CLS_HOOKED) {\n        debug2('CONTEXT-runPromise AFTER catch: (' + this.name + ') currentUid:' + currentUid + ' len:' + this._set.length + ' ' + util.inspect(context));\n      }\n      this.exit(context);\n      throw err;\n    });\n};\n\nNamespace.prototype.bind = function bindFactory(fn, context) {\n  if (!context) {\n    if (!this.active) {\n      context = this.createContext();\n    } else {\n      context = this.active;\n    }\n  }\n\n  let self = this;\n  return function clsBind() {\n    self.enter(context);\n    try {\n      return fn.apply(this, arguments);\n    } catch (exception) {\n      if (exception) {\n        exception[ERROR_SYMBOL] = context;\n      }\n      throw exception;\n    } finally {\n      self.exit(context);\n    }\n  };\n};\n\nNamespace.prototype.enter = function enter(context) {\n  assert.ok(context, 'context must be provided for entering');\n  if (DEBUG_CLS_HOOKED) {\n    const asyncHooksCurrentId = async_hooks.executionAsyncId();\n    const triggerId = async_hooks.triggerAsyncId();\n    const indentStr = ' '.repeat(this._indent < 0 ? 0 : this._indent);\n    debug2(`${indentStr}CONTEXT-ENTER: (${this.name}) currentUid:${currentUid} triggerId:${triggerId} asyncHooksCurrentId:${asyncHooksCurrentId} len:${this._set.length} ${util.inspect(context)}`);\n  }\n\n  this._set.push(this.active);\n  this.active = context;\n};\n\nNamespace.prototype.exit = function exit(context) {\n  assert.ok(context, 'context must be provided for exiting');\n  if (DEBUG_CLS_HOOKED) {\n    const asyncHooksCurrentId = async_hooks.executionAsyncId();\n    const triggerId = async_hooks.triggerAsyncId();\n    const indentStr = ' '.repeat(this._indent < 0 ? 0 : this._indent);\n    debug2(`${indentStr}CONTEXT-EXIT: (${this.name}) currentUid:${currentUid} triggerId:${triggerId} asyncHooksCurrentId:${asyncHooksCurrentId} len:${this._set.length} ${util.inspect(context)}`);\n  }\n\n  // Fast path for most exits that are at the top of the stack\n  if (this.active === context) {\n    assert.ok(this._set.length, 'can\\'t remove top context');\n    this.active = this._set.pop();\n    return;\n  }\n\n  // Fast search in the stack using lastIndexOf\n  let index = this._set.lastIndexOf(context);\n\n  if (index < 0) {\n    if (DEBUG_CLS_HOOKED) {\n      debug2('??ERROR?? context exiting but not entered - ignoring: ' + util.inspect(context));\n    }\n    assert.ok(index >= 0, 'context not currently entered; can\\'t exit. \\n' + util.inspect(this) + '\\n' + util.inspect(context));\n  } else {\n    assert.ok(index, 'can\\'t remove top context');\n    this._set.splice(index, 1);\n  }\n};\n\nNamespace.prototype.bindEmitter = function bindEmitter(emitter) {\n  assert.ok(emitter.on && emitter.addListener && emitter.emit, 'can only bind real EEs');\n\n  let namespace = this;\n  let thisSymbol = 'context@' + this.name;\n\n  // Capture the context active at the time the emitter is bound.\n  function attach(listener) {\n    if (!listener) {\n      return;\n    }\n    if (!listener[CONTEXTS_SYMBOL]) {\n      listener[CONTEXTS_SYMBOL] = Object.create(null);\n    }\n\n    listener[CONTEXTS_SYMBOL][thisSymbol] = {\n      namespace: namespace,\n      context: namespace.active\n    };\n  }\n\n  // At emit time, bind the listener within the correct context.\n  function bind(unwrapped) {\n    if (!(unwrapped && unwrapped[CONTEXTS_SYMBOL])) {\n      return unwrapped;\n    }\n\n    let wrapped = unwrapped;\n    let unwrappedContexts = unwrapped[CONTEXTS_SYMBOL];\n    Object.keys(unwrappedContexts).forEach(function (name) {\n      let thunk = unwrappedContexts[name];\n      wrapped = thunk.namespace.bind(wrapped, thunk.context);\n    });\n    return wrapped;\n  }\n\n  wrapEmitter(emitter, attach, bind);\n};\n\n/**\n * If an error comes out of a namespace, it will have a context attached to it.\n * This function knows how to find it.\n *\n * @param {Error} exception Possibly annotated error.\n */\nNamespace.prototype.fromException = function fromException(exception) {\n  return exception[ERROR_SYMBOL];\n};\n\nfunction getNamespace(name) {\n  return process.namespaces[name];\n}\n\nfunction createNamespace(name) {\n  assert.ok(name, 'namespace must be given a name.');\n\n  if (DEBUG_CLS_HOOKED) {\n    debug2(`NS-CREATING NAMESPACE (${name})`);\n  }\n  let namespace = new Namespace(name);\n  namespace.id = currentUid;\n\n  const hook = async_hooks.createHook({\n    init(asyncId, type, triggerId, resource) {\n      currentUid = async_hooks.executionAsyncId();\n\n      //CHAIN Parent's Context onto child if none exists. This is needed to pass net-events.spec\n      // let initContext = namespace.active;\n      // if(!initContext && triggerId) {\n      //   let parentContext = namespace._contexts.get(triggerId);\n      //   if (parentContext) {\n      //     namespace.active = parentContext;\n      //     namespace._contexts.set(currentUid, parentContext);\n      //     if (DEBUG_CLS_HOOKED) {\n      //       const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n      //       debug2(`${indentStr}INIT [${type}] (${name}) WITH PARENT CONTEXT asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, true)} resource:${resource}`);\n      //     }\n      //   } else if (DEBUG_CLS_HOOKED) {\n      //       const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n      //       debug2(`${indentStr}INIT [${type}] (${name}) MISSING CONTEXT asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, true)} resource:${resource}`);\n      //     }\n      // }else {\n      //   namespace._contexts.set(currentUid, namespace.active);\n      //   if (DEBUG_CLS_HOOKED) {\n      //     const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n      //     debug2(`${indentStr}INIT [${type}] (${name}) asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, true)} resource:${resource}`);\n      //   }\n      // }\n      if(namespace.active) {\n        namespace._contexts.set(asyncId, namespace.active);\n\n        if (DEBUG_CLS_HOOKED) {\n          const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n          debug2(`${indentStr}INIT [${type}] (${name}) asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, {showHidden:true, depth:2, colors:true})} resource:${resource}`);\n        }\n      }else if(currentUid === 0){\n        // CurrentId will be 0 when triggered from C++. Promise events\n        // https://github.com/nodejs/node/blob/master/doc/api/async_hooks.md#triggerid\n        const triggerId = async_hooks.triggerAsyncId();\n        const triggerIdContext = namespace._contexts.get(triggerId);\n        if (triggerIdContext) {\n          namespace._contexts.set(asyncId, triggerIdContext);\n          if (DEBUG_CLS_HOOKED) {\n            const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n            debug2(`${indentStr}INIT USING CONTEXT FROM TRIGGERID [${type}] (${name}) asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, { showHidden: true, depth: 2, colors: true })} resource:${resource}`);\n          }\n        } else if (DEBUG_CLS_HOOKED) {\n          const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n          debug2(`${indentStr}INIT MISSING CONTEXT [${type}] (${name}) asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, { showHidden: true, depth: 2, colors: true })} resource:${resource}`);\n        }\n      }\n\n\n      if(DEBUG_CLS_HOOKED && type === 'PROMISE'){\n        debug2(util.inspect(resource, {showHidden: true}));\n        const parentId = resource.parentId;\n        const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n        debug2(`${indentStr}INIT RESOURCE-PROMISE [${type}] (${name}) parentId:${parentId} asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, {showHidden:true, depth:2, colors:true})} resource:${resource}`);\n      }\n\n    },\n    before(asyncId) {\n      currentUid = async_hooks.executionAsyncId();\n      let context;\n\n      /*\n      if(currentUid === 0){\n        // CurrentId will be 0 when triggered from C++. Promise events\n        // https://github.com/nodejs/node/blob/master/doc/api/async_hooks.md#triggerid\n        //const triggerId = async_hooks.triggerAsyncId();\n        context = namespace._contexts.get(asyncId); // || namespace._contexts.get(triggerId);\n      }else{\n        context = namespace._contexts.get(currentUid);\n      }\n      */\n\n      //HACK to work with promises until they are fixed in node > 8.1.1\n      context = namespace._contexts.get(asyncId) || namespace._contexts.get(currentUid);\n\n      if (context) {\n        if (DEBUG_CLS_HOOKED) {\n          const triggerId = async_hooks.triggerAsyncId();\n          const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n          debug2(`${indentStr}BEFORE (${name}) asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, {showHidden:true, depth:2, colors:true})} context:${util.inspect(context)}`);\n          namespace._indent += 2;\n        }\n\n        namespace.enter(context);\n\n      } else if (DEBUG_CLS_HOOKED) {\n        const triggerId = async_hooks.triggerAsyncId();\n        const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n        debug2(`${indentStr}BEFORE MISSING CONTEXT (${name}) asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, {showHidden:true, depth:2, colors:true})} namespace._contexts:${util.inspect(namespace._contexts, {showHidden:true, depth:2, colors:true})}`);\n        namespace._indent += 2;\n      }\n    },\n    after(asyncId) {\n      currentUid = async_hooks.executionAsyncId();\n      let context; // = namespace._contexts.get(currentUid);\n      /*\n      if(currentUid === 0){\n        // CurrentId will be 0 when triggered from C++. Promise events\n        // https://github.com/nodejs/node/blob/master/doc/api/async_hooks.md#triggerid\n        //const triggerId = async_hooks.triggerAsyncId();\n        context = namespace._contexts.get(asyncId); // || namespace._contexts.get(triggerId);\n      }else{\n        context = namespace._contexts.get(currentUid);\n      }\n      */\n      //HACK to work with promises until they are fixed in node > 8.1.1\n      context = namespace._contexts.get(asyncId) || namespace._contexts.get(currentUid);\n\n      if (context) {\n        if (DEBUG_CLS_HOOKED) {\n          const triggerId = async_hooks.triggerAsyncId();\n          namespace._indent -= 2;\n          const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n          debug2(`${indentStr}AFTER (${name}) asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, {showHidden:true, depth:2, colors:true})} context:${util.inspect(context)}`);\n        }\n\n        namespace.exit(context);\n\n      } else if (DEBUG_CLS_HOOKED) {\n        const triggerId = async_hooks.triggerAsyncId();\n        namespace._indent -= 2;\n        const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n        debug2(`${indentStr}AFTER MISSING CONTEXT (${name}) asyncId:${asyncId} currentUid:${currentUid} triggerId:${triggerId} active:${util.inspect(namespace.active, {showHidden:true, depth:2, colors:true})} context:${util.inspect(context)}`);\n      }\n    },\n    destroy(asyncId) {\n      currentUid = async_hooks.executionAsyncId();\n      if (DEBUG_CLS_HOOKED) {\n        const triggerId = async_hooks.triggerAsyncId();\n        const indentStr = ' '.repeat(namespace._indent < 0 ? 0 : namespace._indent);\n        debug2(`${indentStr}DESTROY (${name}) currentUid:${currentUid} asyncId:${asyncId} triggerId:${triggerId} active:${util.inspect(namespace.active, {showHidden:true, depth:2, colors:true})} context:${util.inspect(namespace._contexts.get(currentUid))}`);\n      }\n\n      namespace._contexts.delete(asyncId);\n    }\n  });\n\n  hook.enable();\n\n  process.namespaces[name] = namespace;\n  return namespace;\n}\n\nfunction destroyNamespace(name) {\n  let namespace = getNamespace(name);\n\n  assert.ok(namespace, 'can\\'t delete nonexistent namespace! \"' + name + '\"');\n  assert.ok(namespace.id, 'don\\'t assign to process.namespaces directly! ' + util.inspect(namespace));\n\n  process.namespaces[name] = null;\n}\n\nfunction reset() {\n  // must unregister async listeners\n  if (process.namespaces) {\n    Object.keys(process.namespaces).forEach(function (name) {\n      destroyNamespace(name);\n    });\n  }\n  process.namespaces = Object.create(null);\n}\n\nprocess.namespaces = {};\n\n//const fs = require('fs');\nfunction debug2(...args) {\n  if (DEBUG_CLS_HOOKED) {\n    //fs.writeSync(1, `${util.format(...args)}\\n`);\n    process._rawDebug(`${util.format(...args)}`);\n  }\n}\n\n/*function getFunctionName(fn) {\n  if (!fn) {\n    return fn;\n  }\n  if (typeof fn === 'function') {\n    if (fn.name) {\n      return fn.name;\n    }\n    return (fn.toString().trim().match(/^function\\s*([^\\s(]+)/) || [])[1];\n  } else if (fn.constructor && fn.constructor.name) {\n    return fn.constructor.name;\n  }\n}*/\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/cls-hooked/context.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/cls-hooked/index.js":
/*!**********************************************!*\
  !*** ../../node_modules/cls-hooked/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nconst semver = __webpack_require__(/*! semver */ \"(instrument)/../../node_modules/cls-hooked/node_modules/semver/semver.js\");\n\n/**\n * In order to increase node version support, this loads the version of context\n * that is appropriate for the version of on nodejs that is running.\n * Node < v8 - uses AsyncWrap and async-hooks-jl\n * Node >= v8 - uses native async-hooks\n */\nif(process && semver.gte(process.versions.node, '8.0.0')){\n  module.exports = __webpack_require__(/*! ./context */ \"(instrument)/../../node_modules/cls-hooked/context.js\");\n}else{\n  module.exports = __webpack_require__(/*! ./context-legacy */ \"(instrument)/../../node_modules/cls-hooked/context-legacy.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9jbHMtaG9va2VkL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGVBQWUsbUJBQU8sQ0FBQyx3RkFBUTs7QUFFL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLDhHQUFxQztBQUN2QyxDQUFDO0FBQ0QsRUFBRSw0SEFBNEM7QUFDOUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2Nscy1ob29rZWQvaW5kZXguanM/NGU2NCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmNvbnN0IHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG4vKipcbiAqIEluIG9yZGVyIHRvIGluY3JlYXNlIG5vZGUgdmVyc2lvbiBzdXBwb3J0LCB0aGlzIGxvYWRzIHRoZSB2ZXJzaW9uIG9mIGNvbnRleHRcbiAqIHRoYXQgaXMgYXBwcm9wcmlhdGUgZm9yIHRoZSB2ZXJzaW9uIG9mIG9uIG5vZGVqcyB0aGF0IGlzIHJ1bm5pbmcuXG4gKiBOb2RlIDwgdjggLSB1c2VzIEFzeW5jV3JhcCBhbmQgYXN5bmMtaG9va3MtamxcbiAqIE5vZGUgPj0gdjggLSB1c2VzIG5hdGl2ZSBhc3luYy1ob29rc1xuICovXG5pZihwcm9jZXNzICYmIHNlbXZlci5ndGUocHJvY2Vzcy52ZXJzaW9ucy5ub2RlLCAnOC4wLjAnKSl7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9jb250ZXh0Jyk7XG59ZWxzZXtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2NvbnRleHQtbGVnYWN5Jyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/cls-hooked/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/cls-hooked/node_modules/semver/semver.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/cls-hooked/node_modules/semver/semver.js ***!
  \*******************************************************************/
/***/ ((module, exports) => {

eval("exports = module.exports = SemVer\n\nvar debug\n/* istanbul ignore next */\nif (typeof process === 'object' &&\n    process.env &&\n    process.env.NODE_DEBUG &&\n    /\\bsemver\\b/i.test(process.env.NODE_DEBUG)) {\n  debug = function () {\n    var args = Array.prototype.slice.call(arguments, 0)\n    args.unshift('SEMVER')\n    console.log.apply(console, args)\n  }\n} else {\n  debug = function () {}\n}\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nexports.SEMVER_SPEC_VERSION = '2.0.0'\n\nvar MAX_LENGTH = 256\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n  /* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nvar MAX_SAFE_COMPONENT_LENGTH = 16\n\nvar MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\n// The actual regexps go on exports.re\nvar re = exports.re = []\nvar safeRe = exports.safeRe = []\nvar src = exports.src = []\nvar R = 0\n\nvar LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nvar safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nfunction makeSafeRe (value) {\n  for (var i = 0; i < safeRegexReplacements.length; i++) {\n    var token = safeRegexReplacements[i][0]\n    var max = safeRegexReplacements[i][1]\n    value = value\n      .split(token + '*').join(token + '{0,' + max + '}')\n      .split(token + '+').join(token + '{1,' + max + '}')\n  }\n  return value\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\nvar NUMERICIDENTIFIER = R++\nsrc[NUMERICIDENTIFIER] = '0|[1-9]\\\\d*'\nvar NUMERICIDENTIFIERLOOSE = R++\nsrc[NUMERICIDENTIFIERLOOSE] = '\\\\d+'\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\nvar NONNUMERICIDENTIFIER = R++\nsrc[NONNUMERICIDENTIFIER] = '\\\\d*[a-zA-Z-]' + LETTERDASHNUMBER + '*'\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\nvar MAINVERSION = R++\nsrc[MAINVERSION] = '(' + src[NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[NUMERICIDENTIFIER] + ')'\n\nvar MAINVERSIONLOOSE = R++\nsrc[MAINVERSIONLOOSE] = '(' + src[NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[NUMERICIDENTIFIERLOOSE] + ')'\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n\nvar PRERELEASEIDENTIFIER = R++\nsrc[PRERELEASEIDENTIFIER] = '(?:' + src[NUMERICIDENTIFIER] +\n                            '|' + src[NONNUMERICIDENTIFIER] + ')'\n\nvar PRERELEASEIDENTIFIERLOOSE = R++\nsrc[PRERELEASEIDENTIFIERLOOSE] = '(?:' + src[NUMERICIDENTIFIERLOOSE] +\n                                 '|' + src[NONNUMERICIDENTIFIER] + ')'\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\nvar PRERELEASE = R++\nsrc[PRERELEASE] = '(?:-(' + src[PRERELEASEIDENTIFIER] +\n                  '(?:\\\\.' + src[PRERELEASEIDENTIFIER] + ')*))'\n\nvar PRERELEASELOOSE = R++\nsrc[PRERELEASELOOSE] = '(?:-?(' + src[PRERELEASEIDENTIFIERLOOSE] +\n                       '(?:\\\\.' + src[PRERELEASEIDENTIFIERLOOSE] + ')*))'\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\nvar BUILDIDENTIFIER = R++\nsrc[BUILDIDENTIFIER] = LETTERDASHNUMBER + '+'\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\nvar BUILD = R++\nsrc[BUILD] = '(?:\\\\+(' + src[BUILDIDENTIFIER] +\n             '(?:\\\\.' + src[BUILDIDENTIFIER] + ')*))'\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\nvar FULL = R++\nvar FULLPLAIN = 'v?' + src[MAINVERSION] +\n                src[PRERELEASE] + '?' +\n                src[BUILD] + '?'\n\nsrc[FULL] = '^' + FULLPLAIN + '$'\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\nvar LOOSEPLAIN = '[v=\\\\s]*' + src[MAINVERSIONLOOSE] +\n                 src[PRERELEASELOOSE] + '?' +\n                 src[BUILD] + '?'\n\nvar LOOSE = R++\nsrc[LOOSE] = '^' + LOOSEPLAIN + '$'\n\nvar GTLT = R++\nsrc[GTLT] = '((?:<|>)?=?)'\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\nvar XRANGEIDENTIFIERLOOSE = R++\nsrc[XRANGEIDENTIFIERLOOSE] = src[NUMERICIDENTIFIERLOOSE] + '|x|X|\\\\*'\nvar XRANGEIDENTIFIER = R++\nsrc[XRANGEIDENTIFIER] = src[NUMERICIDENTIFIER] + '|x|X|\\\\*'\n\nvar XRANGEPLAIN = R++\nsrc[XRANGEPLAIN] = '[v=\\\\s]*(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:' + src[PRERELEASE] + ')?' +\n                   src[BUILD] + '?' +\n                   ')?)?'\n\nvar XRANGEPLAINLOOSE = R++\nsrc[XRANGEPLAINLOOSE] = '[v=\\\\s]*(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:' + src[PRERELEASELOOSE] + ')?' +\n                        src[BUILD] + '?' +\n                        ')?)?'\n\nvar XRANGE = R++\nsrc[XRANGE] = '^' + src[GTLT] + '\\\\s*' + src[XRANGEPLAIN] + '$'\nvar XRANGELOOSE = R++\nsrc[XRANGELOOSE] = '^' + src[GTLT] + '\\\\s*' + src[XRANGEPLAINLOOSE] + '$'\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\nvar COERCE = R++\nsrc[COERCE] = '(?:^|[^\\\\d])' +\n              '(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '})' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:$|[^\\\\d])'\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\nvar LONETILDE = R++\nsrc[LONETILDE] = '(?:~>?)'\n\nvar TILDETRIM = R++\nsrc[TILDETRIM] = '(\\\\s*)' + src[LONETILDE] + '\\\\s+'\nre[TILDETRIM] = new RegExp(src[TILDETRIM], 'g')\nsafeRe[TILDETRIM] = new RegExp(makeSafeRe(src[TILDETRIM]), 'g')\nvar tildeTrimReplace = '$1~'\n\nvar TILDE = R++\nsrc[TILDE] = '^' + src[LONETILDE] + src[XRANGEPLAIN] + '$'\nvar TILDELOOSE = R++\nsrc[TILDELOOSE] = '^' + src[LONETILDE] + src[XRANGEPLAINLOOSE] + '$'\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\nvar LONECARET = R++\nsrc[LONECARET] = '(?:\\\\^)'\n\nvar CARETTRIM = R++\nsrc[CARETTRIM] = '(\\\\s*)' + src[LONECARET] + '\\\\s+'\nre[CARETTRIM] = new RegExp(src[CARETTRIM], 'g')\nsafeRe[CARETTRIM] = new RegExp(makeSafeRe(src[CARETTRIM]), 'g')\nvar caretTrimReplace = '$1^'\n\nvar CARET = R++\nsrc[CARET] = '^' + src[LONECARET] + src[XRANGEPLAIN] + '$'\nvar CARETLOOSE = R++\nsrc[CARETLOOSE] = '^' + src[LONECARET] + src[XRANGEPLAINLOOSE] + '$'\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\nvar COMPARATORLOOSE = R++\nsrc[COMPARATORLOOSE] = '^' + src[GTLT] + '\\\\s*(' + LOOSEPLAIN + ')$|^$'\nvar COMPARATOR = R++\nsrc[COMPARATOR] = '^' + src[GTLT] + '\\\\s*(' + FULLPLAIN + ')$|^$'\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\nvar COMPARATORTRIM = R++\nsrc[COMPARATORTRIM] = '(\\\\s*)' + src[GTLT] +\n                      '\\\\s*(' + LOOSEPLAIN + '|' + src[XRANGEPLAIN] + ')'\n\n// this one has to use the /g flag\nre[COMPARATORTRIM] = new RegExp(src[COMPARATORTRIM], 'g')\nsafeRe[COMPARATORTRIM] = new RegExp(makeSafeRe(src[COMPARATORTRIM]), 'g')\nvar comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\nvar HYPHENRANGE = R++\nsrc[HYPHENRANGE] = '^\\\\s*(' + src[XRANGEPLAIN] + ')' +\n                   '\\\\s+-\\\\s+' +\n                   '(' + src[XRANGEPLAIN] + ')' +\n                   '\\\\s*$'\n\nvar HYPHENRANGELOOSE = R++\nsrc[HYPHENRANGELOOSE] = '^\\\\s*(' + src[XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s+-\\\\s+' +\n                        '(' + src[XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s*$'\n\n// Star ranges basically just allow anything at all.\nvar STAR = R++\nsrc[STAR] = '(<|>)?=?\\\\s*\\\\*'\n\n// Compile to actual regexp objects.\n// All are flag-free, unless they were created above with a flag.\nfor (var i = 0; i < R; i++) {\n  debug(i, src[i])\n  if (!re[i]) {\n    re[i] = new RegExp(src[i])\n\n    // Replace all greedy whitespace to prevent regex dos issues. These regex are\n    // used internally via the safeRe object since all inputs in this library get\n    // normalized first to trim and collapse all extra whitespace. The original\n    // regexes are exported for userland consumption and lower level usage. A\n    // future breaking change could export the safer regex only with a note that\n    // all input should have extra whitespace removed.\n    safeRe[i] = new RegExp(makeSafeRe(src[i]))\n  }\n}\n\nexports.parse = parse\nfunction parse (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  if (version.length > MAX_LENGTH) {\n    return null\n  }\n\n  var r = options.loose ? safeRe[LOOSE] : safeRe[FULL]\n  if (!r.test(version)) {\n    return null\n  }\n\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    return null\n  }\n}\n\nexports.valid = valid\nfunction valid (version, options) {\n  var v = parse(version, options)\n  return v ? v.version : null\n}\n\nexports.clean = clean\nfunction clean (version, options) {\n  var s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\n\nexports.SemVer = SemVer\n\nfunction SemVer (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n  if (version instanceof SemVer) {\n    if (version.loose === options.loose) {\n      return version\n    } else {\n      version = version.version\n    }\n  } else if (typeof version !== 'string') {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  if (version.length > MAX_LENGTH) {\n    throw new TypeError('version is longer than ' + MAX_LENGTH + ' characters')\n  }\n\n  if (!(this instanceof SemVer)) {\n    return new SemVer(version, options)\n  }\n\n  debug('SemVer', version, options)\n  this.options = options\n  this.loose = !!options.loose\n\n  var m = version.trim().match(options.loose ? safeRe[LOOSE] : safeRe[FULL])\n\n  if (!m) {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  this.raw = version\n\n  // these are actually numbers\n  this.major = +m[1]\n  this.minor = +m[2]\n  this.patch = +m[3]\n\n  if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n    throw new TypeError('Invalid major version')\n  }\n\n  if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n    throw new TypeError('Invalid minor version')\n  }\n\n  if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n    throw new TypeError('Invalid patch version')\n  }\n\n  // numberify any prerelease numeric ids\n  if (!m[4]) {\n    this.prerelease = []\n  } else {\n    this.prerelease = m[4].split('.').map(function (id) {\n      if (/^[0-9]+$/.test(id)) {\n        var num = +id\n        if (num >= 0 && num < MAX_SAFE_INTEGER) {\n          return num\n        }\n      }\n      return id\n    })\n  }\n\n  this.build = m[5] ? m[5].split('.') : []\n  this.format()\n}\n\nSemVer.prototype.format = function () {\n  this.version = this.major + '.' + this.minor + '.' + this.patch\n  if (this.prerelease.length) {\n    this.version += '-' + this.prerelease.join('.')\n  }\n  return this.version\n}\n\nSemVer.prototype.toString = function () {\n  return this.version\n}\n\nSemVer.prototype.compare = function (other) {\n  debug('SemVer.compare', this.version, this.options, other)\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return this.compareMain(other) || this.comparePre(other)\n}\n\nSemVer.prototype.compareMain = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return compareIdentifiers(this.major, other.major) ||\n         compareIdentifiers(this.minor, other.minor) ||\n         compareIdentifiers(this.patch, other.patch)\n}\n\nSemVer.prototype.comparePre = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  // NOT having a prerelease is > having one\n  if (this.prerelease.length && !other.prerelease.length) {\n    return -1\n  } else if (!this.prerelease.length && other.prerelease.length) {\n    return 1\n  } else if (!this.prerelease.length && !other.prerelease.length) {\n    return 0\n  }\n\n  var i = 0\n  do {\n    var a = this.prerelease[i]\n    var b = other.prerelease[i]\n    debug('prerelease compare', i, a, b)\n    if (a === undefined && b === undefined) {\n      return 0\n    } else if (b === undefined) {\n      return 1\n    } else if (a === undefined) {\n      return -1\n    } else if (a === b) {\n      continue\n    } else {\n      return compareIdentifiers(a, b)\n    }\n  } while (++i)\n}\n\n// preminor will bump the version up to the next minor release, and immediately\n// down to pre-release. premajor and prepatch work the same way.\nSemVer.prototype.inc = function (release, identifier) {\n  switch (release) {\n    case 'premajor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor = 0\n      this.major++\n      this.inc('pre', identifier)\n      break\n    case 'preminor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor++\n      this.inc('pre', identifier)\n      break\n    case 'prepatch':\n      // If this is already a prerelease, it will bump to the next version\n      // drop any prereleases that might already exist, since they are not\n      // relevant at this point.\n      this.prerelease.length = 0\n      this.inc('patch', identifier)\n      this.inc('pre', identifier)\n      break\n    // If the input is a non-prerelease version, this acts the same as\n    // prepatch.\n    case 'prerelease':\n      if (this.prerelease.length === 0) {\n        this.inc('patch', identifier)\n      }\n      this.inc('pre', identifier)\n      break\n\n    case 'major':\n      // If this is a pre-major version, bump up to the same major version.\n      // Otherwise increment major.\n      // 1.0.0-5 bumps to 1.0.0\n      // 1.1.0 bumps to 2.0.0\n      if (this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0) {\n        this.major++\n      }\n      this.minor = 0\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'minor':\n      // If this is a pre-minor version, bump up to the same minor version.\n      // Otherwise increment minor.\n      // 1.2.0-5 bumps to 1.2.0\n      // 1.2.1 bumps to 1.3.0\n      if (this.patch !== 0 || this.prerelease.length === 0) {\n        this.minor++\n      }\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'patch':\n      // If this is not a pre-release version, it will increment the patch.\n      // If it is a pre-release it will bump up to the same patch version.\n      // 1.2.0-5 patches to 1.2.0\n      // 1.2.0 patches to 1.2.1\n      if (this.prerelease.length === 0) {\n        this.patch++\n      }\n      this.prerelease = []\n      break\n    // This probably shouldn't be used publicly.\n    // 1.0.0 \"pre\" would become 1.0.0-0 which is the wrong direction.\n    case 'pre':\n      if (this.prerelease.length === 0) {\n        this.prerelease = [0]\n      } else {\n        var i = this.prerelease.length\n        while (--i >= 0) {\n          if (typeof this.prerelease[i] === 'number') {\n            this.prerelease[i]++\n            i = -2\n          }\n        }\n        if (i === -1) {\n          // didn't increment anything\n          this.prerelease.push(0)\n        }\n      }\n      if (identifier) {\n        // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n        // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n        if (this.prerelease[0] === identifier) {\n          if (isNaN(this.prerelease[1])) {\n            this.prerelease = [identifier, 0]\n          }\n        } else {\n          this.prerelease = [identifier, 0]\n        }\n      }\n      break\n\n    default:\n      throw new Error('invalid increment argument: ' + release)\n  }\n  this.format()\n  this.raw = this.version\n  return this\n}\n\nexports.inc = inc\nfunction inc (version, release, loose, identifier) {\n  if (typeof (loose) === 'string') {\n    identifier = loose\n    loose = undefined\n  }\n\n  try {\n    return new SemVer(version, loose).inc(release, identifier).version\n  } catch (er) {\n    return null\n  }\n}\n\nexports.diff = diff\nfunction diff (version1, version2) {\n  if (eq(version1, version2)) {\n    return null\n  } else {\n    var v1 = parse(version1)\n    var v2 = parse(version2)\n    var prefix = ''\n    if (v1.prerelease.length || v2.prerelease.length) {\n      prefix = 'pre'\n      var defaultResult = 'prerelease'\n    }\n    for (var key in v1) {\n      if (key === 'major' || key === 'minor' || key === 'patch') {\n        if (v1[key] !== v2[key]) {\n          return prefix + key\n        }\n      }\n    }\n    return defaultResult // may be undefined\n  }\n}\n\nexports.compareIdentifiers = compareIdentifiers\n\nvar numeric = /^[0-9]+$/\nfunction compareIdentifiers (a, b) {\n  var anum = numeric.test(a)\n  var bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nexports.rcompareIdentifiers = rcompareIdentifiers\nfunction rcompareIdentifiers (a, b) {\n  return compareIdentifiers(b, a)\n}\n\nexports.major = major\nfunction major (a, loose) {\n  return new SemVer(a, loose).major\n}\n\nexports.minor = minor\nfunction minor (a, loose) {\n  return new SemVer(a, loose).minor\n}\n\nexports.patch = patch\nfunction patch (a, loose) {\n  return new SemVer(a, loose).patch\n}\n\nexports.compare = compare\nfunction compare (a, b, loose) {\n  return new SemVer(a, loose).compare(new SemVer(b, loose))\n}\n\nexports.compareLoose = compareLoose\nfunction compareLoose (a, b) {\n  return compare(a, b, true)\n}\n\nexports.rcompare = rcompare\nfunction rcompare (a, b, loose) {\n  return compare(b, a, loose)\n}\n\nexports.sort = sort\nfunction sort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.compare(a, b, loose)\n  })\n}\n\nexports.rsort = rsort\nfunction rsort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.rcompare(a, b, loose)\n  })\n}\n\nexports.gt = gt\nfunction gt (a, b, loose) {\n  return compare(a, b, loose) > 0\n}\n\nexports.lt = lt\nfunction lt (a, b, loose) {\n  return compare(a, b, loose) < 0\n}\n\nexports.eq = eq\nfunction eq (a, b, loose) {\n  return compare(a, b, loose) === 0\n}\n\nexports.neq = neq\nfunction neq (a, b, loose) {\n  return compare(a, b, loose) !== 0\n}\n\nexports.gte = gte\nfunction gte (a, b, loose) {\n  return compare(a, b, loose) >= 0\n}\n\nexports.lte = lte\nfunction lte (a, b, loose) {\n  return compare(a, b, loose) <= 0\n}\n\nexports.cmp = cmp\nfunction cmp (a, op, b, loose) {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError('Invalid operator: ' + op)\n  }\n}\n\nexports.Comparator = Comparator\nfunction Comparator (comp, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (comp instanceof Comparator) {\n    if (comp.loose === !!options.loose) {\n      return comp\n    } else {\n      comp = comp.value\n    }\n  }\n\n  if (!(this instanceof Comparator)) {\n    return new Comparator(comp, options)\n  }\n\n  comp = comp.trim().split(/\\s+/).join(' ')\n  debug('comparator', comp, options)\n  this.options = options\n  this.loose = !!options.loose\n  this.parse(comp)\n\n  if (this.semver === ANY) {\n    this.value = ''\n  } else {\n    this.value = this.operator + this.semver.version\n  }\n\n  debug('comp', this)\n}\n\nvar ANY = {}\nComparator.prototype.parse = function (comp) {\n  var r = this.options.loose ? safeRe[COMPARATORLOOSE] : safeRe[COMPARATOR]\n  var m = comp.match(r)\n\n  if (!m) {\n    throw new TypeError('Invalid comparator: ' + comp)\n  }\n\n  this.operator = m[1]\n  if (this.operator === '=') {\n    this.operator = ''\n  }\n\n  // if it literally is just '>' or '' then allow anything.\n  if (!m[2]) {\n    this.semver = ANY\n  } else {\n    this.semver = new SemVer(m[2], this.options.loose)\n  }\n}\n\nComparator.prototype.toString = function () {\n  return this.value\n}\n\nComparator.prototype.test = function (version) {\n  debug('Comparator.test', version, this.options.loose)\n\n  if (this.semver === ANY) {\n    return true\n  }\n\n  if (typeof version === 'string') {\n    version = new SemVer(version, this.options)\n  }\n\n  return cmp(version, this.operator, this.semver, this.options)\n}\n\nComparator.prototype.intersects = function (comp, options) {\n  if (!(comp instanceof Comparator)) {\n    throw new TypeError('a Comparator is required')\n  }\n\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  var rangeTmp\n\n  if (this.operator === '') {\n    rangeTmp = new Range(comp.value, options)\n    return satisfies(this.value, rangeTmp, options)\n  } else if (comp.operator === '') {\n    rangeTmp = new Range(this.value, options)\n    return satisfies(comp.semver, rangeTmp, options)\n  }\n\n  var sameDirectionIncreasing =\n    (this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '>=' || comp.operator === '>')\n  var sameDirectionDecreasing =\n    (this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '<=' || comp.operator === '<')\n  var sameSemVer = this.semver.version === comp.semver.version\n  var differentDirectionsInclusive =\n    (this.operator === '>=' || this.operator === '<=') &&\n    (comp.operator === '>=' || comp.operator === '<=')\n  var oppositeDirectionsLessThan =\n    cmp(this.semver, '<', comp.semver, options) &&\n    ((this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '<=' || comp.operator === '<'))\n  var oppositeDirectionsGreaterThan =\n    cmp(this.semver, '>', comp.semver, options) &&\n    ((this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '>=' || comp.operator === '>'))\n\n  return sameDirectionIncreasing || sameDirectionDecreasing ||\n    (sameSemVer && differentDirectionsInclusive) ||\n    oppositeDirectionsLessThan || oppositeDirectionsGreaterThan\n}\n\nexports.Range = Range\nfunction Range (range, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (range instanceof Range) {\n    if (range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease) {\n      return range\n    } else {\n      return new Range(range.raw, options)\n    }\n  }\n\n  if (range instanceof Comparator) {\n    return new Range(range.value, options)\n  }\n\n  if (!(this instanceof Range)) {\n    return new Range(range, options)\n  }\n\n  this.options = options\n  this.loose = !!options.loose\n  this.includePrerelease = !!options.includePrerelease\n\n  // First reduce all whitespace as much as possible so we do not have to rely\n  // on potentially slow regexes like \\s*. This is then stored and used for\n  // future error messages as well.\n  this.raw = range\n    .trim()\n    .split(/\\s+/)\n    .join(' ')\n\n  // First, split based on boolean or ||\n  this.set = this.raw.split('||').map(function (range) {\n    return this.parseRange(range.trim())\n  }, this).filter(function (c) {\n    // throw out any that are not relevant for whatever reason\n    return c.length\n  })\n\n  if (!this.set.length) {\n    throw new TypeError('Invalid SemVer Range: ' + this.raw)\n  }\n\n  this.format()\n}\n\nRange.prototype.format = function () {\n  this.range = this.set.map(function (comps) {\n    return comps.join(' ').trim()\n  }).join('||').trim()\n  return this.range\n}\n\nRange.prototype.toString = function () {\n  return this.range\n}\n\nRange.prototype.parseRange = function (range) {\n  var loose = this.options.loose\n  // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n  var hr = loose ? safeRe[HYPHENRANGELOOSE] : safeRe[HYPHENRANGE]\n  range = range.replace(hr, hyphenReplace)\n  debug('hyphen replace', range)\n  // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n  range = range.replace(safeRe[COMPARATORTRIM], comparatorTrimReplace)\n  debug('comparator trim', range, safeRe[COMPARATORTRIM])\n\n  // `~ 1.2.3` => `~1.2.3`\n  range = range.replace(safeRe[TILDETRIM], tildeTrimReplace)\n\n  // `^ 1.2.3` => `^1.2.3`\n  range = range.replace(safeRe[CARETTRIM], caretTrimReplace)\n\n  // At this point, the range is completely trimmed and\n  // ready to be split into comparators.\n  var compRe = loose ? safeRe[COMPARATORLOOSE] : safeRe[COMPARATOR]\n  var set = range.split(' ').map(function (comp) {\n    return parseComparator(comp, this.options)\n  }, this).join(' ').split(/\\s+/)\n  if (this.options.loose) {\n    // in loose mode, throw out any that are not valid comparators\n    set = set.filter(function (comp) {\n      return !!comp.match(compRe)\n    })\n  }\n  set = set.map(function (comp) {\n    return new Comparator(comp, this.options)\n  }, this)\n\n  return set\n}\n\nRange.prototype.intersects = function (range, options) {\n  if (!(range instanceof Range)) {\n    throw new TypeError('a Range is required')\n  }\n\n  return this.set.some(function (thisComparators) {\n    return thisComparators.every(function (thisComparator) {\n      return range.set.some(function (rangeComparators) {\n        return rangeComparators.every(function (rangeComparator) {\n          return thisComparator.intersects(rangeComparator, options)\n        })\n      })\n    })\n  })\n}\n\n// Mostly just for testing and legacy API reasons\nexports.toComparators = toComparators\nfunction toComparators (range, options) {\n  return new Range(range, options).set.map(function (comp) {\n    return comp.map(function (c) {\n      return c.value\n    }).join(' ').trim().split(' ')\n  })\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nfunction parseComparator (comp, options) {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nfunction isX (id) {\n  return !id || id.toLowerCase() === 'x' || id === '*'\n}\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0\nfunction replaceTildes (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceTilde(comp, options)\n  }).join(' ')\n}\n\nfunction replaceTilde (comp, options) {\n  var r = options.loose ? safeRe[TILDELOOSE] : safeRe[TILDE]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('tilde', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0\n      ret = '>=' + M + '.' + m + '.' + p +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0\n// ^1.2.3 --> >=1.2.3 <2.0.0\n// ^1.2.0 --> >=1.2.0 <2.0.0\nfunction replaceCarets (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceCaret(comp, options)\n  }).join(' ')\n}\n\nfunction replaceCaret (comp, options) {\n  debug('caret', comp, options)\n  var r = options.loose ? safeRe[CARETLOOSE] : safeRe[CARET]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('caret', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n      } else {\n        ret = '>=' + M + '.' + m + '.0 <' + (+M + 1) + '.0.0'\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nfunction replaceXRanges (comp, options) {\n  debug('replaceXRanges', comp, options)\n  return comp.split(/\\s+/).map(function (comp) {\n    return replaceXRange(comp, options)\n  }).join(' ')\n}\n\nfunction replaceXRange (comp, options) {\n  comp = comp.trim()\n  var r = options.loose ? safeRe[XRANGELOOSE] : safeRe[XRANGE]\n  return comp.replace(r, function (ret, gtlt, M, m, p, pr) {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    var xM = isX(M)\n    var xm = xM || isX(m)\n    var xp = xm || isX(p)\n    var anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        // >1.2.3 => >= 1.2.4\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      ret = gtlt + M + '.' + m + '.' + p\n    } else if (xm) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (xp) {\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nfunction replaceStars (comp, options) {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp.trim().replace(safeRe[STAR], '')\n}\n\n// This function is passed to string.replace(safeRe[HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0\nfunction hyphenReplace ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr, tb) {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = '>=' + fM + '.0.0'\n  } else if (isX(fp)) {\n    from = '>=' + fM + '.' + fm + '.0'\n  } else {\n    from = '>=' + from\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = '<' + (+tM + 1) + '.0.0'\n  } else if (isX(tp)) {\n    to = '<' + tM + '.' + (+tm + 1) + '.0'\n  } else if (tpr) {\n    to = '<=' + tM + '.' + tm + '.' + tp + '-' + tpr\n  } else {\n    to = '<=' + to\n  }\n\n  return (from + ' ' + to).trim()\n}\n\n// if ANY of the sets match ALL of its comparators, then pass\nRange.prototype.test = function (version) {\n  if (!version) {\n    return false\n  }\n\n  if (typeof version === 'string') {\n    version = new SemVer(version, this.options)\n  }\n\n  for (var i = 0; i < this.set.length; i++) {\n    if (testSet(this.set[i], version, this.options)) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction testSet (set, version, options) {\n  for (var i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        var allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n\nexports.satisfies = satisfies\nfunction satisfies (version, range, options) {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\n\nexports.maxSatisfying = maxSatisfying\nfunction maxSatisfying (versions, range, options) {\n  var max = null\n  var maxSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\n\nexports.minSatisfying = minSatisfying\nfunction minSatisfying (versions, range, options) {\n  var min = null\n  var minSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\n\nexports.minVersion = minVersion\nfunction minVersion (range, loose) {\n  range = new Range(range, loose)\n\n  var minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    comparators.forEach(function (comparator) {\n      // Clone to avoid manipulating the comparator's semver object.\n      var compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!minver || gt(minver, compver)) {\n            minver = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error('Unexpected operation: ' + comparator.operator)\n      }\n    })\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\n\nexports.validRange = validRange\nfunction validRange (range, options) {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\n\n// Determine if version is less than all the versions possible in the range\nexports.ltr = ltr\nfunction ltr (version, range, options) {\n  return outside(version, range, '<', options)\n}\n\n// Determine if version is greater than all the versions possible in the range.\nexports.gtr = gtr\nfunction gtr (version, range, options) {\n  return outside(version, range, '>', options)\n}\n\nexports.outside = outside\nfunction outside (version, range, hilo, options) {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  var gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisifes the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    var high = null\n    var low = null\n\n    comparators.forEach(function (comparator) {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nexports.prerelease = prerelease\nfunction prerelease (version, options) {\n  var parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\n\nexports.intersects = intersects\nfunction intersects (r1, r2, options) {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2)\n}\n\nexports.coerce = coerce\nfunction coerce (version) {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  var match = version.match(safeRe[COERCE])\n\n  if (match == null) {\n    return null\n  }\n\n  return parse(match[1] +\n    '.' + (match[2] || '0') +\n    '.' + (match[3] || '0'))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/cls-hooked/node_modules/semver/semver.js\n");

/***/ })

};
;
import { ChevronRightIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

import { Text } from './../text/text';

interface BreadcrumbItem {
  label: string;
  slug: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
}

export function Breadcrumb({ items }: BreadcrumbProps) {
  const t = useTranslations('components.breadcrumb');

  if (items.length === 0) {
    return null;
  }

  return (
    <div className="inline-block border-b py-6">
      <Link href="/" className="font-text text-accent">
        {t('home')}
      </Link>
      {items.map((item, index) => {
        const isClickable = index !== items.length - 1;

        return (
          <div key={index} className="inline">
            <div className="inline-flex items-center">
              <ChevronRightIcon className="font-text mx-2 inline h-4 w-4 translate-y-0.5" strokeWidth={3} />
            </div>
            {isClickable ? (
              <Link href={`/${item.slug}`} className="font-text text-accent inline hyphens-auto break-words">
                {item.label}
              </Link>
            ) : (
              <Text className="font-text hyphens-auto break-words font-medium" inline>
                {item.label}
              </Text>
            )}
          </div>
        );
      })}
    </div>
  );
}

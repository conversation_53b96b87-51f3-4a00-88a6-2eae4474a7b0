import { ValidationRule, ValidationRuleType, ValidatorFn, ValidatorFnFactory } from '../models/validator.models';

export interface DateTimeRangeValidationRule extends ValidationRule<ValidationRuleType.ValidatePastDate> {
  ruleValue: {
    startDateTime: Date | string;
    endDateTime: Date | string;
  };
}

export const isDateTimeRangeRule = (rule: ValidationRule): rule is DateTimeRangeValidationRule => {
  return rule.ruleType === ValidationRuleType.ValidateDateTimeRange;
};

export const dateTimeRangeValidatorFactory: ValidatorFnFactory<string> = (_ruleValue): ValidatorFn => {
  return ({ value }) => {
    const errors: { dateTimeRange?: boolean; bothFieldsRequiredOrEmpty?: boolean } = {};

    if (typeof value !== 'object' || value === null || !('start' in value) || !('end' in value)) {
      return null;
    }

    const { start, end } = value;

    if ((start === null && end !== null) || (start !== null && end === null)) {
      errors.bothFieldsRequiredOrEmpty = true;
      return errors;
    }

    if (typeof start !== 'string' || typeof end !== 'string') {
      return null;
    }

    const startTimeAsDateObject = new Date(start);
    const endTimeAsDateObject = new Date(end);

    if (startTimeAsDateObject >= endTimeAsDateObject) {
      errors.dateTimeRange = true;
      return errors;
    }

    return null;
  };
};

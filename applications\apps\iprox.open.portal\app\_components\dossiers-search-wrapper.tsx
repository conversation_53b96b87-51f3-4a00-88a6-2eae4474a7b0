'use client';

import { SearchType } from '@/models/search.model';
import { Button, FieldType, RadioButton, TextField } from '@iprox/react-ui';
import { Form, Formik } from 'formik';
import { useTranslations } from 'next-intl';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useMemo, useState } from 'react';

interface DossierSearchWrapperProps {
  children?: React.ReactNode;
  defaultSearchType: SearchType[];
}

export function DossierSearchWrapper({ children, defaultSearchType }: DossierSearchWrapperProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const t = useTranslations('search');

  const initialState = useMemo(() => {
    return {
      query: searchParams.get('query') || '',
      typeFilter: searchParams.getAll('type') || defaultSearchType,
    };
  }, [searchParams, defaultSearchType]);

  const handleSubmit = (values: typeof initialState) => {
    const queryParams = new URLSearchParams({ query: values.query });
    typeFilter.forEach((filter) => queryParams.append('type', filter));

    router.push(`${pathname}?${queryParams.toString()}`);
  };

  const [typeFilter, setTypeFilter] = useState(
    initialState.typeFilter.length ? initialState.typeFilter : defaultSearchType
  );

  const filterOptions = [
    { label: t('typeSelection.types.dossier'), value: [SearchType.Dossiers] },
    { label: t('typeSelection.types.file'), value: [SearchType.Files] },
    { label: t('typeSelection.types.all'), value: [SearchType.Dossiers, SearchType.Files] },
  ].sort((a, b) => {
    // Move the item with value equal to defaultSearchType to the first position
    const equalsDefault = (sts: SearchType[]) =>
      sts.length === defaultSearchType.length && sts.every((value, index) => value === defaultSearchType[index]);

    if (equalsDefault(a.value)) {
      return -1;
    }

    if (equalsDefault(b.value)) {
      return 1;
    }

    return 0;
  });

  return (
    <div className="flex h-full w-full flex-col">
      <Formik initialValues={initialState} onSubmit={handleSubmit}>
        <Form>
          <TextField name="query" label={t('form.queryLabel')} fieldType={FieldType.Text} validationRules={[]} />
          <div className="mt-4 items-center md:flex">
            <legend className="hidden">{t('typeSelection.title')}</legend>
            {filterOptions.map((filterOption, index) => (
              <div key={index} className="my-4 mr-4 md:my-0">
                <RadioButton
                  label={filterOption.label}
                  isSelected={
                    typeFilter.length === filterOption.value.length &&
                    filterOption.value.every((val) => typeFilter.includes(val))
                  }
                  handleSelect={() => setTypeFilter(filterOption.value)}
                />
              </div>
            ))}
            <div className="ml-auto mt-8 md:mt-0">
              <Button type="submit" variant="primary">
                {t('form.submit')}
              </Button>
            </div>
          </div>
        </Form>
      </Formik>
      {children}
    </div>
  );
}

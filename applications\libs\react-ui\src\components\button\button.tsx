import * as OutlineIcons from '@heroicons/react/24/outline';
import * as SolidIcons from '@heroicons/react/24/solid';
import { ButtonHTMLAttributes, useMemo } from 'react';

import './button.scss';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'tertiary';
  className?: string;
  icon?: keyof typeof OutlineIcons | keyof typeof SolidIcons;
  iconPosition?: 'left' | 'right';
  shadow?: boolean;
}

export function Button({
  children,
  variant = 'primary',
  className,
  type = 'button',
  icon,
  iconPosition,
  shadow,
  disabled,
  ...rest
}: ButtonProps) {
  const dynamicClasses = useMemo(() => {
    const baseClasses =
      'flex min-w-[150px] min-h-[50px] max-w-fit max-h-fit flex-row items-center justify-center gap-x-[15px] rounded-medium py-[10px] px-8 border-2 border-transparent';

    switch (variant) {
      case 'tertiary':
        return `${
          disabled ? 'opacity-50' : 'hover:text-tertiary-hover-content hover:bg-tertiary-hover'
        } bg-tertiary text-tertiary-content border-button-tertiary-border ${baseClasses}`;
      case 'secondary':
        return `${
          disabled ? 'opacity-50' : 'hover:text-secondary-hover-content hover:bg-secondary-hover'
        } bg-secondary text-secondary-content border-button-secondary-border ${baseClasses}`;
      case 'primary':
      default:
        return `${
          disabled ? 'opacity-50' : 'hover:text-primary-hover-content hover:bg-primary-hover'
        } bg-primary text-primary-content border-button-primary-border ${baseClasses}`;
    }
  }, [disabled, variant]);

  const Icon = OutlineIcons[icon as keyof typeof OutlineIcons] || SolidIcons[icon as keyof typeof SolidIcons];

  return (
    <button
      className={`font-heading text-lg font-bold ${dynamicClasses} ${className} ${shadow ? 'shadow-button' : ''}`}
      type={type}
      disabled={disabled}
      {...rest}
    >
      {icon && (iconPosition === 'left' || !iconPosition) ? <Icon className="h-6 w-6" /> : null}
      {children}
      {icon && iconPosition === 'right' ? <Icon className="h-6 w-6" /> : null}
    </button>
  );
}

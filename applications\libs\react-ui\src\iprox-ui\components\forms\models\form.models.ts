import * as Popper from '@popperjs/core';
import { ReactNode } from 'react';

import { ValidationRule, ValidationRuleType } from './validator.models';

export enum FieldType {
  Text = 'Text',
  Integer = 'Integer',
  Decimal = 'Decimal',
  Date = 'Date',
  Select = 'Select',
  RadioButton = 'RadioButton',
  CheckBox = 'CheckBox',
  TextArea = 'TextArea',
  CheckboxGroup = 'CheckboxGroup',
  Password = 'Password',
  Email = 'Email',
  StringList = 'StringList',
  RichText = 'RichText',
  Color = 'Color',
  DateTime = 'DateTime',
  DateRange = 'DateRange',
  PageZonesField = 'PageZonesField',
  DateTimeRange = 'DateTimeRange',
}

export type ValueTypes = string | number | boolean | Date | DateTimeRange | string[] | PageZone[] | undefined;

type BlockTypes = 'RichText';
export interface ZoneBlock {
  id: string;
  order: number;
  colspan: number;
  blockType: BlockTypes;
  blockContent: {
    content: string;
  };
}
export interface DateTimeRange {
  start?: string;
  end?: string;
}

export interface PageZone {
  id: string;
  order: number;
  blocks: ZoneBlock[];
}

export interface FieldDefinition<T extends FieldType, K extends ValueTypes> {
  /** Provide an id for the input, if not provided the field will generate one. */
  id?: string;
  /** The `alias` of the field */
  name: string;
  label: string;
  value?: K;
  description?: string;
  fieldType: T;
  validationRules: Array<ValidationRule<ValidationRuleType>>;
  style?: {
    colSpan?: string;
    gridRow?: string;
    customClasses?: string;
  };
  hideLabel?: boolean;
  contentEditorClass?: string;
  sectionTitle?: ReactNode;
  labelColor?: string;
  descriptionColor?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface TextFieldDefinition extends FieldDefinition<FieldType.Text | FieldType.TextArea, string> {
  initialValue?: string;
  defaultValue?: string;
  icon?: ReactNode;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface RichTextFieldDefinition extends FieldDefinition<FieldType.RichText, string> {
  initialValue?: string;
  defaultValue?: string;
  contentEditorClass?: string;
  enableSuperlink?: boolean;
}

export interface StringListFieldDefinition extends FieldDefinition<FieldType.StringList, string[]> {
  initialValue?: Array<string>;
  defaultValue?: Array<string>;
}

export interface NumberFieldDefinition extends FieldDefinition<FieldType.Integer | FieldType.Decimal, number | string> {
  initialValue?: number;
  defaultValue?: number;
  icon?: ReactNode;
  step?: number;
}

export interface CheckboxFieldDefinition extends FieldDefinition<FieldType.CheckBox, boolean> {
  initialValue?: boolean;
  defaultValue?: boolean;
}

export interface PageZonesFieldDefinition extends FieldDefinition<FieldType.PageZonesField, PageZone[]> {
  initialValue?: PageZone[];
  defaultValue?: PageZone[];
  enableSuperlink?: boolean;
}

export interface MultiFieldDefinition
  extends FieldDefinition<FieldType.Select | FieldType.RadioButton | FieldType.CheckboxGroup, string | string[]> {
  // Discuss: had to make it 'options' optional, if we make it a required field here, there will be an issue in formBuilder Map
  options?: Array<{ label: string; value: string }>;
  childeren?: ReactNode;
  initialValue?: string;
  defaultValue?: string | Array<string>;
  isMulti?: boolean;
  displayModeRow?: boolean;
  value?: string | string[];
}

export interface DateFieldDefinition extends FieldDefinition<FieldType.Date | FieldType.DateTime, Date | string> {
  initialValue?: Date;
  defaultValue?: Date;
  minDate?: Date | string;
  maxDate?: Date | string;
  isClearable?: boolean;
  popperPlacement?: Popper.Placement;
}

export interface ColorPickerFieldDefinition extends FieldDefinition<FieldType.Color, string> {
  initialValue?: string;
  defaultValue?: string;
}

export interface DateTimeRangeFieldDefinition extends FieldDefinition<FieldType.DateTimeRange, DateTimeRange> {
  initialValue?: DateTimeRange;
  defaultValue?: DateTimeRange;
  minDate?: Date | string;
  maxDate?: Date | string;
  isClearable?: boolean;
  popperPlacement?: Popper.Placement;
}

export type FieldValuesMap<Fields extends FieldDefinition<FieldType, ValueTypes>[]> = {
  [K in Fields[number]['name']]: Fields[number]['fieldType'] extends FieldType.Date
    ? Date
    : string | string[] | number | boolean | Date | DateTimeRange | PageZone[] | undefined;
};

export type FormSubmitValues = FieldValuesMap<FieldDefinition<FieldType, ValueTypes>[]>;

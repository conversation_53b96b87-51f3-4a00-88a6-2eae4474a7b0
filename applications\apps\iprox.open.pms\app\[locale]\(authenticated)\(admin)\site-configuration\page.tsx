import { getPublicAssets, getSiteParameters } from '@/services/public-service.server';
import { getTranslations } from 'next-intl/server';

import { FaviconUploader } from '@/components/favicon-uploader';
import { LogoUploader } from '@/components/logo-uploader';
import { PageHeader } from '@/components/page-header';
import { StyleSettingsForm } from '@/components/style-settings-form';

export default async function Page() {
  const [publicAssets, siteParameters] = await Promise.all([getPublicAssets(), getSiteParameters()]);
  const t = await getTranslations('navigation');

  return (
    <div>
      <PageHeader title={t('siteConfiguration')} />

      <div className="mb-12">
        <FaviconUploader faviconAssetPath={publicAssets?.siteAssets.favicon} />
      </div>
      <div className="mb-12">
        <LogoUploader logoAssetPath={publicAssets?.siteAssets.logo} />
      </div>
      <div className="mb-12">
        <StyleSettingsForm
          tenantName={siteParameters?.siteParametersSettings.tenantName ?? ''}
          cssVariables={siteParameters?.siteParametersSettings.cssVariables ?? []}
        />
      </div>
    </div>
  );
}

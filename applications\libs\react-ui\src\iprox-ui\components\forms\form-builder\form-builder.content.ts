import { v4 as uuid } from 'uuid';

import { FontCategory, FontStyle, FontWeight } from '../../../../models/fonts.model';
import { enumToOptionsArray } from '../../../../utils/enum-to-options-array';
import {
  CheckboxFieldDefinition,
  FieldDefinition,
  FieldType,
  MultiFieldDefinition,
  PageZonesFieldDefinition,
  StringListFieldDefinition,
  ValueTypes,
} from '../models/form.models';
import { ValidationRuleType } from '../models/validator.models';

export const ALLOWED_ZIP_FILE_TYPES = [
  'zip',
  'application/octet-stream',
  'application/zip',
  'application/x-zip',
  'application/x-zip-compressed',
];

const options = [
  {
    label: 'Label 1',
    value: 'label-1',
  },
  {
    label: 'Label 2',
    value: 'label-2',
  },
  {
    label: 'Label 3',
    value: 'label-3',
  },
];

export const dossierForm: FieldDefinition<FieldType, ValueTypes>[] = [
  {
    id: uuid(),
    name: 'title',
    label: 'Title',
    description: 'This is help text',
    fieldType: FieldType.Text,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
      {
        ruleType: ValidationRuleType.ValidateStringLength,
        ruleValue: { minimummumLength: 5, maximumLength: 299 },
      },
    ],
  },
  {
    id: uuid(),
    name: 'summary',
    label: 'Summary',
    description: 'This is help text',
    fieldType: FieldType.RichText,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  },
  {
    id: uuid(),
    name: 'Author',
    label: 'Auteur',
    description: 'Help text for auteur',
    fieldType: FieldType.StringList,
    options: {},
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  } as StringListFieldDefinition,
  {
    id: uuid(),
    name: 'radio-test',
    label: 'Radio Test',
    description: 'This is help text',
    fieldType: FieldType.RadioButton,
    options,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  } as MultiFieldDefinition,
  {
    id: uuid(),
    name: 'publish-date',
    label: 'Publish Date',
    description: 'This is help text',
    fieldType: FieldType.Date,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  },
  {
    id: uuid(),
    name: 'should-populate',
    label: 'Should Populate',
    description: 'This is help text',
    fieldType: FieldType.CheckBox,
    options,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  } as CheckboxFieldDefinition,
  {
    id: uuid(),
    name: 'check-box-group',
    label: 'Check Box Group',
    description: 'This is help text',
    fieldType: FieldType.CheckboxGroup,
    options,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  } as MultiFieldDefinition,
  {
    id: uuid(),
    name: 'select-options',
    label: 'Select Options',
    description: 'This is help text',
    fieldType: FieldType.Select,
    options,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  } as MultiFieldDefinition,
];

export const loginForm: FieldDefinition<FieldType, ValueTypes>[] = [
  {
    id: uuid(),
    name: 'username',
    label: 'Username',
    fieldType: FieldType.Text,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  },
  {
    id: uuid(),
    name: 'password',
    label: 'Password',
    fieldType: FieldType.Text,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  },
];

export const newDossierForm: FieldDefinition<FieldType, ValueTypes>[] = [
  {
    id: uuid(),
    name: 'title',
    label: 'Title',
    fieldType: FieldType.Text,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  },
  {
    id: uuid(),
    name: 'category',
    label: 'Category',
    fieldType: FieldType.Select,
    options,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  } as MultiFieldDefinition,
];

export const fontForm: FieldDefinition<FieldType, ValueTypes>[] = [
  {
    id: uuid(),
    name: 'font-name',
    label: 'Font Name',
    fieldType: FieldType.Text,
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
    style: { colSpan: 'col-span-2', gridRow: 'grid-row-1' },
  },
  {
    id: uuid(),
    name: 'font-weight',
    label: 'Font Weight',
    fieldType: FieldType.Select,
    options: enumToOptionsArray(FontCategory),
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
    style: { colSpan: 'col-span-1', gridRow: 'grid-row-1' },
  } as MultiFieldDefinition,
  {
    id: uuid(),
    name: 'font-type',
    label: 'Font Type',
    fieldType: FieldType.Select,
    options: enumToOptionsArray(FontWeight),
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
    style: { colSpan: 'col-span-1', gridRow: 'grid-row-1' },
  } as MultiFieldDefinition,
  {
    id: uuid(),
    name: 'font-style',
    label: 'Font Style',
    fieldType: FieldType.RadioButton,
    options: enumToOptionsArray(FontStyle),
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
    displayModeRow: true,
    style: { colSpan: 'col-span-2', gridRow: 'grid-row-1' },
  } as MultiFieldDefinition,
];

export const homeConfigForm: FieldDefinition<FieldType, ValueTypes>[] = [
  {
    id: uuid(),
    name: 'title',
    label: 'Title',
    fieldType: FieldType.PageZonesField,
    value: [
      {
        id: uuid(),
        order: 0,
        blocks: [
          {
            id: uuid(),
            order: 0,
            colspan: 12,
            blockType: 'RichText',
            blockContent: {
              content: '<pre><p>Hello world 1</p></pre>',
            },
          },
        ],
      },
    ],
    style: {
      colSpan: 'col-span-3',
    },
    validationRules: [
      {
        ruleType: ValidationRuleType.RequiredProperty,
        ruleValue: {},
      },
    ],
  } as PageZonesFieldDefinition,
];

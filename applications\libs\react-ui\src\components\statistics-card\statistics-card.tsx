import * as OutlineIcons from '@heroicons/react/24/outline';
import { filesize } from 'filesize';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import { Text } from '../text/text';

interface StatisticsCardProps {
  icon: keyof typeof OutlineIcons;
  label: string;
  value: number;
  animate?: boolean;
  animationDuration?: number;
  formatterType?: 'filesize';
}

const easeOut = (t: number): number => 1 - Math.pow(1 - t, 3);

export function StatisticsCard({
  icon,
  label,
  value,
  animate = true,
  animationDuration = 1000,
  formatterType,
}: StatisticsCardProps) {
  const [number, setNumber] = useState(0);
  const startTimeRef = useRef<number>(0);
  const requestRef = useRef<number | null>(null);

  useEffect(() => {
    startTimeRef.current = performance.now();

    const updateNumber = () => {
      const currentTime = performance.now();
      const elapsedTime = currentTime - startTimeRef.current;

      if (elapsedTime >= animationDuration) {
        setNumber(value);
      } else {
        const progress = easeOut(elapsedTime / animationDuration);
        const updatedNumber = Math.floor(progress * value);
        setNumber(updatedNumber);
        requestRef.current = requestAnimationFrame(updateNumber);
      }
    };

    if (animate) {
      requestRef.current = requestAnimationFrame(updateNumber);
    } else {
      setNumber(value);
    }

    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [animate, animationDuration, value]);

  const Icon = OutlineIcons[icon];

  const formattedValue = useMemo(() => {
    if (formatterType === 'filesize') {
      return filesize(number, { base: 2, standard: 'jedec' }).toString();
    }

    return number.toString();
  }, [formatterType, number]);

  return (
    <div className="bg-highlight rounded-input flex h-full flex-col p-5">
      <Icon className="text-content-extra-lite mb-6 h-8 w-8" />
      <div className="flex flex-1 flex-col justify-between">
        <Text className="font-text text-content-extra-lite mb-1 text-sm">{label}</Text>
        <Text className="font-heading text-primary text-4xl font-bold">{formattedValue}</Text>
      </div>
    </div>
  );
}

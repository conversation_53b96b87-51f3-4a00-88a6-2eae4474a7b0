'use client';

import { ErrorComponent } from '@iprox/react-ui';
import { useEffect } from 'react';

export default function Error({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <ErrorComponent error={error} reset={reset} serviceDeskLink={process.env.NEXT_PUBLIC_SERVICE_DESK_URL ?? ''} />
  );
}

import { getDossierCategories } from '@/services/dossier-category-service.server';
import { redirect } from 'next/navigation';

import PageContent from './page-content';

async function getData() {
  try {
    const response = await getDossierCategories();
    return response;
  } catch (error) {
    redirect('/error');
  }
}

export default async function Page() {
  const data = await getData();

  return <PageContent dossierCategories={data?.dossierCategories ?? []} />;
}

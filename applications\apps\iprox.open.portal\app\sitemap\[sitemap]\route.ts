import { getSitemap, getWooCategories } from '@/services/woo-service';
import { pad<PERSON>ero } from '@/utils/pad-zero';
import { getServerSideSitemapIndex } from 'next-sitemap';
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const MAXIMUM_FILES_PER_SITEMAP = 10000;

const getWooCategory = async (wooCategoryId: number) => {
  try {
    const response = await getWooCategories();
    return response.wooCategories.find((category) => category.wooCategoryId === wooCategoryId);
  } catch (error) {
    return Promise.reject(error);
  }
};

export async function GET(_request: Request, { params }: { params: { sitemap: string } }) {
  const sitemapIndexMatch = params.sitemap.match(/sitemapindex-diwoo-infocat(\d+)\.xml/);
  const sitemapMatch = params.sitemap.match(/sitemap-infocat(\d+)-(\d+)\.xml$/);

  try {
    if (sitemapIndexMatch) {
      const wooCategoryId = parseInt(sitemapIndexMatch[1], 10);
      const wooCategory = await getWooCategory(wooCategoryId);

      if (wooCategory && wooCategory.count > 0) {
        const sitemapIndex = Math.ceil(wooCategory.count / MAXIMUM_FILES_PER_SITEMAP);
        const sitemapIndexXml = [];

        for (let index = 1; index <= sitemapIndex; index++) {
          const url = `${process.env.BASE_URL}/sitemap/sitemap-infocat${padZero(wooCategoryId, 3)}-${index}.xml`;
          sitemapIndexXml.push(url);
        }

        return getServerSideSitemapIndex(sitemapIndexXml);
      }

      throw new Error('Not Found');
    }

    if (sitemapMatch) {
      const wooCategoryId = parseInt(sitemapMatch[1], 10);
      const siteMapNumber = parseInt(sitemapMatch[2], 10);

      const wooCategory = await getWooCategory(wooCategoryId);

      if (wooCategory && siteMapNumber <= Math.ceil(wooCategory.count / MAXIMUM_FILES_PER_SITEMAP)) {
        const response = await getSitemap({
          dossierCategoryId: wooCategory.id,
          count: (MAXIMUM_FILES_PER_SITEMAP * siteMapNumber).toString(),
          start: ((siteMapNumber - 1) * MAXIMUM_FILES_PER_SITEMAP).toString(),
        });

        if (response) {
          return new NextResponse(response.body, {
            headers: {
              'Content-Type': 'application/xml',
            },
          });
        }
      }

      throw new Error('Not Found');
    }
  } catch {
    return new Response('Not found', { status: 404 });
  }
}

@use '../../../../scss/headings/headings';

.ltr {
  text-align: left;
}

.rtl {
  text-align: right;
}

.tree-view-output {
  display: block;
  background: #222;
  color: #fff;
  padding: 5px;
  font-size: 12px;
  white-space: pre-wrap;
  margin: 1px auto 10px auto;
  max-height: 250px;
  position: relative;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  overflow: auto;
  line-height: 14px;
}

.editor-input {
  font-size: 1.125rem;
  min-height: 150px;
  position: relative;
  tab-size: 1;
  outline: 0;
  padding: 15px 10px;
  caret-color: #444;
}

.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-link {
  color: theme('colors.anchor');
  text-decoration: underline;
  cursor: pointer;
}

.editor-superlink {
  margin-top: 16px;
  margin-bottom: 16px;
  padding: 16px;
  cursor: pointer;
  display: block;
  color: theme('colors.superlink-text');
  background: theme('colors.superlink-background');
}

.editor-superlink::after {
  content: '';
  height: 39px;
  margin: -6px 0;
  margin-left: -10px;
  width: 30px;
  position: absolute;
  right: 10px;
  mask-image: url('/images/chevron-right.svg');
  mask-size: 100%;
  mask-repeat: no-repeat;
  mask-position: center center;
  background-color: theme('colors.superlink-text');
}

.editor-paragraph {
  font-family: theme('fontFamily.text');
  line-height: 1.6;
  margin-bottom: 0.75rem;
  padding: 0;
}

.editor-paragraph:last-child {
  margin-bottom: 0;
}

.editor-heading-h1 {
  @include headings.h1();
}

.editor-heading-h2 {
  @include headings.h2();
}

.editor-heading-h3 {
  @include headings.h3();
}

.editor-heading-h4 {
  @include headings.h4();
}

.editor-heading-h5 {
  @include headings.h5();
}

.editor-list-ol {
  list-style: number;
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-list-ul {
  list-style: initial;
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-listitem {
  font-family: theme('fontFamily.text');
}

.editor-table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  margin-bottom: 1rem;
  max-width: 100%;
  overflow-y: scroll;
  table-layout: fixed;

  .editor-table-row {
    border: 1px solid theme('colors.base-85');
  }

  .editor-table-cell,
  .editor-table-cell-header {
    text-align: start;
    border: 1px solid theme('colors.base-85');
    position: relative;
    padding: 8px 6px;
    vertical-align: top;

    .editor-paragraph {
      position: relative;
    }
  }

  .editor-table-cell-header {
    background-color: theme('colors.base-10');
  }
}

pre::-webkit-scrollbar {
  background: transparent;
  width: 10px;
}

pre::-webkit-scrollbar-thumb {
  background: #999;
}

@media (max-width: 640px) {
  .editor-listitem {
    margin: 8px 8px 8px 8px;
  }
}

import { FieldInputProps, FieldMetaProps } from 'formik';

import { useUuid } from '../../../../hooks/uuid.hook';
import { FieldDefinition, FieldType, ValueTypes } from '../models/form.models';

export type FormFieldTypes =
  | 'text'
  | 'number'
  | 'text-area'
  | 'password'
  | 'email'
  | 'radio'
  | 'checkbox'
  | 'select'
  | 'date'
  | 'color'
  | 'dateTime';

export type LabelAttributes = {
  id: string;
  htmlFor: string;
};

export type InputAttributes = {
  id: string;
  name: string;
  type: FormFieldTypes;
  'aria-describedby'?: string;
};

export type DescriptionAttributes = {
  id: string;
};

export type ErrorMessageAttributes = {
  id: string;
};

export function useFormField(
  definition: FieldDefinition<FieldType, ValueTypes>,
  _field: FieldInputProps<unknown>,
  meta: FieldMetaProps<unknown>,
  type: FormFieldTypes
): [LabelAttributes, InputAttributes, DescriptionAttributes | undefined, ErrorMessageAttributes | undefined] {
  const inputId = useUuid(definition.id);
  const descriptionId = useUuid();
  const labelId = useUuid();

  const labelAttributes: LabelAttributes = {
    id: labelId,
    htmlFor: inputId,
  };

  const inputAttributes: InputAttributes = {
    id: inputId,
    name: definition.name,
    type,
    'aria-describedby': definition.description ? descriptionId : undefined,
  };

  return [
    labelAttributes,
    inputAttributes,
    definition.description ? { id: descriptionId } : undefined,
    meta.error ? { id: descriptionId } : undefined,
  ];
}

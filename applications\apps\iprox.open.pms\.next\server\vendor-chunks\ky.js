"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ky";
exports.ids = ["vendor-chunks/ky"];
exports.modules = {

/***/ "(ssr)/../../node_modules/ky/distribution/core/Ky.js":
/*!*****************************************************!*\
  !*** ../../node_modules/ky/distribution/core/Ky.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ky: () => (/* binding */ Ky)\n/* harmony export */ });\n/* harmony import */ var _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/HTTPError.js */ \"(ssr)/../../node_modules/ky/distribution/errors/HTTPError.js\");\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/TimeoutError.js */ \"(ssr)/../../node_modules/ky/distribution/errors/TimeoutError.js\");\n/* harmony import */ var _utils_merge_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/merge.js */ \"(ssr)/../../node_modules/ky/distribution/utils/merge.js\");\n/* harmony import */ var _utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/normalize.js */ \"(ssr)/../../node_modules/ky/distribution/utils/normalize.js\");\n/* harmony import */ var _utils_timeout_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/timeout.js */ \"(ssr)/../../node_modules/ky/distribution/utils/timeout.js\");\n/* harmony import */ var _utils_delay_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/delay.js */ \"(ssr)/../../node_modules/ky/distribution/utils/delay.js\");\n/* harmony import */ var _utils_options_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/options.js */ \"(ssr)/../../node_modules/ky/distribution/utils/options.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/../../node_modules/ky/distribution/core/constants.js\");\n\n\n\n\n\n\n\n\nclass Ky {\n    static create(input, options) {\n        const ky = new Ky(input, options);\n        const function_ = async () => {\n            if (typeof ky._options.timeout === 'number' && ky._options.timeout > _constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout) {\n                throw new RangeError(`The \\`timeout\\` option cannot be greater than ${_constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout}`);\n            }\n            // Delay the fetch so that body method shortcuts can set the Accept header\n            await Promise.resolve();\n            let response = await ky._fetch();\n            for (const hook of ky._options.hooks.afterResponse) {\n                // eslint-disable-next-line no-await-in-loop\n                const modifiedResponse = await hook(ky.request, ky._options, ky._decorateResponse(response.clone()));\n                if (modifiedResponse instanceof globalThis.Response) {\n                    response = modifiedResponse;\n                }\n            }\n            ky._decorateResponse(response);\n            if (!response.ok && ky._options.throwHttpErrors) {\n                let error = new _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__.HTTPError(response, ky.request, ky._options);\n                for (const hook of ky._options.hooks.beforeError) {\n                    // eslint-disable-next-line no-await-in-loop\n                    error = await hook(error);\n                }\n                throw error;\n            }\n            // If `onDownloadProgress` is passed, it uses the stream API internally\n            /* istanbul ignore next */\n            if (ky._options.onDownloadProgress) {\n                if (typeof ky._options.onDownloadProgress !== 'function') {\n                    throw new TypeError('The `onDownloadProgress` option must be a function');\n                }\n                if (!_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsResponseStreams) {\n                    throw new Error('Streams are not supported in your environment. `ReadableStream` is missing.');\n                }\n                return ky._stream(response.clone(), ky._options.onDownloadProgress);\n            }\n            return response;\n        };\n        const isRetriableMethod = ky._options.retry.methods.includes(ky.request.method.toLowerCase());\n        const result = (isRetriableMethod ? ky._retry(function_) : function_());\n        for (const [type, mimeType] of Object.entries(_constants_js__WEBPACK_IMPORTED_MODULE_0__.responseTypes)) {\n            result[type] = async () => {\n                // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n                ky.request.headers.set('accept', ky.request.headers.get('accept') || mimeType);\n                const awaitedResult = await result;\n                const response = awaitedResult.clone();\n                if (type === 'json') {\n                    if (response.status === 204) {\n                        return '';\n                    }\n                    const arrayBuffer = await response.clone().arrayBuffer();\n                    const responseSize = arrayBuffer.byteLength;\n                    if (responseSize === 0) {\n                        return '';\n                    }\n                    if (options.parseJson) {\n                        return options.parseJson(await response.text());\n                    }\n                }\n                return response[type]();\n            };\n        }\n        return result;\n    }\n    // eslint-disable-next-line complexity\n    constructor(input, options = {}) {\n        Object.defineProperty(this, \"request\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"abortController\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"_retryCount\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0\n        });\n        Object.defineProperty(this, \"_input\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"_options\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this._input = input;\n        this._options = {\n            // TODO: credentials can be removed when the spec change is implemented in all browsers. Context: https://www.chromestatus.com/feature/4539473312350208\n            credentials: this._input.credentials || 'same-origin',\n            ...options,\n            headers: (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_2__.mergeHeaders)(this._input.headers, options.headers),\n            hooks: (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_2__.deepMerge)({\n                beforeRequest: [],\n                beforeRetry: [],\n                beforeError: [],\n                afterResponse: [],\n            }, options.hooks),\n            method: (0,_utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__.normalizeRequestMethod)(options.method ?? this._input.method),\n            // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n            prefixUrl: String(options.prefixUrl || ''),\n            retry: (0,_utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__.normalizeRetryOptions)(options.retry),\n            throwHttpErrors: options.throwHttpErrors !== false,\n            timeout: options.timeout ?? 10000,\n            fetch: options.fetch ?? globalThis.fetch.bind(globalThis),\n        };\n        if (typeof this._input !== 'string' && !(this._input instanceof URL || this._input instanceof globalThis.Request)) {\n            throw new TypeError('`input` must be a string, URL, or Request');\n        }\n        if (this._options.prefixUrl && typeof this._input === 'string') {\n            if (this._input.startsWith('/')) {\n                throw new Error('`input` must not begin with a slash when using `prefixUrl`');\n            }\n            if (!this._options.prefixUrl.endsWith('/')) {\n                this._options.prefixUrl += '/';\n            }\n            this._input = this._options.prefixUrl + this._input;\n        }\n        if (_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsAbortController) {\n            this.abortController = new globalThis.AbortController();\n            if (this._options.signal) {\n                const originalSignal = this._options.signal;\n                this._options.signal.addEventListener('abort', () => {\n                    this.abortController.abort(originalSignal.reason);\n                });\n            }\n            this._options.signal = this.abortController.signal;\n        }\n        if (_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsRequestStreams) {\n            // @ts-expect-error - Types are outdated.\n            this._options.duplex = 'half';\n        }\n        this.request = new globalThis.Request(this._input, this._options);\n        if (this._options.searchParams) {\n            // eslint-disable-next-line unicorn/prevent-abbreviations\n            const textSearchParams = typeof this._options.searchParams === 'string'\n                ? this._options.searchParams.replace(/^\\?/, '')\n                : new URLSearchParams(this._options.searchParams).toString();\n            // eslint-disable-next-line unicorn/prevent-abbreviations\n            const searchParams = '?' + textSearchParams;\n            const url = this.request.url.replace(/(?:\\?.*?)?(?=#|$)/, searchParams);\n            // To provide correct form boundary, Content-Type header should be deleted each time when new Request instantiated from another one\n            if (((_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsFormData && this._options.body instanceof globalThis.FormData)\n                || this._options.body instanceof URLSearchParams) && !(this._options.headers && this._options.headers['content-type'])) {\n                this.request.headers.delete('content-type');\n            }\n            // The spread of `this.request` is required as otherwise it misses the `duplex` option for some reason and throws.\n            this.request = new globalThis.Request(new globalThis.Request(url, { ...this.request }), this._options);\n        }\n        if (this._options.json !== undefined) {\n            this._options.body = JSON.stringify(this._options.json);\n            this.request.headers.set('content-type', this._options.headers.get('content-type') ?? 'application/json');\n            this.request = new globalThis.Request(this.request, { body: this._options.body });\n        }\n    }\n    _calculateRetryDelay(error) {\n        this._retryCount++;\n        if (this._retryCount <= this._options.retry.limit && !(error instanceof _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__.TimeoutError)) {\n            if (error instanceof _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__.HTTPError) {\n                if (!this._options.retry.statusCodes.includes(error.response.status)) {\n                    return 0;\n                }\n                const retryAfter = error.response.headers.get('Retry-After');\n                if (retryAfter && this._options.retry.afterStatusCodes.includes(error.response.status)) {\n                    let after = Number(retryAfter);\n                    if (Number.isNaN(after)) {\n                        after = Date.parse(retryAfter) - Date.now();\n                    }\n                    else {\n                        after *= 1000;\n                    }\n                    if (this._options.retry.maxRetryAfter !== undefined && after > this._options.retry.maxRetryAfter) {\n                        return 0;\n                    }\n                    return after;\n                }\n                if (error.response.status === 413) {\n                    return 0;\n                }\n            }\n            const retryDelay = this._options.retry.delay(this._retryCount);\n            return Math.min(this._options.retry.backoffLimit, retryDelay);\n        }\n        return 0;\n    }\n    _decorateResponse(response) {\n        if (this._options.parseJson) {\n            response.json = async () => this._options.parseJson(await response.text());\n        }\n        return response;\n    }\n    async _retry(function_) {\n        try {\n            return await function_();\n        }\n        catch (error) {\n            const ms = Math.min(this._calculateRetryDelay(error), _constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout);\n            if (ms !== 0 && this._retryCount > 0) {\n                await (0,_utils_delay_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ms, { signal: this._options.signal });\n                for (const hook of this._options.hooks.beforeRetry) {\n                    // eslint-disable-next-line no-await-in-loop\n                    const hookResult = await hook({\n                        request: this.request,\n                        options: this._options,\n                        error: error,\n                        retryCount: this._retryCount,\n                    });\n                    // If `stop` is returned from the hook, the retry process is stopped\n                    if (hookResult === _constants_js__WEBPACK_IMPORTED_MODULE_0__.stop) {\n                        return;\n                    }\n                }\n                return this._retry(function_);\n            }\n            throw error;\n        }\n    }\n    async _fetch() {\n        for (const hook of this._options.hooks.beforeRequest) {\n            // eslint-disable-next-line no-await-in-loop\n            const result = await hook(this.request, this._options);\n            if (result instanceof Request) {\n                this.request = result;\n                break;\n            }\n            if (result instanceof Response) {\n                return result;\n            }\n        }\n        const nonRequestOptions = (0,_utils_options_js__WEBPACK_IMPORTED_MODULE_6__.findUnknownOptions)(this.request, this._options);\n        if (this._options.timeout === false) {\n            return this._options.fetch(this.request.clone(), nonRequestOptions);\n        }\n        return (0,_utils_timeout_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(this.request.clone(), nonRequestOptions, this.abortController, this._options);\n    }\n    /* istanbul ignore next */\n    _stream(response, onDownloadProgress) {\n        const totalBytes = Number(response.headers.get('content-length')) || 0;\n        let transferredBytes = 0;\n        if (response.status === 204) {\n            if (onDownloadProgress) {\n                onDownloadProgress({ percent: 1, totalBytes, transferredBytes }, new Uint8Array());\n            }\n            return new globalThis.Response(null, {\n                status: response.status,\n                statusText: response.statusText,\n                headers: response.headers,\n            });\n        }\n        return new globalThis.Response(new globalThis.ReadableStream({\n            async start(controller) {\n                const reader = response.body.getReader();\n                if (onDownloadProgress) {\n                    onDownloadProgress({ percent: 0, transferredBytes: 0, totalBytes }, new Uint8Array());\n                }\n                async function read() {\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        controller.close();\n                        return;\n                    }\n                    if (onDownloadProgress) {\n                        transferredBytes += value.byteLength;\n                        const percent = totalBytes === 0 ? 0 : transferredBytes / totalBytes;\n                        onDownloadProgress({ percent, transferredBytes, totalBytes }, value);\n                    }\n                    controller.enqueue(value);\n                    await read();\n                }\n                await read();\n            },\n        }), {\n            status: response.status,\n            statusText: response.statusText,\n            headers: response.headers,\n        });\n    }\n}\n//# sourceMappingURL=Ky.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/core/Ky.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/core/constants.js":
/*!************************************************************!*\
  !*** ../../node_modules/ky/distribution/core/constants.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   kyOptionKeys: () => (/* binding */ kyOptionKeys),\n/* harmony export */   maxSafeTimeout: () => (/* binding */ maxSafeTimeout),\n/* harmony export */   requestMethods: () => (/* binding */ requestMethods),\n/* harmony export */   requestOptionsRegistry: () => (/* binding */ requestOptionsRegistry),\n/* harmony export */   responseTypes: () => (/* binding */ responseTypes),\n/* harmony export */   stop: () => (/* binding */ stop),\n/* harmony export */   supportsAbortController: () => (/* binding */ supportsAbortController),\n/* harmony export */   supportsFormData: () => (/* binding */ supportsFormData),\n/* harmony export */   supportsRequestStreams: () => (/* binding */ supportsRequestStreams),\n/* harmony export */   supportsResponseStreams: () => (/* binding */ supportsResponseStreams)\n/* harmony export */ });\nconst supportsRequestStreams = (() => {\n    let duplexAccessed = false;\n    let hasContentType = false;\n    const supportsReadableStream = typeof globalThis.ReadableStream === 'function';\n    const supportsRequest = typeof globalThis.Request === 'function';\n    if (supportsReadableStream && supportsRequest) {\n        hasContentType = new globalThis.Request('https://empty.invalid', {\n            body: new globalThis.ReadableStream(),\n            method: 'POST',\n            // @ts-expect-error - Types are outdated.\n            get duplex() {\n                duplexAccessed = true;\n                return 'half';\n            },\n        }).headers.has('Content-Type');\n    }\n    return duplexAccessed && !hasContentType;\n})();\nconst supportsAbortController = typeof globalThis.AbortController === 'function';\nconst supportsResponseStreams = typeof globalThis.ReadableStream === 'function';\nconst supportsFormData = typeof globalThis.FormData === 'function';\nconst requestMethods = ['get', 'post', 'put', 'patch', 'head', 'delete'];\nconst validate = () => undefined;\nvalidate();\nconst responseTypes = {\n    json: 'application/json',\n    text: 'text/*',\n    formData: 'multipart/form-data',\n    arrayBuffer: '*/*',\n    blob: '*/*',\n};\n// The maximum value of a 32bit int (see issue #117)\nconst maxSafeTimeout = 2147483647;\nconst stop = Symbol('stop');\nconst kyOptionKeys = {\n    json: true,\n    parseJson: true,\n    searchParams: true,\n    prefixUrl: true,\n    retry: true,\n    timeout: true,\n    hooks: true,\n    throwHttpErrors: true,\n    onDownloadProgress: true,\n    fetch: true,\n};\nconst requestOptionsRegistry = {\n    method: true,\n    headers: true,\n    body: true,\n    mode: true,\n    credentials: true,\n    cache: true,\n    redirect: true,\n    referrer: true,\n    referrerPolicy: true,\n    integrity: true,\n    keepalive: true,\n    signal: true,\n    window: true,\n    dispatcher: true,\n    duplex: true,\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/core/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/errors/HTTPError.js":
/*!**************************************************************!*\
  !*** ../../node_modules/ky/distribution/errors/HTTPError.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTTPError: () => (/* binding */ HTTPError)\n/* harmony export */ });\n// eslint-lint-disable-next-line @typescript-eslint/naming-convention\nclass HTTPError extends Error {\n    constructor(response, request, options) {\n        const code = (response.status || response.status === 0) ? response.status : '';\n        const title = response.statusText || '';\n        const status = `${code} ${title}`.trim();\n        const reason = status ? `status code ${status}` : 'an unknown error';\n        super(`Request failed with ${reason}`);\n        Object.defineProperty(this, \"response\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"request\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"options\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = 'HTTPError';\n        this.response = response;\n        this.request = request;\n        this.options = options;\n    }\n}\n//# sourceMappingURL=HTTPError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/errors/HTTPError.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/errors/TimeoutError.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/ky/distribution/errors/TimeoutError.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimeoutError: () => (/* binding */ TimeoutError)\n/* harmony export */ });\nclass TimeoutError extends Error {\n    constructor(request) {\n        super('Request timed out');\n        Object.defineProperty(this, \"request\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = 'TimeoutError';\n        this.request = request;\n    }\n}\n//# sourceMappingURL=TimeoutError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi9lcnJvcnMvVGltZW91dEVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi9lcnJvcnMvVGltZW91dEVycm9yLmpzPzk2ZDEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIFRpbWVvdXRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihyZXF1ZXN0KSB7XG4gICAgICAgIHN1cGVyKCdSZXF1ZXN0IHRpbWVkIG91dCcpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJyZXF1ZXN0XCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMubmFtZSA9ICdUaW1lb3V0RXJyb3InO1xuICAgICAgICB0aGlzLnJlcXVlc3QgPSByZXF1ZXN0O1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVRpbWVvdXRFcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/errors/TimeoutError.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/index.js":
/*!***************************************************!*\
  !*** ../../node_modules/ky/distribution/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTTPError: () => (/* reexport safe */ _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_3__.HTTPError),\n/* harmony export */   TimeoutError: () => (/* reexport safe */ _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__.TimeoutError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/Ky.js */ \"(ssr)/../../node_modules/ky/distribution/core/Ky.js\");\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./core/constants.js */ \"(ssr)/../../node_modules/ky/distribution/core/constants.js\");\n/* harmony import */ var _utils_merge_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/merge.js */ \"(ssr)/../../node_modules/ky/distribution/utils/merge.js\");\n/* harmony import */ var _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors/HTTPError.js */ \"(ssr)/../../node_modules/ky/distribution/errors/HTTPError.js\");\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errors/TimeoutError.js */ \"(ssr)/../../node_modules/ky/distribution/errors/TimeoutError.js\");\n/*! MIT License © Sindre Sorhus */\n\n\n\nconst createInstance = (defaults) => {\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\n    const ky = (input, options) => _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__.Ky.create(input, (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, options));\n    for (const method of _core_constants_js__WEBPACK_IMPORTED_MODULE_2__.requestMethods) {\n        // eslint-disable-next-line @typescript-eslint/promise-function-async\n        ky[method] = (input, options) => _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__.Ky.create(input, (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, options, { method }));\n    }\n    ky.create = (newDefaults) => createInstance((0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(newDefaults));\n    ky.extend = (newDefaults) => createInstance((0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, newDefaults));\n    ky.stop = _core_constants_js__WEBPACK_IMPORTED_MODULE_2__.stop;\n    return ky;\n};\nconst ky = createInstance();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ky);\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/utils/delay.js":
/*!*********************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/delay.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ delay)\n/* harmony export */ });\n// https://github.com/sindresorhus/delay/tree/ab98ae8dfcb38e1593286c94d934e70d14a4e111\nasync function delay(ms, { signal }) {\n    return new Promise((resolve, reject) => {\n        if (signal) {\n            signal.throwIfAborted();\n            signal.addEventListener('abort', abortHandler, { once: true });\n        }\n        function abortHandler() {\n            clearTimeout(timeoutId);\n            reject(signal.reason);\n        }\n        const timeoutId = setTimeout(() => {\n            signal?.removeEventListener('abort', abortHandler);\n            resolve();\n        }, ms);\n    });\n}\n//# sourceMappingURL=delay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9kZWxheS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDZSwyQkFBMkIsUUFBUTtBQUNsRDtBQUNBO0FBQ0E7QUFDQSw2REFBNkQsWUFBWTtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9reS9kaXN0cmlidXRpb24vdXRpbHMvZGVsYXkuanM/N2FiNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBodHRwczovL2dpdGh1Yi5jb20vc2luZHJlc29yaHVzL2RlbGF5L3RyZWUvYWI5OGFlOGRmY2IzOGUxNTkzMjg2Yzk0ZDkzNGU3MGQxNGE0ZTExMVxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gZGVsYXkobXMsIHsgc2lnbmFsIH0pIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICBpZiAoc2lnbmFsKSB7XG4gICAgICAgICAgICBzaWduYWwudGhyb3dJZkFib3J0ZWQoKTtcbiAgICAgICAgICAgIHNpZ25hbC5hZGRFdmVudExpc3RlbmVyKCdhYm9ydCcsIGFib3J0SGFuZGxlciwgeyBvbmNlOiB0cnVlIH0pO1xuICAgICAgICB9XG4gICAgICAgIGZ1bmN0aW9uIGFib3J0SGFuZGxlcigpIHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgICAgICAgcmVqZWN0KHNpZ25hbC5yZWFzb24pO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgc2lnbmFsPy5yZW1vdmVFdmVudExpc3RlbmVyKCdhYm9ydCcsIGFib3J0SGFuZGxlcik7XG4gICAgICAgICAgICByZXNvbHZlKCk7XG4gICAgICAgIH0sIG1zKTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRlbGF5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/utils/delay.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/utils/is.js":
/*!******************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/is.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObject: () => (/* binding */ isObject)\n/* harmony export */ });\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst isObject = (value) => value !== null && typeof value === 'object';\n//# sourceMappingURL=is.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9pcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9reS9kaXN0cmlidXRpb24vdXRpbHMvaXMuanM/NzhmMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L2Jhbi10eXBlc1xuZXhwb3J0IGNvbnN0IGlzT2JqZWN0ID0gKHZhbHVlKSA9PiB2YWx1ZSAhPT0gbnVsbCAmJiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/utils/is.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/utils/merge.js":
/*!*********************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/merge.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepMerge: () => (/* binding */ deepMerge),\n/* harmony export */   mergeHeaders: () => (/* binding */ mergeHeaders),\n/* harmony export */   validateAndMerge: () => (/* binding */ validateAndMerge)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(ssr)/../../node_modules/ky/distribution/utils/is.js\");\n\nconst validateAndMerge = (...sources) => {\n    for (const source of sources) {\n        if ((!(0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source) || Array.isArray(source)) && source !== undefined) {\n            throw new TypeError('The `options` argument must be an object');\n        }\n    }\n    return deepMerge({}, ...sources);\n};\nconst mergeHeaders = (source1 = {}, source2 = {}) => {\n    const result = new globalThis.Headers(source1);\n    const isHeadersInstance = source2 instanceof globalThis.Headers;\n    const source = new globalThis.Headers(source2);\n    for (const [key, value] of source.entries()) {\n        if ((isHeadersInstance && value === 'undefined') || value === undefined) {\n            result.delete(key);\n        }\n        else {\n            result.set(key, value);\n        }\n    }\n    return result;\n};\n// TODO: Make this strongly-typed (no `any`).\nconst deepMerge = (...sources) => {\n    let returnValue = {};\n    let headers = {};\n    for (const source of sources) {\n        if (Array.isArray(source)) {\n            if (!Array.isArray(returnValue)) {\n                returnValue = [];\n            }\n            returnValue = [...returnValue, ...source];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source)) {\n            for (let [key, value] of Object.entries(source)) {\n                if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(value) && key in returnValue) {\n                    value = deepMerge(returnValue[key], value);\n                }\n                returnValue = { ...returnValue, [key]: value };\n            }\n            if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source.headers)) {\n                headers = mergeHeaders(headers, source.headers);\n                returnValue.headers = headers;\n            }\n        }\n    }\n    return returnValue;\n};\n//# sourceMappingURL=merge.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/utils/merge.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/utils/normalize.js":
/*!*************************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/normalize.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeRequestMethod: () => (/* binding */ normalizeRequestMethod),\n/* harmony export */   normalizeRetryOptions: () => (/* binding */ normalizeRetryOptions)\n/* harmony export */ });\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/constants.js */ \"(ssr)/../../node_modules/ky/distribution/core/constants.js\");\n\nconst normalizeRequestMethod = (input) => _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.requestMethods.includes(input) ? input.toUpperCase() : input;\nconst retryMethods = ['get', 'put', 'head', 'delete', 'options', 'trace'];\nconst retryStatusCodes = [408, 413, 429, 500, 502, 503, 504];\nconst retryAfterStatusCodes = [413, 429, 503];\nconst defaultRetryOptions = {\n    limit: 2,\n    methods: retryMethods,\n    statusCodes: retryStatusCodes,\n    afterStatusCodes: retryAfterStatusCodes,\n    maxRetryAfter: Number.POSITIVE_INFINITY,\n    backoffLimit: Number.POSITIVE_INFINITY,\n    delay: attemptCount => 0.3 * (2 ** (attemptCount - 1)) * 1000,\n};\nconst normalizeRetryOptions = (retry = {}) => {\n    if (typeof retry === 'number') {\n        return {\n            ...defaultRetryOptions,\n            limit: retry,\n        };\n    }\n    if (retry.methods && !Array.isArray(retry.methods)) {\n        throw new Error('retry.methods must be an array');\n    }\n    if (retry.statusCodes && !Array.isArray(retry.statusCodes)) {\n        throw new Error('retry.statusCodes must be an array');\n    }\n    return {\n        ...defaultRetryOptions,\n        ...retry,\n        afterStatusCodes: retryAfterStatusCodes,\n    };\n};\n//# sourceMappingURL=normalize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/utils/normalize.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/utils/options.js":
/*!***********************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/options.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findUnknownOptions: () => (/* binding */ findUnknownOptions)\n/* harmony export */ });\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/constants.js */ \"(ssr)/../../node_modules/ky/distribution/core/constants.js\");\n\nconst findUnknownOptions = (request, options) => {\n    const unknownOptions = {};\n    for (const key in options) {\n        if (!(key in _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.requestOptionsRegistry) && !(key in _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.kyOptionKeys) && !(key in request)) {\n            unknownOptions[key] = options[key];\n        }\n    }\n    return unknownOptions;\n};\n//# sourceMappingURL=options.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9vcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRFO0FBQ3JFO0FBQ1A7QUFDQTtBQUNBLHFCQUFxQixzRUFBc0IsY0FBYyw0REFBWTtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9vcHRpb25zLmpzP2Q4MjgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsga3lPcHRpb25LZXlzLCByZXF1ZXN0T3B0aW9uc1JlZ2lzdHJ5IH0gZnJvbSAnLi4vY29yZS9jb25zdGFudHMuanMnO1xuZXhwb3J0IGNvbnN0IGZpbmRVbmtub3duT3B0aW9ucyA9IChyZXF1ZXN0LCBvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgdW5rbm93bk9wdGlvbnMgPSB7fTtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBvcHRpb25zKSB7XG4gICAgICAgIGlmICghKGtleSBpbiByZXF1ZXN0T3B0aW9uc1JlZ2lzdHJ5KSAmJiAhKGtleSBpbiBreU9wdGlvbktleXMpICYmICEoa2V5IGluIHJlcXVlc3QpKSB7XG4gICAgICAgICAgICB1bmtub3duT3B0aW9uc1trZXldID0gb3B0aW9uc1trZXldO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB1bmtub3duT3B0aW9ucztcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1vcHRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/utils/options.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/ky/distribution/utils/timeout.js":
/*!***********************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/timeout.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ timeout)\n/* harmony export */ });\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/TimeoutError.js */ \"(ssr)/../../node_modules/ky/distribution/errors/TimeoutError.js\");\n\n// `Promise.race()` workaround (#91)\nasync function timeout(request, init, abortController, options) {\n    return new Promise((resolve, reject) => {\n        const timeoutId = setTimeout(() => {\n            if (abortController) {\n                abortController.abort();\n            }\n            reject(new _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_0__.TimeoutError(request));\n        }, options.timeout);\n        void options\n            .fetch(request, init)\n            .then(resolve)\n            .catch(reject)\n            .then(() => {\n            clearTimeout(timeoutId);\n        });\n    });\n}\n//# sourceMappingURL=timeout.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy90aW1lb3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlEO0FBQ3pEO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGlFQUFZO0FBQ25DLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy90aW1lb3V0LmpzP2M2MmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGltZW91dEVycm9yIH0gZnJvbSAnLi4vZXJyb3JzL1RpbWVvdXRFcnJvci5qcyc7XG4vLyBgUHJvbWlzZS5yYWNlKClgIHdvcmthcm91bmQgKCM5MSlcbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIHRpbWVvdXQocmVxdWVzdCwgaW5pdCwgYWJvcnRDb250cm9sbGVyLCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICBpZiAoYWJvcnRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgYWJvcnRDb250cm9sbGVyLmFib3J0KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZWplY3QobmV3IFRpbWVvdXRFcnJvcihyZXF1ZXN0KSk7XG4gICAgICAgIH0sIG9wdGlvbnMudGltZW91dCk7XG4gICAgICAgIHZvaWQgb3B0aW9uc1xuICAgICAgICAgICAgLmZldGNoKHJlcXVlc3QsIGluaXQpXG4gICAgICAgICAgICAudGhlbihyZXNvbHZlKVxuICAgICAgICAgICAgLmNhdGNoKHJlamVjdClcbiAgICAgICAgICAgIC50aGVuKCgpID0+IHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgICB9KTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRpbWVvdXQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/ky/distribution/utils/timeout.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/core/Ky.js":
/*!*****************************************************!*\
  !*** ../../node_modules/ky/distribution/core/Ky.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ky: () => (/* binding */ Ky)\n/* harmony export */ });\n/* harmony import */ var _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../errors/HTTPError.js */ \"(rsc)/../../node_modules/ky/distribution/errors/HTTPError.js\");\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../errors/TimeoutError.js */ \"(rsc)/../../node_modules/ky/distribution/errors/TimeoutError.js\");\n/* harmony import */ var _utils_merge_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/merge.js */ \"(rsc)/../../node_modules/ky/distribution/utils/merge.js\");\n/* harmony import */ var _utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/normalize.js */ \"(rsc)/../../node_modules/ky/distribution/utils/normalize.js\");\n/* harmony import */ var _utils_timeout_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../utils/timeout.js */ \"(rsc)/../../node_modules/ky/distribution/utils/timeout.js\");\n/* harmony import */ var _utils_delay_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/delay.js */ \"(rsc)/../../node_modules/ky/distribution/utils/delay.js\");\n/* harmony import */ var _utils_options_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/options.js */ \"(rsc)/../../node_modules/ky/distribution/utils/options.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(rsc)/../../node_modules/ky/distribution/core/constants.js\");\n\n\n\n\n\n\n\n\nclass Ky {\n    static create(input, options) {\n        const ky = new Ky(input, options);\n        const function_ = async () => {\n            if (typeof ky._options.timeout === 'number' && ky._options.timeout > _constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout) {\n                throw new RangeError(`The \\`timeout\\` option cannot be greater than ${_constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout}`);\n            }\n            // Delay the fetch so that body method shortcuts can set the Accept header\n            await Promise.resolve();\n            let response = await ky._fetch();\n            for (const hook of ky._options.hooks.afterResponse) {\n                // eslint-disable-next-line no-await-in-loop\n                const modifiedResponse = await hook(ky.request, ky._options, ky._decorateResponse(response.clone()));\n                if (modifiedResponse instanceof globalThis.Response) {\n                    response = modifiedResponse;\n                }\n            }\n            ky._decorateResponse(response);\n            if (!response.ok && ky._options.throwHttpErrors) {\n                let error = new _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__.HTTPError(response, ky.request, ky._options);\n                for (const hook of ky._options.hooks.beforeError) {\n                    // eslint-disable-next-line no-await-in-loop\n                    error = await hook(error);\n                }\n                throw error;\n            }\n            // If `onDownloadProgress` is passed, it uses the stream API internally\n            /* istanbul ignore next */\n            if (ky._options.onDownloadProgress) {\n                if (typeof ky._options.onDownloadProgress !== 'function') {\n                    throw new TypeError('The `onDownloadProgress` option must be a function');\n                }\n                if (!_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsResponseStreams) {\n                    throw new Error('Streams are not supported in your environment. `ReadableStream` is missing.');\n                }\n                return ky._stream(response.clone(), ky._options.onDownloadProgress);\n            }\n            return response;\n        };\n        const isRetriableMethod = ky._options.retry.methods.includes(ky.request.method.toLowerCase());\n        const result = (isRetriableMethod ? ky._retry(function_) : function_());\n        for (const [type, mimeType] of Object.entries(_constants_js__WEBPACK_IMPORTED_MODULE_0__.responseTypes)) {\n            result[type] = async () => {\n                // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n                ky.request.headers.set('accept', ky.request.headers.get('accept') || mimeType);\n                const awaitedResult = await result;\n                const response = awaitedResult.clone();\n                if (type === 'json') {\n                    if (response.status === 204) {\n                        return '';\n                    }\n                    const arrayBuffer = await response.clone().arrayBuffer();\n                    const responseSize = arrayBuffer.byteLength;\n                    if (responseSize === 0) {\n                        return '';\n                    }\n                    if (options.parseJson) {\n                        return options.parseJson(await response.text());\n                    }\n                }\n                return response[type]();\n            };\n        }\n        return result;\n    }\n    // eslint-disable-next-line complexity\n    constructor(input, options = {}) {\n        Object.defineProperty(this, \"request\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"abortController\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"_retryCount\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0\n        });\n        Object.defineProperty(this, \"_input\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"_options\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this._input = input;\n        this._options = {\n            // TODO: credentials can be removed when the spec change is implemented in all browsers. Context: https://www.chromestatus.com/feature/4539473312350208\n            credentials: this._input.credentials || 'same-origin',\n            ...options,\n            headers: (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_2__.mergeHeaders)(this._input.headers, options.headers),\n            hooks: (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_2__.deepMerge)({\n                beforeRequest: [],\n                beforeRetry: [],\n                beforeError: [],\n                afterResponse: [],\n            }, options.hooks),\n            method: (0,_utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__.normalizeRequestMethod)(options.method ?? this._input.method),\n            // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n            prefixUrl: String(options.prefixUrl || ''),\n            retry: (0,_utils_normalize_js__WEBPACK_IMPORTED_MODULE_3__.normalizeRetryOptions)(options.retry),\n            throwHttpErrors: options.throwHttpErrors !== false,\n            timeout: options.timeout ?? 10000,\n            fetch: options.fetch ?? globalThis.fetch.bind(globalThis),\n        };\n        if (typeof this._input !== 'string' && !(this._input instanceof URL || this._input instanceof globalThis.Request)) {\n            throw new TypeError('`input` must be a string, URL, or Request');\n        }\n        if (this._options.prefixUrl && typeof this._input === 'string') {\n            if (this._input.startsWith('/')) {\n                throw new Error('`input` must not begin with a slash when using `prefixUrl`');\n            }\n            if (!this._options.prefixUrl.endsWith('/')) {\n                this._options.prefixUrl += '/';\n            }\n            this._input = this._options.prefixUrl + this._input;\n        }\n        if (_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsAbortController) {\n            this.abortController = new globalThis.AbortController();\n            if (this._options.signal) {\n                const originalSignal = this._options.signal;\n                this._options.signal.addEventListener('abort', () => {\n                    this.abortController.abort(originalSignal.reason);\n                });\n            }\n            this._options.signal = this.abortController.signal;\n        }\n        if (_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsRequestStreams) {\n            // @ts-expect-error - Types are outdated.\n            this._options.duplex = 'half';\n        }\n        this.request = new globalThis.Request(this._input, this._options);\n        if (this._options.searchParams) {\n            // eslint-disable-next-line unicorn/prevent-abbreviations\n            const textSearchParams = typeof this._options.searchParams === 'string'\n                ? this._options.searchParams.replace(/^\\?/, '')\n                : new URLSearchParams(this._options.searchParams).toString();\n            // eslint-disable-next-line unicorn/prevent-abbreviations\n            const searchParams = '?' + textSearchParams;\n            const url = this.request.url.replace(/(?:\\?.*?)?(?=#|$)/, searchParams);\n            // To provide correct form boundary, Content-Type header should be deleted each time when new Request instantiated from another one\n            if (((_constants_js__WEBPACK_IMPORTED_MODULE_0__.supportsFormData && this._options.body instanceof globalThis.FormData)\n                || this._options.body instanceof URLSearchParams) && !(this._options.headers && this._options.headers['content-type'])) {\n                this.request.headers.delete('content-type');\n            }\n            // The spread of `this.request` is required as otherwise it misses the `duplex` option for some reason and throws.\n            this.request = new globalThis.Request(new globalThis.Request(url, { ...this.request }), this._options);\n        }\n        if (this._options.json !== undefined) {\n            this._options.body = JSON.stringify(this._options.json);\n            this.request.headers.set('content-type', this._options.headers.get('content-type') ?? 'application/json');\n            this.request = new globalThis.Request(this.request, { body: this._options.body });\n        }\n    }\n    _calculateRetryDelay(error) {\n        this._retryCount++;\n        if (this._retryCount <= this._options.retry.limit && !(error instanceof _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__.TimeoutError)) {\n            if (error instanceof _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_1__.HTTPError) {\n                if (!this._options.retry.statusCodes.includes(error.response.status)) {\n                    return 0;\n                }\n                const retryAfter = error.response.headers.get('Retry-After');\n                if (retryAfter && this._options.retry.afterStatusCodes.includes(error.response.status)) {\n                    let after = Number(retryAfter);\n                    if (Number.isNaN(after)) {\n                        after = Date.parse(retryAfter) - Date.now();\n                    }\n                    else {\n                        after *= 1000;\n                    }\n                    if (this._options.retry.maxRetryAfter !== undefined && after > this._options.retry.maxRetryAfter) {\n                        return 0;\n                    }\n                    return after;\n                }\n                if (error.response.status === 413) {\n                    return 0;\n                }\n            }\n            const retryDelay = this._options.retry.delay(this._retryCount);\n            return Math.min(this._options.retry.backoffLimit, retryDelay);\n        }\n        return 0;\n    }\n    _decorateResponse(response) {\n        if (this._options.parseJson) {\n            response.json = async () => this._options.parseJson(await response.text());\n        }\n        return response;\n    }\n    async _retry(function_) {\n        try {\n            return await function_();\n        }\n        catch (error) {\n            const ms = Math.min(this._calculateRetryDelay(error), _constants_js__WEBPACK_IMPORTED_MODULE_0__.maxSafeTimeout);\n            if (ms !== 0 && this._retryCount > 0) {\n                await (0,_utils_delay_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(ms, { signal: this._options.signal });\n                for (const hook of this._options.hooks.beforeRetry) {\n                    // eslint-disable-next-line no-await-in-loop\n                    const hookResult = await hook({\n                        request: this.request,\n                        options: this._options,\n                        error: error,\n                        retryCount: this._retryCount,\n                    });\n                    // If `stop` is returned from the hook, the retry process is stopped\n                    if (hookResult === _constants_js__WEBPACK_IMPORTED_MODULE_0__.stop) {\n                        return;\n                    }\n                }\n                return this._retry(function_);\n            }\n            throw error;\n        }\n    }\n    async _fetch() {\n        for (const hook of this._options.hooks.beforeRequest) {\n            // eslint-disable-next-line no-await-in-loop\n            const result = await hook(this.request, this._options);\n            if (result instanceof Request) {\n                this.request = result;\n                break;\n            }\n            if (result instanceof Response) {\n                return result;\n            }\n        }\n        const nonRequestOptions = (0,_utils_options_js__WEBPACK_IMPORTED_MODULE_6__.findUnknownOptions)(this.request, this._options);\n        if (this._options.timeout === false) {\n            return this._options.fetch(this.request.clone(), nonRequestOptions);\n        }\n        return (0,_utils_timeout_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(this.request.clone(), nonRequestOptions, this.abortController, this._options);\n    }\n    /* istanbul ignore next */\n    _stream(response, onDownloadProgress) {\n        const totalBytes = Number(response.headers.get('content-length')) || 0;\n        let transferredBytes = 0;\n        if (response.status === 204) {\n            if (onDownloadProgress) {\n                onDownloadProgress({ percent: 1, totalBytes, transferredBytes }, new Uint8Array());\n            }\n            return new globalThis.Response(null, {\n                status: response.status,\n                statusText: response.statusText,\n                headers: response.headers,\n            });\n        }\n        return new globalThis.Response(new globalThis.ReadableStream({\n            async start(controller) {\n                const reader = response.body.getReader();\n                if (onDownloadProgress) {\n                    onDownloadProgress({ percent: 0, transferredBytes: 0, totalBytes }, new Uint8Array());\n                }\n                async function read() {\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        controller.close();\n                        return;\n                    }\n                    if (onDownloadProgress) {\n                        transferredBytes += value.byteLength;\n                        const percent = totalBytes === 0 ? 0 : transferredBytes / totalBytes;\n                        onDownloadProgress({ percent, transferredBytes, totalBytes }, value);\n                    }\n                    controller.enqueue(value);\n                    await read();\n                }\n                await read();\n            },\n        }), {\n            status: response.status,\n            statusText: response.statusText,\n            headers: response.headers,\n        });\n    }\n}\n//# sourceMappingURL=Ky.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/core/Ky.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/core/constants.js":
/*!************************************************************!*\
  !*** ../../node_modules/ky/distribution/core/constants.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   kyOptionKeys: () => (/* binding */ kyOptionKeys),\n/* harmony export */   maxSafeTimeout: () => (/* binding */ maxSafeTimeout),\n/* harmony export */   requestMethods: () => (/* binding */ requestMethods),\n/* harmony export */   requestOptionsRegistry: () => (/* binding */ requestOptionsRegistry),\n/* harmony export */   responseTypes: () => (/* binding */ responseTypes),\n/* harmony export */   stop: () => (/* binding */ stop),\n/* harmony export */   supportsAbortController: () => (/* binding */ supportsAbortController),\n/* harmony export */   supportsFormData: () => (/* binding */ supportsFormData),\n/* harmony export */   supportsRequestStreams: () => (/* binding */ supportsRequestStreams),\n/* harmony export */   supportsResponseStreams: () => (/* binding */ supportsResponseStreams)\n/* harmony export */ });\nconst supportsRequestStreams = (() => {\n    let duplexAccessed = false;\n    let hasContentType = false;\n    const supportsReadableStream = typeof globalThis.ReadableStream === 'function';\n    const supportsRequest = typeof globalThis.Request === 'function';\n    if (supportsReadableStream && supportsRequest) {\n        hasContentType = new globalThis.Request('https://empty.invalid', {\n            body: new globalThis.ReadableStream(),\n            method: 'POST',\n            // @ts-expect-error - Types are outdated.\n            get duplex() {\n                duplexAccessed = true;\n                return 'half';\n            },\n        }).headers.has('Content-Type');\n    }\n    return duplexAccessed && !hasContentType;\n})();\nconst supportsAbortController = typeof globalThis.AbortController === 'function';\nconst supportsResponseStreams = typeof globalThis.ReadableStream === 'function';\nconst supportsFormData = typeof globalThis.FormData === 'function';\nconst requestMethods = ['get', 'post', 'put', 'patch', 'head', 'delete'];\nconst validate = () => undefined;\nvalidate();\nconst responseTypes = {\n    json: 'application/json',\n    text: 'text/*',\n    formData: 'multipart/form-data',\n    arrayBuffer: '*/*',\n    blob: '*/*',\n};\n// The maximum value of a 32bit int (see issue #117)\nconst maxSafeTimeout = 2147483647;\nconst stop = Symbol('stop');\nconst kyOptionKeys = {\n    json: true,\n    parseJson: true,\n    searchParams: true,\n    prefixUrl: true,\n    retry: true,\n    timeout: true,\n    hooks: true,\n    throwHttpErrors: true,\n    onDownloadProgress: true,\n    fetch: true,\n};\nconst requestOptionsRegistry = {\n    method: true,\n    headers: true,\n    body: true,\n    mode: true,\n    credentials: true,\n    cache: true,\n    redirect: true,\n    referrer: true,\n    referrerPolicy: true,\n    integrity: true,\n    keepalive: true,\n    signal: true,\n    window: true,\n    dispatcher: true,\n    duplex: true,\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/core/constants.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/errors/HTTPError.js":
/*!**************************************************************!*\
  !*** ../../node_modules/ky/distribution/errors/HTTPError.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTTPError: () => (/* binding */ HTTPError)\n/* harmony export */ });\n// eslint-lint-disable-next-line @typescript-eslint/naming-convention\nclass HTTPError extends Error {\n    constructor(response, request, options) {\n        const code = (response.status || response.status === 0) ? response.status : '';\n        const title = response.statusText || '';\n        const status = `${code} ${title}`.trim();\n        const reason = status ? `status code ${status}` : 'an unknown error';\n        super(`Request failed with ${reason}`);\n        Object.defineProperty(this, \"response\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"request\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"options\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = 'HTTPError';\n        this.response = response;\n        this.request = request;\n        this.options = options;\n    }\n}\n//# sourceMappingURL=HTTPError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/errors/HTTPError.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/errors/TimeoutError.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/ky/distribution/errors/TimeoutError.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TimeoutError: () => (/* binding */ TimeoutError)\n/* harmony export */ });\nclass TimeoutError extends Error {\n    constructor(request) {\n        super('Request timed out');\n        Object.defineProperty(this, \"request\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = 'TimeoutError';\n        this.request = request;\n    }\n}\n//# sourceMappingURL=TimeoutError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi9lcnJvcnMvVGltZW91dEVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi9lcnJvcnMvVGltZW91dEVycm9yLmpzP2Y2MmUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIFRpbWVvdXRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihyZXF1ZXN0KSB7XG4gICAgICAgIHN1cGVyKCdSZXF1ZXN0IHRpbWVkIG91dCcpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJyZXF1ZXN0XCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMubmFtZSA9ICdUaW1lb3V0RXJyb3InO1xuICAgICAgICB0aGlzLnJlcXVlc3QgPSByZXF1ZXN0O1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVRpbWVvdXRFcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/errors/TimeoutError.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/index.js":
/*!***************************************************!*\
  !*** ../../node_modules/ky/distribution/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HTTPError: () => (/* reexport safe */ _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_3__.HTTPError),\n/* harmony export */   TimeoutError: () => (/* reexport safe */ _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__.TimeoutError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core/Ky.js */ \"(rsc)/../../node_modules/ky/distribution/core/Ky.js\");\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./core/constants.js */ \"(rsc)/../../node_modules/ky/distribution/core/constants.js\");\n/* harmony import */ var _utils_merge_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/merge.js */ \"(rsc)/../../node_modules/ky/distribution/utils/merge.js\");\n/* harmony import */ var _errors_HTTPError_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors/HTTPError.js */ \"(rsc)/../../node_modules/ky/distribution/errors/HTTPError.js\");\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./errors/TimeoutError.js */ \"(rsc)/../../node_modules/ky/distribution/errors/TimeoutError.js\");\n/*! MIT License © Sindre Sorhus */\n\n\n\nconst createInstance = (defaults) => {\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\n    const ky = (input, options) => _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__.Ky.create(input, (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, options));\n    for (const method of _core_constants_js__WEBPACK_IMPORTED_MODULE_2__.requestMethods) {\n        // eslint-disable-next-line @typescript-eslint/promise-function-async\n        ky[method] = (input, options) => _core_Ky_js__WEBPACK_IMPORTED_MODULE_0__.Ky.create(input, (0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, options, { method }));\n    }\n    ky.create = (newDefaults) => createInstance((0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(newDefaults));\n    ky.extend = (newDefaults) => createInstance((0,_utils_merge_js__WEBPACK_IMPORTED_MODULE_1__.validateAndMerge)(defaults, newDefaults));\n    ky.stop = _core_constants_js__WEBPACK_IMPORTED_MODULE_2__.stop;\n    return ky;\n};\nconst ky = createInstance();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ky);\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/utils/delay.js":
/*!*********************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/delay.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ delay)\n/* harmony export */ });\n// https://github.com/sindresorhus/delay/tree/ab98ae8dfcb38e1593286c94d934e70d14a4e111\nasync function delay(ms, { signal }) {\n    return new Promise((resolve, reject) => {\n        if (signal) {\n            signal.throwIfAborted();\n            signal.addEventListener('abort', abortHandler, { once: true });\n        }\n        function abortHandler() {\n            clearTimeout(timeoutId);\n            reject(signal.reason);\n        }\n        const timeoutId = setTimeout(() => {\n            signal?.removeEventListener('abort', abortHandler);\n            resolve();\n        }, ms);\n    });\n}\n//# sourceMappingURL=delay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9kZWxheS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDZSwyQkFBMkIsUUFBUTtBQUNsRDtBQUNBO0FBQ0E7QUFDQSw2REFBNkQsWUFBWTtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9reS9kaXN0cmlidXRpb24vdXRpbHMvZGVsYXkuanM/Y2NlNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBodHRwczovL2dpdGh1Yi5jb20vc2luZHJlc29yaHVzL2RlbGF5L3RyZWUvYWI5OGFlOGRmY2IzOGUxNTkzMjg2Yzk0ZDkzNGU3MGQxNGE0ZTExMVxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gZGVsYXkobXMsIHsgc2lnbmFsIH0pIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICBpZiAoc2lnbmFsKSB7XG4gICAgICAgICAgICBzaWduYWwudGhyb3dJZkFib3J0ZWQoKTtcbiAgICAgICAgICAgIHNpZ25hbC5hZGRFdmVudExpc3RlbmVyKCdhYm9ydCcsIGFib3J0SGFuZGxlciwgeyBvbmNlOiB0cnVlIH0pO1xuICAgICAgICB9XG4gICAgICAgIGZ1bmN0aW9uIGFib3J0SGFuZGxlcigpIHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgICAgICAgcmVqZWN0KHNpZ25hbC5yZWFzb24pO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgc2lnbmFsPy5yZW1vdmVFdmVudExpc3RlbmVyKCdhYm9ydCcsIGFib3J0SGFuZGxlcik7XG4gICAgICAgICAgICByZXNvbHZlKCk7XG4gICAgICAgIH0sIG1zKTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRlbGF5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/utils/delay.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/utils/is.js":
/*!******************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/is.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObject: () => (/* binding */ isObject)\n/* harmony export */ });\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst isObject = (value) => value !== null && typeof value === 'object';\n//# sourceMappingURL=is.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9pcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9reS9kaXN0cmlidXRpb24vdXRpbHMvaXMuanM/OWFhYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L2Jhbi10eXBlc1xuZXhwb3J0IGNvbnN0IGlzT2JqZWN0ID0gKHZhbHVlKSA9PiB2YWx1ZSAhPT0gbnVsbCAmJiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/utils/is.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/utils/merge.js":
/*!*********************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/merge.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepMerge: () => (/* binding */ deepMerge),\n/* harmony export */   mergeHeaders: () => (/* binding */ mergeHeaders),\n/* harmony export */   validateAndMerge: () => (/* binding */ validateAndMerge)\n/* harmony export */ });\n/* harmony import */ var _is_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is.js */ \"(rsc)/../../node_modules/ky/distribution/utils/is.js\");\n\nconst validateAndMerge = (...sources) => {\n    for (const source of sources) {\n        if ((!(0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source) || Array.isArray(source)) && source !== undefined) {\n            throw new TypeError('The `options` argument must be an object');\n        }\n    }\n    return deepMerge({}, ...sources);\n};\nconst mergeHeaders = (source1 = {}, source2 = {}) => {\n    const result = new globalThis.Headers(source1);\n    const isHeadersInstance = source2 instanceof globalThis.Headers;\n    const source = new globalThis.Headers(source2);\n    for (const [key, value] of source.entries()) {\n        if ((isHeadersInstance && value === 'undefined') || value === undefined) {\n            result.delete(key);\n        }\n        else {\n            result.set(key, value);\n        }\n    }\n    return result;\n};\n// TODO: Make this strongly-typed (no `any`).\nconst deepMerge = (...sources) => {\n    let returnValue = {};\n    let headers = {};\n    for (const source of sources) {\n        if (Array.isArray(source)) {\n            if (!Array.isArray(returnValue)) {\n                returnValue = [];\n            }\n            returnValue = [...returnValue, ...source];\n        }\n        else if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source)) {\n            for (let [key, value] of Object.entries(source)) {\n                if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(value) && key in returnValue) {\n                    value = deepMerge(returnValue[key], value);\n                }\n                returnValue = { ...returnValue, [key]: value };\n            }\n            if ((0,_is_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(source.headers)) {\n                headers = mergeHeaders(headers, source.headers);\n                returnValue.headers = headers;\n            }\n        }\n    }\n    return returnValue;\n};\n//# sourceMappingURL=merge.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/utils/merge.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/utils/normalize.js":
/*!*************************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/normalize.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeRequestMethod: () => (/* binding */ normalizeRequestMethod),\n/* harmony export */   normalizeRetryOptions: () => (/* binding */ normalizeRetryOptions)\n/* harmony export */ });\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/constants.js */ \"(rsc)/../../node_modules/ky/distribution/core/constants.js\");\n\nconst normalizeRequestMethod = (input) => _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.requestMethods.includes(input) ? input.toUpperCase() : input;\nconst retryMethods = ['get', 'put', 'head', 'delete', 'options', 'trace'];\nconst retryStatusCodes = [408, 413, 429, 500, 502, 503, 504];\nconst retryAfterStatusCodes = [413, 429, 503];\nconst defaultRetryOptions = {\n    limit: 2,\n    methods: retryMethods,\n    statusCodes: retryStatusCodes,\n    afterStatusCodes: retryAfterStatusCodes,\n    maxRetryAfter: Number.POSITIVE_INFINITY,\n    backoffLimit: Number.POSITIVE_INFINITY,\n    delay: attemptCount => 0.3 * (2 ** (attemptCount - 1)) * 1000,\n};\nconst normalizeRetryOptions = (retry = {}) => {\n    if (typeof retry === 'number') {\n        return {\n            ...defaultRetryOptions,\n            limit: retry,\n        };\n    }\n    if (retry.methods && !Array.isArray(retry.methods)) {\n        throw new Error('retry.methods must be an array');\n    }\n    if (retry.statusCodes && !Array.isArray(retry.statusCodes)) {\n        throw new Error('retry.statusCodes must be an array');\n    }\n    return {\n        ...defaultRetryOptions,\n        ...retry,\n        afterStatusCodes: retryAfterStatusCodes,\n    };\n};\n//# sourceMappingURL=normalize.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/utils/normalize.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/utils/options.js":
/*!***********************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/options.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findUnknownOptions: () => (/* binding */ findUnknownOptions)\n/* harmony export */ });\n/* harmony import */ var _core_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../core/constants.js */ \"(rsc)/../../node_modules/ky/distribution/core/constants.js\");\n\nconst findUnknownOptions = (request, options) => {\n    const unknownOptions = {};\n    for (const key in options) {\n        if (!(key in _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.requestOptionsRegistry) && !(key in _core_constants_js__WEBPACK_IMPORTED_MODULE_0__.kyOptionKeys) && !(key in request)) {\n            unknownOptions[key] = options[key];\n        }\n    }\n    return unknownOptions;\n};\n//# sourceMappingURL=options.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9vcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRFO0FBQ3JFO0FBQ1A7QUFDQTtBQUNBLHFCQUFxQixzRUFBc0IsY0FBYyw0REFBWTtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy9vcHRpb25zLmpzPzg5NWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsga3lPcHRpb25LZXlzLCByZXF1ZXN0T3B0aW9uc1JlZ2lzdHJ5IH0gZnJvbSAnLi4vY29yZS9jb25zdGFudHMuanMnO1xuZXhwb3J0IGNvbnN0IGZpbmRVbmtub3duT3B0aW9ucyA9IChyZXF1ZXN0LCBvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgdW5rbm93bk9wdGlvbnMgPSB7fTtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBvcHRpb25zKSB7XG4gICAgICAgIGlmICghKGtleSBpbiByZXF1ZXN0T3B0aW9uc1JlZ2lzdHJ5KSAmJiAhKGtleSBpbiBreU9wdGlvbktleXMpICYmICEoa2V5IGluIHJlcXVlc3QpKSB7XG4gICAgICAgICAgICB1bmtub3duT3B0aW9uc1trZXldID0gb3B0aW9uc1trZXldO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB1bmtub3duT3B0aW9ucztcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1vcHRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/utils/options.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/ky/distribution/utils/timeout.js":
/*!***********************************************************!*\
  !*** ../../node_modules/ky/distribution/utils/timeout.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ timeout)\n/* harmony export */ });\n/* harmony import */ var _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../errors/TimeoutError.js */ \"(rsc)/../../node_modules/ky/distribution/errors/TimeoutError.js\");\n\n// `Promise.race()` workaround (#91)\nasync function timeout(request, init, abortController, options) {\n    return new Promise((resolve, reject) => {\n        const timeoutId = setTimeout(() => {\n            if (abortController) {\n                abortController.abort();\n            }\n            reject(new _errors_TimeoutError_js__WEBPACK_IMPORTED_MODULE_0__.TimeoutError(request));\n        }, options.timeout);\n        void options\n            .fetch(request, init)\n            .then(resolve)\n            .catch(reject)\n            .then(() => {\n            clearTimeout(timeoutId);\n        });\n    });\n}\n//# sourceMappingURL=timeout.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy90aW1lb3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlEO0FBQ3pEO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGlFQUFZO0FBQ25DLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2t5L2Rpc3RyaWJ1dGlvbi91dGlscy90aW1lb3V0LmpzP2Q2OWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVGltZW91dEVycm9yIH0gZnJvbSAnLi4vZXJyb3JzL1RpbWVvdXRFcnJvci5qcyc7XG4vLyBgUHJvbWlzZS5yYWNlKClgIHdvcmthcm91bmQgKCM5MSlcbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIHRpbWVvdXQocmVxdWVzdCwgaW5pdCwgYWJvcnRDb250cm9sbGVyLCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICBpZiAoYWJvcnRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgYWJvcnRDb250cm9sbGVyLmFib3J0KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZWplY3QobmV3IFRpbWVvdXRFcnJvcihyZXF1ZXN0KSk7XG4gICAgICAgIH0sIG9wdGlvbnMudGltZW91dCk7XG4gICAgICAgIHZvaWQgb3B0aW9uc1xuICAgICAgICAgICAgLmZldGNoKHJlcXVlc3QsIGluaXQpXG4gICAgICAgICAgICAudGhlbihyZXNvbHZlKVxuICAgICAgICAgICAgLmNhdGNoKHJlamVjdClcbiAgICAgICAgICAgIC50aGVuKCgpID0+IHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgICB9KTtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRpbWVvdXQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/ky/distribution/utils/timeout.js\n");

/***/ })

};
;
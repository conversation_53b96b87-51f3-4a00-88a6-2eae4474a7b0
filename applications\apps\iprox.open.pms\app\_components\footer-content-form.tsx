'use client';

import { useClient<PERSON><PERSON> } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { updateFooterComponent } from '@/services/page-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { pageZoneMapper } from '@/utils/page-zone-mapper';
import { getSortedPageZones, reorderZones } from '@/utils/page-zone-utils';
import { Button, FieldType, FormBuilder, FormSubmitValues, PageZone, showToast } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';

import { PageHeader } from '@/components/page-header';

interface FooterContentFormProps {
  pageZones: components['schemas']['PageZoneDto'][];
}

export function FooterContentForm({ pageZones }: FooterContentFormProps) {
  const clientApi = useClientApi();
  const t = useTranslations('footer');

  const [loading, setLoading] = useState(false);

  const formFields = useMemo(() => {
    return [
      {
        name: 'footerContent',
        label: t('footerContent'),
        fieldType: FieldType.PageZonesField,
        value: getSortedPageZones(pageZones),
        validationRules: [],
        style: {
          colSpan: 'col-span-2',
          gridRow: 'grid-row-1',
        },
      },
    ];
  }, [pageZones, t]);

  const handleSubmit = (values: FormSubmitValues) => {
    setLoading(true);
    updateFooterComponent(clientApi, {
      zones: pageZoneMapper(
        Array.isArray(values.footerContent) ? reorderZones(values.footerContent as PageZone[]) : []
      ),
    })
      .then(() => {
        showToast(t('footerComponentUpdated'), { type: 'success' });
      })
      .catch(async (error) => {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      })
      .finally(() => setLoading(false));
  };

  return (
    <div>
      <PageHeader title={t('footer')} />
      <FormBuilder
        fields={formFields}
        onSubmit={handleSubmit}
        disableButton={loading}
        buttons={
          <Button variant="primary" type="submit" disabled={loading} className="mt-20">
            {t('save')}
          </Button>
        }
        gridContainerClasses="grid grid-cols-2 gap-x-16"
      />
    </div>
  );
}

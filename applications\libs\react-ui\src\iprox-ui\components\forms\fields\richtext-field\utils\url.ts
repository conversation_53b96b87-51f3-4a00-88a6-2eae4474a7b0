export function sanitizeUrl(url: string, portalUrl: string): boolean {
  const allowedSchemes = ['http', 'https', 'mailto', 'tel'];
  const unsafePatterns = [
    // JavaScript scheme
    /^javascript:/i,
    // Data scheme
    /^data:/i,
    // Whitespace, angle brackets, pipe or command characters
    /[\s<>|^]/,
    // Encoded <
    /%3C/i,
    // Encoded >
    /%3E/i,
    // Encoded =
    /%3D/i,
    // Encoded '
    /%27/i,
    // Encoded "
    /%22/i,
    // Encoded (
    /%28/i,
    // Encoded )
    /%29/i,
  ];

  const emailPattern =
    // eslint-disable-next-line no-control-regex
    /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/i;
  const telPattern = /^(?:([+]\d{1,4})[-.\s]?)?(?:[(](\d{1,3})[)][-.\s]?)?(\d{1,4})[-.\s]?(\d{1,4})[-.\s]?(\d{1,9})$/;

  try {
    const urlToParse = url.trim();

    let parsedUrl: URL;

    if (urlToParse.startsWith('/')) {
      parsedUrl = new URL(urlToParse, portalUrl);
    } else {
      parsedUrl = new URL(urlToParse);
    }

    // Check if the scheme is in the allowed list
    if (!allowedSchemes.includes(parsedUrl.protocol.replace(':', ''))) {
      return false;
    }

    // Check if the mailto URL has a valid email address
    if (parsedUrl.protocol === 'mailto:') {
      const email = parsedUrl.pathname;
      if (!emailPattern.test(email)) {
        return false;
      }
    }

    // Check if the mailto URL has a valid email address
    if (parsedUrl.protocol === 'tel:') {
      const tel = parsedUrl.pathname;
      if (!telPattern.test(tel)) {
        return false;
      }
    }

    // Check for unsafe patterns
    for (const pattern of unsafePatterns) {
      if (pattern.test(url)) {
        return false;
      }
    }
  } catch (e) {
    // If URL parsing fails, it's not a valid URL
    return false;
  }

  return true;
}

const mimeMap = new Map([
  ['text/html', 'html'],
  ['text/plain', 'txt'],
  ['text/csv', 'csv'],
  ['application/msword', 'doc'],
  ['application/vnd.ms-excel', 'xls'],
  ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'docx'],
  ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'xlsx'],
  ['application/pdf', 'pdf'],
  ['application/zip', 'zip'],
  ['image/svg+xml', 'svg'],
  ['image/gif', 'gif'],
  ['image/jpeg', 'jpg'],
  ['image/png', 'png'],
  ['application/vnd.ms-powerpoint', 'ppt'],
  ['application/vnd.openxmlformats-officedocument.presentationml.presentation', 'pptx'],
  ['application/vnd.oasis.opendocument.text', 'odt'],
  ['application/vnd.oasis.opendocument.spreadsheet', 'ods'],
  ['application/vnd.oasis.opendocument.graphics', 'odg'],
  ['application/vnd.oasis.opendocument.presentation', 'odp'],
  ['application/octet-stream', 'db'],
  ['video/mp4', 'mp4'],
  ['video/webm', 'webm'],
]);

export function getExtensionFromMimeType(mimeType: string) {
  if (mimeMap.has(mimeType)) {
    return mimeMap.get(mimeType);
  }

  return null;
}

import { components } from '@/iprox-open.interface';
import { getDossierCategories } from '@/services/dossier-category-service.server';
import { getPage } from '@/services/page-service.server';

import { AnnouncementPageForm } from '@/components/announcement-page-form';
import { SearchPageForm } from '@/components/search-page-form';
import { SimplePageForm } from '@/components/simple-page-form';

export default async function Page({ params }: { params: { id: string } }) {
  const data = await getPage(params.id);
  const dossierCategoriesData = await getDossierCategories();

  if (data?.page.pageType === 'Search') {
    return (
      <SearchPageForm
        page={(data.page as components['schemas']['SearchPageDto']) ?? null}
        dossierCategories={dossierCategoriesData?.dossierCategories ?? []}
      />
    );
  }

  if (data?.page.pageType === 'Announcement') {
    return <AnnouncementPageForm page={(data.page as components['schemas']['AnnouncementPageDto']) ?? null} />;
  }

  if (data?.page.pageType === 'Simple') {
    return <SimplePageForm page={(data.page as components['schemas']['SimplePageDto']) ?? null} />;
  }

  console.error('Something went wrong. Page type is not supported.');
}

{"name": "workspace-plugin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "tools/workspace-plugin/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/tools/workspace-plugin", "main": "tools/workspace-plugin/src/index.ts", "tsConfig": "tools/workspace-plugin/tsconfig.lib.json", "assets": [{"input": "./tools/workspace-plugin/src", "glob": "**/!(*.ts)", "output": "./src"}, {"input": "./tools/workspace-plugin/src", "glob": "**/*.d.ts", "output": "./src"}, {"input": "./tools/workspace-plugin", "glob": "generators.json", "output": "."}, {"input": "./tools/workspace-plugin", "glob": "executors.json", "output": "."}]}}, "publish": {"executor": "nx:run-commands", "options": {"command": "node tools/scripts/publish.mjs workspace-plugin {args.ver} {args.tag}"}, "dependsOn": ["build"]}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "tools/workspace-plugin/jest.config.ts"}}}, "tags": []}
import { FieldDefinition, FieldType, ValidationRuleType } from '@iprox/iprox-ui';
import { useMemo } from 'react';

export function useTitleFormDefinition(title: string) {
  const titleDefinition = useMemo<FieldDefinition<FieldType.Text, string>>(
    () => ({
      name: 'title',
      label: 'Title',
      fieldType: FieldType.Text,
      value: title,
      validationRules: [
        {
          ruleType: ValidationRuleType.RequiredProperty,
          ruleValue: {},
        },
      ],
    }),
    [title]
  );

  return [titleDefinition];
}

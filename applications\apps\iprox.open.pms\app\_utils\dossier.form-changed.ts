import { FieldDefinition, FieldType, FormSubmitValues, ValueTypes } from '@iprox/iprox-ui';

import { areArraysEqual } from './array';

export const hasFormChanged = (
  formDefinition: FieldDefinition<FieldType, ValueTypes>[],
  formValues: FormSubmitValues | null,
  lastSavedValues: FormSubmitValues | null
) => {
  if (formValues) {
    if (lastSavedValues) {
      for (const key in lastSavedValues) {
        if (formValues[key] === lastSavedValues[key]) {
          const newValue = formValues[key];
          const lastValue = lastSavedValues[key];

          if (Array.isArray(newValue) && Array.isArray(lastValue)) {
            if (!areArraysEqual(newValue, lastValue)) {
              return true;
            }
          } else if (newValue !== lastValue) {
            return true;
          }
        }
      }
    } else {
      for (const item of formDefinition) {
        const newValue = formValues[item.name];
        const lastValue = item.value;

        if (Array.isArray(newValue) && Array.isArray(lastValue)) {
          if (!areArraysEqual(newValue, lastValue)) {
            return true;
          }
        }

        if (newValue !== lastValue) {
          return true;
        }
      }
    }
  }

  return false;
};

import { fetcher } from '@/http/fetcher-public';
import jwtDecode from 'jwt-decode';
import AzureADProvider from 'next-auth/providers/azure-ad';

export const AZUREAD_PROVIDERS = ['azure-ad', 'azure-ad-iprox', 'azure-ad-test'] as const;

export type AzureAdProviderConfig = {
  id: (typeof AZUREAD_PROVIDERS)[number];
  name: string;
  default: boolean;
  clientId: string;
  clientSecret: string;
  uri: string;
  tenantId: string;
};

export interface AzureTokenResponse {
  token_type: string;
  scope: string;
  expires_in: number;
  ext_expires_in: number;
  access_token: string;
  refresh_token: string;
  id_token: string;
}

export const azureAdProvidersConfig: AzureAdProviderConfig[] = JSON.parse(process.env.AZURE_AD_PROVIDERS ?? '[]');

const accessScopes = 'IproxOpen.Access openid profile email offline_access';

export const azureAuthProviderFactory = ({
  id,
  name,
  clientId,
  clientSecret,
  uri,
  tenantId,
}: AzureAdProviderConfig) => {
  return AzureADProvider({
    id,
    name,
    clientId,
    clientSecret,
    tenantId,
    authorization: {
      params: { scope: `${uri}${accessScopes}` },
    },
  });
};

export async function azureAdRefreshToken(accessToken: string, refreshToken: string): Promise<AzureTokenResponse> {
  try {
    const { aud, tid } = jwtDecode<{ aud: string; tid: string }>(accessToken);

    const provider = azureAdProvidersConfig.find(
      (provider) => provider.tenantId === tid && provider.uri.replace(/\/+$/g, '') === aud
    );

    if (!provider) {
      throw new Error('No provider found');
    }

    const response = await fetcher.post(`https://login.microsoftonline.com/${provider.tenantId}/oauth2/v2.0/token`, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        client_Id: provider.clientId,
        scope: `${provider.uri}${accessScopes}`,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
        client_secret: provider.clientSecret,
      }),
    });
    return await response.json<AzureTokenResponse>();
  } catch (error) {
    return Promise.reject(error);
  }
}

import { colors } from '../../config';
import * as http from '../../enums/httpEnums';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';
import { DateTime } from '../../utils/getCurrentDateTime';

describe('Dossier Create Tests', () => {
  let dossierId: string;
  let dossierCategoryId: string | null;
  let bearerToken: any;

  beforeAll(async () => {
    bearerToken = await loginAndGetToken();
    dossierCategoryId = await DossierHelpersNew.getRandomCategoryUuid(false, await bearerToken);
  }, 90000);

  it('DOSSIER - should create a dossier', async () => {
    const requestObject = {
      title: `test dossier token ${DateTime.getCurrentDateTimeIsoFormat()}`,
      categoryId: `${dossierCategoryId}`,
    };

    const response: any = await DossierHelpersNew.createDossier(
      requestObject.title,
      bearerToken,
      requestObject.categoryId
    );
    dossierId = await response.body.dossier.dossierId;

    expect(await response.status).toBe(http.StatusCode.CREATED_201);
  }, 90000);

  afterAll(async () => {
    await DossierHelpersNew.deleteDossier(dossierId, bearerToken);
  });
});

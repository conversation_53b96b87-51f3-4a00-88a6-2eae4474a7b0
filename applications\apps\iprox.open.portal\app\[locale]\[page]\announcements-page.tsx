import { components } from '@/iprox-open.interface';
import { Diagnostics } from '@/models/announcements.api-models';
import { SearchParams } from '@/models/search.model';
import { fetchAnnouncements } from '@/utils/fetch-announcements';
import { Pagination, Text } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';

import { AnnouncementResult } from '@/components/announcement-result';
import { AnnouncementsWrapper } from '@/components/announcements-wrapper';
import { ContentWrapper } from '@/components/content-wrapper';
import { ContentZones } from '@/components/content-zones';
import { GenericErrorDisplay } from '@/components/generic-error-display';
import { PageBreadcrumb } from '@/components/page-breadcrumb';

type AnnouncementSearchResult = {
  title: string;
  url: string;
  subject: string;
  available?: Date;
  fileType?: string;
  fileLink?: string;
};

export const AnnouncementPage = async (_props: {
  page: components['schemas']['AnnouncementPageDto'];
  searchParams: SearchParams;
}) => {
  const { page, searchParams } = _props;
  const { count = '10', start = '1', query = '' } = searchParams;

  const filter = {
    'dt.creator': page.organisationName,
    'w.organisatietype': page.organisationType,
    'c.product-area': page.publicationType,
  };

  const updatedFilter = Object.entries(filter)
    .filter(([_key, value]) => value !== undefined && value !== '-')
    .map(([key, value]) => `${key}=="${value}"`)
    .join(' and ');

  const sort = 'sortBy dt.available /sort.descending';

  const result = (await fetchAnnouncements(start, count, query, updatedFilter, sort))?.searchRetrieveResponse;

  if (!result) {
    return (
      <div className="flex flex-1 flex-col items-center justify-center">
        <GenericErrorDisplay />
      </div>
    );
  }

  const records = Array.isArray(result.records?.record)
    ? result.records.record
    : result.records?.record
    ? [result.records.record]
    : [];

  const mappedRecords = records
    .map<AnnouncementSearchResult | undefined>((record) => {
      if (typeof record.recordData === 'object') {
        return {
          title: record.recordData?.gzd?.originalData?.meta?.owmskern?.title,
          url: record.recordData?.gzd?.originalData?.meta?.owmsmantel?.hasVersion.$?.resourceIdentifier,
          subject: record.recordData?.gzd?.originalData?.meta?.owmsmantel?.subject?._,
          available: record.recordData?.gzd?.originalData?.meta?.owmsmantel?.available
            ? new Date(record.recordData?.gzd?.originalData?.meta?.owmsmantel?.available)
            : undefined,
          fileType: record.recordData?.gzd?.enrichedData?.itemUrl[4]?.$.manifestation,
          fileLink: record.recordData?.gzd?.enrichedData?.itemUrl[4]?._,
        };
      }
    })
    .filter((record): record is AnnouncementSearchResult => record !== undefined);

  const numberOfRecords = Number(result.numberOfRecords);
  const maximumRecords = Number(result.echoedSearchRetrieveRequest?.maximumRecords);
  const startRecord = Number(result.echoedSearchRetrieveRequest?.startRecord);

  return (
    <ContentWrapper>
      <PageBreadcrumb page={page} />
      <div className="py-6">
        <h1 className="font-heading text-heading mb-4 hyphens-auto break-words text-3xl font-bold">{page.label}</h1>
        {page.pageZones.length > 0 && <ContentZones pageZones={page.pageZones} />}
      </div>
      <div className="pb-8">
        <AnnouncementsWrapper>
          <AnnouncementSearchResults
            searchResults={mappedRecords}
            totalCount={numberOfRecords}
            count={maximumRecords}
            start={startRecord}
            diagnostics={result.diagnostics}
          />
        </AnnouncementsWrapper>
      </div>
    </ContentWrapper>
  );
};

const AnnouncementSearchResults: React.FC<{
  searchResults: AnnouncementSearchResult[];
  totalCount: number;
  count: number;
  start: number;
  diagnostics: Diagnostics;
}> = ({ searchResults, totalCount, count, start, diagnostics }) => {
  const t = useTranslations('search');

  if (diagnostics) {
    return <Text className="font-text text-body mt-8 text-center text-sm">{t('diagnosticError')}</Text>;
  }

  if (totalCount === 0) {
    return <Text className="font-text text-body mt-8 text-center text-sm">{t('noResults')}</Text>;
  }

  return (
    <>
      <div className="mt-12 flex flex-col gap-y-16">
        {totalCount && (
          <Text className="font-heading text-base font-bold">
            {totalCount}{' '}
            <Text className="font-heading inline text-base font-normal">{t('results', { count: totalCount })}</Text>
          </Text>
        )}
        {searchResults?.map((item, index) => (
          <AnnouncementResult
            key={index}
            url={item.url}
            title={item.title}
            available={item.available}
            subject={item.subject}
            fileType={item.fileType}
            fileLink={item.fileLink}
          />
        ))}
      </div>
      <div className="mt-10 flex flex-row justify-center">
        <Pagination count={count} start={start} totalCount={totalCount} maxVisiblePages={5} startingIndex={1} />
      </div>
    </>
  );
};

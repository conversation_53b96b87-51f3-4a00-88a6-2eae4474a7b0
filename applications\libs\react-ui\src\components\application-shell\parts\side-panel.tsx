import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

import { closeSidePanel } from '../context/application-shell.actions';
import { useApplicationShellContext } from '../context/application-shell.context';

interface SidePanelProps {
  children: React.ReactNode;
}

export function SidePanel({ children }: SidePanelProps) {
  const { state, dispatch } = useApplicationShellContext();

  return (
    <Transition.Root show={state.isNotificationTrayOpen ?? false} as={Fragment}>
      <Dialog as="div" className="pointer-events-none relative z-10" onClose={() => dispatch(closeSidePanel())}>
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-75"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-75"
          leaveTo="opacity-0"
        >
          <div className="bg-base-35 fixed inset-0 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed bottom-0 right-0 top-16 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                  <div className="bg-base-00 flex h-full flex-col overflow-y-scroll py-6 shadow-xl">
                    <div className="px-4 sm:px-6"></div>
                    <div className="relative flex-1 px-4 sm:px-6">{children}</div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}

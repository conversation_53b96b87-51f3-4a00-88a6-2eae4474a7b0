"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "instrumentation";
exports.ids = ["instrumentation"];
exports.modules = {

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("async_hooks");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "console":
/*!**************************!*\
  !*** external "console" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("console");

/***/ }),

/***/ "constants":
/*!****************************!*\
  !*** external "constants" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("constants");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("perf_hooks");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "timers":
/*!*************************!*\
  !*** external "timers" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("timers");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(instrument)/./instrumentation.ts":
/*!****************************!*\
  !*** ./instrumentation.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   register: () => (/* binding */ register)\n/* harmony export */ });\nasync function register() {\n    if (true) {\n        const applicationinsights = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@opentelemetry\"), __webpack_require__.e(\"vendor-chunks/yallist\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/mime-db\"), __webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/form-data\"), __webpack_require__.e(\"vendor-chunks/asynckit\"), __webpack_require__.e(\"vendor-chunks/combined-stream\"), __webpack_require__.e(\"vendor-chunks/mime-types\"), __webpack_require__.e(\"vendor-chunks/supports-color\"), __webpack_require__.e(\"vendor-chunks/delayed-stream\"), __webpack_require__.e(\"vendor-chunks/has-flag\"), __webpack_require__.e(\"vendor-chunks/applicationinsights\"), __webpack_require__.e(\"vendor-chunks/@azure\"), __webpack_require__.e(\"vendor-chunks/semver\"), __webpack_require__.e(\"vendor-chunks/diagnostic-channel-publishers\"), __webpack_require__.e(\"vendor-chunks/resolve\"), __webpack_require__.e(\"vendor-chunks/async-hook-jl\"), __webpack_require__.e(\"vendor-chunks/https-proxy-agent\"), __webpack_require__.e(\"vendor-chunks/stack-chain\"), __webpack_require__.e(\"vendor-chunks/http-proxy-agent\"), __webpack_require__.e(\"vendor-chunks/cls-hooked\"), __webpack_require__.e(\"vendor-chunks/async-listener\"), __webpack_require__.e(\"vendor-chunks/has\"), __webpack_require__.e(\"vendor-chunks/diagnostic-channel\"), __webpack_require__.e(\"vendor-chunks/require-in-the-middle\"), __webpack_require__.e(\"vendor-chunks/is-core-module\"), __webpack_require__.e(\"vendor-chunks/import-in-the-middle\"), __webpack_require__.e(\"vendor-chunks/shimmer\"), __webpack_require__.e(\"vendor-chunks/path-parse\"), __webpack_require__.e(\"vendor-chunks/module-details-from-path\"), __webpack_require__.e(\"vendor-chunks/emitter-listener\"), __webpack_require__.e(\"vendor-chunks/continuation-local-storage\"), __webpack_require__.e(\"vendor-chunks/@tootallnate\"), __webpack_require__.e(\"vendor-chunks/@microsoft\"), __webpack_require__.e(\"_instrument_node_modules_opentelemetry_instrumentation_build_esm_platform_node_sync_recursive-39498a\")]).then(__webpack_require__.bind(__webpack_require__, /*! applicationinsights */ \"(instrument)/../../node_modules/applicationinsights/out/applicationinsights.js\"));\n        applicationinsights.setup(process.env[\"APPLICATIONINSIGHTS_CONNECTION_STRING\"]).setAutoCollectConsole(true, true).setAutoDependencyCorrelation(true).setAutoCollectRequests(true).setAutoCollectPerformance(true, true).setAutoCollectExceptions(true).setAutoCollectDependencies(true).setUseDiskRetryCaching(true).setAutoCollectPreAggregatedMetrics(true).setSendLiveMetrics(true).setAutoCollectHeartbeat(true).setAutoCollectIncomingRequestAzureFunctions(true).setInternalLogging(false, true).setDistributedTracingMode(applicationinsights.DistributedTracingModes.AI_AND_W3C).enableWebInstrumentation(true).start();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/./instrumentation.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("./webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(instrument)/./instrumentation.ts"));
module.exports = __webpack_exports__;

})();
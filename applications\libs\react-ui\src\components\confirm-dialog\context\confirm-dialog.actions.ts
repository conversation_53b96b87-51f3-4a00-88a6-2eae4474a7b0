import { StateAction } from '../../../models/context.model';
import { ConfirmDialogState } from './confirm-dialog.context';

export type ConfirmDialogActionTypes = 'TOGGLE_CONFIRM_DIALOG' | 'CLOSE_CONFIRM_DIALOG';

type ToggleConfirmDialog = StateAction<'TOGGLE_CONFIRM_DIALOG', ConfirmDialogState>;
type CloseConfirmDialog = StateAction<'CLOSE_CONFIRM_DIALOG', ConfirmDialogState>;

export const toggleConfirmDialog = (payload: ConfirmDialogState): ToggleConfirmDialog => ({
  type: 'TOGGLE_CONFIRM_DIALOG',
  reducer: (state) => ({ ...payload, isOpen: !state.isOpen }),
});

export const closeConfirmDialog = (): CloseConfirmDialog => ({
  type: 'CLOSE_CONFIRM_DIALOG',
  reducer: (state) => ({ ...state, isOpen: false }),
});

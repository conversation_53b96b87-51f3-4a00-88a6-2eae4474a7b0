import { DossierUploadProvider } from '@/context/dossier-upload-context';
import { NotificationsProvider } from '@/context/notifications-context';
import { PortalConfigurationProvider } from '@/context/portal-configuration.context';
import { getSiteParameters } from '@/services/public-service.server';
import { ConfirmDialogProvider, Toast } from '@iprox/iprox-ui';
import { AppSettingsProvider } from '@iprox/shared-context';
import { Metadata } from 'next';
import { NextIntlClientProvider, useLocale } from 'next-intl';

import { timeZone } from '../../i18n';

export const metadata: Metadata = {
  title: 'iprox.open',
};

export default async function LocaleLayout({ children }: { children: React.ReactNode }) {
  const locale = useLocale();

  const siteParameters = await getSiteParameters();

  const messages =
    locale === 'en'
      ? {
          ...(await import('@iprox/react-ui-i18n/en.json')).default,
          ...(await import('../../i18n/en.json')).default,
        }
      : {
          ...(await import('@iprox/react-ui-i18n/nl.json')).default,
          ...(await import('../../i18n/nl.json')).default,
        };

  return (
    <html lang={locale}>
      <body className="flex min-h-screen">
        <AppSettingsProvider
          settings={{
            apiUrl: process.env.IPROX_OPEN_API_URL || '',
            portalUrl: process.env.IPROX_OPEN_PORTAL_URL || '',
          }}
        >
          <NextIntlClientProvider locale={locale} messages={messages} timeZone={timeZone}>
            <DossierUploadProvider>
              <NotificationsProvider>
                <ConfirmDialogProvider>
                  <PortalConfigurationProvider
                    initialPortalConfiguration={{
                      cssVariables: siteParameters?.siteParametersSettings.cssVariables ?? [],
                    }}
                  >
                    {children}
                  </PortalConfigurationProvider>
                </ConfirmDialogProvider>
              </NotificationsProvider>
            </DossierUploadProvider>
            <Toast />
          </NextIntlClientProvider>
        </AppSettingsProvider>
      </body>
    </html>
  );
}

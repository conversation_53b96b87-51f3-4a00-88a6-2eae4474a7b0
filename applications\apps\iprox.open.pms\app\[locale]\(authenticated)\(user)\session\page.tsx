import { authOptions } from '@/auth/auth';
import { getServerSession } from 'next-auth/next';
import { notFound } from 'next/navigation';

export default async function Page() {
  if (process.env.DEVELOPMENT !== 'true') {
    notFound();
  }

  const session = await getServerSession(authOptions);

  return (
    <>
      <div>
        <span>Session</span>
      </div>
      <div>
        <pre>{JSON.stringify(session, null, 2)}</pre>
      </div>
      <div>
        <span>Settings</span>
      </div>
      <div>
        <pre>
          {JSON.stringify(
            {
              IPROX_OPEN_API_URL: process.env.IPROX_OPEN_API_URL,
            },
            null,
            2
          )}
        </pre>
      </div>
    </>
  );
}

{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "types": ["jest", "node"], "paths": {"@iprox-open/shared": ["libs/shared/src/index.ts"], "@iprox-open/shared/server": ["libs/shared/src/server.ts"], "@iprox/react-ui": ["libs/react-ui/src/index.ts"], "@iprox/react-ui/server": ["libs/react-ui/src/server.ts"], "@iprox/react-ui-i18n/*": ["libs/react-ui-i18n/src/i18n/*"], "@iprox/shared-context": ["libs/shared-context/src/index.tsx"], "@/iprox-open.interface": ["libs/iprox-open-api/generated/iprox-open.interface.ts"], "@/components/*": ["apps/iprox.open.portal/app/_components/*"], "@/services/*": ["apps/iprox.open.portal/app/_services/*"], "@/utils/*": ["apps/iprox.open.portal/app/_utils/*"], "@/models/*": ["apps/iprox.open.portal/app/_models/*"], "@/http/*": ["apps/iprox.open.portal/app/_http/*"]}, "plugins": [{"name": "next"}]}, "files": ["i18n.prod.d.ts"], "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "next-env.d.ts", "../../apps/iprox.open.portal/.next/types/**/*.ts", "../../dist/apps/iprox.open.portal/.next/types/**/*.ts"], "exclude": ["node_modules", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}
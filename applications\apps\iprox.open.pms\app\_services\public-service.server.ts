import { serverApi } from '@/http/fetcher-api.server';
import { fetcher } from '@/http/fetcher-public';
import { components } from '@/iprox-open.interface';

const apiUrl = process.env.IPROX_OPEN_API_URL;

export async function getPublicAssets(): Promise<components['schemas']['GetSiteAssetsResponse']> {
  return await fetcher
    .get(`${apiUrl}/public/assets`, {
      cache: 'no-store',
    })
    .json<components['schemas']['GetSiteAssetsResponse']>();
}

export async function getHomepageContent(): Promise<components['schemas']['PublicHomePageResponse']> {
  return await serverApi.get('public/homepage').json<components['schemas']['PublicHomePageResponse']>();
}

export async function getSiteParameters(): Promise<
  components['schemas']['GetSiteParametersSettingsResponse'] | undefined
> {
  return await fetcher
    .get(`${apiUrl}/public/site-parameters`, {
      cache: 'no-cache',
    })
    .json<components['schemas']['GetSiteParametersSettingsResponse']>();
}

export async function getFooterComponentContent(): Promise<components['schemas']['PublicFooterComponentResponse']> {
  return await serverApi.get('public/component/footer').json<components['schemas']['PublicFooterComponentResponse']>();
}

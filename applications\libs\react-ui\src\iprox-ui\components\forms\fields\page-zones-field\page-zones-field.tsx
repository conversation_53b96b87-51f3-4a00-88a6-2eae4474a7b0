import { PlusIcon } from '@heroicons/react/24/outline';
import { FieldArray, FieldArrayRenderProps } from 'formik';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { v4 as uuid } from 'uuid';

import { AddZoneModal, ZoneLayout } from '../../../add-page-zone-modal/add-zone-modal';
import { Button } from '../../../button/button';
import { useConfirmDialog } from '../../../confirm-dialog/context/confirm-dialog.context';
import { Text } from '../../../text/text';
import { FieldType, PageZone, PageZonesFieldDefinition, ZoneBlock } from '../../models/form.models';
import { RichTextField } from '../richtext-field/richtext-field';

export function PageZonesField(props: PageZonesFieldDefinition) {
  const t = useTranslations('components.pageZoneField');
  const [openModal, setOpenModal] = useState(false);
  const { showDialog } = useConfirmDialog();

  const handleAddZone = () => {
    setOpenModal(!openModal);
  };

  const createZone = (selectedLayout: ZoneLayout, nextIndex: number): PageZone => {
    const blocks: ZoneBlock[] = [];

    selectedLayout.split('-').forEach((colspan, index) => {
      blocks.push({
        id: uuid(),
        order: index,
        colspan: parseInt(colspan),
        blockType: 'RichText',
        blockContent: {
          content: '',
        },
      });
    });

    return {
      id: uuid(),
      order: nextIndex,
      blocks,
    };
  };

  const handleDeleteZone = (onConfirm: () => void) => {
    showDialog({
      message: t('deleteZoneMessage'),
      onConfirm,
    });
  };

  return (
    <FieldArray name={props.name}>
      {({ push, remove, form }: FieldArrayRenderProps) => (
        <>
          <label
            htmlFor={props.id ?? uuid()}
            className="font-heading text-heading mb-5 inline-block text-lg font-bold leading-none"
          >
            {props.label}
          </label>
          {Array.isArray(form.values[props.name]) &&
            form.values[props.name].map((field: PageZone, zoneIndex: number) => (
              <div
                key={field.id}
                className={`mb-20 ${
                  zoneIndex === form.values[props.name].length - 1 ? '' : 'border-base-25 border-b pb-20'
                }`}
                aria-label={t('pageZone')}
                role="group"
              >
                <FieldArray name={`${props.name}[${zoneIndex}].blocks`}>
                  {({ form }: FieldArrayRenderProps) => {
                    const blocks: ZoneBlock[] = form.values[props.name][zoneIndex]?.blocks || {};

                    return (
                      <div className="grid grid-cols-12 gap-x-5">
                        {blocks.map((block, blockIndex) => {
                          return (
                            <div
                              key={blockIndex}
                              aria-label={t('richTextField')}
                              role="group"
                              style={{ gridColumn: `span ${block.colspan} / span ${block.colspan}` }}
                            >
                              <RichTextField
                                label={'blockContent'}
                                aria-labelledby={'rich-text-field-block'}
                                fieldType={FieldType.RichText}
                                validationRules={[]}
                                value={block.blockContent?.content ?? ''}
                                name={`${props.name}[${zoneIndex}].blocks[${blockIndex}].blockContent.content`}
                                hideLabel
                                contentEditorClass="h-[320px]"
                                enableSuperlink={props.enableSuperlink}
                              />
                            </div>
                          );
                        })}
                      </div>
                    );
                  }}
                </FieldArray>
                <Button
                  variant="secondary"
                  type="button"
                  className="mt-5"
                  onClick={() => handleDeleteZone(() => remove(zoneIndex))}
                >
                  {t('deleteZone')}
                </Button>
              </div>
            ))}
          <button
            type="button"
            className="border-highlight rounded-input text-base-100 bg-base-10 items flex h-48 w-full flex-col items-center justify-center border border-dashed"
            onClick={handleAddZone}
          >
            <PlusIcon className="h-8 w-8" />
            <Text className="font-text text-xs font-medium">{t('addZone')}</Text>
          </button>
          <AddZoneModal
            isOpen={openModal}
            onClose={() => setOpenModal(false)}
            onCreateZone={(selectedLayout: ZoneLayout) => {
              setOpenModal(false);
              const zone = createZone(selectedLayout, form.values[props.name]?.length);
              push(zone);
            }}
          />
        </>
      )}
    </FieldArray>
  );
}

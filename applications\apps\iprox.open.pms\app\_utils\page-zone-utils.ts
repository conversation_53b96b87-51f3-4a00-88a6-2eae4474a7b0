import { PageZone, ZoneBlock } from '@iprox/iprox-ui';

export const getSortedPageZones = (zones: PageZone[]): PageZone[] => {
  const sortZoneBlocks = (a: ZoneBlock, b: ZoneBlock) => a.order - b.order;

  const sortPageZones = (a: PageZone, b: PageZone) => a.order - b.order;

  const sortedZones = zones.map((zone) => ({
    ...zone,
    blocks: zone.blocks.sort(sortZoneBlocks),
  }));

  return sortedZones.sort(sortPageZones);
};

export const reorderZones = (zones: PageZone[]) => {
  const sortedArray = zones.sort((a, b) => a.order - b.order);

  if (sortedArray.every((zone, index) => zone.order === index)) {
    return zones;
  }

  const updatedArray = sortedArray.map((zone, index) => {
    return { ...zone, order: index };
  });

  return updatedArray;
};

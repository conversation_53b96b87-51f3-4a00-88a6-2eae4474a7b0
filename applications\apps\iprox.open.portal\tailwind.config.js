/* eslint-disable @typescript-eslint/no-var-requires */
const { createGlobPatternsForDependencies } = require('@nx/react/tailwind');
const { join } = require('path');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    join(__dirname, '{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}'),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  theme: {
    colors: {
      transparent: 'transparent',
      current: 'currentColor',

      'base-100': 'var(--sys_color_base_100)',
      'base-85': 'var(--sys_color_base_85)',
      'base-75': 'var(--sys_color_base_75)',
      'base-35': 'var(--sys_color_base_35)',
      'base-25': 'var(--sys_color_base_25)',
      'base-15': 'var(--sys_color_base_15)',
      'base-10': 'var(--sys_color_base_10)',
      'base-00': 'var(--sys_color_base_00)',

      primary: 'var(--sys_color_primary)',
      'primary-content': 'var(--sys_color_primary_content)',
      'primary-hover': 'var(--sys_color_primary_hover)',
      'primary-hover-content': 'var(--sys_color_primary_hover_content)',

      secondary: 'var(--sys_color_secondary)',
      'secondary-content': 'var(--sys_color_secondary_content)',
      'secondary-hover': 'var(--sys_color_secondary_hover)',
      'secondary-hover-content': 'var(--sys_color_secondary_hover_content)',

      tertiary: 'var(--sys_color_tertiary)',
      'tertiary-content': 'var(--sys_color_tertiary_content)',
      'tertiary-hover': 'var(--sys_color_tertiary_hover)',
      'tertiary-hover-content': 'var(--sys_color_tertiary_hover_content)',

      highlight: 'var(--sys_color_highlight)',
      'text-highlight': 'var(--sys_color_text_highlight)',

      accent: 'var(--sys_color_accent)',
      'accent-medium': 'var(--sys_color_accent_medium)',
      'accent-light': 'var(--sys_color_accent_light)',
      'accent-lighter': 'var(--sys_color_accent_lighter)',

      'light-grey': 'var(--sys_color_light_grey)',

      error: 'var(--sys_color_error)',

      heading: 'var(--sys_color_heading_text)',
      body: 'var(--sys_color_body_text)',
      anchor: 'var(--sys_color_anchor_text)',
      'content-lite': 'var(--sys_color_content_lite)',
      'content-extra-lite': 'var(--sys_color_content_extra_lite)',

      'navigation-background': 'var(--sys_color_navigation_background)',
      'navigation-text': 'var(--sys_color_navigation_text)',

      'expanded-background': 'var(--sys_color_file_structure_expanded)',
      'footer-background': 'var(--sys_color_footer_background)',

      'file-tag-background': 'var(--sys_color_primary)',

      'superlink-background': 'var(--sys_color_superlink_background)',
      'superlink-text': 'var(--sys_color_superlink_text)',
    },
    fontFamily: {
      heading: 'var(--sys_font_family_heading)',
      text: 'var(--sys_font_family_text)',
    },
    extend: {
      boxShadow: {
        'input-hover': '0 0 0 2px rgba(var(--sys_color_secondary) / 0.3)',
        'input-error': '0 0 0 2px rgba(var(--sys_color_error) / 0.3)',
        navbar: '0 3px 7px 0 rgba(0 0 0 / 0.3)',
        'navbar-dropdown': '0 6px 6px 0 rgba(0 0 0 / 0.3)',
        'navbar-side-panel': '4px 4px 5px 0px rgba(0, 0, 0, 0.3)',
      },
      fontSize: {
        h1: '2rem',
        h2: '1.75rem',
        h3: '1.5rem',
        h4: '1.375rem',
        h5: '1.25rem',
        h6: '1rem',
        base: '1rem',
      },
      transitionDuration: {
        standard: '260ms',
      },
      transitionTimingFunction: {
        iprox: 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      borderRadius: {
        input: 'var(--sys_border_radius_input)',
        medium: 'var(--sys_border_radius_medium)',
      },
      height: {
        input: 'var(--sys_height_input)',
        navbar: '105px',
        'navbar-sm': '75px',
      },
      minHeight: {
        navbar: '105px',
      },
      margin: {
        navbar: '75px',
      },
      maxWidth: {
        logo: '220px',
        'logo-sm': '140px',
        content: '1280px',
      },
      width: {
        logo: '220px',
      },
      gridTemplateColumns: {
        'file-structure-sm': 'minmax(0, 25px) 1fr',
        'file-structure': 'minmax(0, 25px) 3fr minmax(0, 15%)',
      },
    },
  },
  plugins: [require('@tailwindcss/forms')],
};

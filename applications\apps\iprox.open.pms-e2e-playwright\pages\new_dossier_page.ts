import { type Locator, type Page, expect } from '@playwright/test';

export class CreateNewDossierPage {
  readonly page: Page;
  readonly categoryDropdownOption: Locator;
  readonly categoryDropdownOptions: Locator;
  readonly pageUrl: RegExp;
  readonly categoryWithPublishDates = 'Onteigeningen'; // with the publish 'From' and 'To' dates
  readonly categoryWithoutPublishDates = 'Beschikkingen'; // without the publish 'From' and 'To' dates
  titleInputField: Locator;
  categoryDropdown: Locator;
  addNewDossierButton: Locator;
  titleValidationMessage: Locator;
  categoryValidationMessage: Locator;

  constructor(page: Page) {
    this.page = page;
  }

  async inputTitle(titleText: string) {
    this.titleInputField = this.page.getByRole('textbox', { name: 'Titel' });
    await this.titleInputField.fill(titleText);
  }

  async selectCategory(category: string) {
    this.categoryDropdown = this.page.locator('.react-select-container');
    await this.categoryDropdown.click();
    await this.page.getByText(`${category}`, { exact: true }).click();
  }

  async clickAddNewDossier() {
    this.addNewDossierButton = this.page.getByRole('button', { name: 'Toevoegen' });
    await this.addNewDossierButton.click();
  }

  async createNewDossier(titleText: string, dossierWithPublishFromToDates: boolean) {
    // create a dossier WITH Publish From and To dates
    if (dossierWithPublishFromToDates) {
      await this.inputTitle(titleText);
      await this.selectCategory(this.categoryWithPublishDates);
      this.addNewDossierButton = this.page.getByRole('button', { name: 'Toevoegen' });
      await this.addNewDossierButton.click();
    }
    // create a dossier WITHOUT Publish From and To dates
    else {
      await this.inputTitle(titleText);
      await this.selectCategory(this.categoryWithoutPublishDates);
      this.addNewDossierButton = this.page.getByRole('button', { name: 'Toevoegen' });
      await this.addNewDossierButton.click();
    }
  }

  async assertPageUrl() {
    await expect(this.page).toHaveURL(this.pageUrl);
  }
}

export default CreateNewDossierPage;

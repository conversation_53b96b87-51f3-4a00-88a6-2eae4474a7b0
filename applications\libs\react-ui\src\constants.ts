import { ThemeParameter } from './models/theme-parameter.model';

export const DEFAULT_THEME: ThemeParameter[] = [
  {
    key: 'color_primary',
    value: '#1A327B',
    type: 'color',
  },
  {
    key: 'color_primary_content',
    value: '#ffffff',
    type: 'color',
  },
  {
    key: 'color_primary_hover',
    value: '#00804d',
    type: 'color',
  },
  {
    key: 'color_primary_hover_content',
    value: '#ffffff',
    type: 'color',
  },
  // {
  //   key: 'color_button_primary_border',
  //   value: '#ffffff',
  //   type: 'color',
  // },

  {
    key: 'color_secondary',
    value: 'rgb(0, 0, 51)',
    type: 'color',
  },
  {
    key: 'color_secondary_content',
    value: 'rgb(255, 255, 255)',
    type: 'color',
  },
  {
    key: 'color_secondary_hover',
    value: '#00804d',
    type: 'color',
  },
  {
    key: 'color_secondary_hover_content',
    value: '#ffffff',
    type: 'color',
  },

  {
    key: 'color_tertiary',
    value: 'rgb(244, 245, 246)',
    type: 'color',
  },
  {
    key: 'color_tertiary_content',
    value: 'rgb(255, 255, 255)',
    type: 'color',
  },
  {
    key: 'color_tertiary_hover',
    value: '#00804d',
    type: 'color',
  },
  {
    key: 'color_tertiary_hover_content',
    value: '#ffffff',
    type: 'color',
  },

  {
    key: 'color_content_lite',
    value: 'rgb(102, 102, 133)',
    type: 'color',
  },
  {
    key: 'color_content_extra_lite',
    value: 'rgb(153, 153, 173)',
    type: 'color',
  },
  {
    key: 'color_highlight',
    value: 'rgb(51, 204, 102)',
    type: 'color',
  },
  {
    key: 'color_text_highlight',
    value: 'rgb(173, 235, 194)',
    type: 'color',
  },
  {
    key: 'color_accent',
    value: 'rgb(51, 153, 204)',
    type: 'color',
  },
  {
    key: 'color_accent_medium',
    value: 'rgb(92, 173, 214)',
    type: 'color',
  },
  {
    key: 'color_accent_light',
    value: 'rgb(133, 194, 224)',
    type: 'color',
  },
  {
    key: 'color_accent_lighter',
    value: 'rgb(173, 214, 235)',
    type: 'color',
  },
  {
    key: 'color_base_100',
    value: 'rgb(0, 0, 0)',
    type: 'color',
  },
  {
    key: 'color_base_85',
    value: 'rgb(74, 85, 104)',
    type: 'color',
  },
  {
    key: 'color_base_75',
    value: 'rgb(132, 132, 132)',
    type: 'color',
  },
  {
    key: 'color_base_35',
    value: 'rgb(103, 103, 103)',
    type: 'color',
  },
  {
    key: 'color_base_25',
    value: 'rgb(190, 190, 190)',
    type: 'color',
  },
  {
    key: 'color_base_15',
    value: 'rgb(228, 228, 228)',
    type: 'color',
  },
  {
    key: 'color_base_10',
    value: 'rgb(235, 235, 235)',
    type: 'color',
  },
  {
    key: 'color_base_00',
    value: 'rgb(255, 255, 255)',
    type: 'color',
  },
  {
    key: 'color_error',
    value: 'rgb(222, 10, 10)',
    type: 'color',
  },
  {
    key: 'color_heading_text',
    value: 'rgb(0, 54, 95)',
    type: 'color',
  },
  {
    key: 'color_body_text',
    value: 'rgb(57, 59, 66)',
    type: 'color',
  },
  {
    key: 'color_navigation_background',
    value: 'rgb(255, 255, 255)',
    type: 'color',
  },
  {
    key: 'color_navigation_text',
    value: 'rgb(0, 54, 95)',
    type: 'color',
  },
  {
    key: 'color_anchor_text',
    value: 'rgb(0, 54, 95)',
    type: 'color',
  },
  {
    key: 'color_superlink_background',
    value: 'rgb(233, 233, 245)',
    type: 'color',
  },
  {
    key: 'color_superlink_text',
    value: 'rgb(244, 245, 246)',
    type: 'color',
  },
  {
    key: 'color_footer_background',
    value: 'rgb(233, 233, 245)',
    type: 'color',
  },
  {
    key: 'color_file_structure_expanded',
    value: 'rgb(244, 245, 246)',
    type: 'color',
  },
  {
    key: 'border_radius_input',
    value: '10px',
    type: 'number',
  },
  {
    key: 'border_radius_medium',
    value: '10px',
    type: 'number',
  },
  {
    key: 'height_input',
    value: '45px',
    type: 'number',
  },
];

import { Page, chromium } from 'playwright';

import { baseUrl, colors, fullLoginUrl } from '../config';
import { password, username } from '../user';

export async function loginAndGetToken(): Promise<any | null> {
  const browser = await chromium.launch();
  const page: Page = await browser.newPage();
  let token: string | null = null;
  let jsonResponse: any = null;

  // Perform login steps
  await page.goto(fullLoginUrl, { waitUntil: 'load' });
  console.log('Navigated to:', fullLoginUrl);
  await page.waitForLoadState('domcontentloaded');
  await page.waitForTimeout(3000);
  // await page.getByRole('button', { name: 'Inloggen met Azure' }).click({ timeout: 5000 }); // For dev env
  await page.getByRole('button', { name: 'Sign in with Azure Active Directory' }).nth(1).click({ timeout: 10000 }); // For api auth login
  console.log('Clicked on Azure login button');

  await page.waitForLoadState('domcontentloaded');
  // await page.getByRole('textbox', { name: '<EMAIL>' }).fill(username); // For dev env
  await page.getByRole('textbox', { name: 'Enter your email, phone, or Skype.' }).fill(username); // For PR env api auth login
  await page.waitForLoadState('domcontentloaded');
  console.log('Filled in username:', username);

  await page.getByRole('button', { name: 'Next' }).click();
  console.log('Clicked on Next button');

  await page.waitForLoadState('domcontentloaded');
  await page.getByPlaceholder('Password').fill(password);
  console.log('Filled in password');

  await page.waitForLoadState('domcontentloaded');
  await page.getByRole('button', { name: 'Sign in' }).click();
  console.log('Clicked on Sign in button');

  // For dev env
  // await page.waitForLoadState('domcontentloaded');
  // await page.waitForTimeout(1000 * 60); // Timeout to authenicate via authenticator app
  // await expect(page.getByRole('button', { name: 'Yes' })).toBeVisible();
  // await page.getByRole('button', { name: 'Yes' }).click();

  // For api auth login
  await page.waitForLoadState('domcontentloaded');
  await page.getByRole('button', { name: 'Yes' }).click({ timeout: 5000 });
  console.log('Clicked on Yes button');
  await page.waitForLoadState('networkidle');

  console.log('Access Token evaluating...');
  await page.goto(baseUrl + '/dashboard', { waitUntil: 'load' });
  page.on('response', async (response) => {
    // Check if the response is the one returning the token
    if (response.url().includes('/api/auth/session')) {
      jsonResponse = await response.json();
      token = await jsonResponse.user.access_token;
    }
  });
  await page.waitForLoadState('networkidle');
  await browser.close();
  return token;
}

import { expect, test } from '@playwright/test';

import { loadStory } from '../utils/common-utils';

const snapShots = [
  {
    name: 'button',
    id: 'components-button--default',
  },
  {
    name: 'pagination',
    id: 'components-pagination--default',
  },
  {
    name: 'modal',
    id: 'components-modal--default',
  },
  {
    name: 'statistics-card',
    id: 'components-statistics-card--default',
  },
  {
    name: 'date-range-picker-field',
    id: 'iprox-ui-forms-fields-date-range-picker-field--default',
  },
  {
    name: 'file-structure',
    id: 'components-file-structure--default',
  },
  {
    name: 'color-picker-field',
    id: 'iprox-ui-forms-fields-color-picker-field--default',
  },
];

test('Compare snapshots', async ({ page }) => {
  for (const snapshot of snapShots) {
    await loadStory(page, snapshot.id);

    await expect(page).toHaveScreenshot([`${snapshot.name}.png`]);
  }
});

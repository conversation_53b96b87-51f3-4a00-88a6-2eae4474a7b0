import { AppSettingsContext } from '@iprox/shared-context';
import { useTranslations } from 'next-intl';
import { FC, useContext, useMemo, useState } from 'react';

import { Button } from '../../../../button/button';
import { Modal } from '../../../../modal/modal';
import { FormBuilder } from '../../../form-builder/form-builder';
import { FieldDefinition, FieldType, FormSubmitValues, ValueTypes } from '../../../models/form.models';
import { ValidationRuleType } from '../../../models/validator.models';
import { sanitizeUrl } from '../utils/url';

export interface LinkProps {
  url: string;
  title: string;
  newTab: boolean;
}

interface LinkEditorProps {
  defaultUrl?: string;
  defaultTitle?: string;
  defaultNewTab?: boolean;
  isEdit: boolean;

  isOpen: boolean;
  onClose: () => void;
  onSubmit: (props: LinkProps) => void;
  onRemoveLink: () => void;
}

const LinkEditor: FC<LinkEditorProps> = ({
  isOpen,
  onClose,
  onSubmit,
  onRemoveLink,
  defaultTitle,
  defaultUrl,
  defaultNewTab,
  isEdit,
}) => {
  const t = useTranslations('components.richtexteditor.urlEditor');
  const [errorMessage, setErrorMesssage] = useState('');

  const context = useContext(AppSettingsContext);

  const urlForm = useMemo<FieldDefinition<FieldType, ValueTypes>[]>(() => {
    return [
      {
        name: 'urlInput',
        label: t('enterUrl'),
        fieldType: FieldType.Text,
        value: defaultUrl,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
        ],
      },
      {
        name: 'title',
        label: t('title'),
        fieldType: FieldType.Text,
        value: defaultTitle,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
        ],
      },
      {
        name: 'newTab',
        label: t('openlinkInNewTab'),
        fieldType: FieldType.CheckBox,
        value: defaultNewTab,
        validationRules: [],
      },
    ];
  }, [t, defaultUrl, defaultTitle, defaultNewTab]);

  if (!isOpen) {
    return null;
  }

  const handleSubmit = (values: FormSubmitValues) => {
    setErrorMesssage('');
    const urlInput = values.urlInput as string;
    const title = values.title as string;
    const newTab = values.newTab as boolean;

    if (!sanitizeUrl(urlInput, context.portalUrl)) {
      setErrorMesssage(t('invalidUrl'));
      return;
    }

    onSubmit({
      url: urlInput,
      title,
      newTab,
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="w-[70vw] max-w-[500px]">
        <FormBuilder
          fields={urlForm}
          buttonText={defaultTitle && defaultUrl ? t('change') : t('insert')}
          onSubmit={handleSubmit}
          errorMessage={errorMessage}
          buttons={
            <div className="flex flex-row justify-end gap-4">
              {isEdit && (
                <Button type="button" variant="tertiary" className="inline" onClick={onRemoveLink}>
                  {t('removeLink')}
                </Button>
              )}

              <Button type="submit" variant="primary" className="inline">
                {isEdit ? t('change') : t('insert')}
              </Button>
            </div>
          }
        />
      </div>
    </Modal>
  );
};

export default LinkEditor;

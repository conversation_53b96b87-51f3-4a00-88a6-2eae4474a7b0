type FileTypeLabelProps = {
  label: string;
  className?: string;
};

export function FileTypeLabel({ label, className }: FileTypeLabelProps) {
  return (
    <span
      className={`bg-file-tag-background font-heading text-base-00 inline-flex h-6 max-w-[55px] items-center justify-center rounded-[5px] px-4 text-center align-middle text-[10px] font-bold leading-4 ${className}`}
    >
      {label}
    </span>
  );
}

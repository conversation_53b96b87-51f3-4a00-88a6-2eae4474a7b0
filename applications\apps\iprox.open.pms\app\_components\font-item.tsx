'use client';

import { components } from '@/iprox-open.interface';
import { DocumentIcon, DocumentPlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Button, Text } from '@iprox/iprox-ui';
import { FontCategory, FontStyle, FontWeight } from '@iprox/react-ui';
import { useTranslations } from 'use-intl';

interface FontItemProps {
  font: components['schemas']['FontAssetDto'];
  onClickUpdate: (fontAssetId: string) => void;
  onClickDelete: (fontAssetId: string) => void;
  onClickaddFile: (fontAssetId: string) => void;
  onClickDeleteFile: (fontAssetId: string, fontFileId: string) => void;
}

export function FontItem({ font, onClickUpdate, onClickDelete, onClickaddFile, onClickDeleteFile }: FontItemProps) {
  const t = useTranslations('fonts');

  return (
    <div key={font.id} className="border-base-25 mb-5 flex justify-between rounded-lg border p-4 shadow-md">
      <div>
        <Text className="font-heading text-heading text-xl font-bold">{font.fontFamily}</Text>
        <div className="grid grid-flow-col items-center justify-start gap-2">
          <Text className="text-body border-base-25 border-r pr-4 text-sm">
            {t(font.fontWeight.toString().toLowerCase() as FontWeight)}
          </Text>
          <Text className="text-body border-base-25 border-r pr-4 text-sm">
            {t('style', { value: t(font.fontStyle.toString().toLowerCase() as FontStyle) })}
          </Text>
          <Text className="text-body border-base-25 border-r pr-4 text-sm">
            {t('category', { value: t(font.fontCategory.toString().toLowerCase() as FontCategory) })}
          </Text>
          <button
            type="button"
            className="text-primary m-0 flex items-center outline-none hover:underline"
            onClick={() => onClickaddFile(font.id)}
          >
            <DocumentPlusIcon className="h-5 w-5" />
            <Text className="text-sm">{t('addFontFiles')}</Text>
          </button>
        </div>
        <div className="mt-4 w-2/3">
          {font.fontFiles.map(({ id: fileId, name }) => (
            <div key={fileId} className="hover:bg-base-10 flex justify-between p-2">
              <div className="grid grid-flow-col justify-start gap-2">
                <DocumentIcon className="h-5 w-5" />
                <Text className="text-body text-sm">{name}</Text>
              </div>
              <button
                type="button"
                aria-label={t('deleteFontFile', { fontFileName: name })}
                className="hover:bg-base-25 m-0 flex h-6 w-6 items-center justify-center rounded-full border-none outline-none"
                onClick={() => onClickDeleteFile(font.id, fileId)}
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
      <div className="grid grid-flow-col items-center justify-end gap-2">
        <Button type="button" variant="secondary" onClick={() => onClickUpdate(font.id)}>
          {t('updateFont')}
        </Button>
        <Button type="button" variant="secondary" onClick={() => onClickDelete(font.id)}>
          {t('delete')}
        </Button>
      </div>
    </div>
  );
}

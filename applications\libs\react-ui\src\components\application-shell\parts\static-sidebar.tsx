interface StaticSidebarProps {
  children: React.ReactNode;
}

export function StaticSidebar(props: StaticSidebarProps) {
  return (
    <div className="hidden lg:fixed lg:bottom-0 lg:top-16 lg:z-50 lg:flex lg:w-72 lg:flex-col">
      <div
        className="flex grow flex-col gap-y-5 overflow-y-auto px-6 py-4"
        style={{
          backgroundColor: '#DBDBDB', //TODO: use theme color
        }}
      >
        {props.children}
      </div>
    </div>
  );
}

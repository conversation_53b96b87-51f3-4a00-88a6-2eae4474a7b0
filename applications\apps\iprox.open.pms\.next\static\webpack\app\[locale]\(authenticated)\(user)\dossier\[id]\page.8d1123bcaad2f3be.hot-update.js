"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(authenticated)/(user)/dossier/[id]/page",{

/***/ "(app-pages-browser)/./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx":
/*!***************************************************************************!*\
  !*** ./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx ***!
  \***************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/http/fetcher-api.client */ \"(app-pages-browser)/./app/_http/fetcher-api.client.ts\");\n/* harmony import */ var _services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/dossier-service.client */ \"(app-pages-browser)/./app/_services/dossier-service.client.ts\");\n/* harmony import */ var _utils_dossier_form_changed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/dossier.form-changed */ \"(app-pages-browser)/./app/_utils/dossier.form-changed.ts\");\n/* harmony import */ var _utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dossier.form-definition.hook */ \"(app-pages-browser)/./app/_utils/dossier.form-definition.hook.ts\");\n/* harmony import */ var _utils_error_handler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/error-handler */ \"(app-pages-browser)/./app/_utils/error-handler.ts\");\n/* harmony import */ var _iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @iprox/iprox-ui */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/index.ts\");\n/* harmony import */ var _iprox_react_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @iprox/react-ui */ \"(app-pages-browser)/../../libs/react-ui/src/index.ts\");\n/* harmony import */ var _iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @iprox/shared-context */ \"(app-pages-browser)/../../libs/shared-context/src/index.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _components_dossier_file_manager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dossier-file-manager */ \"(app-pages-browser)/./app/_components/dossier-file-manager.tsx\");\n/* harmony import */ var _components_dossier_image_field__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dossier-image-field */ \"(app-pages-browser)/./app/_components/dossier-image-field.tsx\");\n/* harmony import */ var _components_dossier_status_controls__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dossier-status-controls */ \"(app-pages-browser)/./app/_components/dossier-status-controls.tsx\");\n/* harmony import */ var _components_dossier_title__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dossier-title */ \"(app-pages-browser)/./app/_components/dossier-title.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PageContent(param) {\n    let { dossier: initialDossier, pages } = param;\n    var _dossier_category, _dossier_decorativeImageNode;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations)(\"dossier\");\n    const settings = (0,_iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__.useAppSettings)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const clientApi = (0,_http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__.useClientApi)();\n    const formId = \"dossier-creation-form\";\n    const { showDialog } = (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.useConfirmDialog)();\n    const [dossier, setDossier] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(initialDossier);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(initialDossier.status);\n    const [isSaveAndPublish, setIsSaveAndPublish] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [lastSavedValues, setLastSavedValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [formValues, setFormValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [dateFormValues, setDateFormValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(dossier.publishDates);\n    const [isNew, setIsNew] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(dossier.version === 1);\n    const formDefinition = (0,_utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__.useDossierFormDefinition)(dossier);\n    const editStatus = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        if (status === \"Published\") {\n            return \"published\";\n        }\n        return isNew ? \"new\" : \"modified\";\n    }, [\n        status,\n        isNew\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if ((0,_utils_dossier_form_changed__WEBPACK_IMPORTED_MODULE_3__.hasFormChanged)(formDefinition, formValues, lastSavedValues)) {\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        t,\n        formDefinition,\n        formValues,\n        lastSavedValues\n    ]);\n    /**\r\n   * format form value object to match the API request body required to save form data\r\n   * @param values form values\r\n   * @returns formatted form values object\r\n   */ const formatValues = (values)=>{\n        return Object.keys(values).filter((key)=>key !== \"summary\" && key !== \"fromDate\" && key !== \"toDate\").reduce((acc, value)=>{\n            var _formDefinition_find;\n            return {\n                ...acc,\n                [(_formDefinition_find = formDefinition.find((field)=>field.name === value)) === null || _formDefinition_find === void 0 ? void 0 : _formDefinition_find.id]: values[value]\n            };\n        }, {});\n    };\n    const handleFormChange = (values)=>{\n        setFormValues(values);\n    };\n    const handleDateFormChange = (values)=>{\n        setDateFormValues(values);\n    };\n    /**\r\n   * handle save form data\r\n   * @param values form values\r\n   */ const handleSaveForm = async (values)=>{\n        showDialog({\n            message: isSaveAndPublish ? t(\"confirmation.publish.message\") : t(\"confirmation.save.message\"),\n            onConfirm: async ()=>{\n                setIsLoading(true);\n                const updateDossierRequest = {\n                    dossierId: dossier.dossierId,\n                    summary: typeof values.summary === \"string\" ? values.summary : \"\",\n                    dynamicFieldValues: formatValues(values),\n                    publishFromDate: (dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.fromDate) ? dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.fromDate : new Date().toISOString(),\n                    publishToDate: (dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.toDate) ? dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.toDate : null\n                };\n                try {\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.updateDossier)(clientApi, updateDossierRequest);\n                    if (response) {\n                        setIsNew(response.dossier.version === 1);\n                        setLastSavedValues(formValues);\n                        if (isSaveAndPublish) {\n                            handlePublishDossier();\n                        } else {\n                            setStatus(\"Draft\");\n                            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.save\"), {\n                                type: \"success\"\n                            });\n                        }\n                    }\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    const handleDeleteDossier = ()=>{\n        showDialog({\n            message: t(\"confirmation.delete.message\"),\n            onConfirm: async ()=>{\n                try {\n                    setIsLoading(true);\n                    const reqBody = {\n                        dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n                    };\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.deleteDossier)(clientApi, reqBody);\n                    if (response && response.success) {\n                        router.push(\"/dossier/list\");\n                    }\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.delete\"), {\n                        type: \"success\"\n                    });\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    /**\r\n   * handle publish dossier functionality\r\n   */ const handlePublishDossier = async ()=>{\n        try {\n            const reqBody = {\n                dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n            };\n            const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.publishDossier)(clientApi, reqBody);\n            if (response && response.success) {\n                setStatus(\"Published\");\n            }\n            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.publish\"), {\n                type: \"success\"\n            });\n        } catch (error) {\n            const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                type: \"error\"\n            });\n        }\n    };\n    const handleUnpublishDossier = ()=>{\n        showDialog({\n            message: t(\"confirmation.unpublish.message\"),\n            onConfirm: async ()=>{\n                try {\n                    setIsLoading(true);\n                    const reqBody = {\n                        dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n                    };\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.unpublishDossier)(clientApi, reqBody);\n                    if (response) {\n                        setStatus(\"Draft\");\n                        setIsNew(false);\n                    }\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.unpublish\"), {\n                        type: \"success\"\n                    });\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    if (!dossier.rootFolderNode) {\n        console.error(\"Root folder node is missing\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid h-full grid-cols-3 gap-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        className: \"font-heading text-heading mb-9 truncate text-5xl\",\n                        children: dossier.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_title__WEBPACK_IMPORTED_MODULE_14__.DossierTitle, {\n                            title: dossier.title,\n                            dossierId: dossier.dossierId,\n                            onTitleUpdate: (updatedDossier)=>setDossier(updatedDossier)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                className: \"font-heading text-heading mb-2 text-lg font-bold\",\n                                children: t(\"category\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                className: \"font-text-regular text-content-lite text-sm\",\n                                children: (dossier === null || dossier === void 0 ? void 0 : (_dossier_category = dossier.category) === null || _dossier_category === void 0 ? void 0 : _dossier_category.label) || \"\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b-highlight mb-10 border-b\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 mt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_image_field__WEBPACK_IMPORTED_MODULE_12__.DossierImageField, {\n                                    dossierId: dossier.dossierId,\n                                    imagePath: (_dossier_decorativeImageNode = dossier.decorativeImageNode) === null || _dossier_decorativeImageNode === void 0 ? void 0 : _dossier_decorativeImageNode.fullName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.FormBuilder, {\n                                    fields: formDefinition,\n                                    onChange: handleFormChange,\n                                    onSubmit: handleSaveForm,\n                                    formId: formId,\n                                    buttons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            dossier.rootFolderNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_file_manager__WEBPACK_IMPORTED_MODULE_11__.DossierFileManager, {\n                                dossierId: dossier.dossierId,\n                                dossierName: dossier.title,\n                                apiRootNode: dossier.rootFolderNode\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_react_ui__WEBPACK_IMPORTED_MODULE_7__.StatusBox, {\n                    editStatus: editStatus,\n                    publishDates: dateFormValues,\n                    pages: pages,\n                    dossierId: dossier.dossierId,\n                    categoryId: dossier.categoryId,\n                    portalUrl: settings.portalUrl,\n                    submitForm: handleDateFormChange,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_status_controls__WEBPACK_IMPORTED_MODULE_13__.DossierStatusControls, {\n                        dossierId: dossier.dossierId,\n                        formId: formId,\n                        disabled: isLoading,\n                        status: status,\n                        handleUnpublishDossier: handleUnpublishDossier,\n                        handleDeleteDossier: handleDeleteDossier,\n                        setIsSaveAndPublish: setIsSaveAndPublish\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(PageContent, \"JZmqfZZwoPH8FKpGCQzdCV27d5w=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations,\n        _iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__.useAppSettings,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__.useClientApi,\n        _iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.useConfirmDialog,\n        _utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__.useDossierFormDefinition\n    ];\n});\n_c = PageContent;\nvar _c;\n$RefreshReg$(_c, \"PageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9bbG9jYWxlXS8oYXV0aGVudGljYXRlZCkvKHVzZXIpL2Rvc3NpZXIvW2lkXS9wYWdlLWNvbnRlbnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV5RDtBQUUwRDtBQUNyRDtBQUNrQjtBQUN2QjtBQUMwQztBQUNkO0FBQzlCO0FBQ1g7QUFDQTtBQUNTO0FBRWtCO0FBQ0Y7QUFDUTtBQUNuQjtBQU8zQyxTQUFTdUIsWUFBWSxLQUFvRDtRQUFwRCxFQUFFQyxTQUFTQyxjQUFjLEVBQUVDLEtBQUssRUFBb0IsR0FBcEQ7UUEwTXFDRixtQkFLREE7O0lBOU10RSxNQUFNRyxJQUFJYiwyREFBZUEsQ0FBQztJQUMxQixNQUFNYyxXQUFXZixxRUFBY0E7SUFDL0IsTUFBTWdCLFNBQVNkLDBEQUFTQTtJQUN4QixNQUFNZSxZQUFZOUIsc0VBQVlBO0lBQzlCLE1BQU0rQixTQUFTO0lBQ2YsTUFBTSxFQUFFQyxVQUFVLEVBQUUsR0FBR3JCLGlFQUFnQkE7SUFFdkMsTUFBTSxDQUFDYSxTQUFTUyxXQUFXLEdBQUdmLGdEQUFRQSxDQUFDTztJQUN2QyxNQUFNLENBQUNTLFFBQVFDLFVBQVUsR0FBR2pCLGdEQUFRQSxDQUFnQk8sZUFBZVMsTUFBTTtJQUN6RSxNQUFNLENBQUNFLGtCQUFrQkMsb0JBQW9CLEdBQUduQixnREFBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNvQixXQUFXQyxhQUFhLEdBQUdyQixnREFBUUEsQ0FBQztJQUUzQyxNQUFNLENBQUNzQixpQkFBaUJDLG1CQUFtQixHQUFHdkIsZ0RBQVFBLENBQTBCO0lBQ2hGLE1BQU0sQ0FBQ3dCLFlBQVlDLGNBQWMsR0FBR3pCLGdEQUFRQSxDQUEwQjtJQUN0RSxNQUFNLENBQUMwQixnQkFBZ0JDLGtCQUFrQixHQUFHM0IsZ0RBQVFBLENBQXNCTSxRQUFRc0IsWUFBWTtJQUM5RixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBRzlCLGdEQUFRQSxDQUFDTSxRQUFReUIsT0FBTyxLQUFLO0lBRXZELE1BQU1DLGlCQUFpQjVDLDZGQUF3QkEsQ0FBQ2tCO0lBRWhELE1BQU0yQixhQUFhbEMsK0NBQU9BLENBQWE7UUFDckMsSUFBSWlCLFdBQVcsYUFBYTtZQUMxQixPQUFPO1FBQ1Q7UUFFQSxPQUFPYSxRQUFRLFFBQVE7SUFDekIsR0FBRztRQUFDYjtRQUFRYTtLQUFNO0lBRWxCL0IsaURBQVNBLENBQUM7UUFDUixNQUFNb0MscUJBQXFCLENBQUNDO1lBQzFCLElBQUloRCwyRUFBY0EsQ0FBQzZDLGdCQUFnQlIsWUFBWUYsa0JBQWtCO2dCQUMvRGEsRUFBRUMsV0FBVyxHQUFHO1lBQ2xCO1FBQ0Y7UUFDQUMsT0FBT0MsZ0JBQWdCLENBQUMsZ0JBQWdCSjtRQUN4QyxPQUFPO1lBQ0xHLE9BQU9FLG1CQUFtQixDQUFDLGdCQUFnQkw7UUFDN0M7SUFDRixHQUFHO1FBQUN6QjtRQUFHdUI7UUFBZ0JSO1FBQVlGO0tBQWdCO0lBRW5EOzs7O0dBSUMsR0FDRCxNQUFNa0IsZUFBZSxDQUFDQztRQUNwQixPQUFPQyxPQUFPQyxJQUFJLENBQUNGLFFBQ2hCRyxNQUFNLENBQUMsQ0FBQ0MsTUFBUUEsUUFBUSxhQUFhQSxRQUFRLGNBQWNBLFFBQVEsVUFDbkVDLE1BQU0sQ0FDTCxDQUFDQyxLQUFLQztnQkFFSGhCO21CQUZjO2dCQUNmLEdBQUdlLEdBQUc7Z0JBQ04sRUFBQ2YsdUJBQUFBLGVBQWVpQixJQUFJLENBQUMsQ0FBQ0MsUUFBVUEsTUFBTUMsSUFBSSxLQUFLSCxvQkFBOUNoQiwyQ0FBQUEscUJBQXNEb0IsRUFBRSxDQUFXLEVBQUVYLE1BQU0sQ0FBQ08sTUFBTTtZQUNyRjtXQUNBLENBQUM7SUFFUDtJQUVBLE1BQU1LLG1CQUFtQixDQUFDWjtRQUN4QmhCLGNBQWNnQjtJQUNoQjtJQUVBLE1BQU1hLHVCQUF1QixDQUFDYjtRQUM1QmQsa0JBQWtCYztJQUNwQjtJQUVBOzs7R0FHQyxHQUNELE1BQU1jLGlCQUFpQixPQUFPZDtRQUM1QjNCLFdBQVc7WUFDVDBDLFNBQVN0QyxtQkFBbUJULEVBQUUsa0NBQWtDQSxFQUFFO1lBQ2xFZ0QsV0FBVztnQkFDVHBDLGFBQWE7Z0JBQ2IsTUFBTXFDLHVCQUFzRTtvQkFDMUVDLFdBQVdyRCxRQUFRcUQsU0FBUztvQkFDNUJDLFNBQVMsT0FBT25CLE9BQU9tQixPQUFPLEtBQUssV0FBV25CLE9BQU9tQixPQUFPLEdBQUc7b0JBQy9EQyxvQkFBb0JyQixhQUFhQztvQkFDakNxQixpQkFBaUJwQyxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCcUMsUUFBUSxJQUFJckMsMkJBQUFBLHFDQUFBQSxlQUFnQnFDLFFBQVEsR0FBYyxJQUFJQyxPQUFPQyxXQUFXO29CQUN6R0MsZUFBZXhDLENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0J5QyxNQUFNLElBQUl6QywyQkFBQUEscUNBQUFBLGVBQWdCeUMsTUFBTSxHQUFjO2dCQUMvRTtnQkFFQSxJQUFJO29CQUNGLE1BQU1DLFdBQVcsTUFBTWxGLCtFQUFhQSxDQUFDMEIsV0FBVzhDO29CQUVoRCxJQUFJVSxVQUFVO3dCQUNadEMsU0FBU3NDLFNBQVM5RCxPQUFPLENBQUN5QixPQUFPLEtBQUs7d0JBQ3RDUixtQkFBbUJDO3dCQUVuQixJQUFJTixrQkFBa0I7NEJBQ3BCbUQ7d0JBQ0YsT0FBTzs0QkFDTHBELFVBQVU7NEJBQ1Z6QiwwREFBU0EsQ0FBQ2lCLEVBQUUsaUJBQWlCO2dDQUFFNkQsTUFBTTs0QkFBVTt3QkFDakQ7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPQyxPQUFPO29CQUNkLE1BQU1DLGdCQUFnQixNQUFNbkYsc0VBQWdCQSxDQUFDa0Y7b0JBQzdDL0UsMERBQVNBLENBQUNnRixlQUFlO3dCQUFFRixNQUFNO29CQUFRO2dCQUMzQyxTQUFVO29CQUNSakQsYUFBYTtnQkFDZjtZQUNGO1FBQ0Y7SUFDRjtJQUVBLE1BQU1vRCxzQkFBc0I7UUFDMUIzRCxXQUFXO1lBQ1QwQyxTQUFTL0MsRUFBRTtZQUNYZ0QsV0FBVztnQkFDVCxJQUFJO29CQUNGcEMsYUFBYTtvQkFDYixNQUFNcUQsVUFBeUQ7d0JBQzdEZixTQUFTLEVBQUVyRCxvQkFBQUEsOEJBQUFBLFFBQVNxRCxTQUFTO29CQUMvQjtvQkFFQSxNQUFNUyxXQUFXLE1BQU1yRiwrRUFBYUEsQ0FBQzZCLFdBQVc4RDtvQkFFaEQsSUFBSU4sWUFBWUEsU0FBU08sT0FBTyxFQUFFO3dCQUNoQ2hFLE9BQU9pRSxJQUFJLENBQUM7b0JBQ2Q7b0JBRUFwRiwwREFBU0EsQ0FBQ2lCLEVBQUUsbUJBQW1CO3dCQUFFNkQsTUFBTTtvQkFBVTtnQkFDbkQsRUFBRSxPQUFPQyxPQUFPO29CQUNkLE1BQU1DLGdCQUFnQixNQUFNbkYsc0VBQWdCQSxDQUFDa0Y7b0JBQzdDL0UsMERBQVNBLENBQUNnRixlQUFlO3dCQUFFRixNQUFNO29CQUFRO2dCQUMzQyxTQUFVO29CQUNSakQsYUFBYTtnQkFDZjtZQUNGO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTWdELHVCQUF1QjtRQUMzQixJQUFJO1lBQ0YsTUFBTUssVUFBMEQ7Z0JBQzlEZixTQUFTLEVBQUVyRCxvQkFBQUEsOEJBQUFBLFFBQVNxRCxTQUFTO1lBQy9CO1lBRUEsTUFBTVMsV0FBVyxNQUFNcEYsZ0ZBQWNBLENBQUM0QixXQUFXOEQ7WUFFakQsSUFBSU4sWUFBWUEsU0FBU08sT0FBTyxFQUFFO2dCQUNoQzFELFVBQVU7WUFDWjtZQUVBekIsMERBQVNBLENBQUNpQixFQUFFLG9CQUFvQjtnQkFBRTZELE1BQU07WUFBVTtRQUNwRCxFQUFFLE9BQU9DLE9BQU87WUFDZCxNQUFNQyxnQkFBZ0IsTUFBTW5GLHNFQUFnQkEsQ0FBQ2tGO1lBQzdDL0UsMERBQVNBLENBQUNnRixlQUFlO2dCQUFFRixNQUFNO1lBQVE7UUFDM0M7SUFDRjtJQUVBLE1BQU1PLHlCQUF5QjtRQUM3Qi9ELFdBQVc7WUFDVDBDLFNBQVMvQyxFQUFFO1lBQ1hnRCxXQUFXO2dCQUNULElBQUk7b0JBQ0ZwQyxhQUFhO29CQUNiLE1BQU1xRCxVQUE0RDt3QkFDaEVmLFNBQVMsRUFBRXJELG9CQUFBQSw4QkFBQUEsUUFBU3FELFNBQVM7b0JBQy9CO29CQUVBLE1BQU1TLFdBQVcsTUFBTW5GLGtGQUFnQkEsQ0FBQzJCLFdBQVc4RDtvQkFFbkQsSUFBSU4sVUFBVTt3QkFDWm5ELFVBQVU7d0JBQ1ZhLFNBQVM7b0JBQ1g7b0JBRUF0QywwREFBU0EsQ0FBQ2lCLEVBQUUsc0JBQXNCO3dCQUFFNkQsTUFBTTtvQkFBVTtnQkFDdEQsRUFBRSxPQUFPQyxPQUFPO29CQUNkLE1BQU1DLGdCQUFnQixNQUFNbkYsc0VBQWdCQSxDQUFDa0Y7b0JBQzdDL0UsMERBQVNBLENBQUNnRixlQUFlO3dCQUFFRixNQUFNO29CQUFRO2dCQUMzQyxTQUFVO29CQUNSakQsYUFBYTtnQkFDZjtZQUNGO1FBQ0Y7SUFDRjtJQUVBLElBQUksQ0FBQ2YsUUFBUXdFLGNBQWMsRUFBRTtRQUMzQkMsUUFBUVIsS0FBSyxDQUFDO0lBQ2hCO0lBRUEscUJBQ0UsOERBQUNTO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUMxRixpREFBSUE7d0JBQUMwRixXQUFVO2tDQUFvRDNFLFFBQVE0RSxLQUFLOzs7Ozs7a0NBRWpGLDhEQUFDRjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQzdFLG9FQUFZQTs0QkFDWDhFLE9BQU81RSxRQUFRNEUsS0FBSzs0QkFDcEJ2QixXQUFXckQsUUFBUXFELFNBQVM7NEJBQzVCd0IsZUFBZSxDQUFDQyxpQkFBbUJyRSxXQUFXcUU7Ozs7Ozs7Ozs7O2tDQUlsRCw4REFBQ0o7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDMUYsaURBQUlBO2dDQUFDMEYsV0FBVTswQ0FBb0R4RSxFQUFFOzs7Ozs7MENBQ3RFLDhEQUFDbEIsaURBQUlBO2dDQUFDMEYsV0FBVTswQ0FBK0MzRSxDQUFBQSxvQkFBQUEsK0JBQUFBLG9CQUFBQSxRQUFTK0UsUUFBUSxjQUFqQi9FLHdDQUFBQSxrQkFBbUJnRixLQUFLLEtBQUk7Ozs7Ozs7Ozs7OztrQ0FFN0YsOERBQUNOO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNEOzswQ0FDQyw4REFBQ0E7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUMvRSwrRUFBaUJBO29DQUFDeUQsV0FBV3JELFFBQVFxRCxTQUFTO29DQUFFNEIsU0FBUyxHQUFFakYsK0JBQUFBLFFBQVFrRixtQkFBbUIsY0FBM0JsRixtREFBQUEsNkJBQTZCbUYsUUFBUTs7Ozs7Ozs7Ozs7MENBRW5HLDhEQUFDVDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQzNGLHdEQUFXQTtvQ0FDVm9HLFFBQVExRDtvQ0FDUjJELFVBQVV0QztvQ0FDVnVDLFVBQVVyQztvQ0FDVjFDLFFBQVFBO29DQUNSZ0YsdUJBQVM7Ozs7Ozs7Ozs7OzRCQUdadkYsUUFBUXdFLGNBQWMsa0JBQ3JCLDhEQUFDN0UsaUZBQWtCQTtnQ0FDakIwRCxXQUFXckQsUUFBUXFELFNBQVM7Z0NBQzVCbUMsYUFBYXhGLFFBQVE0RSxLQUFLO2dDQUMxQmEsYUFBYXpGLFFBQVF3RSxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSzNDLDhEQUFDRTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ3ZGLHNEQUFTQTtvQkFDUnVDLFlBQVlBO29CQUNaTCxjQUFjRjtvQkFDZGxCLE9BQU9BO29CQUNQbUQsV0FBV3JELFFBQVFxRCxTQUFTO29CQUM1QnFDLFlBQVkxRixRQUFRMEYsVUFBVTtvQkFDOUJDLFdBQVd2RixTQUFTdUYsU0FBUztvQkFDN0JDLFlBQVk1Qzs4QkFFWiw0RUFBQ25ELHVGQUFxQkE7d0JBQ3BCd0QsV0FBV3JELFFBQVFxRCxTQUFTO3dCQUM1QjlDLFFBQVFBO3dCQUNSc0YsVUFBVS9FO3dCQUNWSixRQUFRQTt3QkFDUjZELHdCQUF3QkE7d0JBQ3hCSixxQkFBcUJBO3dCQUNyQnRELHFCQUFxQkE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNakM7R0ExUHdCZDs7UUFDWlQsdURBQWVBO1FBQ1JELGlFQUFjQTtRQUNoQkUsc0RBQVNBO1FBQ05mLGtFQUFZQTtRQUVQVyw2REFBZ0JBO1FBWWhCTCx5RkFBd0JBOzs7S0FsQnpCaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL1tsb2NhbGVdLyhhdXRoZW50aWNhdGVkKS8odXNlcikvZG9zc2llci9baWRdL3BhZ2UtY29udGVudC50c3g/NTEzZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyB1c2VDbGllbnRBcGkgfSBmcm9tICdAL2h0dHAvZmV0Y2hlci1hcGkuY2xpZW50JztcclxuaW1wb3J0IHsgY29tcG9uZW50cyB9IGZyb20gJ0AvaXByb3gtb3Blbi5pbnRlcmZhY2UnO1xyXG5pbXBvcnQgeyBkZWxldGVEb3NzaWVyLCBwdWJsaXNoRG9zc2llciwgdW5wdWJsaXNoRG9zc2llciwgdXBkYXRlRG9zc2llciB9IGZyb20gJ0Avc2VydmljZXMvZG9zc2llci1zZXJ2aWNlLmNsaWVudCc7XHJcbmltcG9ydCB7IGhhc0Zvcm1DaGFuZ2VkIH0gZnJvbSAnQC91dGlscy9kb3NzaWVyLmZvcm0tY2hhbmdlZCc7XHJcbmltcG9ydCB7IHVzZURvc3NpZXJGb3JtRGVmaW5pdGlvbiB9IGZyb20gJ0AvdXRpbHMvZG9zc2llci5mb3JtLWRlZmluaXRpb24uaG9vayc7XHJcbmltcG9ydCB7IGdldEVycm9yTWVzc2FnZXMgfSBmcm9tICdAL3V0aWxzL2Vycm9yLWhhbmRsZXInO1xyXG5pbXBvcnQgeyBGb3JtQnVpbGRlciwgRm9ybVN1Ym1pdFZhbHVlcywgVGV4dCwgc2hvd1RvYXN0LCB1c2VDb25maXJtRGlhbG9nIH0gZnJvbSAnQGlwcm94L2lwcm94LXVpJztcclxuaW1wb3J0IHsgRG9zc2llclN0YXR1cywgRWRpdFN0YXR1cywgUHVibGlzaERhdGVzLCBTdGF0dXNCb3ggfSBmcm9tICdAaXByb3gvcmVhY3QtdWknO1xyXG5pbXBvcnQgeyB1c2VBcHBTZXR0aW5ncyB9IGZyb20gJ0BpcHJveC9zaGFyZWQtY29udGV4dCc7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9ucyB9IGZyb20gJ25leHQtaW50bCc7XHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTWVtbywgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XHJcblxyXG5pbXBvcnQgeyBEb3NzaWVyRmlsZU1hbmFnZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZG9zc2llci1maWxlLW1hbmFnZXInO1xyXG5pbXBvcnQgeyBEb3NzaWVySW1hZ2VGaWVsZCB9IGZyb20gJ0AvY29tcG9uZW50cy9kb3NzaWVyLWltYWdlLWZpZWxkJztcclxuaW1wb3J0IHsgRG9zc2llclN0YXR1c0NvbnRyb2xzIH0gZnJvbSAnQC9jb21wb25lbnRzL2Rvc3NpZXItc3RhdHVzLWNvbnRyb2xzJztcclxuaW1wb3J0IHsgRG9zc2llclRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL2Rvc3NpZXItdGl0bGUnO1xyXG5cclxuaW50ZXJmYWNlIFBhZ2VDb250ZW50UHJvcHMge1xyXG4gIGRvc3NpZXI6IGNvbXBvbmVudHNbJ3NjaGVtYXMnXVsnRG9zc2llckR0byddO1xyXG4gIHBhZ2VzOiBjb21wb25lbnRzWydzY2hlbWFzJ11bJ1NlYXJjaFBhZ2VEdG8nXVtdO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYWdlQ29udGVudCh7IGRvc3NpZXI6IGluaXRpYWxEb3NzaWVyLCBwYWdlcyB9OiBQYWdlQ29udGVudFByb3BzKSB7XHJcbiAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucygnZG9zc2llcicpO1xyXG4gIGNvbnN0IHNldHRpbmdzID0gdXNlQXBwU2V0dGluZ3MoKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCBjbGllbnRBcGkgPSB1c2VDbGllbnRBcGkoKTtcclxuICBjb25zdCBmb3JtSWQgPSAnZG9zc2llci1jcmVhdGlvbi1mb3JtJztcclxuICBjb25zdCB7IHNob3dEaWFsb2cgfSA9IHVzZUNvbmZpcm1EaWFsb2coKTtcclxuXHJcbiAgY29uc3QgW2Rvc3NpZXIsIHNldERvc3NpZXJdID0gdXNlU3RhdGUoaW5pdGlhbERvc3NpZXIpO1xyXG4gIGNvbnN0IFtzdGF0dXMsIHNldFN0YXR1c10gPSB1c2VTdGF0ZTxEb3NzaWVyU3RhdHVzPihpbml0aWFsRG9zc2llci5zdGF0dXMpO1xyXG4gIGNvbnN0IFtpc1NhdmVBbmRQdWJsaXNoLCBzZXRJc1NhdmVBbmRQdWJsaXNoXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICBjb25zdCBbbGFzdFNhdmVkVmFsdWVzLCBzZXRMYXN0U2F2ZWRWYWx1ZXNdID0gdXNlU3RhdGU8Rm9ybVN1Ym1pdFZhbHVlcyB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtmb3JtVmFsdWVzLCBzZXRGb3JtVmFsdWVzXSA9IHVzZVN0YXRlPEZvcm1TdWJtaXRWYWx1ZXMgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbZGF0ZUZvcm1WYWx1ZXMsIHNldERhdGVGb3JtVmFsdWVzXSA9IHVzZVN0YXRlPFB1Ymxpc2hEYXRlcyB8IG51bGw+KGRvc3NpZXIucHVibGlzaERhdGVzKTtcclxuICBjb25zdCBbaXNOZXcsIHNldElzTmV3XSA9IHVzZVN0YXRlKGRvc3NpZXIudmVyc2lvbiA9PT0gMSk7XHJcblxyXG4gIGNvbnN0IGZvcm1EZWZpbml0aW9uID0gdXNlRG9zc2llckZvcm1EZWZpbml0aW9uKGRvc3NpZXIpO1xyXG5cclxuICBjb25zdCBlZGl0U3RhdHVzID0gdXNlTWVtbzxFZGl0U3RhdHVzPigoKSA9PiB7XHJcbiAgICBpZiAoc3RhdHVzID09PSAnUHVibGlzaGVkJykge1xyXG4gICAgICByZXR1cm4gJ3B1Ymxpc2hlZCc7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIGlzTmV3ID8gJ25ldycgOiAnbW9kaWZpZWQnO1xyXG4gIH0sIFtzdGF0dXMsIGlzTmV3XSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVCZWZvcmVVbmxvYWQgPSAoZTogQmVmb3JlVW5sb2FkRXZlbnQpID0+IHtcclxuICAgICAgaWYgKGhhc0Zvcm1DaGFuZ2VkKGZvcm1EZWZpbml0aW9uLCBmb3JtVmFsdWVzLCBsYXN0U2F2ZWRWYWx1ZXMpKSB7XHJcbiAgICAgICAgZS5yZXR1cm5WYWx1ZSA9ICcnO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZCk7XHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKTtcclxuICAgIH07XHJcbiAgfSwgW3QsIGZvcm1EZWZpbml0aW9uLCBmb3JtVmFsdWVzLCBsYXN0U2F2ZWRWYWx1ZXNdKTtcclxuXHJcbiAgLyoqXHJcbiAgICogZm9ybWF0IGZvcm0gdmFsdWUgb2JqZWN0IHRvIG1hdGNoIHRoZSBBUEkgcmVxdWVzdCBib2R5IHJlcXVpcmVkIHRvIHNhdmUgZm9ybSBkYXRhXHJcbiAgICogQHBhcmFtIHZhbHVlcyBmb3JtIHZhbHVlc1xyXG4gICAqIEByZXR1cm5zIGZvcm1hdHRlZCBmb3JtIHZhbHVlcyBvYmplY3RcclxuICAgKi9cclxuICBjb25zdCBmb3JtYXRWYWx1ZXMgPSAodmFsdWVzOiBGb3JtU3VibWl0VmFsdWVzKSA9PiB7XHJcbiAgICByZXR1cm4gT2JqZWN0LmtleXModmFsdWVzKVxyXG4gICAgICAuZmlsdGVyKChrZXkpID0+IGtleSAhPT0gJ3N1bW1hcnknICYmIGtleSAhPT0gJ2Zyb21EYXRlJyAmJiBrZXkgIT09ICd0b0RhdGUnKVxyXG4gICAgICAucmVkdWNlKFxyXG4gICAgICAgIChhY2MsIHZhbHVlKSA9PiAoe1xyXG4gICAgICAgICAgLi4uYWNjLFxyXG4gICAgICAgICAgW2Zvcm1EZWZpbml0aW9uLmZpbmQoKGZpZWxkKSA9PiBmaWVsZC5uYW1lID09PSB2YWx1ZSk/LmlkIGFzIHN0cmluZ106IHZhbHVlc1t2YWx1ZV0gYXMgc3RyaW5nLFxyXG4gICAgICAgIH0pLFxyXG4gICAgICAgIHt9XHJcbiAgICAgICk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRm9ybUNoYW5nZSA9ICh2YWx1ZXM6IEZvcm1TdWJtaXRWYWx1ZXMpID0+IHtcclxuICAgIHNldEZvcm1WYWx1ZXModmFsdWVzKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVEYXRlRm9ybUNoYW5nZSA9ICh2YWx1ZXM6IFB1Ymxpc2hEYXRlcykgPT4ge1xyXG4gICAgc2V0RGF0ZUZvcm1WYWx1ZXModmFsdWVzKTtcclxuICB9O1xyXG5cclxuICAvKipcclxuICAgKiBoYW5kbGUgc2F2ZSBmb3JtIGRhdGFcclxuICAgKiBAcGFyYW0gdmFsdWVzIGZvcm0gdmFsdWVzXHJcbiAgICovXHJcbiAgY29uc3QgaGFuZGxlU2F2ZUZvcm0gPSBhc3luYyAodmFsdWVzOiBGb3JtU3VibWl0VmFsdWVzKSA9PiB7XHJcbiAgICBzaG93RGlhbG9nKHtcclxuICAgICAgbWVzc2FnZTogaXNTYXZlQW5kUHVibGlzaCA/IHQoJ2NvbmZpcm1hdGlvbi5wdWJsaXNoLm1lc3NhZ2UnKSA6IHQoJ2NvbmZpcm1hdGlvbi5zYXZlLm1lc3NhZ2UnKSxcclxuICAgICAgb25Db25maXJtOiBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xyXG4gICAgICAgIGNvbnN0IHVwZGF0ZURvc3NpZXJSZXF1ZXN0OiBjb21wb25lbnRzWydzY2hlbWFzJ11bJ1VwZGF0ZURvc3NpZXJDb21tYW5kJ10gPSB7XHJcbiAgICAgICAgICBkb3NzaWVySWQ6IGRvc3NpZXIuZG9zc2llcklkLFxyXG4gICAgICAgICAgc3VtbWFyeTogdHlwZW9mIHZhbHVlcy5zdW1tYXJ5ID09PSAnc3RyaW5nJyA/IHZhbHVlcy5zdW1tYXJ5IDogJycsXHJcbiAgICAgICAgICBkeW5hbWljRmllbGRWYWx1ZXM6IGZvcm1hdFZhbHVlcyh2YWx1ZXMpLFxyXG4gICAgICAgICAgcHVibGlzaEZyb21EYXRlOiBkYXRlRm9ybVZhbHVlcz8uZnJvbURhdGUgPyAoZGF0ZUZvcm1WYWx1ZXM/LmZyb21EYXRlIGFzIHN0cmluZykgOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICBwdWJsaXNoVG9EYXRlOiBkYXRlRm9ybVZhbHVlcz8udG9EYXRlID8gKGRhdGVGb3JtVmFsdWVzPy50b0RhdGUgYXMgc3RyaW5nKSA6IG51bGwsXHJcbiAgICAgICAgfTtcclxuXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXBkYXRlRG9zc2llcihjbGllbnRBcGksIHVwZGF0ZURvc3NpZXJSZXF1ZXN0KTtcclxuXHJcbiAgICAgICAgICBpZiAocmVzcG9uc2UpIHtcclxuICAgICAgICAgICAgc2V0SXNOZXcocmVzcG9uc2UuZG9zc2llci52ZXJzaW9uID09PSAxKTtcclxuICAgICAgICAgICAgc2V0TGFzdFNhdmVkVmFsdWVzKGZvcm1WYWx1ZXMpO1xyXG5cclxuICAgICAgICAgICAgaWYgKGlzU2F2ZUFuZFB1Ymxpc2gpIHtcclxuICAgICAgICAgICAgICBoYW5kbGVQdWJsaXNoRG9zc2llcigpO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgIHNldFN0YXR1cygnRHJhZnQnKTtcclxuICAgICAgICAgICAgICBzaG93VG9hc3QodCgnc3VjY2Vzcy5zYXZlJyksIHsgdHlwZTogJ3N1Y2Nlc3MnIH0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZXMgPSBhd2FpdCBnZXRFcnJvck1lc3NhZ2VzKGVycm9yKTtcclxuICAgICAgICAgIHNob3dUb2FzdChlcnJvck1lc3NhZ2VzLCB7IHR5cGU6ICdlcnJvcicgfSk7XHJcbiAgICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9LFxyXG4gICAgfSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlRG9zc2llciA9ICgpID0+IHtcclxuICAgIHNob3dEaWFsb2coe1xyXG4gICAgICBtZXNzYWdlOiB0KCdjb25maXJtYXRpb24uZGVsZXRlLm1lc3NhZ2UnKSxcclxuICAgICAgb25Db25maXJtOiBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIHNldElzTG9hZGluZyh0cnVlKTtcclxuICAgICAgICAgIGNvbnN0IHJlcUJvZHk6IGNvbXBvbmVudHNbJ3NjaGVtYXMnXVsnRGVsZXRlRG9zc2llckNvbW1hbmQnXSA9IHtcclxuICAgICAgICAgICAgZG9zc2llcklkOiBkb3NzaWVyPy5kb3NzaWVySWQsXHJcbiAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZGVsZXRlRG9zc2llcihjbGllbnRBcGksIHJlcUJvZHkpO1xyXG5cclxuICAgICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5zdWNjZXNzKSB7XHJcbiAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvZG9zc2llci9saXN0Jyk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgc2hvd1RvYXN0KHQoJ3N1Y2Nlc3MuZGVsZXRlJyksIHsgdHlwZTogJ3N1Y2Nlc3MnIH0pO1xyXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2VzID0gYXdhaXQgZ2V0RXJyb3JNZXNzYWdlcyhlcnJvcik7XHJcbiAgICAgICAgICBzaG93VG9hc3QoZXJyb3JNZXNzYWdlcywgeyB0eXBlOiAnZXJyb3InIH0pO1xyXG4gICAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xyXG4gICAgICAgIH1cclxuICAgICAgfSxcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIC8qKlxyXG4gICAqIGhhbmRsZSBwdWJsaXNoIGRvc3NpZXIgZnVuY3Rpb25hbGl0eVxyXG4gICAqL1xyXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2hEb3NzaWVyID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVxQm9keTogY29tcG9uZW50c1snc2NoZW1hcyddWydQdWJsaXNoRG9zc2llckNvbW1hbmQnXSA9IHtcclxuICAgICAgICBkb3NzaWVySWQ6IGRvc3NpZXI/LmRvc3NpZXJJZCxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcHVibGlzaERvc3NpZXIoY2xpZW50QXBpLCByZXFCb2R5KTtcclxuXHJcbiAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5zdWNjZXNzKSB7XHJcbiAgICAgICAgc2V0U3RhdHVzKCdQdWJsaXNoZWQnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgc2hvd1RvYXN0KHQoJ3N1Y2Nlc3MucHVibGlzaCcpLCB7IHR5cGU6ICdzdWNjZXNzJyB9KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZXMgPSBhd2FpdCBnZXRFcnJvck1lc3NhZ2VzKGVycm9yKTtcclxuICAgICAgc2hvd1RvYXN0KGVycm9yTWVzc2FnZXMsIHsgdHlwZTogJ2Vycm9yJyB9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVVbnB1Ymxpc2hEb3NzaWVyID0gKCkgPT4ge1xyXG4gICAgc2hvd0RpYWxvZyh7XHJcbiAgICAgIG1lc3NhZ2U6IHQoJ2NvbmZpcm1hdGlvbi51bnB1Ymxpc2gubWVzc2FnZScpLFxyXG4gICAgICBvbkNvbmZpcm06IGFzeW5jICgpID0+IHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgc2V0SXNMb2FkaW5nKHRydWUpO1xyXG4gICAgICAgICAgY29uc3QgcmVxQm9keTogY29tcG9uZW50c1snc2NoZW1hcyddWydVbnB1Ymxpc2hEb3NzaWVyQ29tbWFuZCddID0ge1xyXG4gICAgICAgICAgICBkb3NzaWVySWQ6IGRvc3NpZXI/LmRvc3NpZXJJZCxcclxuICAgICAgICAgIH07XHJcblxyXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1bnB1Ymxpc2hEb3NzaWVyKGNsaWVudEFwaSwgcmVxQm9keSk7XHJcblxyXG4gICAgICAgICAgaWYgKHJlc3BvbnNlKSB7XHJcbiAgICAgICAgICAgIHNldFN0YXR1cygnRHJhZnQnKTtcclxuICAgICAgICAgICAgc2V0SXNOZXcoZmFsc2UpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIHNob3dUb2FzdCh0KCdzdWNjZXNzLnVucHVibGlzaCcpLCB7IHR5cGU6ICdzdWNjZXNzJyB9KTtcclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlcyA9IGF3YWl0IGdldEVycm9yTWVzc2FnZXMoZXJyb3IpO1xyXG4gICAgICAgICAgc2hvd1RvYXN0KGVycm9yTWVzc2FnZXMsIHsgdHlwZTogJ2Vycm9yJyB9KTtcclxuICAgICAgICB9IGZpbmFsbHkge1xyXG4gICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuICB9O1xyXG5cclxuICBpZiAoIWRvc3NpZXIucm9vdEZvbGRlck5vZGUpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ1Jvb3QgZm9sZGVyIG5vZGUgaXMgbWlzc2luZycpO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBoLWZ1bGwgZ3JpZC1jb2xzLTMgZ2FwLThcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0yXCI+XHJcbiAgICAgICAgPFRleHQgY2xhc3NOYW1lPVwiZm9udC1oZWFkaW5nIHRleHQtaGVhZGluZyBtYi05IHRydW5jYXRlIHRleHQtNXhsXCI+e2Rvc3NpZXIudGl0bGV9PC9UZXh0PlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cclxuICAgICAgICAgIDxEb3NzaWVyVGl0bGVcclxuICAgICAgICAgICAgdGl0bGU9e2Rvc3NpZXIudGl0bGV9XHJcbiAgICAgICAgICAgIGRvc3NpZXJJZD17ZG9zc2llci5kb3NzaWVySWR9XHJcbiAgICAgICAgICAgIG9uVGl0bGVVcGRhdGU9eyh1cGRhdGVkRG9zc2llcikgPT4gc2V0RG9zc2llcih1cGRhdGVkRG9zc2llcil9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cclxuICAgICAgICAgIDxUZXh0IGNsYXNzTmFtZT1cImZvbnQtaGVhZGluZyB0ZXh0LWhlYWRpbmcgbWItMiB0ZXh0LWxnIGZvbnQtYm9sZFwiPnt0KCdjYXRlZ29yeScpfTwvVGV4dD5cclxuICAgICAgICAgIDxUZXh0IGNsYXNzTmFtZT1cImZvbnQtdGV4dC1yZWd1bGFyIHRleHQtY29udGVudC1saXRlIHRleHQtc21cIj57ZG9zc2llcj8uY2F0ZWdvcnk/LmxhYmVsIHx8ICcnfTwvVGV4dD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iLWhpZ2hsaWdodCBtYi0xMCBib3JkZXItYlwiIC8+XHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOCBtdC04XCI+XHJcbiAgICAgICAgICAgIDxEb3NzaWVySW1hZ2VGaWVsZCBkb3NzaWVySWQ9e2Rvc3NpZXIuZG9zc2llcklkfSBpbWFnZVBhdGg9e2Rvc3NpZXIuZGVjb3JhdGl2ZUltYWdlTm9kZT8uZnVsbE5hbWV9IC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxyXG4gICAgICAgICAgICA8Rm9ybUJ1aWxkZXJcclxuICAgICAgICAgICAgICBmaWVsZHM9e2Zvcm1EZWZpbml0aW9ufVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGb3JtQ2hhbmdlfVxyXG4gICAgICAgICAgICAgIG9uU3VibWl0PXtoYW5kbGVTYXZlRm9ybX1cclxuICAgICAgICAgICAgICBmb3JtSWQ9e2Zvcm1JZH1cclxuICAgICAgICAgICAgICBidXR0b25zPXs8PjwvPn1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAge2Rvc3NpZXIucm9vdEZvbGRlck5vZGUgJiYgKFxyXG4gICAgICAgICAgICA8RG9zc2llckZpbGVNYW5hZ2VyXHJcbiAgICAgICAgICAgICAgZG9zc2llcklkPXtkb3NzaWVyLmRvc3NpZXJJZH1cclxuICAgICAgICAgICAgICBkb3NzaWVyTmFtZT17ZG9zc2llci50aXRsZX1cclxuICAgICAgICAgICAgICBhcGlSb290Tm9kZT17ZG9zc2llci5yb290Rm9sZGVyTm9kZX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTFcIj5cclxuICAgICAgICA8U3RhdHVzQm94XHJcbiAgICAgICAgICBlZGl0U3RhdHVzPXtlZGl0U3RhdHVzfVxyXG4gICAgICAgICAgcHVibGlzaERhdGVzPXtkYXRlRm9ybVZhbHVlc31cclxuICAgICAgICAgIHBhZ2VzPXtwYWdlc31cclxuICAgICAgICAgIGRvc3NpZXJJZD17ZG9zc2llci5kb3NzaWVySWR9XHJcbiAgICAgICAgICBjYXRlZ29yeUlkPXtkb3NzaWVyLmNhdGVnb3J5SWR9XHJcbiAgICAgICAgICBwb3J0YWxVcmw9e3NldHRpbmdzLnBvcnRhbFVybH1cclxuICAgICAgICAgIHN1Ym1pdEZvcm09e2hhbmRsZURhdGVGb3JtQ2hhbmdlfVxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxEb3NzaWVyU3RhdHVzQ29udHJvbHNcclxuICAgICAgICAgICAgZG9zc2llcklkPXtkb3NzaWVyLmRvc3NpZXJJZH1cclxuICAgICAgICAgICAgZm9ybUlkPXtmb3JtSWR9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XHJcbiAgICAgICAgICAgIHN0YXR1cz17c3RhdHVzfVxyXG4gICAgICAgICAgICBoYW5kbGVVbnB1Ymxpc2hEb3NzaWVyPXtoYW5kbGVVbnB1Ymxpc2hEb3NzaWVyfVxyXG4gICAgICAgICAgICBoYW5kbGVEZWxldGVEb3NzaWVyPXtoYW5kbGVEZWxldGVEb3NzaWVyfVxyXG4gICAgICAgICAgICBzZXRJc1NhdmVBbmRQdWJsaXNoPXtzZXRJc1NhdmVBbmRQdWJsaXNofVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L1N0YXR1c0JveD5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VDbGllbnRBcGkiLCJkZWxldGVEb3NzaWVyIiwicHVibGlzaERvc3NpZXIiLCJ1bnB1Ymxpc2hEb3NzaWVyIiwidXBkYXRlRG9zc2llciIsImhhc0Zvcm1DaGFuZ2VkIiwidXNlRG9zc2llckZvcm1EZWZpbml0aW9uIiwiZ2V0RXJyb3JNZXNzYWdlcyIsIkZvcm1CdWlsZGVyIiwiVGV4dCIsInNob3dUb2FzdCIsInVzZUNvbmZpcm1EaWFsb2ciLCJTdGF0dXNCb3giLCJ1c2VBcHBTZXR0aW5ncyIsInVzZVRyYW5zbGF0aW9ucyIsInVzZVJvdXRlciIsInVzZUVmZmVjdCIsInVzZU1lbW8iLCJ1c2VTdGF0ZSIsIkRvc3NpZXJGaWxlTWFuYWdlciIsIkRvc3NpZXJJbWFnZUZpZWxkIiwiRG9zc2llclN0YXR1c0NvbnRyb2xzIiwiRG9zc2llclRpdGxlIiwiUGFnZUNvbnRlbnQiLCJkb3NzaWVyIiwiaW5pdGlhbERvc3NpZXIiLCJwYWdlcyIsInQiLCJzZXR0aW5ncyIsInJvdXRlciIsImNsaWVudEFwaSIsImZvcm1JZCIsInNob3dEaWFsb2ciLCJzZXREb3NzaWVyIiwic3RhdHVzIiwic2V0U3RhdHVzIiwiaXNTYXZlQW5kUHVibGlzaCIsInNldElzU2F2ZUFuZFB1Ymxpc2giLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJsYXN0U2F2ZWRWYWx1ZXMiLCJzZXRMYXN0U2F2ZWRWYWx1ZXMiLCJmb3JtVmFsdWVzIiwic2V0Rm9ybVZhbHVlcyIsImRhdGVGb3JtVmFsdWVzIiwic2V0RGF0ZUZvcm1WYWx1ZXMiLCJwdWJsaXNoRGF0ZXMiLCJpc05ldyIsInNldElzTmV3IiwidmVyc2lvbiIsImZvcm1EZWZpbml0aW9uIiwiZWRpdFN0YXR1cyIsImhhbmRsZUJlZm9yZVVubG9hZCIsImUiLCJyZXR1cm5WYWx1ZSIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZm9ybWF0VmFsdWVzIiwidmFsdWVzIiwiT2JqZWN0Iiwia2V5cyIsImZpbHRlciIsImtleSIsInJlZHVjZSIsImFjYyIsInZhbHVlIiwiZmluZCIsImZpZWxkIiwibmFtZSIsImlkIiwiaGFuZGxlRm9ybUNoYW5nZSIsImhhbmRsZURhdGVGb3JtQ2hhbmdlIiwiaGFuZGxlU2F2ZUZvcm0iLCJtZXNzYWdlIiwib25Db25maXJtIiwidXBkYXRlRG9zc2llclJlcXVlc3QiLCJkb3NzaWVySWQiLCJzdW1tYXJ5IiwiZHluYW1pY0ZpZWxkVmFsdWVzIiwicHVibGlzaEZyb21EYXRlIiwiZnJvbURhdGUiLCJEYXRlIiwidG9JU09TdHJpbmciLCJwdWJsaXNoVG9EYXRlIiwidG9EYXRlIiwicmVzcG9uc2UiLCJoYW5kbGVQdWJsaXNoRG9zc2llciIsInR5cGUiLCJlcnJvciIsImVycm9yTWVzc2FnZXMiLCJoYW5kbGVEZWxldGVEb3NzaWVyIiwicmVxQm9keSIsInN1Y2Nlc3MiLCJwdXNoIiwiaGFuZGxlVW5wdWJsaXNoRG9zc2llciIsInJvb3RGb2xkZXJOb2RlIiwiY29uc29sZSIsImRpdiIsImNsYXNzTmFtZSIsInRpdGxlIiwib25UaXRsZVVwZGF0ZSIsInVwZGF0ZWREb3NzaWVyIiwiY2F0ZWdvcnkiLCJsYWJlbCIsImltYWdlUGF0aCIsImRlY29yYXRpdmVJbWFnZU5vZGUiLCJmdWxsTmFtZSIsImZpZWxkcyIsIm9uQ2hhbmdlIiwib25TdWJtaXQiLCJidXR0b25zIiwiZG9zc2llck5hbWUiLCJhcGlSb290Tm9kZSIsImNhdGVnb3J5SWQiLCJwb3J0YWxVcmwiLCJzdWJtaXRGb3JtIiwiZGlzYWJsZWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/_services/dossier-service.client.ts":
/*!*************************************************!*\
  !*** ./app/_services/dossier-service.client.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDossier: function() { return /* binding */ createDossier; },\n/* harmony export */   deleteDossier: function() { return /* binding */ deleteDossier; },\n/* harmony export */   getDossierLatestVersion: function() { return /* binding */ getDossierLatestVersion; },\n/* harmony export */   publishDossier: function() { return /* binding */ publishDossier; },\n/* harmony export */   unpublishDossier: function() { return /* binding */ unpublishDossier; },\n/* harmony export */   updateDossier: function() { return /* binding */ updateDossier; },\n/* harmony export */   updateDossierImage: function() { return /* binding */ updateDossierImage; },\n/* harmony export */   updateDossierTitle: function() { return /* binding */ updateDossierTitle; }\n/* harmony export */ });\n/* harmony import */ var _http_fetcher_axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/http/fetcher-axios */ \"(app-pages-browser)/./app/_http/fetcher-axios.ts\");\n\nasync function createDossier(clientApi, dossier) {\n    try {\n        return await clientApi.post(\"dossier\", {\n            json: dossier\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function updateDossier(clientApi, updatedDossier) {\n    try {\n        return await clientApi.put(\"dossier\", {\n            json: updatedDossier\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function publishDossier(clientApi, body) {\n    try {\n        return await clientApi.post(\"dossier/publish-dossier\", {\n            json: body\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function deleteDossier(clientApi, body) {\n    try {\n        return await clientApi.delete(\"dossier\", {\n            json: body\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function unpublishDossier(clientApi, body) {\n    try {\n        return await clientApi.post(\"dossier/unpublish-dossier\", {\n            json: body\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function updateDossierTitle(clientApi, body) {\n    try {\n        return await clientApi.put(\"dossier/dossier-title\", {\n            json: body\n        }).json();\n    } catch (error) {\n        return Promise.reject(error);\n    }\n}\nasync function updateDossierImage(apiUrl, id, file, onUploadProgress) {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n    return await (0,_http_fetcher_axios__WEBPACK_IMPORTED_MODULE_0__.put)(\"\".concat(apiUrl, \"/dossier/\").concat(id, \"/image\"), formData, {\n        onUploadProgress,\n        headers: {\n            \"Content-Type\": \"multipart/form-data\"\n        }\n    });\n}\nasync function getDossierLatestVersion(clientApi, dossierId) {\n    return await clientApi.get(\"dossier/\".concat(dossierId, \"/latest-version\")).json();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/_services/dossier-service.client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx":
/*!**********************************************************************************!*\
  !*** ../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx ***!
  \**********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DossierViewLink: function() { return /* binding */ DossierViewLink; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nfunction DossierViewLink(param) {\n    let { url } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)(\"components.dossierViewLink\");\n    if (url) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            target: \"_blank\",\n            rel: \"noreferrer\",\n            href: url,\n            className: \"text-base-00 font-text-regular underline\",\n            children: t(\"view\")\n        }, void 0, false, {\n            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\dossier-view-link\\\\dossier-view-link.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: \"font-text-regular text-base-25 text-sm\",\n        children: t(\"noAssociatedPage\")\n    }, void 0, false, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\dossier-view-link\\\\dossier-view-link.tsx\",\n        lineNumber: 17,\n        columnNumber: 10\n    }, this);\n}\n_s(DossierViewLink, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations\n    ];\n});\n_c = DossierViewLink;\nvar _c;\n$RefreshReg$(_c, \"DossierViewLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9saWJzL3JlYWN0LXVpL3NyYy9jb21wb25lbnRzL2Rvc3NpZXItdmlldy1saW5rL2Rvc3NpZXItdmlldy1saW5rLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEM7QUFNckMsU0FBU0MsZ0JBQWdCLEtBQTZCO1FBQTdCLEVBQUVDLEdBQUcsRUFBd0IsR0FBN0I7O0lBQzlCLE1BQU1DLElBQUlILDBEQUFlQSxDQUFDO0lBRTFCLElBQUlFLEtBQUs7UUFDUCxxQkFDRSw4REFBQ0U7WUFBRUMsUUFBTztZQUFTQyxLQUFJO1lBQWFDLE1BQU1MO1lBQUtNLFdBQVU7c0JBQ3RETCxFQUFFOzs7Ozs7SUFHVDtJQUNBLHFCQUFPLDhEQUFDTTtRQUFFRCxXQUFVO2tCQUEwQ0wsRUFBRTs7Ozs7O0FBQ2xFO0dBWGdCRjs7UUFDSkQsc0RBQWVBOzs7S0FEWEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL2xpYnMvcmVhY3QtdWkvc3JjL2NvbXBvbmVudHMvZG9zc2llci12aWV3LWxpbmsvZG9zc2llci12aWV3LWxpbmsudHN4PzAxNzIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSAnbmV4dC1pbnRsJztcclxuXHJcbmludGVyZmFjZSBEb3NzaWVyVmlld0xpbmtQcm9wcyB7XHJcbiAgdXJsOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBEb3NzaWVyVmlld0xpbmsoeyB1cmwgfTogRG9zc2llclZpZXdMaW5rUHJvcHMpIHtcclxuICBjb25zdCB0ID0gdXNlVHJhbnNsYXRpb25zKCdjb21wb25lbnRzLmRvc3NpZXJWaWV3TGluaycpO1xyXG5cclxuICBpZiAodXJsKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8YSB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub3JlZmVycmVyXCIgaHJlZj17dXJsfSBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UtMDAgZm9udC10ZXh0LXJlZ3VsYXIgdW5kZXJsaW5lXCI+XHJcbiAgICAgICAge3QoJ3ZpZXcnKX1cclxuICAgICAgPC9hPlxyXG4gICAgKTtcclxuICB9XHJcbiAgcmV0dXJuIDxwIGNsYXNzTmFtZT1cImZvbnQtdGV4dC1yZWd1bGFyIHRleHQtYmFzZS0yNSB0ZXh0LXNtXCI+e3QoJ25vQXNzb2NpYXRlZFBhZ2UnKX08L3A+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VUcmFuc2xhdGlvbnMiLCJEb3NzaWVyVmlld0xpbmsiLCJ1cmwiLCJ0IiwiYSIsInRhcmdldCIsInJlbCIsImhyZWYiLCJjbGFzc05hbWUiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../libs/react-ui/src/components/status-box/status-box.tsx":
/*!********************************************************************!*\
  !*** ../../libs/react-ui/src/components/status-box/status-box.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatusBox: function() { return /* binding */ StatusBox; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/../../node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _iprox_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../iprox-ui */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/index.ts\");\n/* harmony import */ var _iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../iprox-ui/components/forms/fields/date-time-picker/date-time-picker */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/components/forms/fields/date-time-picker/date-time-picker.tsx\");\n/* harmony import */ var _dossier_view_link_dossier_view_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dossier-view-link/dossier-view-link */ \"(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst AutoSubmitForm = ()=>{\n    _s();\n    const { values, submitForm } = (0,formik__WEBPACK_IMPORTED_MODULE_1__.useFormikContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (values.fromDate) {\n            submitForm();\n        }\n    }, [\n        values,\n        submitForm\n    ]);\n    return null;\n};\n_s(AutoSubmitForm, \"eluiyIPyaFuYIzYXLcNy1h5e6gs=\", false, function() {\n    return [\n        formik__WEBPACK_IMPORTED_MODULE_1__.useFormikContext\n    ];\n});\n_c = AutoSubmitForm;\nfunction StatusBox(param) {\n    let { children, editStatus, publishDates, dossierId, categoryId, pages, portalUrl, submitForm } = param;\n    _s1();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"components.statusBox\");\n    const initialValues = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            fromDate: (publishDates === null || publishDates === void 0 ? void 0 : publishDates.fromDate) || new Date(),\n            toDate: (publishDates === null || publishDates === void 0 ? void 0 : publishDates.toDate) || null\n        };\n    }, [\n        publishDates\n    ]);\n    const pageSlug = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const page = pages.filter((page)=>page.pageState === \"Published\").find((page)=>{\n            var _page_categories;\n            return (_page_categories = page.categories) === null || _page_categories === void 0 ? void 0 : _page_categories.some((category)=>category.id === categoryId);\n        });\n        return page === null || page === void 0 ? void 0 : page.slug;\n    }, [\n        categoryId,\n        pages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-input bg-highlight sticky top-[110px] h-fit max-w-sm p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base-00 font-heading mb-2 text-2xl font-semibold\",\n                        children: t(\"title\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-base-00 font-text capitalize\",\n                        children: t(editStatus)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            editStatus === \"published\" && pageSlug ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dossier_view_link_dossier_view_link__WEBPACK_IMPORTED_MODULE_5__.DossierViewLink, {\n                    url: \"\".concat(portalUrl, \"/\").concat(pageSlug, \"/\").concat(dossierId)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_1__.Formik, {\n                initialValues: initialValues,\n                onSubmit: (values)=>{\n                    submitForm === null || submitForm === void 0 ? void 0 : submitForm(values);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_1__.Form, {\n                    className: \"my-5 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AutoSubmitForm, {}, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__.DateTimePickerField, {\n                                name: \"fromDate\",\n                                label: t(\"dossierDate\"),\n                                fieldType: _iprox_ui__WEBPACK_IMPORTED_MODULE_3__.FieldType.DateTime,\n                                labelColor: \"text-base-00\",\n                                descriptionColor: \"text-base-25\",\n                                validationRules: [],\n                                popperPlacement: \"left\",\n                                description: t(\"dossierDateDescription\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__.DateTimePickerField, {\n                                name: \"toDate\",\n                                label: t(\"expirationDate\"),\n                                fieldType: _iprox_ui__WEBPACK_IMPORTED_MODULE_3__.FieldType.DateTime,\n                                labelColor: \"text-base-00\",\n                                descriptionColor: \"text-base-25\",\n                                validationRules: [],\n                                popperPlacement: \"left\",\n                                minDate: initialValues.fromDate,\n                                isClearable: true,\n                                description: t(\"expirationDateDescription\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s1(StatusBox, \"YotryI4LkBDdKtSeB6WZy3Aptuw=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations\n    ];\n});\n_c1 = StatusBox;\nvar _c, _c1;\n$RefreshReg$(_c, \"AutoSubmitForm\");\n$RefreshReg$(_c1, \"StatusBox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../libs/react-ui/src/components/status-box/status-box.tsx\n"));

/***/ })

});
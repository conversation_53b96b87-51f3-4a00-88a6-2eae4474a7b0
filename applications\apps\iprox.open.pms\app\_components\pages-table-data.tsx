import { CommonPageDto } from '@/models/page-dto.model';
import { useTranslations } from 'next-intl';
import Link from 'next/link';

import { PageSelector } from '@/components/page-selector';

interface PagesTableDataProps {
  pages: CommonPageDto[];
  selectedPage: CommonPageDto | null;
  setSelectedPage: React.Dispatch<React.SetStateAction<CommonPageDto | null>>;
}

export function PagesTableData({ pages, selectedPage, setSelectedPage }: PagesTableDataProps) {
  const t = useTranslations('pages');

  return pages.map((page, index) => {
    const { id, label } = page;

    const getPageType = (pageType: 'Search' | 'Announcement' | 'Simple') => {
      switch (pageType) {
        case 'Search':
          return t('search');
        case 'Announcement':
          return t('announcements');
        case 'Simple':
          return t('simple');
      }
    };

    return (
      <tr
        key={id}
        className={`font-text text-base-100 flex h-[50px] items-center gap-x-4 text-sm ${
          index % 2 === 0 ? 'bg-base-00' : 'bg-base-10'
        }`}
      >
        <td
          className="max-w-[128px] text-center"
          style={{
            flex: 1 / 5,
          }}
        >
          <PageSelector
            page={label}
            checked={selectedPage?.id === id}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
              const isChecked = event.target.checked;
              if (isChecked) {
                setSelectedPage(page);
              } else {
                setSelectedPage(null);
              }
            }}
          />
        </td>
        <td
          className="line-clamp-1 break-words"
          style={{
            flex: 2 / 5,
          }}
        >
          <Link prefetch={false} href={`/page/${page.id}`} className="cursor-pointer">
            {label}
          </Link>
        </td>
        <td
          className="line-clamp-1 break-words"
          style={{
            flex: 1 / 5,
          }}
        >
          {page.pageState === 'Published' ? t('published') : t('unpublished')}
        </td>
        <td
          className="line-clamp-1 break-words"
          style={{
            flex: 1 / 5,
          }}
        >
          {getPageType(page.pageType as 'Search' | 'Announcement' | 'Simple')}
        </td>
      </tr>
    );
  });
}

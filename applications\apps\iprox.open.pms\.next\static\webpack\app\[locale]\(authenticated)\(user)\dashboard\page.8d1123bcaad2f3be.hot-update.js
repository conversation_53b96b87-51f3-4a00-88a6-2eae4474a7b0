"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(authenticated)/(user)/dashboard/page",{

/***/ "(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx":
/*!**********************************************************************************!*\
  !*** ../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx ***!
  \**********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DossierViewLink: function() { return /* binding */ DossierViewLink; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nfunction DossierViewLink(param) {\n    let { url } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations)(\"components.dossierViewLink\");\n    if (url) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            target: \"_blank\",\n            rel: \"noreferrer\",\n            href: url,\n            className: \"text-base-00 font-text-regular underline\",\n            children: t(\"view\")\n        }, void 0, false, {\n            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\dossier-view-link\\\\dossier-view-link.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: \"font-text-regular text-base-25 text-sm\",\n        children: t(\"noAssociatedPage\")\n    }, void 0, false, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\dossier-view-link\\\\dossier-view-link.tsx\",\n        lineNumber: 17,\n        columnNumber: 10\n    }, this);\n}\n_s(DossierViewLink, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_1__.useTranslations\n    ];\n});\n_c = DossierViewLink;\nvar _c;\n$RefreshReg$(_c, \"DossierViewLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9saWJzL3JlYWN0LXVpL3NyYy9jb21wb25lbnRzL2Rvc3NpZXItdmlldy1saW5rL2Rvc3NpZXItdmlldy1saW5rLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEM7QUFNckMsU0FBU0MsZ0JBQWdCLEtBQTZCO1FBQTdCLEVBQUVDLEdBQUcsRUFBd0IsR0FBN0I7O0lBQzlCLE1BQU1DLElBQUlILDBEQUFlQSxDQUFDO0lBRTFCLElBQUlFLEtBQUs7UUFDUCxxQkFDRSw4REFBQ0U7WUFBRUMsUUFBTztZQUFTQyxLQUFJO1lBQWFDLE1BQU1MO1lBQUtNLFdBQVU7c0JBQ3RETCxFQUFFOzs7Ozs7SUFHVDtJQUNBLHFCQUFPLDhEQUFDTTtRQUFFRCxXQUFVO2tCQUEwQ0wsRUFBRTs7Ozs7O0FBQ2xFO0dBWGdCRjs7UUFDSkQsc0RBQWVBOzs7S0FEWEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL2xpYnMvcmVhY3QtdWkvc3JjL2NvbXBvbmVudHMvZG9zc2llci12aWV3LWxpbmsvZG9zc2llci12aWV3LWxpbmsudHN4PzAxNzIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSAnbmV4dC1pbnRsJztcclxuXHJcbmludGVyZmFjZSBEb3NzaWVyVmlld0xpbmtQcm9wcyB7XHJcbiAgdXJsOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBEb3NzaWVyVmlld0xpbmsoeyB1cmwgfTogRG9zc2llclZpZXdMaW5rUHJvcHMpIHtcclxuICBjb25zdCB0ID0gdXNlVHJhbnNsYXRpb25zKCdjb21wb25lbnRzLmRvc3NpZXJWaWV3TGluaycpO1xyXG5cclxuICBpZiAodXJsKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8YSB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub3JlZmVycmVyXCIgaHJlZj17dXJsfSBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UtMDAgZm9udC10ZXh0LXJlZ3VsYXIgdW5kZXJsaW5lXCI+XHJcbiAgICAgICAge3QoJ3ZpZXcnKX1cclxuICAgICAgPC9hPlxyXG4gICAgKTtcclxuICB9XHJcbiAgcmV0dXJuIDxwIGNsYXNzTmFtZT1cImZvbnQtdGV4dC1yZWd1bGFyIHRleHQtYmFzZS0yNSB0ZXh0LXNtXCI+e3QoJ25vQXNzb2NpYXRlZFBhZ2UnKX08L3A+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VUcmFuc2xhdGlvbnMiLCJEb3NzaWVyVmlld0xpbmsiLCJ1cmwiLCJ0IiwiYSIsInRhcmdldCIsInJlbCIsImhyZWYiLCJjbGFzc05hbWUiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../libs/react-ui/src/components/status-box/status-box.tsx":
/*!********************************************************************!*\
  !*** ../../libs/react-ui/src/components/status-box/status-box.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatusBox: function() { return /* binding */ StatusBox; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/../../node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _iprox_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../iprox-ui */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/index.ts\");\n/* harmony import */ var _iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../iprox-ui/components/forms/fields/date-time-picker/date-time-picker */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/components/forms/fields/date-time-picker/date-time-picker.tsx\");\n/* harmony import */ var _dossier_view_link_dossier_view_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dossier-view-link/dossier-view-link */ \"(app-pages-browser)/../../libs/react-ui/src/components/dossier-view-link/dossier-view-link.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst AutoSubmitForm = ()=>{\n    _s();\n    const { values, submitForm } = (0,formik__WEBPACK_IMPORTED_MODULE_1__.useFormikContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (values.fromDate) {\n            submitForm();\n        }\n    }, [\n        values,\n        submitForm\n    ]);\n    return null;\n};\n_s(AutoSubmitForm, \"eluiyIPyaFuYIzYXLcNy1h5e6gs=\", false, function() {\n    return [\n        formik__WEBPACK_IMPORTED_MODULE_1__.useFormikContext\n    ];\n});\n_c = AutoSubmitForm;\nfunction StatusBox(param) {\n    let { children, editStatus, publishDates, dossierId, categoryId, pages, portalUrl, submitForm } = param;\n    _s1();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"components.statusBox\");\n    const initialValues = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            fromDate: (publishDates === null || publishDates === void 0 ? void 0 : publishDates.fromDate) || new Date(),\n            toDate: (publishDates === null || publishDates === void 0 ? void 0 : publishDates.toDate) || null\n        };\n    }, [\n        publishDates\n    ]);\n    const pageSlug = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const page = pages.filter((page)=>page.pageState === \"Published\").find((page)=>{\n            var _page_categories;\n            return (_page_categories = page.categories) === null || _page_categories === void 0 ? void 0 : _page_categories.some((category)=>category.id === categoryId);\n        });\n        return page === null || page === void 0 ? void 0 : page.slug;\n    }, [\n        categoryId,\n        pages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-input bg-highlight sticky top-[110px] h-fit max-w-sm p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base-00 font-heading mb-2 text-2xl font-semibold\",\n                        children: t(\"title\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-base-00 font-text capitalize\",\n                        children: t(editStatus)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            editStatus === \"published\" && pageSlug ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dossier_view_link_dossier_view_link__WEBPACK_IMPORTED_MODULE_5__.DossierViewLink, {\n                    url: \"\".concat(portalUrl, \"/\").concat(pageSlug, \"/\").concat(dossierId)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_1__.Formik, {\n                initialValues: initialValues,\n                onSubmit: (values)=>{\n                    submitForm === null || submitForm === void 0 ? void 0 : submitForm(values);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_1__.Form, {\n                    className: \"my-5 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AutoSubmitForm, {}, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__.DateTimePickerField, {\n                                name: \"fromDate\",\n                                label: t(\"dossierDate\"),\n                                fieldType: _iprox_ui__WEBPACK_IMPORTED_MODULE_3__.FieldType.DateTime,\n                                labelColor: \"text-base-00\",\n                                descriptionColor: \"text-base-25\",\n                                validationRules: [],\n                                popperPlacement: \"left\",\n                                description: t(\"dossierDateDescription\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_ui_components_forms_fields_date_time_picker_date_time_picker__WEBPACK_IMPORTED_MODULE_4__.DateTimePickerField, {\n                                name: \"toDate\",\n                                label: t(\"expirationDate\"),\n                                fieldType: _iprox_ui__WEBPACK_IMPORTED_MODULE_3__.FieldType.DateTime,\n                                labelColor: \"text-base-00\",\n                                descriptionColor: \"text-base-25\",\n                                validationRules: [],\n                                popperPlacement: \"left\",\n                                minDate: initialValues.fromDate,\n                                isClearable: true,\n                                description: t(\"expirationDateDescription\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\libs\\\\react-ui\\\\src\\\\components\\\\status-box\\\\status-box.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s1(StatusBox, \"YotryI4LkBDdKtSeB6WZy3Aptuw=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations\n    ];\n});\n_c1 = StatusBox;\nvar _c, _c1;\n$RefreshReg$(_c, \"AutoSubmitForm\");\n$RefreshReg$(_c1, \"StatusBox\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../libs/react-ui/src/components/status-box/status-box.tsx\n"));

/***/ })

});
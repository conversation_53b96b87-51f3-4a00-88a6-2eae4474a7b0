import { FieldType } from '../models/form.models';

export const getFieldValue = (fieldType: FieldType, value: unknown) => {
  if (fieldType === FieldType.CheckBox) {
    return value ? true : false;
  }
  if (fieldType === FieldType.Date) {
    return value ?? new Date();
  }
  if (fieldType === FieldType.StringList) {
    return value ?? [];
  }
  if (fieldType === FieldType.PageZonesField) {
    return value ?? [];
  }
  return value ?? '';
};

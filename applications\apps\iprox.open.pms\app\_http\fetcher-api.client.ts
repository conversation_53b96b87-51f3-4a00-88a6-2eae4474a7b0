'use client';

import { AppSettingsContext } from '@iprox/shared-context';
import ky, { NormalizedOptions } from 'ky';
import { KyInstance } from 'ky/distribution/types/ky';
import { getSession } from 'next-auth/react';
import { useContext, useMemo } from 'react';

const redirectToLogin = () => {
  const searchParams = new URLSearchParams();
  searchParams.set('callbackUrl', window.location.pathname + window.location.search);

  // TODO: Hook up redirect and native NextJS navigation.
  // import { redirect } from 'next/navigation';
  // redirect(`/login?${searchParams.toString()}`);

  window.location.replace(`/login?${searchParams.toString()}`);
};

/** Use this to call the iprox.open API in **client** components */
const clientApi = ky.extend({
  headers: {
    'Content-Type': 'application/json',
  },
  hooks: {
    beforeRequest: [
      async (request) => {
        const session = await getSession();
        const token = session?.user?.access_token;

        if (token) {
          request.headers.set('Authorization', `Bearer ${token}`);
        } else {
          redirectToLogin();
        }
      },
    ],
    afterResponse: [
      async (_request: Request, _options: NormalizedOptions, response: Response) => {
        if (response.status === 401) {
          redirectToLogin();
        }
      },
    ],
  },
  timeout: 20000,
});

/** Returns a `KyInstance` which can be used to call the iprox.open API. It takes care Authorization headers and a prefixUrl */
export const useClientApi = (): KyInstance => {
  const context = useContext(AppSettingsContext);

  if (context === undefined) {
    throw new Error('useClientApi must be used within a AppSettingsProvider');
  }

  return useMemo(
    () =>
      clientApi.extend({
        prefixUrl: context.apiUrl,
      }),
    [context]
  );
};

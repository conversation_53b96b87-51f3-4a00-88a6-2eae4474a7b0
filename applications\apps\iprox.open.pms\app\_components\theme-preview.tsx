import { Button, Pagination } from '@iprox/react-ui';

export const ThemePreview = () => {
  return (
    <div>
      <h3 className="mb-4 mt-6">Knoppen</h3>
      <div>
        <div className="mx-2 inline-block">
          <Button variant="primary">Primaire knop</Button>
        </div>
        <div className="mx-2 inline-block">
          <Button variant="secondary">Secundaire knop</Button>
        </div>
        <div className="mx-2 inline-block">
          <Button variant="tertiary">Tertiare knop</Button>
        </div>
      </div>
      <div className="mt-2">
        <div className="mx-2 inline-block">
          <Button disabled variant="primary">
            Primaire knop
          </Button>
        </div>
        <div className="mx-2 inline-block">
          <Button disabled variant="secondary">
            Secundaire knop
          </Button>
        </div>
        <div className="mx-2 inline-block">
          <Button disabled variant="tertiary">
            Tertiare knop
          </Button>
        </div>
      </div>

      <h3 className="mb-4 mt-6">Pagination</h3>
      <div className="grid gap-2">
        <Pagination count={10} start={30} totalCount={70} maxVisiblePages={5} startingIndex={1} />
        <Pagination count={10} start={60} totalCount={70} maxVisiblePages={5} startingIndex={1} />
      </div>
    </div>
  );
};

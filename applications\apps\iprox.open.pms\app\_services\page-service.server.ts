import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { PageListParams } from '@/models/page-list-filter.model';

export async function getSearchPages(): Promise<{
  pages: components['schemas']['SearchPageDto'][];
}> {
  return await serverApi.get(`page/search-page`).json<{
    pages: components['schemas']['SearchPageDto'][];
  }>();
}

export async function getPage(id: string): Promise<components['schemas']['GetPageResponse']> {
  return await serverApi.get(`page/${id}`).json<components['schemas']['GetPageResponse']>();
}

export async function getPagesPaged(params: PageListParams): Promise<components['schemas']['GetPagedPagesResponse']> {
  const paramsString = new URLSearchParams(
    Object.entries(params).reduce<Record<string, string>>((acc, [key, value]: [string, string | undefined]) => {
      if (value) {
        acc[key] = value;
      }

      return acc;
    }, {})
  ).toString();

  return await serverApi.get(`page/paged?${paramsString}`).json<components['schemas']['GetPagedPagesResponse']>();
}

export async function getNavigationStructure(): Promise<components['schemas']['GetNavigationStructureResponse']> {
  return await serverApi
    .get(`page/navigation-structure`)
    .json<components['schemas']['GetNavigationStructureResponse']>();
}

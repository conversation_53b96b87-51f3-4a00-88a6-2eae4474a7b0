import { components } from '@/iprox-open.interface';

export const mapDossiers = (dossiers: components['schemas']['DossierPagedDto'][]) => {
  return dossiers.map((dossier) => ({
    id: dossier.id,
    dossierId: dossier.dossierId,
    name: dossier.title,
    type: dossier.categoryLabel,
    modifiedDate: dossier.modified?.dateTime !== undefined ? new Date(dossier.modified.dateTime) : undefined,
    modifiedBy: dossier.modified?.userDisplayName,
    publishFromDate:
      dossier.publishDates?.fromDate !== undefined ? new Date(dossier.publishDates?.fromDate) : undefined,
    publishedDate: dossier.published?.dateTime !== undefined ? new Date(dossier.published.dateTime) : undefined,
    author: dossier.created.userDisplayName,
    dossierPublishedDate:
      dossier.dossierPublished?.dateTime !== undefined ? new Date(dossier.dossierPublished.dateTime) : undefined,
  }));
};

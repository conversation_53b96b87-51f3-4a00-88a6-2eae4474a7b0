"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/require-in-the-middle";
exports.ids = ["vendor-chunks/require-in-the-middle"];
exports.modules = {

/***/ "(instrument)/../../node_modules/require-in-the-middle/index.js":
/*!*********************************************************!*\
  !*** ../../node_modules/require-in-the-middle/index.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst Module = __webpack_require__(/*! module */ \"module\")\nconst resolve = __webpack_require__(/*! resolve */ \"(instrument)/../../node_modules/resolve/index.js\")\nconst debug = __webpack_require__(/*! debug */ \"(instrument)/../../node_modules/debug/src/index.js\")('require-in-the-middle')\nconst moduleDetailsFromPath = __webpack_require__(/*! module-details-from-path */ \"(instrument)/../../node_modules/module-details-from-path/index.js\")\n\n// Using the default export is discouraged, but kept for backward compatibility.\n// Use this instead:\n//    const { Hook } = require('require-in-the-middle')\nmodule.exports = Hook\nmodule.exports.Hook = Hook\n\n/**\n * Is the given module a \"core\" module?\n * https://nodejs.org/api/modules.html#core-modules\n *\n * @type {(moduleName: string) => boolean}\n */\nlet isCore\nif (Module.isBuiltin) { // Added in node v18.6.0, v16.17.0\n  isCore = Module.isBuiltin\n} else {\n  const [major, minor] = process.versions.node.split('.').map(Number)\n  if (major === 8 && minor < 8) {\n    // For node versions `[8.0, 8.8)` the \"http2\" module was built-in but\n    // behind the `--expose-http2` flag. `resolve` only considers unflagged\n    // modules to be core: https://github.com/browserify/resolve/issues/139\n    // However, for `ExportsCache` to work for \"http2\" we need it to be\n    // considered core.\n    isCore = moduleName => {\n      if (moduleName === 'http2') {\n        return true\n      }\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!resolve.core[moduleName]\n    }\n  } else {\n    isCore = moduleName => {\n      // Prefer `resolve.core` lookup to `resolve.isCore(moduleName)` because\n      // the latter is doing version range matches for every call.\n      return !!resolve.core[moduleName]\n    }\n  }\n}\n\n// 'foo/bar.js' or 'foo/bar/index.js' => 'foo/bar'\nconst normalize = /([/\\\\]index)?(\\.js)?$/\n\n// Cache `onrequire`-patched exports for modules.\n//\n// Exports for built-in (a.k.a. \"core\") modules are stored in an internal Map.\n//\n// Exports for non-core modules are stored on a private field on the `Module`\n// object in `require.cache`. This allows users to delete from `require.cache`\n// to trigger a re-load (and re-run of the hook's `onrequire`) of a module the\n// next time it is required.\n// https://nodejs.org/docs/latest/api/all.html#all_modules_requirecache\n//\n// In some special cases -- e.g. some other `require()` hook swapping out\n// `Module._cache` like `@babel/register` -- a non-core module won't be in\n// `require.cache`. In that case this falls back to caching on the internal Map.\nclass ExportsCache {\n  constructor () {\n    this._localCache = new Map() // <module filename or id> -> <exports>\n    this._kRitmExports = Symbol('RitmExports')\n  }\n\n  has (filename, isBuiltin) {\n    if (this._localCache.has(filename)) {\n      return true\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return !!(mod && this._kRitmExports in mod)\n    } else {\n      return false\n    }\n  }\n\n  get (filename, isBuiltin) {\n    const cachedExports = this._localCache.get(filename)\n    if (cachedExports !== undefined) {\n      return cachedExports\n    } else if (!isBuiltin) {\n      const mod = __webpack_require__.c[filename]\n      return (mod && mod[this._kRitmExports])\n    }\n  }\n\n  set (filename, exports, isBuiltin) {\n    if (isBuiltin) {\n      this._localCache.set(filename, exports)\n    } else if (filename in __webpack_require__.c) {\n      __webpack_require__.c[filename][this._kRitmExports] = exports\n    } else {\n      debug('non-core module is unexpectedly not in require.cache: \"%s\"', filename)\n      this._localCache.set(filename, exports)\n    }\n  }\n}\n\nfunction Hook (modules, options, onrequire) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, onrequire)\n  if (typeof modules === 'function') {\n    onrequire = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    onrequire = options\n    options = null\n  }\n\n  if (typeof Module._resolveFilename !== 'function') {\n    console.error('Error: Expected Module._resolveFilename to be a function (was: %s) - aborting!', typeof Module._resolveFilename)\n    console.error('Please report this error as an issue related to Node.js %s at %s', process.version, (__webpack_require__(/*! ./package.json */ \"(instrument)/../../node_modules/require-in-the-middle/package.json\").bugs.url))\n    return\n  }\n\n  this._cache = new ExportsCache()\n\n  this._unhooked = false\n  this._origRequire = Module.prototype.require\n\n  const self = this\n  const patching = new Set()\n  const internals = options ? options.internals === true : false\n  const hasWhitelist = Array.isArray(modules)\n\n  debug('registering require hook')\n\n  this._require = Module.prototype.require = function (id) {\n    if (self._unhooked === true) {\n      // if the patched require function could not be removed because\n      // someone else patched it after it was patched here, we just\n      // abort and pass the request onwards to the original require\n      debug('ignoring require call - module is soft-unhooked')\n      return self._origRequire.apply(this, arguments)\n    }\n\n    const core = isCore(id)\n    let filename // the string used for caching\n    if (core) {\n      filename = id\n      // If this is a builtin module that can be identified both as 'foo' and\n      // 'node:foo', then prefer 'foo' as the caching key.\n      if (id.startsWith('node:')) {\n        const idWithoutPrefix = id.slice(5)\n        if (isCore(idWithoutPrefix)) {\n          filename = idWithoutPrefix\n        }\n      }\n    } else {\n      try {\n        filename = Module._resolveFilename(id, this)\n      } catch (resolveErr) {\n        // If someone *else* monkey-patches before this monkey-patch, then that\n        // code might expect `require(someId)` to get through so it can be\n        // handled, even if `someId` cannot be resolved to a filename. In this\n        // case, instead of throwing we defer to the underlying `require`.\n        //\n        // For example the Azure Functions Node.js worker module does this,\n        // where `@azure/functions-core` resolves to an internal object.\n        // https://github.com/Azure/azure-functions-nodejs-worker/blob/v3.5.2/src/setupCoreModule.ts#L46-L54\n        debug('Module._resolveFilename(\"%s\") threw %j, calling original Module.require', id, resolveErr.message)\n        return self._origRequire.apply(this, arguments)\n      }\n    }\n\n    let moduleName, basedir\n\n    debug('processing %s module require(\\'%s\\'): %s', core === true ? 'core' : 'non-core', id, filename)\n\n    // return known patched modules immediately\n    if (self._cache.has(filename, core) === true) {\n      debug('returning already patched cached module: %s', filename)\n      return self._cache.get(filename, core)\n    }\n\n    // Check if this module has a patcher in-progress already.\n    // Otherwise, mark this module as patching in-progress.\n    const isPatching = patching.has(filename)\n    if (isPatching === false) {\n      patching.add(filename)\n    }\n\n    const exports = self._origRequire.apply(this, arguments)\n\n    // If it's already patched, just return it as-is.\n    if (isPatching === true) {\n      debug('module is in the process of being patched already - ignoring: %s', filename)\n      return exports\n    }\n\n    // The module has already been loaded,\n    // so the patching mark can be cleaned up.\n    patching.delete(filename)\n\n    if (core === true) {\n      if (hasWhitelist === true && modules.includes(filename) === false) {\n        debug('ignoring core module not on whitelist: %s', filename)\n        return exports // abort if module name isn't on whitelist\n      }\n      moduleName = filename\n    } else if (hasWhitelist === true && modules.includes(filename)) {\n      // whitelist includes the absolute path to the file including extension\n      const parsedPath = path.parse(filename)\n      moduleName = parsedPath.name\n      basedir = parsedPath.dir\n    } else {\n      const stat = moduleDetailsFromPath(filename)\n      if (stat === undefined) {\n        debug('could not parse filename: %s', filename)\n        return exports // abort if filename could not be parsed\n      }\n      moduleName = stat.name\n      basedir = stat.basedir\n\n      const fullModuleName = resolveModuleName(stat)\n\n      debug('resolved filename to module: %s (id: %s, resolved: %s, basedir: %s)', moduleName, id, fullModuleName, basedir)\n\n      // Ex: require('foo/lib/../bar.js')\n      // moduleName = 'foo'\n      // fullModuleName = 'foo/bar'\n      if (hasWhitelist === true && modules.includes(moduleName) === false) {\n        if (modules.includes(fullModuleName) === false) return exports // abort if module name isn't on whitelist\n\n        // if we get to this point, it means that we're requiring a whitelisted sub-module\n        moduleName = fullModuleName\n      } else {\n        // figure out if this is the main module file, or a file inside the module\n        let res\n        try {\n          res = resolve.sync(moduleName, { basedir })\n        } catch (e) {\n          debug('could not resolve module: %s', moduleName)\n          self._cache.set(filename, exports, core)\n          return exports // abort if module could not be resolved (e.g. no main in package.json and no index.js file)\n        }\n\n        if (res !== filename) {\n          // this is a module-internal file\n          if (internals === true) {\n            // use the module-relative path to the file, prefixed by original module name\n            moduleName = moduleName + path.sep + path.relative(basedir, filename)\n            debug('preparing to process require of internal file: %s', moduleName)\n          } else {\n            debug('ignoring require of non-main module file: %s', res)\n            self._cache.set(filename, exports, core)\n            return exports // abort if not main module file\n          }\n        }\n      }\n    }\n\n    // ensure that the cache entry is assigned a value before calling\n    // onrequire, in case calling onrequire requires the same module.\n    self._cache.set(filename, exports, core)\n    debug('calling require hook: %s', moduleName)\n    const patchedExports = onrequire(exports, moduleName, basedir)\n    self._cache.set(filename, patchedExports, core)\n\n    debug('returning module: %s', moduleName)\n    return patchedExports\n  }\n}\n\nHook.prototype.unhook = function () {\n  this._unhooked = true\n  if (this._require === Module.prototype.require) {\n    Module.prototype.require = this._origRequire\n    debug('unhook successful')\n  } else {\n    debug('unhook unsuccessful')\n  }\n}\n\nfunction resolveModuleName (stat) {\n  const normalizedPath = path.sep !== '/' ? stat.path.split(path.sep).join('/') : stat.path\n  return path.posix.join(stat.name, normalizedPath).replace(normalize, '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/require-in-the-middle/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/require-in-the-middle/package.json":
/*!*************************************************************!*\
  !*** ../../node_modules/require-in-the-middle/package.json ***!
  \*************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"require-in-the-middle","version":"7.2.0","description":"Module to hook into the Node.js require function","main":"index.js","types":"types/index.d.ts","dependencies":{"debug":"^4.1.1","module-details-from-path":"^1.0.3","resolve":"^1.22.1"},"devDependencies":{"@babel/core":"^7.9.0","@babel/preset-env":"^7.9.5","@babel/preset-typescript":"^7.9.0","@babel/register":"^7.9.0","ipp-printer":"^1.0.0","patterns":"^1.0.3","roundround":"^0.2.0","semver":"^6.3.0","standard":"^14.3.1","tape":"^4.11.0"},"scripts":{"test":"npm run test:lint && npm run test:tape && npm run test:babel","test:lint":"standard","test:tape":"tape test/*.js","test:babel":"node test/babel/babel-register.js"},"repository":{"type":"git","url":"git+https://github.com/elastic/require-in-the-middle.git"},"keywords":["require","hook","shim","shimmer","shimming","patch","monkey","monkeypatch","module","load"],"files":["types"],"author":"Thomas Watson Steen <<EMAIL>> (https://twitter.com/wa7son)","license":"MIT","bugs":{"url":"https://github.com/elastic/require-in-the-middle/issues"},"homepage":"https://github.com/elastic/require-in-the-middle#readme","engines":{"node":">=8.6.0"}}');

/***/ })

};
;
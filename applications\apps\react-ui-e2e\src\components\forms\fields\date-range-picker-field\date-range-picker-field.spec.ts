import AxeBuilder from '@axe-core/playwright';
import { expect, test } from '@playwright/test';
import { format } from 'date-fns';

import { loadStory } from '../../../../utils/common-utils';

type DateRangePickerFieldArgs = {
  minDate?: string;
  maxDate?: string;
  id?: string;
  name: string;
  label: string;
  value?: string;
  description?: string;
  defaultDateRange?: [string, string];
};

const storyId = 'iprox-ui-forms-fields-date-range-picker-field--default';

test.describe('<DateRangePickerField />', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await loadStory<DateRangePickerFieldArgs>(page, storyId);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should display the date range picker field', async ({ page }) => {
    const props: DateRangePickerFieldArgs = {
      minDate: new Date().toISOString(),
      maxDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
      id: 'date-range-picker',
      name: 'dateRange',
      label: 'Select Date Range',
      description: 'Choose a date range for your event',
    };

    await loadStory<DateRangePickerFieldArgs>(page, storyId, props);

    await expect(page.getByText(props.label)).toBeVisible();
    await expect(page.getByText(props.description)).toBeVisible();

    const dateRangePicker = await page.$(`#${props.id}`);
    expect(dateRangePicker).not.toBeNull();
  });

  test('should display the current month and year in the calendar header when the date range picker field is clicked', async ({
    page,
  }) => {
    const props: DateRangePickerFieldArgs = {
      minDate: new Date().toISOString(),
      maxDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
      id: 'date-range-picker',
      name: 'dateRange',
      label: 'Select Date Range',
      description: 'Choose a date range for your event',
    };

    await loadStory<DateRangePickerFieldArgs>(page, storyId, props);

    await page.click(`#${props.id}`);
    await expect(
      page.getByText(new Date().toLocaleString('default', { month: 'long', year: 'numeric' }))
    ).toBeVisible();
  });

  test('should select start date and end date from the date range picker', async ({ page }) => {
    const props: DateRangePickerFieldArgs = {
      minDate: new Date().toISOString(),
      maxDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
      id: 'date-range-picker',
      name: 'dateRange',
      label: 'Select Date Range',
      description: 'Choose a date range for your event',
    };

    await loadStory<DateRangePickerFieldArgs>(page, storyId, props);

    await page.click(`#${props.id}`);
    await expect(
      page.getByText(new Date().toLocaleString('default', { month: 'long', year: 'numeric' }))
    ).toBeVisible();

    await page
      .getByRole('option', {
        name: `Choose ${format(new Date(), 'EEEE, MMMM do, yyyy')}`,
      })
      .click();

    const nextDate = page.getByRole('option', {
      name: `Choose ${format(new Date(new Date().setDate(new Date().getDate() + 20)), 'EEEE, MMMM do, yyyy')}`,
    });

    if (!(await nextDate.isVisible())) {
      await page.locator('.react-datepicker__navigation--next').click();
      await nextDate.click();
    } else {
      await nextDate.click();
    }
  });
});

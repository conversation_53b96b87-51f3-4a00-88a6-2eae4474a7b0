import { FolderNode } from './models/file-structure';

export const rootFolderNode: FolderNode = {
  nodeId: '24d0b0e9-fff7-4b4e-91db-9a337ae76258',
  isSelected: false,
  children: [
    {
      nodeId: '8c2da1a6-d8ba-4c72-b13c-a53077d549f6',
      parentId: '24d0b0e9-fff7-4b4e-91db-9a337ae76258',
      isSelected: false,
      children: [],
      nodeName: 'A. <PERSON>e beschrijving',
      nodeType: 'Folder',
      totalDescendantSize: 306792,
      isExpanded: false,
      numberOfDescendantFiles: 1,
      numberOfDescendantFolders: 0,
    },
    {
      nodeId: '6eedd453-5d4c-4f22-a3fc-7205481eabc4',
      parentId: '24d0b0e9-fff7-4b4e-91db-9a337ae76258',
      isSelected: false,
      children: [],
      nodeName: 'Beterschap',
      nodeType: 'Folder',
      totalDescendantSize: 0,
      isExpanded: false,
      numberOfDescendantFiles: 1,
      numberOfDescendantFolders: 0,
    },
    {
      nodeId: '32483c71-7acf-4946-842d-091e1f71cd8a',
      parentId: '24d0b0e9-fff7-4b4e-91db-9a337ae76258',
      isSelected: false,
      children: [],
      nodeName: 'E. Grondtekeningen',
      nodeType: 'Folder',
      totalDescendantSize: 372493,
      isExpanded: false,
      numberOfDescendantFiles: 4,
      numberOfDescendantFolders: 0,
    },
    {
      nodeId: 'fe2abdc8-fdc7-4733-8eee-ea3d7fc7a55b',
      parentId: '24d0b0e9-fff7-4b4e-91db-9a337ae76258',
      isSelected: false,
      children: [],
      nodeName: 'F. Kadaster',
      nodeType: 'Folder',
      totalDescendantSize: 9722117,
      isExpanded: false,
      numberOfDescendantFiles: 205,
      numberOfDescendantFolders: 3,
    },
    {
      nodeId: 'f43aa8c5-0522-46ce-9b5b-13cd021811e0',
      parentId: '24d0b0e9-fff7-4b4e-91db-9a337ae76258',
      isSelected: false,
      children: [],
      nodeName: 'G. Onteigeningslijst',
      nodeType: 'Folder',
      totalDescendantSize: 143858,
      isExpanded: false,
      numberOfDescendantFiles: 1,
      numberOfDescendantFolders: 0,
    },
    {
      nodeId: 'e7e762db-f4e6-493f-9597-cc0e2bd36857',
      parentId: '24d0b0e9-fff7-4b4e-91db-9a337ae76258',
      isSelected: false,
      nodeName: 'sample.docx',
      nodeType: 'File',
      size: 13369,
      blobReference: 'f0f7371a-aee7-42b4-932a-846273e61163',
    },
    {
      nodeId: '5a302aa4-5c13-4b6b-aece-80755771eb1c',
      parentId: '24d0b0e9-fff7-4b4e-91db-9a337ae76258',
      isSelected: false,
      nodeName: 'sample.pdf',
      nodeType: 'File',
      size: 3028,
      blobReference: '1863e3d7-7c69-4e69-86c0-6da1850b559a',
    },
  ],
  nodeName: '',
  nodeType: 'Folder',
  totalDescendantSize: 446354291,
  isExpanded: false,
  numberOfDescendantFiles: 400,
  numberOfDescendantFolders: 22,
};

'use client';

import { use<PERSON><PERSON><PERSON><PERSON> } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import {
  deleteDossier,
  getDossierVersions,
  publishDossier,
  unpublishDossier,
  updateDossier,
} from '@/services/dossier-service.client';
import { hasFormChanged } from '@/utils/dossier.form-changed';
import { useDossierFormDefinition } from '@/utils/dossier.form-definition.hook';
import { getErrorMessages } from '@/utils/error-handler';
import { FormBuilder, FormSubmitValues, Text, showToast, useConfirmDialog } from '@iprox/iprox-ui';
import { DossierLiveStatus, DossierStatus, EditStatus, PublishDates, StatusBox } from '@iprox/react-ui';
import { useAppSettings } from '@iprox/shared-context';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import { DossierFileManager } from '@/components/dossier-file-manager';
import { DossierImageField } from '@/components/dossier-image-field';
import { DossierStatusControls } from '@/components/dossier-status-controls';
import { DossierTitle } from '@/components/dossier-title';

interface PageContentProps {
  dossier: components['schemas']['DossierDto'];
  versions: components['schemas']['DossierDto'][];
  pages: components['schemas']['SearchPageDto'][];
}

export default function PageContent({ dossier: initialDossier, versions: initialVersions, pages }: PageContentProps) {
  const t = useTranslations('dossier');
  const settings = useAppSettings();
  const router = useRouter();
  const clientApi = useClientApi();
  const formId = 'dossier-creation-form';
  const { showDialog } = useConfirmDialog();

  const [dossier, setDossier] = useState(initialDossier);
  const [status, setStatus] = useState<DossierStatus>(initialDossier.status);
  const [isSaveAndPublish, setIsSaveAndPublish] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [versions, setVersions] = useState<components['schemas']['DossierDto'][]>(initialVersions);

  const [lastSavedValues, setLastSavedValues] = useState<FormSubmitValues | null>(null);
  const [formValues, setFormValues] = useState<FormSubmitValues | null>(null);
  const [dateFormValues, setDateFormValues] = useState<PublishDates | null>(dossier.publishDates);
  const [isNew, setIsNew] = useState(dossier.version === 1);

  const formDefinition = useDossierFormDefinition(dossier);

  const editStatus = useMemo<EditStatus>(() => {
    switch (true) {
      case status === 'Published':
        return {
          status: 'published',
          date: dossier.published?.dateTime,
        };
      case status === 'Unpublished':
        return {
          status: 'unpublished',
          date: dossier.unpublished?.dateTime,
        };
      case isNew:
        return {
          status: 'new',
          date: dossier.created?.dateTime,
        };
      default:
        return {
          status: 'modified',
          date: versions[0]?.modified?.dateTime,
        };
    }
  }, [status, isNew, dossier.created?.dateTime, dossier.published?.dateTime, dossier.unpublished?.dateTime, versions]);

  const dossierLiveStatus = useMemo<DossierLiveStatus | null>(() => {
    const version = versions.find((version) => version.status !== 'Draft');
    if (!version) {
      return { status: 'Draft', date: dossier.modified?.dateTime };
    }

    const dates: DossierLiveStatus[] = (
      [
        { status: 'Published', date: version.published?.dateTime },
        { status: 'Unpublished', date: version.unpublished?.dateTime },
        { status: 'Deleted', date: version.deleted?.dateTime },
      ] as DossierLiveStatus[]
    ).filter((item) => item.date !== null);

    const mostRecent = dates.reduce<DossierLiveStatus | null>((latest, current) => {
      if (!latest || (current.date && new Date(current.date) > new Date(latest.date!))) {
        return current;
      }
      return latest;
    }, null);

    return mostRecent;
  }, [versions, dossier.modified?.dateTime]);

  const refetchVersions = async () => {
    try {
      const response = await getDossierVersions(clientApi, dossier.dossierId);
      if (response?.dossiers) {
        setVersions(response.dossiers);
      }
    } catch (error) {
      console.error('Failed to refetch versions:', error);
    }
  };

  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasFormChanged(formDefinition, formValues, lastSavedValues)) {
        e.returnValue = '';
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [t, formDefinition, formValues, lastSavedValues]);

  /**
   * format form value object to match the API request body required to save form data
   * @param values form values
   * @returns formatted form values object
   */
  const formatValues = (values: FormSubmitValues) => {
    return Object.keys(values)
      .filter((key) => key !== 'summary' && key !== 'fromDate' && key !== 'toDate')
      .reduce(
        (acc, value) => ({
          ...acc,
          [formDefinition.find((field) => field.name === value)?.id as string]: values[value] as string,
        }),
        {}
      );
  };

  const handleFormChange = (values: FormSubmitValues) => {
    setFormValues(values);
  };

  const handleDateFormChange = (values: PublishDates) => {
    setDateFormValues(values);
  };

  /**
   * handle save form data
   * @param values form values
   */
  const handleSaveForm = async (values: FormSubmitValues) => {
    showDialog({
      message: isSaveAndPublish ? t('confirmation.publish.message') : t('confirmation.save.message'),
      onConfirm: async () => {
        setIsLoading(true);
        const updateDossierRequest: components['schemas']['UpdateDossierCommand'] = {
          dossierId: dossier.dossierId,
          summary: typeof values.summary === 'string' ? values.summary : '',
          dynamicFieldValues: formatValues(values),
          publishFromDate: dateFormValues?.fromDate ? (dateFormValues?.fromDate as string) : new Date().toISOString(),
          publishToDate: dateFormValues?.toDate ? (dateFormValues?.toDate as string) : null,
        };

        try {
          const response = await updateDossier(clientApi, updateDossierRequest);

          if (response) {
            setIsNew(response.dossier.version === 1);
            setLastSavedValues(formValues);
            await refetchVersions();

            if (isSaveAndPublish) {
              handlePublishDossier();
            } else {
              setStatus('Draft');
              showToast(t('success.save'), { type: 'success' });
            }
          }
        } catch (error) {
          const errorMessages = await getErrorMessages(error);
          showToast(errorMessages, { type: 'error' });
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  const handleDeleteDossier = () => {
    showDialog({
      message: t('confirmation.delete.message'),
      onConfirm: async () => {
        try {
          setIsLoading(true);
          const reqBody: components['schemas']['DeleteDossierCommand'] = {
            dossierId: dossier?.dossierId,
          };

          const response = await deleteDossier(clientApi, reqBody);

          if (response && response.success) {
            // Refetch versions to get updated delete timestamp (though we're navigating away)
            await refetchVersions();
            router.push('/dossier/list');
          }

          showToast(t('success.delete'), { type: 'success' });
        } catch (error) {
          const errorMessages = await getErrorMessages(error);
          showToast(errorMessages, { type: 'error' });
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  /**
   * handle publish dossier functionality
   */
  const handlePublishDossier = async () => {
    try {
      const reqBody: components['schemas']['PublishDossierCommand'] = {
        dossierId: dossier?.dossierId,
      };

      const response = await publishDossier(clientApi, reqBody);

      if (response && response.success) {
        setStatus('Published');
        // Refetch versions to get updated publish timestamp
        await refetchVersions();
      }

      showToast(t('success.publish'), { type: 'success' });
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    }
  };

  const handleUnpublishDossier = () => {
    showDialog({
      message: t('confirmation.unpublish.message'),
      onConfirm: async () => {
        try {
          setIsLoading(true);
          const reqBody: components['schemas']['UnpublishDossierCommand'] = {
            dossierId: dossier?.dossierId,
          };

          const response = await unpublishDossier(clientApi, reqBody);

          if (response) {
            setStatus('Draft');
            setIsNew(false);
            // Refetch versions to get updated unpublish timestamp
            await refetchVersions();
          }

          showToast(t('success.unpublish'), { type: 'success' });
        } catch (error) {
          const errorMessages = await getErrorMessages(error);
          showToast(errorMessages, { type: 'error' });
        } finally {
          setIsLoading(false);
        }
      },
    });
  };

  if (!dossier.rootFolderNode) {
    console.error('Root folder node is missing');
  }

  return (
    <div className="grid h-full grid-cols-3 gap-8">
      <div className="col-span-2">
        <Text className="font-heading text-heading mb-9 truncate text-5xl">{dossier.title}</Text>

        <div className="mb-8">
          <DossierTitle
            title={dossier.title}
            dossierId={dossier.dossierId}
            onTitleUpdate={(updatedDossier) => setDossier(updatedDossier)}
          />
        </div>

        <div className="mb-8">
          <Text className="font-heading text-heading mb-2 text-lg font-bold">{t('category')}</Text>
          <Text className="font-text-regular text-content-lite text-sm">{dossier?.category?.label || ''}</Text>
        </div>
        <div className="border-b-highlight mb-10 border-b" />
        <div>
          <div className="mb-8 mt-8">
            <DossierImageField dossierId={dossier.dossierId} imagePath={dossier.decorativeImageNode?.fullName} />
          </div>
          <div className="mb-8">
            <FormBuilder
              fields={formDefinition}
              onChange={handleFormChange}
              onSubmit={handleSaveForm}
              formId={formId}
              buttons={<></>}
            />
          </div>
          {dossier.rootFolderNode && (
            <DossierFileManager
              dossierId={dossier.dossierId}
              dossierName={dossier.title}
              apiRootNode={dossier.rootFolderNode}
            />
          )}
        </div>
      </div>
      <div className="col-span-1">
        <StatusBox
          editStatus={editStatus}
          dossierLiveStatus={dossierLiveStatus}
          publishDates={dateFormValues}
          pages={pages}
          dossierId={dossier.dossierId}
          categoryId={dossier.categoryId}
          portalUrl={settings.portalUrl}
          submitForm={handleDateFormChange}
        >
          <DossierStatusControls
            dossierId={dossier.dossierId}
            formId={formId}
            disabled={isLoading}
            status={status}
            handleUnpublishDossier={handleUnpublishDossier}
            handleDeleteDossier={handleDeleteDossier}
            setIsSaveAndPublish={setIsSaveAndPublish}
          />
        </StatusBox>
      </div>
    </div>
  );
}

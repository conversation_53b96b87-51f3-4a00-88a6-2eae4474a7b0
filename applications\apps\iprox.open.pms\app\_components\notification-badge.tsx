import { Text } from '@iprox/iprox-ui';

interface NotificationBadgeProps {
  count?: number;
}

export function NotificationBadge({ count }: NotificationBadgeProps) {
  if (!count) {
    return null;
  }
  return (
    <div className="bg-error flex h-5 w-5 flex-row items-center justify-center rounded-full">
      <Text className={`font-text text-base-00 leading-3 ${count > 99 ? 'text-[8px]' : 'text-[10px]'}`}>
        {count > 99 ? '99+' : count}
      </Text>
    </div>
  );
}

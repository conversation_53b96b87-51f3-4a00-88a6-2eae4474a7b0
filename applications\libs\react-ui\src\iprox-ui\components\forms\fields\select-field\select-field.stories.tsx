import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { SelectField } from './select-field';

const options = [
  {
    label: 'Some long names as Label 01',
    value: 'label-1',
  },
  {
    label: 'Some long names as Label 02',
    value: 'label-2',
  },
  {
    label: 'Some long names as Label 03',
    value: 'label-3',
  },
  {
    label: 'Some long names as Label 04',
    value: 'label-4',
  },
  {
    label: 'Some long names as Label 05',
    value: 'label-5',
  },
  {
    label: 'Some long names as Label 06',
    value: 'label-6',
  },
  {
    label: 'Some long names as Label 07',
    value: 'label-7',
  },
];

const meta: Meta<typeof SelectField> = {
  title: 'iprox-ui/forms/fields/selectfield',
  component: SelectField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof SelectField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'select-field',
    options,
    fieldType: FieldType.Select,
    isMulti: false,
  },
};

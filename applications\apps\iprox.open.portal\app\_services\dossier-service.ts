import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { HTTPError } from 'ky';

export async function getDossier(
  id: string
): Promise<components['schemas']['GetLatestPublicDossierResponse'] | undefined> {
  try {
    return await serverApi.get(`dossier/${id}`).json<components['schemas']['GetLatestPublicDossierResponse']>();
  } catch (error) {
    if (error instanceof HTTPError && error.response.status === 404) {
      return undefined;
    }
    return Promise.reject(error);
  }
}

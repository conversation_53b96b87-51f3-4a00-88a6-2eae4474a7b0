import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ParsedUrlQueryInput } from 'querystring';
import { memo } from 'react';

import { Text } from '../../text/text';

interface PaginationButtonProps {
  page: string;
  query: ParsedUrlQueryInput | undefined;
  isCurrent: boolean;
  type: 'button' | 'ellipsis';
}

export const PaginationButton = memo(({ page, query, isCurrent, type }: PaginationButtonProps) => {
  const t = useTranslations('components');
  const pathname = usePathname();

  if (isCurrent) {
    return (
      <span
        key={page}
        className="font-heading bg-primary text-primary-content border-primary flex h-12 w-12 items-center justify-center rounded-[1px] border text-lg font-medium"
        aria-current={'page'}
      >
        {t('pagination.pageDisplayLabel', { page })}
      </span>
    );
  }

  if (type === 'button') {
    return (
      <Link
        prefetch={false}
        key={page}
        href={{
          pathname,
          query,
        }}
        className="font-heading text-base-100 hover:bg-highlight hover:text-base-00 flex h-12 w-12 items-center justify-center rounded-[1px] text-lg font-medium"
        aria-label={t('pagination.pageAriaLabel', { page })}
      >
        {t('pagination.pageDisplayLabel', { page })}
      </Link>
    );
  }

  if (type === 'ellipsis') {
    return (
      <Text className="font-heading text-base-100 flex h-12 w-12 items-center justify-center text-lg font-medium">
        {t('pagination.pageDisplayLabel', { page })}
      </Text>
    );
  }

  return null;
});

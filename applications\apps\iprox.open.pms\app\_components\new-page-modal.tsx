import { useClientApi } from '@/http/fetcher-api.client';
import { createPage } from '@/services/page-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { convertToSlugCase } from '@/utils/text-case-utils';
import {
  Button,
  FieldType,
  FieldValuesMap,
  FormBuilder,
  FormSubmitValues,
  Modal,
  ValidationRuleType,
  showToast,
} from '@iprox/iprox-ui';
import { FormikProps } from 'formik';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';

import { OptionCard } from '@/components/option-card';

interface NewPageModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function NewPageModal({ isOpen, onClose }: NewPageModalProps) {
  const t = useTranslations('pages');
  const clientApi = useClientApi();
  const router = useRouter();

  const [activeTab, setActiveTab] = useState<'Search' | 'Announcement' | 'Simple' | undefined>();
  const [isCreatingPage, setIsCreatingPage] = useState(false);
  const [shouldAutofillSlug, setShouldAutofillSlug] = useState(true);

  const handleChange = (values: FormSubmitValues, formik: FormikProps<FieldValuesMap<typeof formFields>>) => {
    const currentSlug = typeof values.slug === 'string' ? values.slug : '';
    const newSlug = typeof values.title === 'string' ? convertToSlugCase(values.title) : '';

    if (currentSlug === newSlug) {
      setShouldAutofillSlug(true);
    }

    if (shouldAutofillSlug && currentSlug !== newSlug) {
      formik.setFieldValue('slug', newSlug);
    }
  };

  const handleSlugChange = () => {
    setShouldAutofillSlug(false);
  };

  const formFields = useMemo(() => {
    const minimumLength = 1;
    const maximumLength = 60;

    return [
      {
        name: 'title',
        label: t('title'),
        description: t('titleHelperText', { min: minimumLength, max: maximumLength }),
        fieldType: FieldType.Text,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateStringLength,
            ruleValue: { minimumLength, maximumLength },
          },
        ],
      },
      {
        name: 'slug',
        label: t('slug'),
        description: t('slugHelperText'),
        fieldType: FieldType.Text,
        onChange: handleSlugChange,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateStringLength,
            ruleValue: { minimumLength, maximumLength },
          },
        ],
      },
    ];
  }, [t]);

  const handleSubmit = async (values: FormSubmitValues) => {
    if (activeTab) {
      try {
        setIsCreatingPage(true);
        const response = await createPage(clientApi, activeTab, {
          label: typeof values.title === 'string' ? values.title.trim() : '',
          slug: typeof values.slug === 'string' ? values.slug.trim() : '',
        });
        router.push(`/page/${response.page.id}`);
      } catch (error) {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      } finally {
        setTimeout(() => {
          setIsCreatingPage(false);
        }, 3000);
      }
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {
        onClose();
        setActiveTab(undefined);
      }}
    >
      <div className="px-[10px] pb-[10px] pt-[50px]">
        <div className="mb-14 flex flex-row items-center gap-x-10">
          <OptionCard
            isActive={activeTab === 'Announcement'}
            onClick={() => setActiveTab('Announcement')}
            icon="ClipboardDocumentListIcon"
            text={t('announcements')}
          />
          <OptionCard
            isActive={activeTab === 'Search'}
            onClick={() => setActiveTab('Search')}
            icon="MagnifyingGlassIcon"
            text={t('searchPage')}
          />
          <OptionCard
            isActive={activeTab === 'Simple'}
            onClick={() => setActiveTab('Simple')}
            icon="DocumentTextIcon"
            text={t('simplePage')}
          />
        </div>

        <div>
          <FormBuilder
            fields={formFields}
            onChange={(values, formik) => handleChange(values, formik)}
            onSubmit={handleSubmit}
            buttons={
              <div className="mt-9 flex justify-center">
                <Button variant="primary" type="submit" disabled={isCreatingPage || !activeTab}>
                  {t('continue')}
                </Button>
              </div>
            }
          />
        </div>
      </div>
    </Modal>
  );
}

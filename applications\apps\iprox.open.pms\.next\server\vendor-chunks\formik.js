"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/formik";
exports.ids = ["vendor-chunks/formik"];
exports.modules = {

/***/ "(ssr)/../../node_modules/formik/dist/formik.esm.js":
/*!****************************************************!*\
  !*** ../../node_modules/formik/dist/formik.esm.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorMessage: () => (/* binding */ ErrorMessage),\n/* harmony export */   FastField: () => (/* binding */ FastField),\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FieldArray: () => (/* binding */ FieldArray),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   Formik: () => (/* binding */ Formik),\n/* harmony export */   FormikConsumer: () => (/* binding */ FormikConsumer),\n/* harmony export */   FormikContext: () => (/* binding */ FormikContext),\n/* harmony export */   FormikProvider: () => (/* binding */ FormikProvider),\n/* harmony export */   connect: () => (/* binding */ connect),\n/* harmony export */   getActiveElement: () => (/* binding */ getActiveElement),\n/* harmony export */   getIn: () => (/* binding */ getIn),\n/* harmony export */   insert: () => (/* binding */ insert),\n/* harmony export */   isEmptyArray: () => (/* binding */ isEmptyArray),\n/* harmony export */   isEmptyChildren: () => (/* binding */ isEmptyChildren),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isInputEvent: () => (/* binding */ isInputEvent),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   isNaN: () => (/* binding */ isNaN$1),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   move: () => (/* binding */ move),\n/* harmony export */   prepareDataForValidation: () => (/* binding */ prepareDataForValidation),\n/* harmony export */   replace: () => (/* binding */ replace),\n/* harmony export */   setIn: () => (/* binding */ setIn),\n/* harmony export */   setNestedObjectValues: () => (/* binding */ setNestedObjectValues),\n/* harmony export */   swap: () => (/* binding */ swap),\n/* harmony export */   useField: () => (/* binding */ useField),\n/* harmony export */   useFormik: () => (/* binding */ useFormik),\n/* harmony export */   useFormikContext: () => (/* binding */ useFormikContext),\n/* harmony export */   validateYupSchema: () => (/* binding */ validateYupSchema),\n/* harmony export */   withFormik: () => (/* binding */ withFormik),\n/* harmony export */   yupToFormErrors: () => (/* binding */ yupToFormErrors)\n/* harmony export */ });\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! deepmerge */ \"(ssr)/../../node_modules/formik/node_modules/deepmerge/dist/es.js\");\n/* harmony import */ var lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash-es/isPlainObject */ \"(ssr)/../../node_modules/lodash-es/isPlainObject.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-fast-compare */ \"(ssr)/../../node_modules/formik/node_modules/react-fast-compare/index.js\");\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_fast_compare__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var tiny_warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tiny-warning */ \"(ssr)/../../node_modules/tiny-warning/dist/tiny-warning.esm.js\");\n/* harmony import */ var lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es/clone */ \"(ssr)/../../node_modules/lodash-es/clone.js\");\n/* harmony import */ var lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es/toPath */ \"(ssr)/../../node_modules/lodash-es/toPath.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/../../node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash-es/cloneDeep */ \"(ssr)/../../node_modules/lodash-es/cloneDeep.js\");\n\n\n\n\n\n\n\n\n\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nvar FormikContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nFormikContext.displayName = 'FormikContext';\nvar FormikProvider = FormikContext.Provider;\nvar FormikConsumer = FormikContext.Consumer;\nfunction useFormikContext() {\n  var formik = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FormikContext);\n  !!!formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Formik context is undefined, please verify you are calling useFormikContext() as child of a <Formik> component.\") : 0 : void 0;\n  return formik;\n}\n\n/** @private is the value an empty array? */\n\nvar isEmptyArray = function isEmptyArray(value) {\n  return Array.isArray(value) && value.length === 0;\n};\n/** @private is the given object a Function? */\n\nvar isFunction = function isFunction(obj) {\n  return typeof obj === 'function';\n};\n/** @private is the given object an Object? */\n\nvar isObject = function isObject(obj) {\n  return obj !== null && typeof obj === 'object';\n};\n/** @private is the given object an integer? */\n\nvar isInteger = function isInteger(obj) {\n  return String(Math.floor(Number(obj))) === obj;\n};\n/** @private is the given object a string? */\n\nvar isString = function isString(obj) {\n  return Object.prototype.toString.call(obj) === '[object String]';\n};\n/** @private is the given object a NaN? */\n// eslint-disable-next-line no-self-compare\n\nvar isNaN$1 = function isNaN(obj) {\n  return obj !== obj;\n};\n/** @private Does a React component have exactly 0 children? */\n\nvar isEmptyChildren = function isEmptyChildren(children) {\n  return react__WEBPACK_IMPORTED_MODULE_1__.Children.count(children) === 0;\n};\n/** @private is the given object/value a promise? */\n\nvar isPromise = function isPromise(value) {\n  return isObject(value) && isFunction(value.then);\n};\n/** @private is the given object/value a type of synthetic event? */\n\nvar isInputEvent = function isInputEvent(value) {\n  return value && isObject(value) && isObject(value.target);\n};\n/**\r\n * Same as document.activeElement but wraps in a try-catch block. In IE it is\r\n * not safe to call document.activeElement if there is nothing focused.\r\n *\r\n * The activeElement will be null only if the document or document body is not\r\n * yet defined.\r\n *\r\n * @param {?Document} doc Defaults to current document.\r\n * @return {Element | null}\r\n * @see https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/core/dom/getActiveElement.js\r\n */\n\nfunction getActiveElement(doc) {\n  doc = doc || (typeof document !== 'undefined' ? document : undefined);\n\n  if (typeof doc === 'undefined') {\n    return null;\n  }\n\n  try {\n    return doc.activeElement || doc.body;\n  } catch (e) {\n    return doc.body;\n  }\n}\n/**\r\n * Deeply get a value from an object via its path.\r\n */\n\nfunction getIn(obj, key, def, p) {\n  if (p === void 0) {\n    p = 0;\n  }\n\n  var path = (0,lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key);\n\n  while (obj && p < path.length) {\n    obj = obj[path[p++]];\n  } // check if path is not in the end\n\n\n  if (p !== path.length && !obj) {\n    return def;\n  }\n\n  return obj === undefined ? def : obj;\n}\n/**\r\n * Deeply set a value from in object via it's path. If the value at `path`\r\n * has changed, return a shallow copy of obj with `value` set at `path`.\r\n * If `value` has not changed, return the original `obj`.\r\n *\r\n * Existing objects / arrays along `path` are also shallow copied. Sibling\r\n * objects along path retain the same internal js reference. Since new\r\n * objects / arrays are only created along `path`, we can test if anything\r\n * changed in a nested structure by comparing the object's reference in\r\n * the old and new object, similar to how russian doll cache invalidation\r\n * works.\r\n *\r\n * In earlier versions of this function, which used cloneDeep, there were\r\n * issues whereby settings a nested value would mutate the parent\r\n * instead of creating a new object. `clone` avoids that bug making a\r\n * shallow copy of the objects along the update path\r\n * so no object is mutated in place.\r\n *\r\n * Before changing this function, please read through the following\r\n * discussions.\r\n *\r\n * @see https://github.com/developit/linkstate\r\n * @see https://github.com/jaredpalmer/formik/pull/123\r\n */\n\nfunction setIn(obj, path, value) {\n  var res = (0,lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(obj); // this keeps inheritance when obj is a class\n\n  var resVal = res;\n  var i = 0;\n  var pathArray = (0,lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(path);\n\n  for (; i < pathArray.length - 1; i++) {\n    var currentPath = pathArray[i];\n    var currentObj = getIn(obj, pathArray.slice(0, i + 1));\n\n    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n      resVal = resVal[currentPath] = (0,lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(currentObj);\n    } else {\n      var nextPath = pathArray[i + 1];\n      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n    }\n  } // Return original object if new value is the same as current\n\n\n  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n    return obj;\n  }\n\n  if (value === undefined) {\n    delete resVal[pathArray[i]];\n  } else {\n    resVal[pathArray[i]] = value;\n  } // If the path array has a single element, the loop did not run.\n  // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n\n\n  if (i === 0 && value === undefined) {\n    delete res[pathArray[i]];\n  }\n\n  return res;\n}\n/**\r\n * Recursively a set the same value for all keys and arrays nested object, cloning\r\n * @param object\r\n * @param value\r\n * @param visited\r\n * @param response\r\n */\n\nfunction setNestedObjectValues(object, value, visited, response) {\n  if (visited === void 0) {\n    visited = new WeakMap();\n  }\n\n  if (response === void 0) {\n    response = {};\n  }\n\n  for (var _i = 0, _Object$keys = Object.keys(object); _i < _Object$keys.length; _i++) {\n    var k = _Object$keys[_i];\n    var val = object[k];\n\n    if (isObject(val)) {\n      if (!visited.get(val)) {\n        visited.set(val, true); // In order to keep array values consistent for both dot path  and\n        // bracket syntax, we need to check if this is an array so that\n        // this will output  { friends: [true] } and not { friends: { \"0\": true } }\n\n        response[k] = Array.isArray(val) ? [] : {};\n        setNestedObjectValues(val, value, visited, response[k]);\n      }\n    } else {\n      response[k] = value;\n    }\n  }\n\n  return response;\n}\n\nfunction formikReducer(state, msg) {\n  switch (msg.type) {\n    case 'SET_VALUES':\n      return _extends({}, state, {\n        values: msg.payload\n      });\n\n    case 'SET_TOUCHED':\n      return _extends({}, state, {\n        touched: msg.payload\n      });\n\n    case 'SET_ERRORS':\n      if (react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(state.errors, msg.payload)) {\n        return state;\n      }\n\n      return _extends({}, state, {\n        errors: msg.payload\n      });\n\n    case 'SET_STATUS':\n      return _extends({}, state, {\n        status: msg.payload\n      });\n\n    case 'SET_ISSUBMITTING':\n      return _extends({}, state, {\n        isSubmitting: msg.payload\n      });\n\n    case 'SET_ISVALIDATING':\n      return _extends({}, state, {\n        isValidating: msg.payload\n      });\n\n    case 'SET_FIELD_VALUE':\n      return _extends({}, state, {\n        values: setIn(state.values, msg.payload.field, msg.payload.value)\n      });\n\n    case 'SET_FIELD_TOUCHED':\n      return _extends({}, state, {\n        touched: setIn(state.touched, msg.payload.field, msg.payload.value)\n      });\n\n    case 'SET_FIELD_ERROR':\n      return _extends({}, state, {\n        errors: setIn(state.errors, msg.payload.field, msg.payload.value)\n      });\n\n    case 'RESET_FORM':\n      return _extends({}, state, msg.payload);\n\n    case 'SET_FORMIK_STATE':\n      return msg.payload(state);\n\n    case 'SUBMIT_ATTEMPT':\n      return _extends({}, state, {\n        touched: setNestedObjectValues(state.values, true),\n        isSubmitting: true,\n        submitCount: state.submitCount + 1\n      });\n\n    case 'SUBMIT_FAILURE':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n\n    case 'SUBMIT_SUCCESS':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n\n    default:\n      return state;\n  }\n} // Initial empty states // objects\n\n\nvar emptyErrors = {};\nvar emptyTouched = {};\nfunction useFormik(_ref) {\n  var _ref$validateOnChange = _ref.validateOnChange,\n      validateOnChange = _ref$validateOnChange === void 0 ? true : _ref$validateOnChange,\n      _ref$validateOnBlur = _ref.validateOnBlur,\n      validateOnBlur = _ref$validateOnBlur === void 0 ? true : _ref$validateOnBlur,\n      _ref$validateOnMount = _ref.validateOnMount,\n      validateOnMount = _ref$validateOnMount === void 0 ? false : _ref$validateOnMount,\n      isInitialValid = _ref.isInitialValid,\n      _ref$enableReinitiali = _ref.enableReinitialize,\n      enableReinitialize = _ref$enableReinitiali === void 0 ? false : _ref$enableReinitiali,\n      onSubmit = _ref.onSubmit,\n      rest = _objectWithoutPropertiesLoose(_ref, [\"validateOnChange\", \"validateOnBlur\", \"validateOnMount\", \"isInitialValid\", \"enableReinitialize\", \"onSubmit\"]);\n\n  var props = _extends({\n    validateOnChange: validateOnChange,\n    validateOnBlur: validateOnBlur,\n    validateOnMount: validateOnMount,\n    onSubmit: onSubmit\n  }, rest);\n\n  var initialValues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialValues);\n  var initialErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialErrors || emptyErrors);\n  var initialTouched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialTouched || emptyTouched);\n  var initialStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialStatus);\n  var isMounted = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  var fieldRegistry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !(typeof isInitialValid === 'undefined') ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'isInitialValid has been deprecated and will be removed in future versions of Formik. Please use initialErrors or validateOnMount instead.') : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  }\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    isMounted.current = true;\n    return function () {\n      isMounted.current = false;\n    };\n  }, []);\n\n  var _React$useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),\n      setIteration = _React$useState[1];\n\n  var stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n    values: props.initialValues,\n    errors: props.initialErrors || emptyErrors,\n    touched: props.initialTouched || emptyTouched,\n    status: props.initialStatus,\n    isSubmitting: false,\n    isValidating: false,\n    submitCount: 0\n  });\n  var state = stateRef.current;\n  var dispatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (action) {\n    var prev = stateRef.current;\n    stateRef.current = formikReducer(prev, action); // force rerender\n\n    if (prev !== stateRef.current) setIteration(function (x) {\n      return x + 1;\n    });\n  }, []);\n  var runValidateHandler = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values, field) {\n    return new Promise(function (resolve, reject) {\n      var maybePromisedErrors = props.validate(values, field);\n\n      if (maybePromisedErrors == null) {\n        // use loose null check here on purpose\n        resolve(emptyErrors);\n      } else if (isPromise(maybePromisedErrors)) {\n        maybePromisedErrors.then(function (errors) {\n          resolve(errors || emptyErrors);\n        }, function (actualException) {\n          if (true) {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validate />\", actualException);\n          }\n\n          reject(actualException);\n        });\n      } else {\n        resolve(maybePromisedErrors);\n      }\n    });\n  }, [props.validate]);\n  /**\r\n   * Run validation against a Yup schema and optionally run a function if successful\r\n   */\n\n  var runValidationSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values, field) {\n    var validationSchema = props.validationSchema;\n    var schema = isFunction(validationSchema) ? validationSchema(field) : validationSchema;\n    var promise = field && schema.validateAt ? schema.validateAt(field, values) : validateYupSchema(values, schema);\n    return new Promise(function (resolve, reject) {\n      promise.then(function () {\n        resolve(emptyErrors);\n      }, function (err) {\n        // Yup will throw a validation error if validation fails. We catch those and\n        // resolve them into Formik errors. We can sniff if something is a Yup error\n        // by checking error.name.\n        // @see https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n        if (err.name === 'ValidationError') {\n          resolve(yupToFormErrors(err));\n        } else {\n          // We throw any other errors\n          if (true) {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validationSchema />\", err);\n          }\n\n          reject(err);\n        }\n      });\n    });\n  }, [props.validationSchema]);\n  var runSingleFieldLevelValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (field, value) {\n    return new Promise(function (resolve) {\n      return resolve(fieldRegistry.current[field].validate(value));\n    });\n  }, []);\n  var runFieldLevelValidations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values) {\n    var fieldKeysWithValidation = Object.keys(fieldRegistry.current).filter(function (f) {\n      return isFunction(fieldRegistry.current[f].validate);\n    }); // Construct an array with all of the field validation functions\n\n    var fieldValidations = fieldKeysWithValidation.length > 0 ? fieldKeysWithValidation.map(function (f) {\n      return runSingleFieldLevelValidation(f, getIn(values, f));\n    }) : [Promise.resolve('DO_NOT_DELETE_YOU_WILL_BE_FIRED')]; // use special case ;)\n\n    return Promise.all(fieldValidations).then(function (fieldErrorsList) {\n      return fieldErrorsList.reduce(function (prev, curr, index) {\n        if (curr === 'DO_NOT_DELETE_YOU_WILL_BE_FIRED') {\n          return prev;\n        }\n\n        if (curr) {\n          prev = setIn(prev, fieldKeysWithValidation[index], curr);\n        }\n\n        return prev;\n      }, {});\n    });\n  }, [runSingleFieldLevelValidation]); // Run all validations and return the result\n\n  var runAllValidations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values) {\n    return Promise.all([runFieldLevelValidations(values), props.validationSchema ? runValidationSchema(values) : {}, props.validate ? runValidateHandler(values) : {}]).then(function (_ref2) {\n      var fieldErrors = _ref2[0],\n          schemaErrors = _ref2[1],\n          validateErrors = _ref2[2];\n      var combinedErrors = deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"].all([fieldErrors, schemaErrors, validateErrors], {\n        arrayMerge: arrayMerge\n      });\n      return combinedErrors;\n    });\n  }, [props.validate, props.validationSchema, runFieldLevelValidations, runValidateHandler, runValidationSchema]); // Run all validations methods and update state accordingly\n\n  var validateFormWithHighPriority = useEventCallback(function (values) {\n    if (values === void 0) {\n      values = state.values;\n    }\n\n    dispatch({\n      type: 'SET_ISVALIDATING',\n      payload: true\n    });\n    return runAllValidations(values).then(function (combinedErrors) {\n      if (!!isMounted.current) {\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n        dispatch({\n          type: 'SET_ERRORS',\n          payload: combinedErrors\n        });\n      }\n\n      return combinedErrors;\n    });\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (validateOnMount && isMounted.current === true && react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, props.initialValues)) {\n      validateFormWithHighPriority(initialValues.current);\n    }\n  }, [validateOnMount, validateFormWithHighPriority]);\n  var resetForm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (nextState) {\n    var values = nextState && nextState.values ? nextState.values : initialValues.current;\n    var errors = nextState && nextState.errors ? nextState.errors : initialErrors.current ? initialErrors.current : props.initialErrors || {};\n    var touched = nextState && nextState.touched ? nextState.touched : initialTouched.current ? initialTouched.current : props.initialTouched || {};\n    var status = nextState && nextState.status ? nextState.status : initialStatus.current ? initialStatus.current : props.initialStatus;\n    initialValues.current = values;\n    initialErrors.current = errors;\n    initialTouched.current = touched;\n    initialStatus.current = status;\n\n    var dispatchFn = function dispatchFn() {\n      dispatch({\n        type: 'RESET_FORM',\n        payload: {\n          isSubmitting: !!nextState && !!nextState.isSubmitting,\n          errors: errors,\n          touched: touched,\n          status: status,\n          values: values,\n          isValidating: !!nextState && !!nextState.isValidating,\n          submitCount: !!nextState && !!nextState.submitCount && typeof nextState.submitCount === 'number' ? nextState.submitCount : 0\n        }\n      });\n    };\n\n    if (props.onReset) {\n      var maybePromisedOnReset = props.onReset(state.values, imperativeMethods);\n\n      if (isPromise(maybePromisedOnReset)) {\n        maybePromisedOnReset.then(dispatchFn);\n      } else {\n        dispatchFn();\n      }\n    } else {\n      dispatchFn();\n    }\n  }, [props.initialErrors, props.initialStatus, props.initialTouched]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, props.initialValues)) {\n      if (enableReinitialize) {\n        initialValues.current = props.initialValues;\n        resetForm();\n\n        if (validateOnMount) {\n          validateFormWithHighPriority(initialValues.current);\n        }\n      }\n    }\n  }, [enableReinitialize, props.initialValues, resetForm, validateOnMount, validateFormWithHighPriority]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialErrors.current, props.initialErrors)) {\n      initialErrors.current = props.initialErrors || emptyErrors;\n      dispatch({\n        type: 'SET_ERRORS',\n        payload: props.initialErrors || emptyErrors\n      });\n    }\n  }, [enableReinitialize, props.initialErrors]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialTouched.current, props.initialTouched)) {\n      initialTouched.current = props.initialTouched || emptyTouched;\n      dispatch({\n        type: 'SET_TOUCHED',\n        payload: props.initialTouched || emptyTouched\n      });\n    }\n  }, [enableReinitialize, props.initialTouched]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialStatus.current, props.initialStatus)) {\n      initialStatus.current = props.initialStatus;\n      dispatch({\n        type: 'SET_STATUS',\n        payload: props.initialStatus\n      });\n    }\n  }, [enableReinitialize, props.initialStatus, props.initialTouched]);\n  var validateField = useEventCallback(function (name) {\n    // This will efficiently validate a single field by avoiding state\n    // changes if the validation function is synchronous. It's different from\n    // what is called when using validateForm.\n    if (fieldRegistry.current[name] && isFunction(fieldRegistry.current[name].validate)) {\n      var value = getIn(state.values, name);\n      var maybePromise = fieldRegistry.current[name].validate(value);\n\n      if (isPromise(maybePromise)) {\n        // Only flip isValidating if the function is async.\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: true\n        });\n        return maybePromise.then(function (x) {\n          return x;\n        }).then(function (error) {\n          dispatch({\n            type: 'SET_FIELD_ERROR',\n            payload: {\n              field: name,\n              value: error\n            }\n          });\n          dispatch({\n            type: 'SET_ISVALIDATING',\n            payload: false\n          });\n        });\n      } else {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: maybePromise\n          }\n        });\n        return Promise.resolve(maybePromise);\n      }\n    } else if (props.validationSchema) {\n      dispatch({\n        type: 'SET_ISVALIDATING',\n        payload: true\n      });\n      return runValidationSchema(state.values, name).then(function (x) {\n        return x;\n      }).then(function (error) {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: getIn(error, name)\n          }\n        });\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n      });\n    }\n\n    return Promise.resolve();\n  });\n  var registerField = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name, _ref3) {\n    var validate = _ref3.validate;\n    fieldRegistry.current[name] = {\n      validate: validate\n    };\n  }, []);\n  var unregisterField = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    delete fieldRegistry.current[name];\n  }, []);\n  var setTouched = useEventCallback(function (touched, shouldValidate) {\n    dispatch({\n      type: 'SET_TOUCHED',\n      payload: touched\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var setErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (errors) {\n    dispatch({\n      type: 'SET_ERRORS',\n      payload: errors\n    });\n  }, []);\n  var setValues = useEventCallback(function (values, shouldValidate) {\n    var resolvedValues = isFunction(values) ? values(state.values) : values;\n    dispatch({\n      type: 'SET_VALUES',\n      payload: resolvedValues\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(resolvedValues) : Promise.resolve();\n  });\n  var setFieldError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (field, value) {\n    dispatch({\n      type: 'SET_FIELD_ERROR',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n  }, []);\n  var setFieldValue = useEventCallback(function (field, value, shouldValidate) {\n    dispatch({\n      type: 'SET_FIELD_VALUE',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(setIn(state.values, field, value)) : Promise.resolve();\n  });\n  var executeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (eventOrTextValue, maybePath) {\n    // By default, assume that the first argument is a string. This allows us to use\n    // handleChange with React Native and React Native Web's onChangeText prop which\n    // provides just the value of the input.\n    var field = maybePath;\n    var val = eventOrTextValue;\n    var parsed; // If the first argument is not a string though, it has to be a synthetic React Event (or a fake one),\n    // so we handle like we would a normal HTML change event.\n\n    if (!isString(eventOrTextValue)) {\n      // If we can, persist the event\n      // @see https://reactjs.org/docs/events.html#event-pooling\n      if (eventOrTextValue.persist) {\n        eventOrTextValue.persist();\n      }\n\n      var target = eventOrTextValue.target ? eventOrTextValue.target : eventOrTextValue.currentTarget;\n      var type = target.type,\n          name = target.name,\n          id = target.id,\n          value = target.value,\n          checked = target.checked,\n          outerHTML = target.outerHTML,\n          options = target.options,\n          multiple = target.multiple;\n      field = maybePath ? maybePath : name ? name : id;\n\n      if (!field && \"development\" !== \"production\") {\n        warnAboutMissingIdentifier({\n          htmlContent: outerHTML,\n          documentationAnchorLink: 'handlechange-e-reactchangeeventany--void',\n          handlerName: 'handleChange'\n        });\n      }\n\n      val = /number|range/.test(type) ? (parsed = parseFloat(value), isNaN(parsed) ? '' : parsed) : /checkbox/.test(type) // checkboxes\n      ? getValueForCheckbox(getIn(state.values, field), checked, value) : options && multiple // <select multiple>\n      ? getSelectedValues(options) : value;\n    }\n\n    if (field) {\n      // Set form fields by name\n      setFieldValue(field, val);\n    }\n  }, [setFieldValue, state.values]);\n  var handleChange = useEventCallback(function (eventOrPath) {\n    if (isString(eventOrPath)) {\n      return function (event) {\n        return executeChange(event, eventOrPath);\n      };\n    } else {\n      executeChange(eventOrPath);\n    }\n  });\n  var setFieldTouched = useEventCallback(function (field, touched, shouldValidate) {\n    if (touched === void 0) {\n      touched = true;\n    }\n\n    dispatch({\n      type: 'SET_FIELD_TOUCHED',\n      payload: {\n        field: field,\n        value: touched\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var executeBlur = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (e, path) {\n    if (e.persist) {\n      e.persist();\n    }\n\n    var _e$target = e.target,\n        name = _e$target.name,\n        id = _e$target.id,\n        outerHTML = _e$target.outerHTML;\n    var field = path ? path : name ? name : id;\n\n    if (!field && \"development\" !== \"production\") {\n      warnAboutMissingIdentifier({\n        htmlContent: outerHTML,\n        documentationAnchorLink: 'handleblur-e-any--void',\n        handlerName: 'handleBlur'\n      });\n    }\n\n    setFieldTouched(field, true);\n  }, [setFieldTouched]);\n  var handleBlur = useEventCallback(function (eventOrString) {\n    if (isString(eventOrString)) {\n      return function (event) {\n        return executeBlur(event, eventOrString);\n      };\n    } else {\n      executeBlur(eventOrString);\n    }\n  });\n  var setFormikState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (stateOrCb) {\n    if (isFunction(stateOrCb)) {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: stateOrCb\n      });\n    } else {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: function payload() {\n          return stateOrCb;\n        }\n      });\n    }\n  }, []);\n  var setStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (status) {\n    dispatch({\n      type: 'SET_STATUS',\n      payload: status\n    });\n  }, []);\n  var setSubmitting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (isSubmitting) {\n    dispatch({\n      type: 'SET_ISSUBMITTING',\n      payload: isSubmitting\n    });\n  }, []);\n  var submitForm = useEventCallback(function () {\n    dispatch({\n      type: 'SUBMIT_ATTEMPT'\n    });\n    return validateFormWithHighPriority().then(function (combinedErrors) {\n      // In case an error was thrown and passed to the resolved Promise,\n      // `combinedErrors` can be an instance of an Error. We need to check\n      // that and abort the submit.\n      // If we don't do that, calling `Object.keys(new Error())` yields an\n      // empty array, which causes the validation to pass and the form\n      // to be submitted.\n      var isInstanceOfError = combinedErrors instanceof Error;\n      var isActuallyValid = !isInstanceOfError && Object.keys(combinedErrors).length === 0;\n\n      if (isActuallyValid) {\n        // Proceed with submit...\n        //\n        // To respect sync submit fns, we can't simply wrap executeSubmit in a promise and\n        // _always_ dispatch SUBMIT_SUCCESS because isSubmitting would then always be false.\n        // This would be fine in simple cases, but make it impossible to disable submit\n        // buttons where people use callbacks or promises as side effects (which is basically\n        // all of v1 Formik code). Instead, recall that we are inside of a promise chain already,\n        //  so we can try/catch executeSubmit(), if it returns undefined, then just bail.\n        // If there are errors, throw em. Otherwise, wrap executeSubmit in a promise and handle\n        // cleanup of isSubmitting on behalf of the consumer.\n        var promiseOrUndefined;\n\n        try {\n          promiseOrUndefined = executeSubmit(); // Bail if it's sync, consumer is responsible for cleaning up\n          // via setSubmitting(false)\n\n          if (promiseOrUndefined === undefined) {\n            return;\n          }\n        } catch (error) {\n          throw error;\n        }\n\n        return Promise.resolve(promiseOrUndefined).then(function (result) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_SUCCESS'\n            });\n          }\n\n          return result;\n        })[\"catch\"](function (_errors) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_FAILURE'\n            }); // This is a legit error rejected by the onSubmit fn\n            // so we don't want to break the promise chain\n\n            throw _errors;\n          }\n        });\n      } else if (!!isMounted.current) {\n        // ^^^ Make sure Formik is still mounted before updating state\n        dispatch({\n          type: 'SUBMIT_FAILURE'\n        }); // throw combinedErrors;\n\n        if (isInstanceOfError) {\n          throw combinedErrors;\n        }\n      }\n\n      return;\n    });\n  });\n  var handleSubmit = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    } // Warn if form submission is triggered by a <button> without a\n    // specified `type` attribute during development. This mitigates\n    // a common gotcha in forms with both reset and submit buttons,\n    // where the dev forgets to add type=\"button\" to the reset button.\n\n\n    if ( true && typeof document !== 'undefined') {\n      // Safely get the active element (works with IE)\n      var activeElement = getActiveElement();\n\n      if (activeElement !== null && activeElement instanceof HTMLButtonElement) {\n        !(activeElement.attributes && activeElement.attributes.getNamedItem('type')) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You submitted a Formik form using a button with an unspecified `type` attribute.  Most browsers default button elements to `type=\"submit\"`. If this is not a submit button, please add `type=\"button\"`.') : 0 : void 0;\n      }\n    }\n\n    submitForm()[\"catch\"](function (reason) {\n      console.warn(\"Warning: An unhandled error was caught from submitForm()\", reason);\n    });\n  });\n  var imperativeMethods = {\n    resetForm: resetForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    setErrors: setErrors,\n    setFieldError: setFieldError,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    setFormikState: setFormikState,\n    submitForm: submitForm\n  };\n  var executeSubmit = useEventCallback(function () {\n    return onSubmit(state.values, imperativeMethods);\n  });\n  var handleReset = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    }\n\n    resetForm();\n  });\n  var getFieldMeta = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    return {\n      value: getIn(state.values, name),\n      error: getIn(state.errors, name),\n      touched: !!getIn(state.touched, name),\n      initialValue: getIn(initialValues.current, name),\n      initialTouched: !!getIn(initialTouched.current, name),\n      initialError: getIn(initialErrors.current, name)\n    };\n  }, [state.errors, state.touched, state.values]);\n  var getFieldHelpers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    return {\n      setValue: function setValue(value, shouldValidate) {\n        return setFieldValue(name, value, shouldValidate);\n      },\n      setTouched: function setTouched(value, shouldValidate) {\n        return setFieldTouched(name, value, shouldValidate);\n      },\n      setError: function setError(value) {\n        return setFieldError(name, value);\n      }\n    };\n  }, [setFieldValue, setFieldTouched, setFieldError]);\n  var getFieldProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (nameOrOptions) {\n    var isAnObject = isObject(nameOrOptions);\n    var name = isAnObject ? nameOrOptions.name : nameOrOptions;\n    var valueState = getIn(state.values, name);\n    var field = {\n      name: name,\n      value: valueState,\n      onChange: handleChange,\n      onBlur: handleBlur\n    };\n\n    if (isAnObject) {\n      var type = nameOrOptions.type,\n          valueProp = nameOrOptions.value,\n          is = nameOrOptions.as,\n          multiple = nameOrOptions.multiple;\n\n      if (type === 'checkbox') {\n        if (valueProp === undefined) {\n          field.checked = !!valueState;\n        } else {\n          field.checked = !!(Array.isArray(valueState) && ~valueState.indexOf(valueProp));\n          field.value = valueProp;\n        }\n      } else if (type === 'radio') {\n        field.checked = valueState === valueProp;\n        field.value = valueProp;\n      } else if (is === 'select' && multiple) {\n        field.value = field.value || [];\n        field.multiple = true;\n      }\n    }\n\n    return field;\n  }, [handleBlur, handleChange, state.values]);\n  var dirty = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, state.values);\n  }, [initialValues.current, state.values]);\n  var isValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return typeof isInitialValid !== 'undefined' ? dirty ? state.errors && Object.keys(state.errors).length === 0 : isInitialValid !== false && isFunction(isInitialValid) ? isInitialValid(props) : isInitialValid : state.errors && Object.keys(state.errors).length === 0;\n  }, [isInitialValid, dirty, state.errors, props]);\n\n  var ctx = _extends({}, state, {\n    initialValues: initialValues.current,\n    initialErrors: initialErrors.current,\n    initialTouched: initialTouched.current,\n    initialStatus: initialStatus.current,\n    handleBlur: handleBlur,\n    handleChange: handleChange,\n    handleReset: handleReset,\n    handleSubmit: handleSubmit,\n    resetForm: resetForm,\n    setErrors: setErrors,\n    setFormikState: setFormikState,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setFieldError: setFieldError,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    submitForm: submitForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    isValid: isValid,\n    dirty: dirty,\n    unregisterField: unregisterField,\n    registerField: registerField,\n    getFieldProps: getFieldProps,\n    getFieldMeta: getFieldMeta,\n    getFieldHelpers: getFieldHelpers,\n    validateOnBlur: validateOnBlur,\n    validateOnChange: validateOnChange,\n    validateOnMount: validateOnMount\n  });\n\n  return ctx;\n}\nfunction Formik(props) {\n  var formikbag = useFormik(props);\n  var component = props.component,\n      children = props.children,\n      render = props.render,\n      innerRef = props.innerRef; // This allows folks to pass a ref to <Formik />\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(innerRef, function () {\n    return formikbag;\n  });\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !!props.render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<Formik render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Formik render={(props) => ...} /> with <Formik>{(props) => ...}</Formik>\") : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(FormikProvider, {\n    value: formikbag\n  }, component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, formikbag) : render ? render(formikbag) : children // children come last, always called\n  ? isFunction(children) ? children(formikbag) : !isEmptyChildren(children) ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(children) : null : null);\n}\n\nfunction warnAboutMissingIdentifier(_ref4) {\n  var htmlContent = _ref4.htmlContent,\n      documentationAnchorLink = _ref4.documentationAnchorLink,\n      handlerName = _ref4.handlerName;\n  console.warn(\"Warning: Formik called `\" + handlerName + \"`, but you forgot to pass an `id` or `name` attribute to your input:\\n    \" + htmlContent + \"\\n    Formik cannot determine which value to update. For more info see https://formik.org/docs/api/formik#\" + documentationAnchorLink + \"\\n  \");\n}\n/**\r\n * Transform Yup ValidationError to a more usable object\r\n */\n\n\nfunction yupToFormErrors(yupError) {\n  var errors = {};\n\n  if (yupError.inner) {\n    if (yupError.inner.length === 0) {\n      return setIn(errors, yupError.path, yupError.message);\n    }\n\n    for (var _iterator = yupError.inner, _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;) {\n      var _ref5;\n\n      if (_isArray) {\n        if (_i >= _iterator.length) break;\n        _ref5 = _iterator[_i++];\n      } else {\n        _i = _iterator.next();\n        if (_i.done) break;\n        _ref5 = _i.value;\n      }\n\n      var err = _ref5;\n\n      if (!getIn(errors, err.path)) {\n        errors = setIn(errors, err.path, err.message);\n      }\n    }\n  }\n\n  return errors;\n}\n/**\r\n * Validate a yup schema.\r\n */\n\nfunction validateYupSchema(values, schema, sync, context) {\n  if (sync === void 0) {\n    sync = false;\n  }\n\n  var normalizedValues = prepareDataForValidation(values);\n  return schema[sync ? 'validateSync' : 'validate'](normalizedValues, {\n    abortEarly: false,\n    context: context || normalizedValues\n  });\n}\n/**\r\n * Recursively prepare values.\r\n */\n\nfunction prepareDataForValidation(values) {\n  var data = Array.isArray(values) ? [] : {};\n\n  for (var k in values) {\n    if (Object.prototype.hasOwnProperty.call(values, k)) {\n      var key = String(k);\n\n      if (Array.isArray(values[key]) === true) {\n        data[key] = values[key].map(function (value) {\n          if (Array.isArray(value) === true || (0,lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(value)) {\n            return prepareDataForValidation(value);\n          } else {\n            return value !== '' ? value : undefined;\n          }\n        });\n      } else if ((0,lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(values[key])) {\n        data[key] = prepareDataForValidation(values[key]);\n      } else {\n        data[key] = values[key] !== '' ? values[key] : undefined;\n      }\n    }\n  }\n\n  return data;\n}\n/**\r\n * deepmerge array merging algorithm\r\n * https://github.com/KyleAMathews/deepmerge#combine-array\r\n */\n\nfunction arrayMerge(target, source, options) {\n  var destination = target.slice();\n  source.forEach(function merge(e, i) {\n    if (typeof destination[i] === 'undefined') {\n      var cloneRequested = options.clone !== false;\n      var shouldClone = cloneRequested && options.isMergeableObject(e);\n      destination[i] = shouldClone ? (0,deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.isArray(e) ? [] : {}, e, options) : e;\n    } else if (options.isMergeableObject(e)) {\n      destination[i] = (0,deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(target[i], e, options);\n    } else if (target.indexOf(e) === -1) {\n      destination.push(e);\n    }\n  });\n  return destination;\n}\n/** Return multi select values based on an array of options */\n\n\nfunction getSelectedValues(options) {\n  return Array.from(options).filter(function (el) {\n    return el.selected;\n  }).map(function (el) {\n    return el.value;\n  });\n}\n/** Return the next value for a checkbox */\n\n\nfunction getValueForCheckbox(currentValue, checked, valueProp) {\n  // If the current value was a boolean, return a boolean\n  if (typeof currentValue === 'boolean') {\n    return Boolean(checked);\n  } // If the currentValue was not a boolean we want to return an array\n\n\n  var currentArrayOfValues = [];\n  var isValueInArray = false;\n  var index = -1;\n\n  if (!Array.isArray(currentValue)) {\n    // eslint-disable-next-line eqeqeq\n    if (!valueProp || valueProp == 'true' || valueProp == 'false') {\n      return Boolean(checked);\n    }\n  } else {\n    // If the current value is already an array, use it\n    currentArrayOfValues = currentValue;\n    index = currentValue.indexOf(valueProp);\n    isValueInArray = index >= 0;\n  } // If the checkbox was checked and the value is not already present in the aray we want to add the new value to the array of values\n\n\n  if (checked && valueProp && !isValueInArray) {\n    return currentArrayOfValues.concat(valueProp);\n  } // If the checkbox was unchecked and the value is not in the array, simply return the already existing array of values\n\n\n  if (!isValueInArray) {\n    return currentArrayOfValues;\n  } // If the checkbox was unchecked and the value is in the array, remove the value and return the array\n\n\n  return currentArrayOfValues.slice(0, index).concat(currentArrayOfValues.slice(index + 1));\n} // React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\n// @see https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\n\nfunction useEventCallback(fn) {\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(fn); // we copy a ref to the callback scoped to the current state/props on each render\n\n  useIsomorphicLayoutEffect(function () {\n    ref.current = fn;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return ref.current.apply(void 0, args);\n  }, []);\n}\n\nfunction useField(propsOrFieldName) {\n  var formik = useFormikContext();\n  var getFieldProps = formik.getFieldProps,\n      getFieldMeta = formik.getFieldMeta,\n      getFieldHelpers = formik.getFieldHelpers,\n      registerField = formik.registerField,\n      unregisterField = formik.unregisterField;\n  var isAnObject = isObject(propsOrFieldName); // Normalize propsOrFieldName to FieldHookConfig<Val>\n\n  var props = isAnObject ? propsOrFieldName : {\n    name: propsOrFieldName\n  };\n  var fieldName = props.name,\n      validateFn = props.validate;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (fieldName) {\n      registerField(fieldName, {\n        validate: validateFn\n      });\n    }\n\n    return function () {\n      if (fieldName) {\n        unregisterField(fieldName);\n      }\n    };\n  }, [registerField, unregisterField, fieldName, validateFn]);\n\n  if (true) {\n    !formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'useField() / <Field /> must be used underneath a <Formik> component or withFormik() higher order component') : 0 : void 0;\n  }\n\n  !fieldName ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'Invalid field name. Either pass `useField` a string or an object containing a `name` key.') : 0 : void 0;\n  var fieldHelpers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return getFieldHelpers(fieldName);\n  }, [getFieldHelpers, fieldName]);\n  return [getFieldProps(props), getFieldMeta(fieldName), fieldHelpers];\n}\nfunction Field(_ref) {\n  var validate = _ref.validate,\n      name = _ref.name,\n      render = _ref.render,\n      children = _ref.children,\n      is = _ref.as,\n      component = _ref.component,\n      props = _objectWithoutPropertiesLoose(_ref, [\"validate\", \"name\", \"render\", \"children\", \"as\", \"component\"]);\n\n  var _useFormikContext = useFormikContext(),\n      formik = _objectWithoutPropertiesLoose(_useFormikContext, [\"validate\", \"validationSchema\"]);\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !!render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<Field render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Field name=\\\"\" + name + \"\\\" render={({field, form}) => ...} /> with <Field name=\\\"\" + name + \"\\\">{({field, form, meta}) => ...}</Field>\") : 0 : void 0;\n      !!(is && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field as> and <Field children> as a function in the same <Field> component; <Field as> will be ignored.') : 0 : void 0;\n      !!(component && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field component> and <Field children> as a function in the same <Field> component; <Field component> will be ignored.') : 0 : void 0;\n      !!(render && children && !isEmptyChildren(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field render> and <Field children> in the same <Field> component; <Field children> will be ignored') : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  } // Register field and field-level validation with parent <Formik>\n\n\n  var registerField = formik.registerField,\n      unregisterField = formik.unregisterField;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    registerField(name, {\n      validate: validate\n    });\n    return function () {\n      unregisterField(name);\n    };\n  }, [registerField, unregisterField, name, validate]);\n  var field = formik.getFieldProps(_extends({\n    name: name\n  }, props));\n  var meta = formik.getFieldMeta(name);\n  var legacyBag = {\n    field: field,\n    form: formik\n  };\n\n  if (render) {\n    return render(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n\n  if (isFunction(children)) {\n    return children(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n\n  if (component) {\n    // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n    if (typeof component === 'string') {\n      var innerRef = props.innerRef,\n          rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n        ref: innerRef\n      }, field, rest), children);\n    } // We don't pass `meta` for backwards compat\n\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n      field: field,\n      form: formik\n    }, props), children);\n  } // default to input here so we can check for both `as` and `children` above\n\n\n  var asElement = is || 'input';\n\n  if (typeof asElement === 'string') {\n    var _innerRef = props.innerRef,\n        _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({\n      ref: _innerRef\n    }, field, _rest), children);\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({}, field, props), children);\n}\n\nvar Form = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {\n  // iOS needs an \"action\" attribute for nice input: https://stackoverflow.com/a/39485162/406725\n  // We default the action to \"#\" in case the preventDefault fails (just updates the URL hash)\n  var action = props.action,\n      rest = _objectWithoutPropertiesLoose(props, [\"action\"]);\n\n  var _action = action != null ? action : '#';\n\n  var _useFormikContext = useFormikContext(),\n      handleReset = _useFormikContext.handleReset,\n      handleSubmit = _useFormikContext.handleSubmit;\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"form\", _extends({\n    onSubmit: handleSubmit,\n    ref: ref,\n    onReset: handleReset,\n    action: _action\n  }, rest));\n});\nForm.displayName = 'Form';\n\n/**\r\n * A public higher-order component to access the imperative API\r\n */\n\nfunction withFormik(_ref) {\n  var _ref$mapPropsToValues = _ref.mapPropsToValues,\n      mapPropsToValues = _ref$mapPropsToValues === void 0 ? function (vanillaProps) {\n    var val = {};\n\n    for (var k in vanillaProps) {\n      if (vanillaProps.hasOwnProperty(k) && typeof vanillaProps[k] !== 'function') {\n        // @todo TypeScript fix\n        val[k] = vanillaProps[k];\n      }\n    }\n\n    return val;\n  } : _ref$mapPropsToValues,\n      config = _objectWithoutPropertiesLoose(_ref, [\"mapPropsToValues\"]);\n\n  return function createFormik(Component$1) {\n    var componentDisplayName = Component$1.displayName || Component$1.name || Component$1.constructor && Component$1.constructor.name || 'Component';\n    /**\r\n     * We need to use closures here for to provide the wrapped component's props to\r\n     * the respective withFormik config methods.\r\n     */\n\n    var C = /*#__PURE__*/function (_React$Component) {\n      _inheritsLoose(C, _React$Component);\n\n      function C() {\n        var _this;\n\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n        _this.validate = function (values) {\n          return config.validate(values, _this.props);\n        };\n\n        _this.validationSchema = function () {\n          return isFunction(config.validationSchema) ? config.validationSchema(_this.props) : config.validationSchema;\n        };\n\n        _this.handleSubmit = function (values, actions) {\n          return config.handleSubmit(values, _extends({}, actions, {\n            props: _this.props\n          }));\n        };\n\n        _this.renderFormComponent = function (formikProps) {\n          return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Component$1, _extends({}, _this.props, formikProps));\n        };\n\n        return _this;\n      }\n\n      var _proto = C.prototype;\n\n      _proto.render = function render() {\n        var _this$props = this.props,\n            props = _objectWithoutPropertiesLoose(_this$props, [\"children\"]);\n\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Formik, _extends({}, props, config, {\n          validate: config.validate && this.validate,\n          validationSchema: config.validationSchema && this.validationSchema,\n          initialValues: mapPropsToValues(this.props),\n          initialStatus: config.mapPropsToStatus && config.mapPropsToStatus(this.props),\n          initialErrors: config.mapPropsToErrors && config.mapPropsToErrors(this.props),\n          initialTouched: config.mapPropsToTouched && config.mapPropsToTouched(this.props),\n          onSubmit: this.handleSubmit,\n          children: this.renderFormComponent\n        }));\n      };\n\n      return C;\n    }(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\n    C.displayName = \"WithFormik(\" + componentDisplayName + \")\";\n    return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(C, Component$1 // cast type to ComponentClass (even if SFC)\n    );\n  };\n}\n\n/**\r\n * Connect any component to Formik context, and inject as a prop called `formik`;\r\n * @param Comp React Component\r\n */\n\nfunction connect(Comp) {\n  var C = function C(props) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(FormikConsumer, null, function (formik) {\n      !!!formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Formik context is undefined, please verify you are rendering <Form>, <Field>, <FastField>, <FieldArray>, or your custom context-using component as a child of a <Formik> component. Component name: \" + Comp.name) : 0 : void 0;\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Comp, _extends({}, props, {\n        formik: formik\n      }));\n    });\n  };\n\n  var componentDisplayName = Comp.displayName || Comp.name || Comp.constructor && Comp.constructor.name || 'Component'; // Assign Comp to C.WrappedComponent so we can access the inner component in tests\n  // For example, <Field.WrappedComponent /> gets us <FieldInner/>\n\n  C.WrappedComponent = Comp;\n  C.displayName = \"FormikConnect(\" + componentDisplayName + \")\";\n  return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(C, Comp // cast type to ComponentClass (even if SFC)\n  );\n}\n\n/**\r\n * Some array helpers!\r\n */\n\nvar move = function move(array, from, to) {\n  var copy = copyArrayLike(array);\n  var value = copy[from];\n  copy.splice(from, 1);\n  copy.splice(to, 0, value);\n  return copy;\n};\nvar swap = function swap(arrayLike, indexA, indexB) {\n  var copy = copyArrayLike(arrayLike);\n  var a = copy[indexA];\n  copy[indexA] = copy[indexB];\n  copy[indexB] = a;\n  return copy;\n};\nvar insert = function insert(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy.splice(index, 0, value);\n  return copy;\n};\nvar replace = function replace(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy[index] = value;\n  return copy;\n};\n\nvar copyArrayLike = function copyArrayLike(arrayLike) {\n  if (!arrayLike) {\n    return [];\n  } else if (Array.isArray(arrayLike)) {\n    return [].concat(arrayLike);\n  } else {\n    var maxIndex = Object.keys(arrayLike).map(function (key) {\n      return parseInt(key);\n    }).reduce(function (max, el) {\n      return el > max ? el : max;\n    }, 0);\n    return Array.from(_extends({}, arrayLike, {\n      length: maxIndex + 1\n    }));\n  }\n};\n\nvar createAlterationHandler = function createAlterationHandler(alteration, defaultFunction) {\n  var fn = typeof alteration === 'function' ? alteration : defaultFunction;\n  return function (data) {\n    if (Array.isArray(data) || isObject(data)) {\n      var clone = copyArrayLike(data);\n      return fn(clone);\n    } // This can be assumed to be a primitive, which\n    // is a case for top level validation errors\n\n\n    return data;\n  };\n};\n\nvar FieldArrayInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FieldArrayInner, _React$Component);\n\n  function FieldArrayInner(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this; // We need TypeScript generics on these, so we'll bind them in the constructor\n    // @todo Fix TS 3.2.1\n\n    _this.updateArrayField = function (fn, alterTouched, alterErrors) {\n      var _this$props = _this.props,\n          name = _this$props.name,\n          setFormikState = _this$props.formik.setFormikState;\n      setFormikState(function (prevState) {\n        var updateErrors = createAlterationHandler(alterErrors, fn);\n        var updateTouched = createAlterationHandler(alterTouched, fn); // values fn should be executed before updateErrors and updateTouched,\n        // otherwise it causes an error with unshift.\n\n        var values = setIn(prevState.values, name, fn(getIn(prevState.values, name)));\n        var fieldError = alterErrors ? updateErrors(getIn(prevState.errors, name)) : undefined;\n        var fieldTouched = alterTouched ? updateTouched(getIn(prevState.touched, name)) : undefined;\n\n        if (isEmptyArray(fieldError)) {\n          fieldError = undefined;\n        }\n\n        if (isEmptyArray(fieldTouched)) {\n          fieldTouched = undefined;\n        }\n\n        return _extends({}, prevState, {\n          values: values,\n          errors: alterErrors ? setIn(prevState.errors, name, fieldError) : prevState.errors,\n          touched: alterTouched ? setIn(prevState.touched, name, fieldTouched) : prevState.touched\n        });\n      });\n    };\n\n    _this.push = function (value) {\n      return _this.updateArrayField(function (arrayLike) {\n        return [].concat(copyArrayLike(arrayLike), [(0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(value)]);\n      }, false, false);\n    };\n\n    _this.handlePush = function (value) {\n      return function () {\n        return _this.push(value);\n      };\n    };\n\n    _this.swap = function (indexA, indexB) {\n      return _this.updateArrayField(function (array) {\n        return swap(array, indexA, indexB);\n      }, true, true);\n    };\n\n    _this.handleSwap = function (indexA, indexB) {\n      return function () {\n        return _this.swap(indexA, indexB);\n      };\n    };\n\n    _this.move = function (from, to) {\n      return _this.updateArrayField(function (array) {\n        return move(array, from, to);\n      }, true, true);\n    };\n\n    _this.handleMove = function (from, to) {\n      return function () {\n        return _this.move(from, to);\n      };\n    };\n\n    _this.insert = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return insert(array, index, value);\n      }, function (array) {\n        return insert(array, index, null);\n      }, function (array) {\n        return insert(array, index, null);\n      });\n    };\n\n    _this.handleInsert = function (index, value) {\n      return function () {\n        return _this.insert(index, value);\n      };\n    };\n\n    _this.replace = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return replace(array, index, value);\n      }, false, false);\n    };\n\n    _this.handleReplace = function (index, value) {\n      return function () {\n        return _this.replace(index, value);\n      };\n    };\n\n    _this.unshift = function (value) {\n      var length = -1;\n\n      _this.updateArrayField(function (array) {\n        var arr = array ? [value].concat(array) : [value];\n        length = arr.length;\n        return arr;\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      });\n\n      return length;\n    };\n\n    _this.handleUnshift = function (value) {\n      return function () {\n        return _this.unshift(value);\n      };\n    };\n\n    _this.handleRemove = function (index) {\n      return function () {\n        return _this.remove(index);\n      };\n    };\n\n    _this.handlePop = function () {\n      return function () {\n        return _this.pop();\n      };\n    };\n\n    _this.remove = _this.remove.bind(_assertThisInitialized(_this));\n    _this.pop = _this.pop.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  var _proto = FieldArrayInner.prototype;\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.validateOnChange && this.props.formik.validateOnChange && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(getIn(prevProps.formik.values, prevProps.name), getIn(this.props.formik.values, this.props.name))) {\n      this.props.formik.validateForm(this.props.formik.values);\n    }\n  };\n\n  _proto.remove = function remove(index) {\n    // We need to make sure we also remove relevant pieces of `touched` and `errors`\n    var result;\n    this.updateArrayField( // so this gets call 3 times\n    function (array) {\n      var copy = array ? copyArrayLike(array) : [];\n\n      if (!result) {\n        result = copy[index];\n      }\n\n      if (isFunction(copy.splice)) {\n        copy.splice(index, 1);\n      } // if the array only includes undefined values we have to return an empty array\n\n\n      return isFunction(copy.every) ? copy.every(function (v) {\n        return v === undefined;\n      }) ? [] : copy : copy;\n    }, true, true);\n    return result;\n  };\n\n  _proto.pop = function pop() {\n    // Remove relevant pieces of `touched` and `errors` too!\n    var result;\n    this.updateArrayField( // so this gets call 3 times\n    function (array) {\n      var tmp = array.slice();\n\n      if (!result) {\n        result = tmp && tmp.pop && tmp.pop();\n      }\n\n      return tmp;\n    }, true, true);\n    return result;\n  };\n\n  _proto.render = function render() {\n    var arrayHelpers = {\n      push: this.push,\n      pop: this.pop,\n      swap: this.swap,\n      move: this.move,\n      insert: this.insert,\n      replace: this.replace,\n      unshift: this.unshift,\n      remove: this.remove,\n      handlePush: this.handlePush,\n      handlePop: this.handlePop,\n      handleSwap: this.handleSwap,\n      handleMove: this.handleMove,\n      handleInsert: this.handleInsert,\n      handleReplace: this.handleReplace,\n      handleUnshift: this.handleUnshift,\n      handleRemove: this.handleRemove\n    };\n\n    var _this$props2 = this.props,\n        component = _this$props2.component,\n        render = _this$props2.render,\n        children = _this$props2.children,\n        name = _this$props2.name,\n        _this$props2$formik = _this$props2.formik,\n        restOfFormik = _objectWithoutPropertiesLoose(_this$props2$formik, [\"validate\", \"validationSchema\"]);\n\n    var props = _extends({}, arrayHelpers, {\n      form: restOfFormik,\n      name: name\n    });\n\n    return component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, props) : render ? render(props) : children // children come last, always called\n    ? typeof children === 'function' ? children(props) : !isEmptyChildren(children) ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(children) : null : null;\n  };\n\n  return FieldArrayInner;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nFieldArrayInner.defaultProps = {\n  validateOnChange: true\n};\nvar FieldArray = /*#__PURE__*/connect(FieldArrayInner);\n\nvar ErrorMessageImpl = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ErrorMessageImpl, _React$Component);\n\n  function ErrorMessageImpl() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n\n  var _proto = ErrorMessageImpl.prototype;\n\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (getIn(this.props.formik.errors, this.props.name) !== getIn(props.formik.errors, this.props.name) || getIn(this.props.formik.touched, this.props.name) !== getIn(props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        component = _this$props.component,\n        formik = _this$props.formik,\n        render = _this$props.render,\n        children = _this$props.children,\n        name = _this$props.name,\n        rest = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"formik\", \"render\", \"children\", \"name\"]);\n\n    var touch = getIn(formik.touched, name);\n    var error = getIn(formik.errors, name);\n    return !!touch && !!error ? render ? isFunction(render) ? render(error) : null : children ? isFunction(children) ? children(error) : null : component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, rest, error) : error : null;\n  };\n\n  return ErrorMessageImpl;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nvar ErrorMessage = /*#__PURE__*/connect(ErrorMessageImpl);\n\n/**\r\n * Custom Field component for quickly hooking into Formik\r\n * context and wiring up forms.\r\n */\n\nvar FastFieldInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FastFieldInner, _React$Component);\n\n  function FastFieldInner(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this;\n    var render = props.render,\n        children = props.children,\n        component = props.component,\n        is = props.as,\n        name = props.name;\n    !!render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<FastField render> has been deprecated. Please use a child callback function instead: <FastField name={\" + name + \"}>{props => ...}</FastField> instead.\") : 0 : void 0;\n    !!(component && render) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField component> and <FastField render> in the same <FastField> component; <FastField component> will be ignored') : 0 : void 0;\n    !!(is && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField as> and <FastField children> as a function in the same <FastField> component; <FastField as> will be ignored.') : 0 : void 0;\n    !!(component && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField component> and <FastField children> as a function in the same <FastField> component; <FastField component> will be ignored.') : 0 : void 0;\n    !!(render && children && !isEmptyChildren(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField render> and <FastField children> in the same <FastField> component; <FastField children> will be ignored') : 0 : void 0;\n    return _this;\n  }\n\n  var _proto = FastFieldInner.prototype;\n\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (this.props.shouldUpdate) {\n      return this.props.shouldUpdate(props, this.props);\n    } else if (props.name !== this.props.name || getIn(props.formik.values, this.props.name) !== getIn(this.props.formik.values, this.props.name) || getIn(props.formik.errors, this.props.name) !== getIn(this.props.formik.errors, this.props.name) || getIn(props.formik.touched, this.props.name) !== getIn(this.props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length || props.formik.isSubmitting !== this.props.formik.isSubmitting) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  _proto.componentDidMount = function componentDidMount() {\n    // Register the Field with the parent Formik. Parent will cycle through\n    // registered Field's validate fns right prior to submit\n    this.props.formik.registerField(this.props.name, {\n      validate: this.props.validate\n    });\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.name !== prevProps.name) {\n      this.props.formik.unregisterField(prevProps.name);\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n\n    if (this.props.validate !== prevProps.validate) {\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.props.formik.unregisterField(this.props.name);\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        name = _this$props.name,\n        render = _this$props.render,\n        is = _this$props.as,\n        children = _this$props.children,\n        component = _this$props.component,\n        formik = _this$props.formik,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"validate\", \"name\", \"render\", \"as\", \"children\", \"component\", \"shouldUpdate\", \"formik\"]);\n\n    var restOfFormik = _objectWithoutPropertiesLoose(formik, [\"validate\", \"validationSchema\"]);\n\n    var field = formik.getFieldProps(_extends({\n      name: name\n    }, props));\n    var meta = {\n      value: getIn(formik.values, name),\n      error: getIn(formik.errors, name),\n      touched: !!getIn(formik.touched, name),\n      initialValue: getIn(formik.initialValues, name),\n      initialTouched: !!getIn(formik.initialTouched, name),\n      initialError: getIn(formik.initialErrors, name)\n    };\n    var bag = {\n      field: field,\n      meta: meta,\n      form: restOfFormik\n    };\n\n    if (render) {\n      return render(bag);\n    }\n\n    if (isFunction(children)) {\n      return children(bag);\n    }\n\n    if (component) {\n      // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n      if (typeof component === 'string') {\n        var innerRef = props.innerRef,\n            rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n          ref: innerRef\n        }, field, rest), children);\n      } // We don't pass `meta` for backwards compat\n\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n        field: field,\n        form: formik\n      }, props), children);\n    } // default to input here so we can check for both `as` and `children` above\n\n\n    var asElement = is || 'input';\n\n    if (typeof asElement === 'string') {\n      var _innerRef = props.innerRef,\n          _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({\n        ref: _innerRef\n      }, field, _rest), children);\n    }\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({}, field, props), children);\n  };\n\n  return FastFieldInner;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nvar FastField = /*#__PURE__*/connect(FastFieldInner);\n\n\n//# sourceMappingURL=formik.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/formik/dist/formik.esm.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/formik/node_modules/deepmerge/dist/es.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/formik/node_modules/deepmerge/dist/es.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tObject.keys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tObject.keys(source).forEach(function(key) {\n\t\tif (!options.isMergeableObject(source[key]) || !target[key]) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = deepmerge(target[key], source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (deepmerge_1);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/formik/node_modules/deepmerge/dist/es.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/formik/node_modules/react-fast-compare/index.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/formik/node_modules/react-fast-compare/index.js ***!
  \**************************************************************************/
/***/ ((module) => {

eval("\n\nvar isArray = Array.isArray;\nvar keyList = Object.keys;\nvar hasProp = Object.prototype.hasOwnProperty;\nvar hasElementType = typeof Element !== 'undefined';\n\nfunction equal(a, b) {\n  // fast-deep-equal index.js 2.0.1\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    var arrA = isArray(a)\n      , arrB = isArray(b)\n      , i\n      , length\n      , key;\n\n    if (arrA && arrB) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    if (arrA != arrB) return false;\n\n    var dateA = a instanceof Date\n      , dateB = b instanceof Date;\n    if (dateA != dateB) return false;\n    if (dateA && dateB) return a.getTime() == b.getTime();\n\n    var regexpA = a instanceof RegExp\n      , regexpB = b instanceof RegExp;\n    if (regexpA != regexpB) return false;\n    if (regexpA && regexpB) return a.toString() == b.toString();\n\n    var keys = keyList(a);\n    length = keys.length;\n\n    if (length !== keyList(b).length)\n      return false;\n\n    for (i = length; i-- !== 0;)\n      if (!hasProp.call(b, keys[i])) return false;\n    // end fast-deep-equal\n\n    // start react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element && b instanceof Element)\n      return a === b;\n\n    // custom handling for React\n    for (i = length; i-- !== 0;) {\n      key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of a react element\n        continue;\n      } else {\n        // all other properties should be traversed as usual\n        if (!equal(a[key], b[key])) return false;\n      }\n    }\n    // end react-fast-compare\n\n    // fast-deep-equal index.js 2.0.1\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function exportedEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if ((error.message && error.message.match(/stack|recursion/i)) || (error.number === -2146828260)) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('Warning: react-fast-compare does not handle circular references.', error.name, error.message);\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/formik/node_modules/react-fast-compare/index.js\n");

/***/ })

};
;
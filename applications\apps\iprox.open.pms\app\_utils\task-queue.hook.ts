import { useCallback, useEffect, useState } from 'react';

export function useTaskQueue(params: { shouldProcess: boolean; maxProcessingCount: number }): {
  tasks: ReadonlyArray<TaskWithId>;
  workingCount: number;
  addTask: (taskId: string, task: Task) => void;
  removeTask: (taskId: string) => void;
} {
  const [queue, setQueue] = useState<{
    workingCount: number;
    tasks: Array<TaskWithId>;
  }>({ workingCount: 0, tasks: [] });

  useEffect(() => {
    if (!params.shouldProcess) return;
    if (queue.tasks.length === 0) return;
    if (queue.workingCount >= params.maxProcessingCount) return;

    const task = queue.tasks[0];

    setQueue((prev) => ({
      workingCount: prev.workingCount + 1,
      tasks: prev.tasks.slice(1),
    }));

    Promise.resolve(task.task()).finally(() => {
      setQueue((prev) => ({
        workingCount: prev.workingCount - 1,
        tasks: prev.tasks,
      }));
    });
  }, [queue, params.shouldProcess, params.maxProcessingCount]);

  return {
    tasks: queue.tasks,
    workingCount: queue.workingCount,
    addTask: useCallback((taskId, task) => {
      setQueue((prev) => ({
        workingCount: prev.workingCount,
        tasks: [...prev.tasks, { id: taskId, task }],
      }));
    }, []),
    removeTask: useCallback((taskId: string) => {
      setQueue((prev) => ({
        workingCount: prev.workingCount,
        tasks: prev.tasks.filter((t) => t.id !== taskId),
      }));
    }, []),
  };
}

type Task = () => Promise<unknown> | void;

interface TaskWithId {
  id: string;
  task: Task;
}

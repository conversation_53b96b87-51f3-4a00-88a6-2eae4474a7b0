import nl from 'date-fns/locale/nl';
import { FieldHookConfig, useField } from 'formik';
import { useLocale } from 'next-intl';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { DateFieldDefinition } from '../../models/form.models';
import './date-time-picker.scss';

const getSelectedValue = (value: string | Date | null | undefined, isNullable?: boolean): Date | null => {
  if (value) {
    if (typeof value === 'string') {
      return new Date(value);
    }

    if (value instanceof Date) {
      return value;
    }
  }

  return !isNullable ? new Date() : null;
};

export function DateTimePickerField(props: DateFieldDefinition) {
  const locale = useLocale();

  const updatedProps = {
    ...props,
    defaultValue: props.value,
  };
  const [field, meta, helpers] = useField(updatedProps as FieldHookConfig<Date | string | null | undefined>);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'date');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  const handleDateChange = (date?: Date | null) => {
    helpers.setValue(date, true);
  };

  return (
    <FormField
      labelColor={props.labelColor}
      descriptionColor={props.descriptionColor}
      definition={props}
      errorMessage={meta.touched ? meta.error : undefined}
      {...formControlProps}
    >
      <DatePicker
        selected={getSelectedValue(field.value, props.isClearable)}
        onChange={handleDateChange}
        id={inputProps?.id}
        ariaDescribedBy={inputProps?.['aria-describedby']}
        name={field.name}
        onBlur={() => helpers.setTouched(true, true)}
        minDate={
          props?.minDate instanceof Date || typeof props?.minDate === 'string' ? new Date(props?.minDate) : undefined
        }
        maxDate={
          props?.maxDate instanceof Date || typeof props?.maxDate === 'string' ? new Date(props?.maxDate) : undefined
        }
        locale={locale === 'nl' ? nl : undefined}
        dateFormat={locale === 'nl' ? `dd-MM-yyyy '-' HH:mm` : `dd/MM/yyyy '-' HH:mm`}
        showTimeInput
        timeInputLabel="Time:"
        timeFormat="HH:mm"
        showMonthDropdown
        showYearDropdown
        dropdownMode="select"
        popperClassName="z-90"
        isClearable={props.isClearable}
        popperPlacement={props.popperPlacement}
      />
    </FormField>
  );
}

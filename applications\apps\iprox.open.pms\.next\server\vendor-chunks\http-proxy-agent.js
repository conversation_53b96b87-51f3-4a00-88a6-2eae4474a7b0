"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/http-proxy-agent";
exports.ids = ["vendor-chunks/http-proxy-agent"];
exports.modules = {

/***/ "(instrument)/../../node_modules/http-proxy-agent/dist/agent.js":
/*!*********************************************************!*\
  !*** ../../node_modules/http-proxy-agent/dist/agent.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst net_1 = __importDefault(__webpack_require__(/*! net */ \"net\"));\nconst tls_1 = __importDefault(__webpack_require__(/*! tls */ \"tls\"));\nconst url_1 = __importDefault(__webpack_require__(/*! url */ \"url\"));\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(instrument)/../../node_modules/debug/src/index.js\"));\nconst once_1 = __importDefault(__webpack_require__(/*! @tootallnate/once */ \"(instrument)/../../node_modules/@tootallnate/once/dist/index.js\"));\nconst agent_base_1 = __webpack_require__(/*! agent-base */ \"(instrument)/../../node_modules/http-proxy-agent/node_modules/agent-base/dist/src/index.js\");\nconst debug = (0, debug_1.default)('http-proxy-agent');\nfunction isHTTPS(protocol) {\n    return typeof protocol === 'string' ? /^https:?$/i.test(protocol) : false;\n}\n/**\n * The `HttpProxyAgent` implements an HTTP Agent subclass that connects\n * to the specified \"HTTP proxy server\" in order to proxy HTTP requests.\n *\n * @api public\n */\nclass HttpProxyAgent extends agent_base_1.Agent {\n    constructor(_opts) {\n        let opts;\n        if (typeof _opts === 'string') {\n            opts = url_1.default.parse(_opts);\n        }\n        else {\n            opts = _opts;\n        }\n        if (!opts) {\n            throw new Error('an HTTP(S) proxy server `host` and `port` must be specified!');\n        }\n        debug('Creating new HttpProxyAgent instance: %o', opts);\n        super(opts);\n        const proxy = Object.assign({}, opts);\n        // If `true`, then connect to the proxy server over TLS.\n        // Defaults to `false`.\n        this.secureProxy = opts.secureProxy || isHTTPS(proxy.protocol);\n        // Prefer `hostname` over `host`, and set the `port` if needed.\n        proxy.host = proxy.hostname || proxy.host;\n        if (typeof proxy.port === 'string') {\n            proxy.port = parseInt(proxy.port, 10);\n        }\n        if (!proxy.port && proxy.host) {\n            proxy.port = this.secureProxy ? 443 : 80;\n        }\n        if (proxy.host && proxy.path) {\n            // If both a `host` and `path` are specified then it's most likely\n            // the result of a `url.parse()` call... we need to remove the\n            // `path` portion so that `net.connect()` doesn't attempt to open\n            // that as a Unix socket file.\n            delete proxy.path;\n            delete proxy.pathname;\n        }\n        this.proxy = proxy;\n    }\n    /**\n     * Called when the node-core HTTP client library is creating a\n     * new HTTP request.\n     *\n     * @api protected\n     */\n    callback(req, opts) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const { proxy, secureProxy } = this;\n            const parsed = url_1.default.parse(req.path);\n            if (!parsed.protocol) {\n                parsed.protocol = 'http:';\n            }\n            if (!parsed.hostname) {\n                parsed.hostname = opts.hostname || opts.host || null;\n            }\n            if (parsed.port == null && typeof opts.port) {\n                parsed.port = String(opts.port);\n            }\n            if (parsed.port === '80') {\n                // if port is 80, then we can remove the port so that the\n                // \":80\" portion is not on the produced URL\n                parsed.port = '';\n            }\n            // Change the `http.ClientRequest` instance's \"path\" field\n            // to the absolute path of the URL that will be requested.\n            req.path = url_1.default.format(parsed);\n            // Inject the `Proxy-Authorization` header if necessary.\n            if (proxy.auth) {\n                req.setHeader('Proxy-Authorization', `Basic ${Buffer.from(proxy.auth).toString('base64')}`);\n            }\n            // Create a socket connection to the proxy server.\n            let socket;\n            if (secureProxy) {\n                debug('Creating `tls.Socket`: %o', proxy);\n                socket = tls_1.default.connect(proxy);\n            }\n            else {\n                debug('Creating `net.Socket`: %o', proxy);\n                socket = net_1.default.connect(proxy);\n            }\n            // At this point, the http ClientRequest's internal `_header` field\n            // might have already been set. If this is the case then we'll need\n            // to re-generate the string since we just changed the `req.path`.\n            if (req._header) {\n                let first;\n                let endOfHeaders;\n                debug('Regenerating stored HTTP header string for request');\n                req._header = null;\n                req._implicitHeader();\n                if (req.output && req.output.length > 0) {\n                    // Node < 12\n                    debug('Patching connection write() output buffer with updated header');\n                    first = req.output[0];\n                    endOfHeaders = first.indexOf('\\r\\n\\r\\n') + 4;\n                    req.output[0] = req._header + first.substring(endOfHeaders);\n                    debug('Output buffer: %o', req.output);\n                }\n                else if (req.outputData && req.outputData.length > 0) {\n                    // Node >= 12\n                    debug('Patching connection write() output buffer with updated header');\n                    first = req.outputData[0].data;\n                    endOfHeaders = first.indexOf('\\r\\n\\r\\n') + 4;\n                    req.outputData[0].data =\n                        req._header + first.substring(endOfHeaders);\n                    debug('Output buffer: %o', req.outputData[0].data);\n                }\n            }\n            // Wait for the socket's `connect` event, so that this `callback()`\n            // function throws instead of the `http` request machinery. This is\n            // important for i.e. `PacProxyAgent` which determines a failed proxy\n            // connection via the `callback()` function throwing.\n            yield (0, once_1.default)(socket, 'connect');\n            return socket;\n        });\n    }\n}\nexports[\"default\"] = HttpProxyAgent;\n//# sourceMappingURL=agent.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9odHRwLXByb3h5LWFnZW50L2Rpc3QvYWdlbnQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDRCQUE0QiwrREFBK0QsaUJBQWlCO0FBQzVHO0FBQ0Esb0NBQW9DLE1BQU0sK0JBQStCLFlBQVk7QUFDckYsbUNBQW1DLE1BQU0sbUNBQW1DLFlBQVk7QUFDeEYsZ0NBQWdDO0FBQ2hDO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsOEJBQThCLG1CQUFPLENBQUMsZ0JBQUs7QUFDM0MsOEJBQThCLG1CQUFPLENBQUMsZ0JBQUs7QUFDM0MsOEJBQThCLG1CQUFPLENBQUMsZ0JBQUs7QUFDM0MsZ0NBQWdDLG1CQUFPLENBQUMsaUVBQU87QUFDL0MsK0JBQStCLG1CQUFPLENBQUMsMEZBQW1CO0FBQzFELHFCQUFxQixtQkFBTyxDQUFDLDhHQUFZO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHFCQUFxQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOERBQThELDJDQUEyQztBQUN6RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxrQkFBZTtBQUNmIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9odHRwLXByb3h5LWFnZW50L2Rpc3QvYWdlbnQuanM/NThhNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2F3YWl0ZXIgPSAodGhpcyAmJiB0aGlzLl9fYXdhaXRlcikgfHwgZnVuY3Rpb24gKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xuICAgIGZ1bmN0aW9uIGFkb3B0KHZhbHVlKSB7IHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFAgPyB2YWx1ZSA6IG5ldyBQKGZ1bmN0aW9uIChyZXNvbHZlKSB7IHJlc29sdmUodmFsdWUpOyB9KTsgfVxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHJlamVjdGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yW1widGhyb3dcIl0odmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxuICAgICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XG4gICAgfSk7XG59O1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgbmV0XzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIm5ldFwiKSk7XG5jb25zdCB0bHNfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwidGxzXCIpKTtcbmNvbnN0IHVybF8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJ1cmxcIikpO1xuY29uc3QgZGVidWdfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiZGVidWdcIikpO1xuY29uc3Qgb25jZV8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJAdG9vdGFsbG5hdGUvb25jZVwiKSk7XG5jb25zdCBhZ2VudF9iYXNlXzEgPSByZXF1aXJlKFwiYWdlbnQtYmFzZVwiKTtcbmNvbnN0IGRlYnVnID0gKDAsIGRlYnVnXzEuZGVmYXVsdCkoJ2h0dHAtcHJveHktYWdlbnQnKTtcbmZ1bmN0aW9uIGlzSFRUUFMocHJvdG9jb2wpIHtcbiAgICByZXR1cm4gdHlwZW9mIHByb3RvY29sID09PSAnc3RyaW5nJyA/IC9eaHR0cHM6PyQvaS50ZXN0KHByb3RvY29sKSA6IGZhbHNlO1xufVxuLyoqXG4gKiBUaGUgYEh0dHBQcm94eUFnZW50YCBpbXBsZW1lbnRzIGFuIEhUVFAgQWdlbnQgc3ViY2xhc3MgdGhhdCBjb25uZWN0c1xuICogdG8gdGhlIHNwZWNpZmllZCBcIkhUVFAgcHJveHkgc2VydmVyXCIgaW4gb3JkZXIgdG8gcHJveHkgSFRUUCByZXF1ZXN0cy5cbiAqXG4gKiBAYXBpIHB1YmxpY1xuICovXG5jbGFzcyBIdHRwUHJveHlBZ2VudCBleHRlbmRzIGFnZW50X2Jhc2VfMS5BZ2VudCB7XG4gICAgY29uc3RydWN0b3IoX29wdHMpIHtcbiAgICAgICAgbGV0IG9wdHM7XG4gICAgICAgIGlmICh0eXBlb2YgX29wdHMgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICBvcHRzID0gdXJsXzEuZGVmYXVsdC5wYXJzZShfb3B0cyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBvcHRzID0gX29wdHM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFvcHRzKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2FuIEhUVFAoUykgcHJveHkgc2VydmVyIGBob3N0YCBhbmQgYHBvcnRgIG11c3QgYmUgc3BlY2lmaWVkIScpO1xuICAgICAgICB9XG4gICAgICAgIGRlYnVnKCdDcmVhdGluZyBuZXcgSHR0cFByb3h5QWdlbnQgaW5zdGFuY2U6ICVvJywgb3B0cyk7XG4gICAgICAgIHN1cGVyKG9wdHMpO1xuICAgICAgICBjb25zdCBwcm94eSA9IE9iamVjdC5hc3NpZ24oe30sIG9wdHMpO1xuICAgICAgICAvLyBJZiBgdHJ1ZWAsIHRoZW4gY29ubmVjdCB0byB0aGUgcHJveHkgc2VydmVyIG92ZXIgVExTLlxuICAgICAgICAvLyBEZWZhdWx0cyB0byBgZmFsc2VgLlxuICAgICAgICB0aGlzLnNlY3VyZVByb3h5ID0gb3B0cy5zZWN1cmVQcm94eSB8fCBpc0hUVFBTKHByb3h5LnByb3RvY29sKTtcbiAgICAgICAgLy8gUHJlZmVyIGBob3N0bmFtZWAgb3ZlciBgaG9zdGAsIGFuZCBzZXQgdGhlIGBwb3J0YCBpZiBuZWVkZWQuXG4gICAgICAgIHByb3h5Lmhvc3QgPSBwcm94eS5ob3N0bmFtZSB8fCBwcm94eS5ob3N0O1xuICAgICAgICBpZiAodHlwZW9mIHByb3h5LnBvcnQgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICBwcm94eS5wb3J0ID0gcGFyc2VJbnQocHJveHkucG9ydCwgMTApO1xuICAgICAgICB9XG4gICAgICAgIGlmICghcHJveHkucG9ydCAmJiBwcm94eS5ob3N0KSB7XG4gICAgICAgICAgICBwcm94eS5wb3J0ID0gdGhpcy5zZWN1cmVQcm94eSA/IDQ0MyA6IDgwO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcm94eS5ob3N0ICYmIHByb3h5LnBhdGgpIHtcbiAgICAgICAgICAgIC8vIElmIGJvdGggYSBgaG9zdGAgYW5kIGBwYXRoYCBhcmUgc3BlY2lmaWVkIHRoZW4gaXQncyBtb3N0IGxpa2VseVxuICAgICAgICAgICAgLy8gdGhlIHJlc3VsdCBvZiBhIGB1cmwucGFyc2UoKWAgY2FsbC4uLiB3ZSBuZWVkIHRvIHJlbW92ZSB0aGVcbiAgICAgICAgICAgIC8vIGBwYXRoYCBwb3J0aW9uIHNvIHRoYXQgYG5ldC5jb25uZWN0KClgIGRvZXNuJ3QgYXR0ZW1wdCB0byBvcGVuXG4gICAgICAgICAgICAvLyB0aGF0IGFzIGEgVW5peCBzb2NrZXQgZmlsZS5cbiAgICAgICAgICAgIGRlbGV0ZSBwcm94eS5wYXRoO1xuICAgICAgICAgICAgZGVsZXRlIHByb3h5LnBhdGhuYW1lO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMucHJveHkgPSBwcm94eTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FsbGVkIHdoZW4gdGhlIG5vZGUtY29yZSBIVFRQIGNsaWVudCBsaWJyYXJ5IGlzIGNyZWF0aW5nIGFcbiAgICAgKiBuZXcgSFRUUCByZXF1ZXN0LlxuICAgICAqXG4gICAgICogQGFwaSBwcm90ZWN0ZWRcbiAgICAgKi9cbiAgICBjYWxsYmFjayhyZXEsIG9wdHMpIHtcbiAgICAgICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgcHJveHksIHNlY3VyZVByb3h5IH0gPSB0aGlzO1xuICAgICAgICAgICAgY29uc3QgcGFyc2VkID0gdXJsXzEuZGVmYXVsdC5wYXJzZShyZXEucGF0aCk7XG4gICAgICAgICAgICBpZiAoIXBhcnNlZC5wcm90b2NvbCkge1xuICAgICAgICAgICAgICAgIHBhcnNlZC5wcm90b2NvbCA9ICdodHRwOic7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIXBhcnNlZC5ob3N0bmFtZSkge1xuICAgICAgICAgICAgICAgIHBhcnNlZC5ob3N0bmFtZSA9IG9wdHMuaG9zdG5hbWUgfHwgb3B0cy5ob3N0IHx8IG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocGFyc2VkLnBvcnQgPT0gbnVsbCAmJiB0eXBlb2Ygb3B0cy5wb3J0KSB7XG4gICAgICAgICAgICAgICAgcGFyc2VkLnBvcnQgPSBTdHJpbmcob3B0cy5wb3J0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChwYXJzZWQucG9ydCA9PT0gJzgwJykge1xuICAgICAgICAgICAgICAgIC8vIGlmIHBvcnQgaXMgODAsIHRoZW4gd2UgY2FuIHJlbW92ZSB0aGUgcG9ydCBzbyB0aGF0IHRoZVxuICAgICAgICAgICAgICAgIC8vIFwiOjgwXCIgcG9ydGlvbiBpcyBub3Qgb24gdGhlIHByb2R1Y2VkIFVSTFxuICAgICAgICAgICAgICAgIHBhcnNlZC5wb3J0ID0gJyc7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBDaGFuZ2UgdGhlIGBodHRwLkNsaWVudFJlcXVlc3RgIGluc3RhbmNlJ3MgXCJwYXRoXCIgZmllbGRcbiAgICAgICAgICAgIC8vIHRvIHRoZSBhYnNvbHV0ZSBwYXRoIG9mIHRoZSBVUkwgdGhhdCB3aWxsIGJlIHJlcXVlc3RlZC5cbiAgICAgICAgICAgIHJlcS5wYXRoID0gdXJsXzEuZGVmYXVsdC5mb3JtYXQocGFyc2VkKTtcbiAgICAgICAgICAgIC8vIEluamVjdCB0aGUgYFByb3h5LUF1dGhvcml6YXRpb25gIGhlYWRlciBpZiBuZWNlc3NhcnkuXG4gICAgICAgICAgICBpZiAocHJveHkuYXV0aCkge1xuICAgICAgICAgICAgICAgIHJlcS5zZXRIZWFkZXIoJ1Byb3h5LUF1dGhvcml6YXRpb24nLCBgQmFzaWMgJHtCdWZmZXIuZnJvbShwcm94eS5hdXRoKS50b1N0cmluZygnYmFzZTY0Jyl9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBDcmVhdGUgYSBzb2NrZXQgY29ubmVjdGlvbiB0byB0aGUgcHJveHkgc2VydmVyLlxuICAgICAgICAgICAgbGV0IHNvY2tldDtcbiAgICAgICAgICAgIGlmIChzZWN1cmVQcm94eSkge1xuICAgICAgICAgICAgICAgIGRlYnVnKCdDcmVhdGluZyBgdGxzLlNvY2tldGA6ICVvJywgcHJveHkpO1xuICAgICAgICAgICAgICAgIHNvY2tldCA9IHRsc18xLmRlZmF1bHQuY29ubmVjdChwcm94eSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBkZWJ1ZygnQ3JlYXRpbmcgYG5ldC5Tb2NrZXRgOiAlbycsIHByb3h5KTtcbiAgICAgICAgICAgICAgICBzb2NrZXQgPSBuZXRfMS5kZWZhdWx0LmNvbm5lY3QocHJveHkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gQXQgdGhpcyBwb2ludCwgdGhlIGh0dHAgQ2xpZW50UmVxdWVzdCdzIGludGVybmFsIGBfaGVhZGVyYCBmaWVsZFxuICAgICAgICAgICAgLy8gbWlnaHQgaGF2ZSBhbHJlYWR5IGJlZW4gc2V0LiBJZiB0aGlzIGlzIHRoZSBjYXNlIHRoZW4gd2UnbGwgbmVlZFxuICAgICAgICAgICAgLy8gdG8gcmUtZ2VuZXJhdGUgdGhlIHN0cmluZyBzaW5jZSB3ZSBqdXN0IGNoYW5nZWQgdGhlIGByZXEucGF0aGAuXG4gICAgICAgICAgICBpZiAocmVxLl9oZWFkZXIpIHtcbiAgICAgICAgICAgICAgICBsZXQgZmlyc3Q7XG4gICAgICAgICAgICAgICAgbGV0IGVuZE9mSGVhZGVycztcbiAgICAgICAgICAgICAgICBkZWJ1ZygnUmVnZW5lcmF0aW5nIHN0b3JlZCBIVFRQIGhlYWRlciBzdHJpbmcgZm9yIHJlcXVlc3QnKTtcbiAgICAgICAgICAgICAgICByZXEuX2hlYWRlciA9IG51bGw7XG4gICAgICAgICAgICAgICAgcmVxLl9pbXBsaWNpdEhlYWRlcigpO1xuICAgICAgICAgICAgICAgIGlmIChyZXEub3V0cHV0ICYmIHJlcS5vdXRwdXQubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBOb2RlIDwgMTJcbiAgICAgICAgICAgICAgICAgICAgZGVidWcoJ1BhdGNoaW5nIGNvbm5lY3Rpb24gd3JpdGUoKSBvdXRwdXQgYnVmZmVyIHdpdGggdXBkYXRlZCBoZWFkZXInKTtcbiAgICAgICAgICAgICAgICAgICAgZmlyc3QgPSByZXEub3V0cHV0WzBdO1xuICAgICAgICAgICAgICAgICAgICBlbmRPZkhlYWRlcnMgPSBmaXJzdC5pbmRleE9mKCdcXHJcXG5cXHJcXG4nKSArIDQ7XG4gICAgICAgICAgICAgICAgICAgIHJlcS5vdXRwdXRbMF0gPSByZXEuX2hlYWRlciArIGZpcnN0LnN1YnN0cmluZyhlbmRPZkhlYWRlcnMpO1xuICAgICAgICAgICAgICAgICAgICBkZWJ1ZygnT3V0cHV0IGJ1ZmZlcjogJW8nLCByZXEub3V0cHV0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAocmVxLm91dHB1dERhdGEgJiYgcmVxLm91dHB1dERhdGEubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBOb2RlID49IDEyXG4gICAgICAgICAgICAgICAgICAgIGRlYnVnKCdQYXRjaGluZyBjb25uZWN0aW9uIHdyaXRlKCkgb3V0cHV0IGJ1ZmZlciB3aXRoIHVwZGF0ZWQgaGVhZGVyJyk7XG4gICAgICAgICAgICAgICAgICAgIGZpcnN0ID0gcmVxLm91dHB1dERhdGFbMF0uZGF0YTtcbiAgICAgICAgICAgICAgICAgICAgZW5kT2ZIZWFkZXJzID0gZmlyc3QuaW5kZXhPZignXFxyXFxuXFxyXFxuJykgKyA0O1xuICAgICAgICAgICAgICAgICAgICByZXEub3V0cHV0RGF0YVswXS5kYXRhID1cbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcS5faGVhZGVyICsgZmlyc3Quc3Vic3RyaW5nKGVuZE9mSGVhZGVycyk7XG4gICAgICAgICAgICAgICAgICAgIGRlYnVnKCdPdXRwdXQgYnVmZmVyOiAlbycsIHJlcS5vdXRwdXREYXRhWzBdLmRhdGEpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIFdhaXQgZm9yIHRoZSBzb2NrZXQncyBgY29ubmVjdGAgZXZlbnQsIHNvIHRoYXQgdGhpcyBgY2FsbGJhY2soKWBcbiAgICAgICAgICAgIC8vIGZ1bmN0aW9uIHRocm93cyBpbnN0ZWFkIG9mIHRoZSBgaHR0cGAgcmVxdWVzdCBtYWNoaW5lcnkuIFRoaXMgaXNcbiAgICAgICAgICAgIC8vIGltcG9ydGFudCBmb3IgaS5lLiBgUGFjUHJveHlBZ2VudGAgd2hpY2ggZGV0ZXJtaW5lcyBhIGZhaWxlZCBwcm94eVxuICAgICAgICAgICAgLy8gY29ubmVjdGlvbiB2aWEgdGhlIGBjYWxsYmFjaygpYCBmdW5jdGlvbiB0aHJvd2luZy5cbiAgICAgICAgICAgIHlpZWxkICgwLCBvbmNlXzEuZGVmYXVsdCkoc29ja2V0LCAnY29ubmVjdCcpO1xuICAgICAgICAgICAgcmV0dXJuIHNvY2tldDtcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gSHR0cFByb3h5QWdlbnQ7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hZ2VudC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/http-proxy-agent/dist/agent.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/http-proxy-agent/dist/index.js":
/*!*********************************************************!*\
  !*** ../../node_modules/http-proxy-agent/dist/index.js ***!
  \*********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nconst agent_1 = __importDefault(__webpack_require__(/*! ./agent */ \"(instrument)/../../node_modules/http-proxy-agent/dist/agent.js\"));\nfunction createHttpProxyAgent(opts) {\n    return new agent_1.default(opts);\n}\n(function (createHttpProxyAgent) {\n    createHttpProxyAgent.HttpProxyAgent = agent_1.default;\n    createHttpProxyAgent.prototype = agent_1.default.prototype;\n})(createHttpProxyAgent || (createHttpProxyAgent = {}));\nmodule.exports = createHttpProxyAgent;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9odHRwLXByb3h5LWFnZW50L2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDZDQUE2QztBQUM3QztBQUNBLGdDQUFnQyxtQkFBTyxDQUFDLCtFQUFTO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsb0RBQW9EO0FBQ3JEO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2h0dHAtcHJveHktYWdlbnQvZGlzdC9pbmRleC5qcz83NTMzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuY29uc3QgYWdlbnRfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiLi9hZ2VudFwiKSk7XG5mdW5jdGlvbiBjcmVhdGVIdHRwUHJveHlBZ2VudChvcHRzKSB7XG4gICAgcmV0dXJuIG5ldyBhZ2VudF8xLmRlZmF1bHQob3B0cyk7XG59XG4oZnVuY3Rpb24gKGNyZWF0ZUh0dHBQcm94eUFnZW50KSB7XG4gICAgY3JlYXRlSHR0cFByb3h5QWdlbnQuSHR0cFByb3h5QWdlbnQgPSBhZ2VudF8xLmRlZmF1bHQ7XG4gICAgY3JlYXRlSHR0cFByb3h5QWdlbnQucHJvdG90eXBlID0gYWdlbnRfMS5kZWZhdWx0LnByb3RvdHlwZTtcbn0pKGNyZWF0ZUh0dHBQcm94eUFnZW50IHx8IChjcmVhdGVIdHRwUHJveHlBZ2VudCA9IHt9KSk7XG5tb2R1bGUuZXhwb3J0cyA9IGNyZWF0ZUh0dHBQcm94eUFnZW50O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/http-proxy-agent/dist/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/http-proxy-agent/node_modules/agent-base/dist/src/index.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/http-proxy-agent/node_modules/agent-base/dist/src/index.js ***!
  \*************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(instrument)/../../node_modules/debug/src/index.js\"));\nconst promisify_1 = __importDefault(__webpack_require__(/*! ./promisify */ \"(instrument)/../../node_modules/http-proxy-agent/node_modules/agent-base/dist/src/promisify.js\"));\nconst debug = debug_1.default('agent-base');\nfunction isAgent(v) {\n    return Boolean(v) && typeof v.addRequest === 'function';\n}\nfunction isSecureEndpoint() {\n    const { stack } = new Error();\n    if (typeof stack !== 'string')\n        return false;\n    return stack.split('\\n').some(l => l.indexOf('(https.js:') !== -1 || l.indexOf('node:https:') !== -1);\n}\nfunction createAgent(callback, opts) {\n    return new createAgent.Agent(callback, opts);\n}\n(function (createAgent) {\n    /**\n     * Base `http.Agent` implementation.\n     * No pooling/keep-alive is implemented by default.\n     *\n     * @param {Function} callback\n     * @api public\n     */\n    class Agent extends events_1.EventEmitter {\n        constructor(callback, _opts) {\n            super();\n            let opts = _opts;\n            if (typeof callback === 'function') {\n                this.callback = callback;\n            }\n            else if (callback) {\n                opts = callback;\n            }\n            // Timeout for the socket to be returned from the callback\n            this.timeout = null;\n            if (opts && typeof opts.timeout === 'number') {\n                this.timeout = opts.timeout;\n            }\n            // These aren't actually used by `agent-base`, but are required\n            // for the TypeScript definition files in `@types/node` :/\n            this.maxFreeSockets = 1;\n            this.maxSockets = 1;\n            this.maxTotalSockets = Infinity;\n            this.sockets = {};\n            this.freeSockets = {};\n            this.requests = {};\n            this.options = {};\n        }\n        get defaultPort() {\n            if (typeof this.explicitDefaultPort === 'number') {\n                return this.explicitDefaultPort;\n            }\n            return isSecureEndpoint() ? 443 : 80;\n        }\n        set defaultPort(v) {\n            this.explicitDefaultPort = v;\n        }\n        get protocol() {\n            if (typeof this.explicitProtocol === 'string') {\n                return this.explicitProtocol;\n            }\n            return isSecureEndpoint() ? 'https:' : 'http:';\n        }\n        set protocol(v) {\n            this.explicitProtocol = v;\n        }\n        callback(req, opts, fn) {\n            throw new Error('\"agent-base\" has no default implementation, you must subclass and override `callback()`');\n        }\n        /**\n         * Called by node-core's \"_http_client.js\" module when creating\n         * a new HTTP request with this Agent instance.\n         *\n         * @api public\n         */\n        addRequest(req, _opts) {\n            const opts = Object.assign({}, _opts);\n            if (typeof opts.secureEndpoint !== 'boolean') {\n                opts.secureEndpoint = isSecureEndpoint();\n            }\n            if (opts.host == null) {\n                opts.host = 'localhost';\n            }\n            if (opts.port == null) {\n                opts.port = opts.secureEndpoint ? 443 : 80;\n            }\n            if (opts.protocol == null) {\n                opts.protocol = opts.secureEndpoint ? 'https:' : 'http:';\n            }\n            if (opts.host && opts.path) {\n                // If both a `host` and `path` are specified then it's most\n                // likely the result of a `url.parse()` call... we need to\n                // remove the `path` portion so that `net.connect()` doesn't\n                // attempt to open that as a unix socket file.\n                delete opts.path;\n            }\n            delete opts.agent;\n            delete opts.hostname;\n            delete opts._defaultAgent;\n            delete opts.defaultPort;\n            delete opts.createConnection;\n            // Hint to use \"Connection: close\"\n            // XXX: non-documented `http` module API :(\n            req._last = true;\n            req.shouldKeepAlive = false;\n            let timedOut = false;\n            let timeoutId = null;\n            const timeoutMs = opts.timeout || this.timeout;\n            const onerror = (err) => {\n                if (req._hadError)\n                    return;\n                req.emit('error', err);\n                // For Safety. Some additional errors might fire later on\n                // and we need to make sure we don't double-fire the error event.\n                req._hadError = true;\n            };\n            const ontimeout = () => {\n                timeoutId = null;\n                timedOut = true;\n                const err = new Error(`A \"socket\" was not created for HTTP request before ${timeoutMs}ms`);\n                err.code = 'ETIMEOUT';\n                onerror(err);\n            };\n            const callbackError = (err) => {\n                if (timedOut)\n                    return;\n                if (timeoutId !== null) {\n                    clearTimeout(timeoutId);\n                    timeoutId = null;\n                }\n                onerror(err);\n            };\n            const onsocket = (socket) => {\n                if (timedOut)\n                    return;\n                if (timeoutId != null) {\n                    clearTimeout(timeoutId);\n                    timeoutId = null;\n                }\n                if (isAgent(socket)) {\n                    // `socket` is actually an `http.Agent` instance, so\n                    // relinquish responsibility for this `req` to the Agent\n                    // from here on\n                    debug('Callback returned another Agent instance %o', socket.constructor.name);\n                    socket.addRequest(req, opts);\n                    return;\n                }\n                if (socket) {\n                    socket.once('free', () => {\n                        this.freeSocket(socket, opts);\n                    });\n                    req.onSocket(socket);\n                    return;\n                }\n                const err = new Error(`no Duplex stream was returned to agent-base for \\`${req.method} ${req.path}\\``);\n                onerror(err);\n            };\n            if (typeof this.callback !== 'function') {\n                onerror(new Error('`callback` is not defined'));\n                return;\n            }\n            if (!this.promisifiedCallback) {\n                if (this.callback.length >= 3) {\n                    debug('Converting legacy callback function to promise');\n                    this.promisifiedCallback = promisify_1.default(this.callback);\n                }\n                else {\n                    this.promisifiedCallback = this.callback;\n                }\n            }\n            if (typeof timeoutMs === 'number' && timeoutMs > 0) {\n                timeoutId = setTimeout(ontimeout, timeoutMs);\n            }\n            if ('port' in opts && typeof opts.port !== 'number') {\n                opts.port = Number(opts.port);\n            }\n            try {\n                debug('Resolving socket for %o request: %o', opts.protocol, `${req.method} ${req.path}`);\n                Promise.resolve(this.promisifiedCallback(req, opts)).then(onsocket, callbackError);\n            }\n            catch (err) {\n                Promise.reject(err).catch(callbackError);\n            }\n        }\n        freeSocket(socket, opts) {\n            debug('Freeing socket %o %o', socket.constructor.name, opts);\n            socket.destroy();\n        }\n        destroy() {\n            debug('Destroying agent %o', this.constructor.name);\n        }\n    }\n    createAgent.Agent = Agent;\n    // So that `instanceof` works correctly\n    createAgent.prototype = createAgent.Agent.prototype;\n})(createAgent || (createAgent = {}));\nmodule.exports = createAgent;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/http-proxy-agent/node_modules/agent-base/dist/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/http-proxy-agent/node_modules/agent-base/dist/src/promisify.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/http-proxy-agent/node_modules/agent-base/dist/src/promisify.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nfunction promisify(fn) {\n    return function (req, opts) {\n        return new Promise((resolve, reject) => {\n            fn.call(this, req, opts, (err, rtn) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(rtn);\n                }\n            });\n        });\n    };\n}\nexports[\"default\"] = promisify;\n//# sourceMappingURL=promisify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9odHRwLXByb3h5LWFnZW50L25vZGVfbW9kdWxlcy9hZ2VudC1iYXNlL2Rpc3Qvc3JjL3Byb21pc2lmeS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBLGtCQUFlO0FBQ2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL2h0dHAtcHJveHktYWdlbnQvbm9kZV9tb2R1bGVzL2FnZW50LWJhc2UvZGlzdC9zcmMvcHJvbWlzaWZ5LmpzP2UxMTgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5mdW5jdGlvbiBwcm9taXNpZnkoZm4pIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKHJlcSwgb3B0cykge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgZm4uY2FsbCh0aGlzLCByZXEsIG9wdHMsIChlcnIsIHJ0bikgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKHJ0bik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIH07XG59XG5leHBvcnRzLmRlZmF1bHQgPSBwcm9taXNpZnk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wcm9taXNpZnkuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/http-proxy-agent/node_modules/agent-base/dist/src/promisify.js\n");

/***/ })

};
;
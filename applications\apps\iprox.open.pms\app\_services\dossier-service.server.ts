import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { DossierListParams } from '@/models/dossier-list-filter.model';

// TODO: Add sortfield?
export async function getDossierPaged(
  params: DossierListParams
): Promise<components['schemas']['GetPagedDossierResponse']> {
  try {
    // Filter undefined values from the string;
    const paramsString = new URLSearchParams(
      Object.entries(params).reduce<Record<string, string>>((acc, [key, value]: [string, string | undefined]) => {
        if (value) {
          acc[key] = value;
        }

        return acc;
      }, {})
    ).toString();

    return await serverApi
      .get(`dossier/paged?${paramsString}`)
      .json<components['schemas']['GetPagedDossierResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function getDossierLatestVersion(
  dossierId: string
): Promise<components['schemas']['GetLatestDossierVersionResponse']> {
  try {
    return await serverApi
      .get(`dossier/${dossierId}/latest-version`, { cache: 'no-store' })
      .json<components['schemas']['GetLatestDossierVersionResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function getDossierVersions(
  dossierId: string
): Promise<components['schemas']['GetDossierAllVersionsResponse']> {
  try {
    return await serverApi
      .get(`dossier/${dossierId}/versions`, { cache: 'no-store' })
      .json<components['schemas']['GetDossierAllVersionsResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

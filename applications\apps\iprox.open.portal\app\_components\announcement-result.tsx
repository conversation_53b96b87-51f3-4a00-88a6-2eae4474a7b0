import { FileTypeLabel, Text } from '@iprox/react-ui';
import { useFormatter } from 'next-intl';
import Link from 'next/link';

import { FileDownloadLink } from './file-download-link';
import { HighlightSearchParams } from './highlight-search-params';

export interface AnnouncementResultProps {
  url: string;
  title: string;
  available?: Date;
  subject: string;
  fileType?: string;
  fileLink?: string;
}

export function AnnouncementResult({ url, title, available, subject, fileType, fileLink }: AnnouncementResultProps) {
  const format = useFormatter();

  return (
    <div>
      {fileType || subject ? (
        <div className="mb-4 flex flex-row items-center gap-x-2.5">
          {fileType && <FileTypeLabel label={fileType.toUpperCase()} />}
          {fileType && subject && <Text className="font-heading text-body text-base font-normal">|</Text>}
          {subject && (
            <Text className="font-heading text-body text-base font-normal">
              <HighlightSearchParams content={subject} />
            </Text>
          )}
        </div>
      ) : null}
      <div className="mb-4">
        <Link
          href={url ?? ''}
          className="font-heading text-heading text-2xl font-semibold underline-offset-2 hover:underline"
        >
          <HighlightSearchParams content={title} />
        </Link>
      </div>
      {available ? (
        <div className="font-heading text-body mb-4 text-base font-normal">
          {`${format.dateTime(new Date(available), {
            dateStyle: 'long',
          })}`}
        </div>
      ) : null}

      {fileLink && fileType && <FileDownloadLink link={fileLink ?? ''} fileType={fileType.toUpperCase()} />}
    </div>
  );
}

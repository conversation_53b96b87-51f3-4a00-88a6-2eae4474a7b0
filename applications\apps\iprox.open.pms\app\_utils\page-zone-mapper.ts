import { components } from '@/iprox-open.interface';
import { PageZone } from '@iprox/iprox-ui';

export const pageZoneMapper = (zonesValue: PageZone[]): components['schemas']['PageZoneUpdateModel'][] => {
  if (zonesValue.length) {
    const zones: components['schemas']['PageZoneUpdateModel'][] = [];

    Array.isArray(zonesValue) &&
      zonesValue.forEach((zone) => {
        if (typeof zone === 'object') {
          const { id, order, blocks } = zone;

          const sanitizedBlocks: components['schemas']['RichTextBlockUpdateModel'][] = [];

          Array.isArray(blocks) &&
            blocks.forEach((blockItem) => {
              const block: components['schemas']['RichTextBlockUpdateModel'] = {
                id: typeof blockItem.id === 'string' ? blockItem.id : null,
                colspan: typeof blockItem.colspan === 'number' ? blockItem.colspan : 0,
                blockContent: {
                  content: typeof blockItem.blockContent?.content === 'string' ? blockItem.blockContent?.content : '',
                } as components['schemas']['RichTextBlockContentUpdateModel'],
                blockType: typeof blockItem.blockType === 'string' ? blockItem.blockType : 'RichText',
                order: typeof blockItem.order === 'number' ? blockItem.order : 0,
              };

              sanitizedBlocks.push(block);
            });

          const sanatizedZone: components['schemas']['PageZoneUpdateModel'] = {
            id: typeof id === 'string' ? id : null,
            order: typeof order === 'number' ? order : 0,
            blocks: sanitizedBlocks,
          };

          zones.push(sanatizedZone);
        }
      });

    return zones;
  }

  return [];
};

import { getHomepageContent, getPublicAssets } from '@/services/public-service.server';
import { getTranslations } from 'next-intl/server';

import { HomePageContentForm } from '@/components/home-page-content-form';
import { HomePageImageUploader } from '@/components/home-page-image-uploader';
import { PageHeader } from '@/components/page-header';

export default async function Page() {
  const [publicAssets, homePageContent] = await Promise.all([getPublicAssets(), getHomepageContent()]);
  const t = await getTranslations('navigation');

  return (
    <div>
      <PageHeader title={t('homepage')} />
      <div className="mb-12">
        <HomePageImageUploader homePageImageAssetPath={publicAssets?.siteAssets.homePageImage} />
      </div>
      <div className="mb-12">
        <HomePageContentForm pageZones={homePageContent.homePage.pageZones} />
      </div>
    </div>
  );
}

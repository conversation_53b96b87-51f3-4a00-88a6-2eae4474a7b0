@use '../../scss/utils.scss';

.ipx-application-shell {
  /* stylelint-disable-next-line selector-pseudo-class-no-unknown -- global */
  :global {
    .ipx-menu {
      li {
        background-color: theme('colors.transparent');
        color: theme('colors.base-100');

        &.is-active {
          background-color: theme('colors.highlight');
          color: theme('colors.base-00');
        }

        &.is-open {
          background-color: theme('colors.base-10');
        }
      }
    }
  }
}

.content-wrapper {
  padding: 66px 92px;
}

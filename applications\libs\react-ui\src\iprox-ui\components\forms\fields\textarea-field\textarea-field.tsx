import cx from 'classnames';
import { useField } from 'formik';

import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { TextFieldDefinition } from '../../models/form.models';
import { borderClassname } from '../../utils/border-classname';

export function TextareaField(props: TextFieldDefinition) {
  const [field, meta, _helpers] = useField(props);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'text-area');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  return (
    <FormField definition={props} errorMessage={meta.touched ? meta.error : undefined} {...formControlProps}>
      <textarea
        {...inputProps}
        {...field}
        className={cx(
          'rounded-input font-text text-body focus:!border-highlight h-40 w-full resize-none bg-transparent text-sm focus:ring-0 focus:ring-offset-0',
          borderClassname(meta.touched ? meta.error : undefined, field.value)
        )}
      />
    </FormField>
  );
}

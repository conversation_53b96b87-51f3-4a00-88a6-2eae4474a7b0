import { components } from '@/iprox-open.interface';

import { ContentZones } from './content-zones';

interface SiteFooterProps {
  pageZones: components['schemas']['PageZoneDto'][];
}

export function SiteFooter({ pageZones }: SiteFooterProps) {
  return (
    <div className="bg-footer-background flex w-full justify-center">
      <div className="max-w-content px-4 py-12 md:px-8 xl:px-0">
        <ContentZones pageZones={pageZones} />
      </div>
    </div>
  );
}

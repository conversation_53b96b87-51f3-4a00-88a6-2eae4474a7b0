{"dossier": {"pageTitle": "This page is about this site", "blockedFiles": "Blocked Files", "ok": "OK", "closeZip": "Close zip upload screen", "formCtaText": "Save changes", "newDossier": "[New dossier]", "newDossierTitle": "Add a new dossier", "newDossierCta": "Add", "category": "Category", "loading": "Loading dossier", "title": "Title", "changeTitle": "Edit title", "saveTitle": "Save title", "cancelEditTitle": "Cancel", "uploadFiles": "Upload files", "finalizingUpload": "Finalizing upload", "uploadingFiles": "Uploading file..", "downloadSelected": "Download", "save": "Save", "publish": "Publish", "unpublish": "Unpublish", "delete": "Delete", "summary": "Summary", "toDate": "To Date", "changeImage": "Change dossier image", "close": "Close", "done": "Done", "clearAll": "Clear all", "clearToUpload": "Clear Previous Uploads to Upload New Files", "confirmation": {"publish": {"title": "Publish Dossier", "message": "Are you sure your want to publish this dossier?"}, "save": {"title": "Save <PERSON><PERSON>r", "message": "Are you sure you want to save these changes?"}, "delete": {"title": "Delete Dossier", "message": "Are you sure you want to delete this dossier?"}, "unpublish": {"title": "Unpublish Dossier", "message": "Are you sure your want to unpublish this dossier?"}, "deleteSelectedNodes": {"title": "Delete Selected Folder(s)/File(s)", "message": "Are you sure your want to delete the selected folder(s)/file(s)?"}}, "success": {"save": "<PERSON><PERSON><PERSON> Saved", "delete": "Dossier Deleted", "publish": "Dossier Published", "unpublish": "Dossier Unpublished"}, "uploadImage": "Upload image", "imageUpdated": "Image updated", "action": "Action", "fileUpload": {"minutes": "minutes", "seconds": "seconds", "left": "over", "success": "Upload successful!", "error": "error - {error}", "queued": "Queued...", "selected": "Selected", "remove": "Remove {fileName}"}, "disallowedFileType": "This file type is not allowed", "chooseOption": "Choose an option", "addFolder": "Add Folder", "uploadDocuments": "Upload documents", "createNewFolder": "Create new folder", "folderName": "Folder Name", "folderNameHelperText": "Folder name must be between 1 and 256 characters long", "createFolder": "Create Folder", "cancel": "Cancel", "renameSuccess": "{nodeName} renamed to {newName}", "folderCreated": "{folderName} was created under {parentFolderName} successfully", "deleteSuccess": "Successfully deleted", "zipUpload": "Upload zip", "disallowedFiles": "Red colored files are disallowed and will not be uploaded"}, "dashboard": {"greeting": "Hello {name}", "intro": "You can start right away, use the button below to create new documents or files.", "cta": "New dossier", "statistics": "Statistics", "typeOfPublication": "Choose type of publication", "publishedFromTill": "Published from/till", "publishedBy": "Published by", "numberOfDossiers": "Number of published dossiers", "numberOfFiles": "Number of published files", "data": "Data", "allPublicationTypes": "All publication types"}, "navigation": {"dashboard": "Dashboard", "dossiers": "Dossiers", "management": "Management", "session": "Session", "sessionClient": "Session Client", "views": "Views", "fonts": "Fonts", "siteConfiguration": "Site configuration", "siteNavigation": "Site navigation", "homepage": "Homepage", "back": "Back", "pages": "Pages", "footer": "Footer"}, "iprox": {"test": "testEnglish"}, "dossiersList": {"title": "Dossiers", "headerName": "Name", "headerType": "Type", "headerModified": "Modified on", "headerDossierDate": "Dossier date", "headerBy": "By", "headerPublished": "Latest publication", "headerAuthor": "Author", "noResults": "No results found", "search": "Search", "type": {"title": "Type", "all": "All types"}, "status": {"title": "Status", "all": "All statuses"}, "published": "Published", "unpublished": "Unpublished"}, "logout": {"logout": "Logout", "warningMessage": "Are you certain you want to log out? Any unsaved data will be lost.", "errorMessage": "Logout Error! Please try again.", "cancel": "Cancel"}, "login": {"CredentialsSignin": "Invalid credentials", "FetchUser": "User fetching error. Please try again.", "azureLoginButton": "Login with Azure"}, "site": {"logoSettings": "Logo settings", "changeLogo": "Change logo", "faviconSettings": "Favicon settings", "changeFavicon": "Change favicon", "logoUpdated": "Logo updated", "faviconUpdated": "Favicon updated", "homePageImageUpdated": "Home page image updated", "changeHomePageImage": "Change image", "sections": "Sections", "bottomLeft": "Bottom left", "bottomRight": "Bottom right", "footerLeft": "<PERSON><PERSON> left", "footerRight": "Footer right", "save": "Save", "contentUpdated": "Content updated", "styleSettings": "Style settings", "stylesUpdated": "Styles updated", "unit": "Unit: px", "content": "Content", "bannerPhoto": "Banner photo", "addBannerPhoto": "Put the file here \nor", "pageContent": "Page Content"}, "views": {"views": "Views", "newView": "New view", "newViewTitle": "Add a new view", "label": "Label", "slug": "Slug", "add": "Add", "editViewTitle": "Edit view", "save": "Save", "introduction": "Introduction", "categories": "Categories", "viewUpdated": "View updated", "delete": "Delete", "deleteConfirmationTitle": "Delete view", "deleteConfirmationMessage": "Are you sure you want to delete the view {viewTitle}?", "viewDeleted": "View deleted"}, "siteNavigation": {"unusedPages": "Unused pages", "editNavigationItemLabel": "Edit the page navigation name", "save": "Save navigation structure", "editPage": "Edit page", "outsideNavigation": "Pages outside the navigation", "navigationStructureUpdated": "Navigation structure updated", "navigation": "Navigation", "navigationItems": "Navigation items"}, "fonts": {"fonts": "Fonts", "fontFamily": "Font Family", "fontWeight": "Font Weight", "fontStyle": "Font Style", "fontCategory": "Font Category", "normal": "Normal", "italic": "Italic", "oblique": "Oblique", "initial": "Initial", "inherit": "Inherit", "lighter": "Lighter", "w100": "100 (Thin)", "w200": "200 (Extra Light)", "w300": "300 (Light)", "w400": "400 (Normal)", "w500": "500 (Medium)", "w600": "600 (Semi Bold)", "w700": "700 (Bold)", "w800": "800 (Extra Bold)", "w900": "900 (Black)", "heading": "Headings", "text": "Text", "footer": "Footer", "category": "Category: {value}", "style": "Style: {value}", "addFontFiles": "Add Font Files", "createFont": "C<PERSON><PERSON>", "updateFont": "Update", "saveFont": "Save Font", "cancel": "Cancel", "delete": "Delete", "deleteFontFile": "Delete {fontFileName}", "uploadingFonts": "Uploading Fonts", "finalizingUpload": "Finalizing upload", "retry": "Retry", "confirmation": {"deleteFontAsset": {"title": "Delete font asset", "message": "Are you sure you want to delete this font?"}, "deleteFontFile": {"title": "Delete font file", "message": "Are you sure you want to delete this font file?"}}}, "pages": {"heading": "Pages", "title": "Title", "published": "Published", "type": "Type", "category": "Category", "search": "Search", "status": "Status", "all": "All", "unpublished": "Unpublished", "searchPage": "Search page", "announcements": "Announcements", "addAPage": "Add a page", "action": "Action", "delete": "Delete", "publish": "Publish", "hide": "<PERSON>de", "deselectPage": "Deselect page {page}", "selectPage": "Select page {page}", "continue": "Continue", "slug": "Slug", "categories": "Categories", "introductionText": "Introduction text", "save": "Save", "pageDeleted": "Page deleted", "pagePublished": "Page published", "pageUnpublished": "Page unpublished", "titleHelperText": "The title of the page shows in the main navigation and must contain at least {min} and at most {max} characters", "pageUpdated": "Page updated", "deleteConfirmationMessage": "Are you sure you want to delete this page?", "publishConfirmationMessage": "Are you sure you want to publish this page?", "unpublishConfirmationMessage": "Are you sure you want to unpublish this page?", "organizationName": "Organization Name", "organizationNameHelperText": "Used as a GVOP creator filter", "organizationType": "Organization Type", "organizationTypeHelperText": "Used as a GVOP organization type filter", "publicationType": "Publication Type", "publicationTypeHelperText": "Used as GVOP publication name filter", "slugHelperText": "The slug is the page title that can be found in the URL (for example: /my-new-page)", "pageContent": "Page Content", "simplePage": "Simple page", "simple": "Simple"}, "notifications": {"notifications": "Notifications", "uploading": "Uploading", "uploadingFiles": "Uploading files of \"{dossierName}\"", "noNotifications": "No notifications", "dismissAll": "Dismiss all", "queued": "Queued", "uploadComplete": "Success", "error": "Error", "queuedFile": "Queued file {fileName}", "uploadingFile": "Uploading file {fileName}", "fileUploaded": "File {fileName} uploaded successfully!", "fileUploadError": "Error uploading file {fileName}: {error}", "view": "View"}, "footer": {"footer": "Footer", "footerContent": "Footer content", "socialMedia": "Social media", "addLink": "Add link", "save": "Save", "footerComponentUpdated": "Footer component updated"}}
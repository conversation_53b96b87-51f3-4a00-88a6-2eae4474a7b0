import * as OutlineIcons from '@heroicons/react/24/outline';
import * as SolidIcons from '@heroicons/react/24/solid';
import { ButtonHTMLAttributes, useMemo } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'tertiary';
  className?: string;
  icon?: keyof typeof OutlineIcons | keyof typeof SolidIcons;
  iconPosition?: 'left' | 'right';
}

export function Button({
  children,
  variant = 'primary',
  className,
  type = 'button',
  icon,
  iconPosition,
  disabled,
  ...rest
}: ButtonProps) {
  const dynamicClasses = useMemo(() => {
    const baseClasses =
      'flex min-w-[220px] min-h-[50px] max-w-fit max-h-fit flex-row items-center justify-center gap-x-[15px] rounded-medium py-[10px] px-[30px] text-lg border-2 border-transparent';
    switch (variant) {
      case 'tertiary':
        return `${disabled ? 'text-content-extra-lite' : 'text-primary-content'} underline underline-offset-4`;
      case 'secondary':
        return `${
          disabled ? 'opacity-80 text-content-lite' : 'bg-secondary text-secondary-content hover:bg-secondary-hover'
        } bg-secondary text-secondary-content focus:border-2 focus:border-secondary  ${baseClasses}`;
      case 'primary':
      default:
        return `${
          disabled ? 'opacity-80' : 'hover:bg-primary-hover hover:text-primary-hover-content'
        } bg-primary text-primary-content ${baseClasses}`;
    }
  }, [disabled, variant]);

  const Icon = OutlineIcons[icon as keyof typeof OutlineIcons] || SolidIcons[icon as keyof typeof SolidIcons];

  return (
    <button
      className={`font-heading font-bold ${dynamicClasses} ${className}`}
      type={type}
      disabled={disabled}
      {...rest}
    >
      {icon && (iconPosition === 'left' || !iconPosition) ? <Icon className="h-6 w-6" /> : null}
      {children}
      {icon && iconPosition === 'right' ? <Icon className="h-6 w-6" /> : null}
    </button>
  );
}

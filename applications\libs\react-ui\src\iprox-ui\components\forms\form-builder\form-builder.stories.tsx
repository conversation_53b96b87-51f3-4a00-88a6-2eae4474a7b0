import { action } from '@storybook/addon-actions';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Button } from '../../button/button';
import { ConfirmDialogProvider } from '../../confirm-dialog/context/confirm-dialog.context';
import { FileUpload } from '../../file-upload/file-upload';
import { FormBuilder } from './form-builder';
import {
  ALLOWED_ZIP_FILE_TYPES,
  dossierForm,
  fontForm,
  homeConfigForm,
  loginForm,
  newDossierForm,
} from './form-builder.content';

const meta: Meta<typeof FormBuilder> = {
  title: 'iprox-ui/forms/form-builder',
  component: FormBuilder,
  argTypes: {
    onSubmit: {
      action: 'onClick',
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof FormBuilder>;

export const Default: Story = {
  name: 'default',
  args: {
    buttonText: 'Save',
    children: (
      <div className="mb-5">
        <FileUpload onFileSelect={() => null} allowedFileTypes={ALLOWED_ZIP_FILE_TYPES} />
      </div>
    ),
    fields: dossierForm,
  },
};

export const Login: Story = {
  name: 'login',
  args: {
    buttonText: 'Login',
    fields: loginForm,
  },
};

export const AddNewDossier: Story = {
  name: 'add-new-dossier',
  args: {
    buttonText: 'Next',
    fields: newDossierForm,
  },
};

export const FontForm: Story = {
  name: 'add-new-font-asset',
  args: {
    buttonText: 'Save',
    fields: fontForm,
    gridContainerClasses: 'grid grid-cols-6 gap-2',
    buttons: (
      <div className="col-span-6 grid grid-flow-col justify-end gap-2">
        <Button type="submit" variant="primary">
          Lettertype maken
        </Button>
        <Button type="reset" variant="secondary">
          Annuleren
        </Button>
      </div>
    ),
  },
};

export const HomeConfigForm: Story = {
  name: 'Home-cofig-form',
  args: {
    fields: homeConfigForm,
    buttons: (
      <div className="col-span-3 grid grid-flow-col justify-end gap-2">
        <Button type="submit" variant="primary">
          Opslaan
        </Button>
      </div>
    ),
    onSubmit: action('on-click'),
  },
  render: (args) => {
    return (
      <ConfirmDialogProvider>
        <FormBuilder fields={args.fields} buttons={args.buttons} onSubmit={args.onSubmit} />
      </ConfirmDialogProvider>
    );
  },
};

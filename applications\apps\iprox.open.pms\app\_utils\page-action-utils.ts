import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { getPage, managePage } from '@/services/page-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { showToast } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

export const usePageAction = () => {
  const t = useTranslations('pages');
  const clientApi = useClientApi();

  const [isActionProcessing, setIsActionProcessing] = useState(false);

  const displaySuccessToast = (action: 'delete' | 'publish' | 'unpublish') => {
    switch (action) {
      case 'delete':
        showToast(t('pageDeleted'), { type: 'success' });
        break;
      case 'publish':
        showToast(t('pagePublished'), { type: 'success' });
        break;
      case 'unpublish':
        showToast(t('pageUnpublished'), { type: 'success' });
        break;
    }
  };

  const getConfirmationMessage = (action: 'delete' | 'publish' | 'unpublish') => {
    switch (action) {
      case 'delete':
        return t('deleteConfirmationMessage');
      case 'publish':
        return t('publishConfirmationMessage');
      case 'unpublish':
        return t('unpublishConfirmationMessage');
    }
  };

  const performPageAction = async (
    pageId: string,
    pageType: 'Search' | 'Announcement' | 'Simple',
    action: 'delete' | 'publish' | 'unpublish'
  ): Promise<components['schemas']['GetPageResponse'] | undefined> => {
    try {
      setIsActionProcessing(true);
      await managePage(clientApi, pageId, pageType, action);
      displaySuccessToast(action);
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    } finally {
      setIsActionProcessing(false);
    }

    if (action === 'delete') {
      window.location.href = '/page/list';
    } else {
      const pageData = await getPage(clientApi, pageId);
      return pageData;
    }
  };

  return {
    displaySuccessToast,
    getConfirmationMessage,
    performPageAction,
    isActionProcessing,
  };
};

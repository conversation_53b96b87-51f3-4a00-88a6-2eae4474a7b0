import { serverApi } from '@/http/fetcher-api.server';
import { components } from '@/iprox-open.interface';
import { SearchParams } from '@/models/search.model';

interface SearchServiceParams extends SearchParams {
  pageSlug: string;
}

export async function search(params: SearchServiceParams): Promise<components['schemas']['CognitiveSearchResponse']> {
  try {
    const searchParams = new URLSearchParams(
      Object.entries(params).reduce<Record<string, string>>((acc, [key, value]: [string, string | undefined]) => {
        if (value && !Array.isArray(value)) {
          acc[key] = value;
        }
        return acc;
      }, {})
    );

    Object.entries(params).forEach(([key, value]: [string, string | undefined]) => {
      if (Array.isArray(value)) {
        value.forEach((v) => searchParams.append(key, v));
      }
    });

    const paramString = searchParams.toString();

    return await serverApi.get(`search?${paramString}`).json<components['schemas']['CognitiveSearchResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

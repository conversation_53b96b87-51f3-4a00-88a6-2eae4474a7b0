import { Meta, StoryObj } from '@storybook/react';

import { FileTypeLabel } from './file-type-label';

const meta: Meta<typeof FileTypeLabel> = {
  title: 'components/file-type-label',
  component: FileTypeLabel,
};

export default meta;

type Story = StoryObj<typeof FileTypeLabel>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'DOSSIER',
    className: 'bg-primary-content',
  },
  decorators: [
    (Story) => (
      <div className="w-100 flex h-screen flex-row items-center justify-center">
        <Story />
      </div>
    ),
  ],
};

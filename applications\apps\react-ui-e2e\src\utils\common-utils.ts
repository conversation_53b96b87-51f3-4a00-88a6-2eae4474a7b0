import * as Playwright from '@playwright/test';

export async function loadStory<T>(page: Playwright.Page, storyId: string, args?: T) {
  const urlArgs = (args: T): string => {
    return `&args=${Object.entries(args)
      .map((arg) => arg.join(':'))
      .join(';')}`;
  };

  const baseUrl = process.env.CI ? 'http://localhost:8080' : 'http://localhost:4400';

  await page.goto(`${baseUrl}/iframe.html?viewMode=story&id=${storyId}${args ? urlArgs(args) : ''}`);

  // wait for page to finish rendering before starting test
  await page.waitForSelector('#storybook-root');
}

import cx from 'classnames';
import { useTranslations } from 'next-intl';
import React, { useRef, useState } from 'react';

import { Text } from '../text/text';
import CloudUploadIcon from './cloud-upload.icon';

interface FileUploadProps {
  allowedFileTypes: string[];
  onFileSelect: (file: File) => void;
  disableMultipleFiles?: boolean;
}

export function FileUpload({ allowedFileTypes, onFileSelect, disableMultipleFiles = true }: FileUploadProps) {
  const t = useTranslations('components.fileUpload');
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);

    const files = event.dataTransfer.files;
    if (files.length > 1 && disableMultipleFiles) {
      alert(t('multipleFileWarning'));
      return;
    }
    if (files?.[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleOnChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files ? event.target.files[0] : null;
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleFileSelect = (file: File) => {
    if (!allowedFileTypes.includes(file.type)) {
      alert(t('fileTypeWarning'));
      return;
    }

    onFileSelect(file);
  };

  const handleIconClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div
      className={cx(
        'after:bg-base-100 after:z-9 rounded-input border-highlight bg-secondary-extra-light bg-base-15 group relative flex h-60 h-full w-full items-center justify-center overflow-hidden border border border-dashed border-dashed px-6 py-6 text-lg after:pointer-events-none after:absolute after:h-full after:w-full after:opacity-0 after:transition-all after:duration-300 after:ease-in-out',
        { 'after:opacity-75': isDragging }
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      aria-label="File drop area"
      role="group"
    >
      <div>
        <div className="px-16 py-7">
          <input type="file" ref={fileInputRef} className="hidden" onChange={handleOnChange} aria-label="File input" />
          <div className="flex justify-center">
            <CloudUploadIcon className="text-content-lite h-14 w-14 text-center" />
          </div>
          <Text className="font-heading text-heading text-center text-base font-bold">
            {t('dropFileHere')}
            <br />
            <span
              className="cursor-pointer underline"
              onClick={handleIconClick}
              role="button"
              tabIndex={0}
              onKeyDown={(event) => {
                if (event.key === 'Enter') {
                  handleIconClick();
                }
              }}
            >
              {t('clickHereToBrowse')}
            </span>
          </Text>
        </div>
      </div>
    </div>
  );
}

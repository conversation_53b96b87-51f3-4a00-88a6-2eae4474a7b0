import { getWebStatsSettings } from '@/services/settings-service';
import Script from 'next/script';

let webStatsSettings: { type?: string; key?: string; url?: string } | undefined = undefined;
let webStatsSettingsPromise: Promise<{ type?: string; key?: string; url?: string } | undefined> | undefined = undefined;

async function GetSettings(): Promise<{ type?: string; key?: string; url?: string } | undefined> {
  if (webStatsSettings) {
    return webStatsSettings;
  }

  if (!webStatsSettingsPromise) {
    webStatsSettingsPromise = getWebStatsSettings().then((result) => {
      webStatsSettings = {
        type: 'Siteimprove',
        key: '6123021',
      };

      //webStatsSettings = result?.webStatsSettings;
      return webStatsSettings;
    });
  }

  return webStatsSettingsPromise;
}

export async function WebStatsDomain() {
  const settings = await GetSettings();
  if (!settings) {
    return;
  }

  const { type, key, url } = settings;

  if (!key || !type) return null;

  switch (type) {
    case 'Google':
      return {
        analyticsImgSrc: 'https://www.google-analytics.com',
        analyticsScriptSrc: 'https://www.google-analytics.com',
      };
    case 'Piwik':
      return url;
    case 'Siteimprove':
      return {
        analyticsImgSrc: `https://${key}.global.siteimproveanalytics.io`,
        analyticsScriptSrc: 'https://siteimproveanalytics.com',
      };
  }
}

export async function WebStats() {
  const settings = await GetSettings();
  if (!settings) {
    return;
  }

  const { type, key } = settings;
  if (!key || !type) return null;

  const { analyticsScriptSrc } = await WebStatsDomain();

  switch (type) {
    case 'Google':
      return (
        <>
          <Script src={`${analyticsScriptSrc}/gtag/js?id=${key}`} strategy="afterInteractive" />
          <Script id="gtag-init" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${key}');
            `}
          </Script>
        </>
      );

    case 'Piwik':
      return (
        <Script id="piwik-init" strategy="afterInteractive">
          {`
            var _paq = window._paq = window._paq || [];
            _paq.push(['trackPageView']);
            _paq.push(['enableLinkTracking']);
            (function() {
              var u="//${analyticsScriptSrc}/";
              _paq.push(['setTrackerUrl', u+'piwik.php']);
              _paq.push(['setSiteId', '${key}']);
              var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
              g.async=true; g.src=u+'piwik.js'; s.parentNode.insertBefore(g,s);
            })();
          `}
        </Script>
      );

    case 'Siteimprove':
      return (
        <Script id="siteimprove-init" strategy="afterInteractive">
          {`
            (function() {
              var sz = document.createElement('script'); sz.type = 'text/javascript'; sz.async = true;
              sz.src = '${analyticsScriptSrc}/js/siteanalyze_${key}.js';
              var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(sz, s);
            })();
          `}
        </Script>
      );

    default:
      return null;
  }
}

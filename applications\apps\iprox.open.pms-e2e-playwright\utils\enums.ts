export enum DossierCatergory {
  WooVerzoeken = 'Woo-verzoeken',
  Beschikkingen = 'Beschikkingen',
  Onderzoeksrapporten = 'Onderzoeksrapporten',
  Klachtoordelen = 'Klachtoordelen',
  OverigeBesluitenVanAlgemeneStrekking = 'Overige besluiten van algemene strekking',
  OverigeTerinzagelegging = 'Overige terinzagelegging',
  Onteigeningen = 'Onteigeningen',
  Vergunningen = 'Vergunningen',
  Adviezen = 'Adviezen',
  Onttrekkingen = 'Onttrekkingen',
  Subsidieverplichtingen = 'Subsidieverplichtingen',
  JaarplannenEnJaarverslagen = 'Jaarplannen en jaarverslagen',
  Convenanten = 'Convenanten',
}

export enum Status {
  Published = 'Gepubliceerd',
  Unpublished = 'Ongepubliceerd',
}

export enum PageType {
  SearchPage = 'Search',
  AnnouncementsPage = 'Bekendmakingen',
  SimplePage = 'Eenvoudig',
}

export enum ModalPageTypeButton {
  SearchButton = 'Zoekpagina',
  AnnouncementsButton = 'Bekendmakingen',
  SimpleButton = 'Eenvoudige pagina',
}

export enum BekendmakingenPageFields {
  Title = 'Titel',
  Slug = 'Slug',
  OrganizationName = 'Organisatie naam',
  OrganizationType = 'Organisatie type',
  PublicationType = 'Publicatie type',
}

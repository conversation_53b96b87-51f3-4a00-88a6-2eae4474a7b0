import { PencilSquareIcon } from '@heroicons/react/24/outline';
import cx from 'classnames';
import { filesize } from 'filesize';
import { useTranslations } from 'next-intl';
import { memo, useMemo, useRef, useState } from 'react';

import { getFileExtension } from '../../../utils/file-extention-util';
import { FileTypeLabel } from '../../file-type-label/file-type-label';
import { Text } from '../../text/text';
import { FileNode, Node } from '../models/file-structure';
import { filenameExtensionFilter } from '../utils/filename-extension-filter';
import { Checkbox } from './checkbox';
import { RenamingForm } from './renaming-form';

interface DisplayFileNodeProps {
  item: FileNode;
  level: number;
  editableNode?: boolean;
  /** Emits when a filenode is clicked. */
  onFileClick: (item: FileNode) => void;
  onSelectItem: (item: Node, isSelected: boolean) => void;
  onRenaming?: (item: Node, newName: string) => Promise<boolean>;
}

function DisplayFileNodeComponent({
  item,
  level,
  editableNode = false,
  onFileClick,
  onSelectItem,
  onRenaming,
}: DisplayFileNodeProps) {
  const selectInputRef = useRef<HTMLInputElement>(null);
  const t = useTranslations('components.fileStructure');

  const [isUpdatingName, setIsUpdatingName] = useState(false);
  const [isRenaming, setIsRenaming] = useState(false);

  const handleSelectItem = (item: Node, isSelected: boolean) => {
    onSelectItem?.(item, isSelected);
  };

  const handleItemClick = async (clickedItem: FileNode) => {
    onFileClick(clickedItem);
  };

  const handleItemKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      event.nativeEvent.stopImmediatePropagation();
      handleItemClick(item);
    }
  };

  const fileSize = useMemo(
    () =>
      filesize(item.size, {
        base: 2,
        standard: 'jedec',
      }).toString(),
    [item.size]
  );

  const itemStyle = {
    marginLeft: `${level * 24}px`,
  };

  return (
    <div className="flex grid-cols-[max-content_3fr_minmax(0,_20%)] p-3 md:grid md:gap-4">
      <div
        style={itemStyle}
        className={cx('font-text text-body w-100 flex grid cursor-pointer text-sm', { 'items-center': isUpdatingName })}
      >
        <Checkbox
          inputRef={selectInputRef}
          ariaLabel={
            item.isSelected
              ? t('deselectNode', { nodeName: item.nodeName })
              : t('selectNode', { nodeName: item.nodeName })
          }
          value={item.nodeId}
          isSelected={item.isSelected}
          isDisabled={isUpdatingName}
          handleSelect={(e) => handleSelectItem(item, e.target.checked)}
        />
      </div>
      <div>
        {isUpdatingName ? (
          <RenamingForm
            label={filenameExtensionFilter(item.nodeName)}
            onLabelUpdate={async (values) => {
              const newName =
                typeof values.name === 'string' ? `${values.name}.${getFileExtension(item.nodeName)}` : '';

              let updated: boolean | undefined;

              try {
                setIsRenaming(true);
                updated = await onRenaming?.(item, newName);
              } finally {
                setTimeout(() => setIsRenaming(false), 1000);
              }

              if (updated) {
                item.nodeName = newName;
                setIsUpdatingName(false);
              }
            }}
            onCancel={() => setIsUpdatingName(false)}
            isSubmitting={isRenaming}
          />
        ) : (
          <div className="flex gap-2 align-top">
            <button
              className="items-top group flex w-full flex-col md:flex-row"
              onClickCapture={(event) => {
                event.preventDefault();
                event.stopPropagation();
                event.nativeEvent.stopImmediatePropagation();
                handleItemClick(item);
              }}
              onKeyDown={(e) => handleItemKeyDown(e)}
              aria-label={t('file', { fileName: item.nodeName ?? '' })}
            >
              <span className="order-2 flex md:order-1">
                <FileTypeLabel label={getFileExtension(item.nodeName).toUpperCase()} className="mr-3" />
                <Text className="font-heading text-body inline-block text-left text-base md:hidden">{fileSize}</Text>
              </span>
              <Text className="font-heading text-body order-1 mb-4 text-left text-base group-hover:underline md:order-2 md:mb-0">
                {item.nodeName}
              </Text>
            </button>
            {editableNode && (
              <button
                className="hover:bg-secondary flex h-min w-min"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  e.nativeEvent.stopImmediatePropagation();
                  setIsUpdatingName(!isUpdatingName);
                }}
                aria-label={t('rename', { nodeName: item.nodeName })}
              >
                <PencilSquareIcon className="text-primary h-5 w-5" />
              </button>
            )}
          </div>
        )}
      </div>
      <Text className="font-heading text-body hidden items-center text-left text-base md:flex">{fileSize}</Text>
    </div>
  );
}

export const DisplayFileNode = memo(DisplayFileNodeComponent);

import { expect, test } from '@playwright/test';

import { baseUrl, root_with_sub_dirs_FileCount } from '../config';
import DashboardPage from '../pages/dashboard_page';
import EditDossierPage from '../pages/edit_dossier_page';
import CreateNewDossierPage from '../pages/new_dossier_page';
import { DateTimeFormatter } from '../utils/date_time_formatter';

let dashboardPage: DashboardPage;
let newDossierPage: CreateNewDossierPage;
let editDossierPage: EditDossierPage;

test.beforeEach(async ({ page }) => {
  await page.goto(baseUrl);
  dashboardPage = new DashboardPage(page);
  newDossierPage = new CreateNewDossierPage(page);
  editDossierPage = new EditDossierPage(page);
});

test.describe.skip('Verify the dossier structure file count', () => {
  test('Verify the fresh upload file structure count', async ({ page }) => {
    test.slow();
    const title = `Playwright dossier with publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;

    await dashboardPage.clickNewDossierButton();
    await newDossierPage.createNewDossier(title, true);

    // uploadImageFile
    // await editDossierPage.uploadImage('iprox_1.png');

    // Input dynamic fields
    await editDossierPage.inputDynamicFields();
    console.log('Inputting dynamic fields completed');

    // Dossier upload
    console.log('uploading zip file');
    await editDossierPage.uploadZipFile('root_with_sub_dirs_new');

    // save dossier
    await editDossierPage.clickActionButton('Opslaan');

    // Check file structure and file count
    await editDossierPage.checkFileStructure('root_with_sub_dirs_new');
    let fileCount: number | null = await editDossierPage.getZipFileCount();
    await expect(fileCount).toEqual(root_with_sub_dirs_FileCount);
  });

  test('Verify the file count after file deletion', async ({ page }) => {
    test.slow();
    const title = `PW file count update - publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;

    await dashboardPage.clickNewDossierButton();
    await newDossierPage.createNewDossier(title, true);

    // uploadImageFile
    // await editDossierPage.uploadImage('iprox_1.png');

    // Input dynamic fields
    await editDossierPage.inputDynamicFields();

    // Dossier upload
    await editDossierPage.uploadZipFile('root_with_sub_dirs_new');

    // save dossier
    await editDossierPage.clickActionButton('Opslaan');

    // Check file structure,expand the structure
    await editDossierPage.checkFileStructure('root_with_sub_dirs_new');

    //  verify file count
    let fileCount: number | null = await editDossierPage.getZipFileCount();
    expect(fileCount).toEqual(root_with_sub_dirs_FileCount);

    //UI is slow render the elements
    await page.waitForLoadState('domcontentloaded');
    await expect(page.getByLabel(`Map root_with_sub_dirs_new`)).toBeVisible();

    await page.getByLabel(`Map doc`, { exact: true }).first().scrollIntoViewIfNeeded();
    await expect(page.getByLabel(`Map doc`, { exact: true }).first()).toBeVisible();
    await page.getByLabel(`Map doc`, { exact: true }).first().click();

    await expect(page.getByLabel(`Selecteer tzahvytclu.doc`)).toBeVisible();
    await page.getByLabel('Selecteer tzahvytclu.doc').check();

    await page.getByRole('button', { name: 'Verwijderen' }).click();
    await page.getByRole('button', { name: 'Bevestigen' }).click();

    await expect(page.getByRole('button', { name: 'Verwijderen' })).toBeDisabled();
    let newfileCount: number | null = await editDossierPage.getZipFileCount();
    expect(newfileCount).toBe(fileCount - 1);
  });
});

import ky, { NormalizedOptions } from 'ky';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';

import { authOptions } from '../_auth/auth';

/** Use this to call the iprox.open API in **server** components */
export const serverApi = ky.extend({
  prefixUrl: process.env.IPROX_OPEN_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  hooks: {
    beforeRequest: [
      async (request) => {
        if (process.env.IPROX_DEBUG) {
          console.info('fetching', request.url);
        }

        const session = await getServerSession(authOptions);
        const token = session?.user?.access_token;

        if (token) {
          request.headers.set('Authorization', `Bearer ${token}`);
        } else {
          redirect('/login');
        }
      },
    ],
    afterResponse: [
      async (_request: Request, _options: NormalizedOptions, response: Response) => {
        if (response.status === 401) {
          // TODO: Add returnUrl.
          redirect('/login');
        }
      },
    ],
  },
  timeout: 20000,
});

{"migrations": [{"cli": "nx", "version": "19.2.0-beta.2", "description": "Updates the default workspace data directory to .nx/workspace-data", "implementation": "./src/migrations/update-19-2-0/move-workspace-data-directory", "package": "nx", "name": "19-2-0-move-graph-cache-directory"}, {"cli": "nx", "version": "19.2.2-beta.0", "description": "Updates the nx wrapper.", "implementation": "./src/migrations/update-17-3-0/update-nxw", "package": "nx", "name": "19-2-2-update-nx-wrapper"}, {"version": "19.2.4-beta.0", "description": "Set project name in nx.json explicitly", "implementation": "./src/migrations/update-19-2-4/set-project-name", "x-repair-skip": true, "package": "nx", "name": "19-2-4-set-project-name"}, {"version": "20.0.0-beta.7", "description": "Migration for v20.0.0-beta.7", "implementation": "./src/migrations/update-20-0-0/move-use-daemon-process", "package": "nx", "name": "move-use-daemon-process"}, {"version": "20.0.1", "description": "Set `useLegacyCache` to true for migrating workspaces", "implementation": "./src/migrations/update-20-0-1/use-legacy-cache", "x-repair-skip": true, "package": "nx", "name": "use-legacy-cache"}, {"cli": "nx", "version": "19.6.0-beta.4", "description": "Update ciWebServerCommand to use static serve for the application.", "implementation": "./src/migrations/update-19-6-0/update-ci-webserver-for-static-serve", "package": "@nx/cypress", "name": "update-19-6-0-update-ci-webserver-for-vite"}, {"cli": "nx", "version": "19.6.0-beta.0", "description": "Update workspace to use Storybook v8", "implementation": "./src/migrations/update-19-6-0/update-sb-8", "package": "@nx/storybook", "name": "update-19-6-0-add-nx-packages"}, {"version": "20.2.0-beta.5", "description": "Update TypeScript ESLint packages to v8.13.0 if they are already on v8", "implementation": "./src/migrations/update-20-2-0/update-typescript-eslint-v8-13-0", "package": "@nx/eslint", "name": "update-typescript-eslint-v8.13.0"}, {"cli": "nx", "version": "20.0.0-beta.5", "description": "replace getJestProjects with getJestProjectsAsync", "implementation": "./src/migrations/update-20-0-0/replace-getJestProjects-with-getJestProjectsAsync", "package": "@nx/jest", "name": "replace-getJestProjects-with-getJestProjectsAsync"}, {"cli": "nx", "version": "19.6.0-beta.4", "description": "Ensure Module Federation DTS is turned off by default.", "factory": "./src/migrations/update-19-6-0/turn-off-dts-by-default", "package": "@nx/react", "name": "update-19-6-0-turn-module-federation-dts-off"}, {"cli": "nx", "version": "19.6.0-beta.4", "description": "Update the server file for Module Federation SSR port value to be the same as the 'serve' target port value.", "factory": "./src/migrations/update-19-6-0/update-ssr-server-port", "package": "@nx/react", "name": "update-module-federation-ssr-server-file"}, {"cli": "nx", "version": "19.6.1-beta.0", "description": "Ensure Target Defaults are set correctly for Module Federation.", "factory": "./src/migrations/update-19-6-1/ensure-depends-on-for-mf", "package": "@nx/react", "name": "update-19-6-1-ensure-module-federation-target-defaults"}, {"cli": "nx", "version": "20.2.0-beta.2", "description": "Update the ModuleFederationConfig import use @nx/module-federation.", "factory": "./src/migrations/update-20-2-0/migrate-mf-imports-to-new-package", "package": "@nx/react", "name": "update-20-2-0-update-module-federation-config-import"}, {"cli": "nx", "version": "20.2.0-beta.2", "description": "Update the withModuleFederation import use @nx/module-federation/webpack.", "factory": "./src/migrations/update-20-2-0/migrate-with-mf-import-to-new-package", "package": "@nx/react", "name": "update-20-2-0-update-with-module-federation-import"}]}
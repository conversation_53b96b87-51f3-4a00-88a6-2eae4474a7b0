import type { <PERSON>a, StoryObj } from '@storybook/react';

import { TruncatedText } from './truncated-text';

const meta: Meta<typeof TruncatedText> = {
  title: 'components/truncated-text',
  component: TruncatedText,
};

export default meta;
type Story = StoryObj<typeof TruncatedText>;

export const Default: Story = {
  name: 'default',
  render: (args) => {
    return (
      <div className="w-64">
        <TruncatedText {...args} />
      </div>
    );
  },
  args: {
    children: 'Description_of_Professionality_16-08-2023.pdf',
  },
};

export const WithoutTooltip: Story = {
  name: 'without tooltip',
  render: (args) => {
    return (
      <div className="w-64">
        <TruncatedText {...args} />
      </div>
    );
  },
  args: {
    children: 'Description_of_Professionality_16-08-2023.pdf',
    showTooltip: false,
  },
};

import { useAppSettings } from '@iprox/shared-context';
import { ListItemNode, ListNode } from '@lexical/list';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import LexicalErrorBoundary from '@lexical/react/LexicalErrorBoundary';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { TablePlugin } from '@lexical/react/LexicalTablePlugin';
import { HeadingNode } from '@lexical/rich-text';
import { TableCellNode, TableNode, TableRowNode } from '@lexical/table';
import cx from 'classnames';
import { useField } from 'formik';
import { useTranslations } from 'next-intl';
import { FC, useMemo, useState } from 'react';

import { showToast } from '../../../toast/toast';
import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { RichTextFieldDefinition } from '../../models/form.models';
import { borderClassname } from '../../utils/border-classname';
import './index.scss';
import { LinkNode } from './nodes/link';
import { SuperLinkNode } from './nodes/super-link';
import DeserializationPlugin, { DeserializationError } from './plugins/deserialization-plugin';
import LinkPlugin from './plugins/link-plugin';
import PastePlugin, { InvalidClipboardError } from './plugins/paste-plugin';
import SerializationPlugin, { SerializationError } from './plugins/serialization-plugin';
import { TableActionMenuPlugin } from './plugins/tabel-action-menu-plugin';
import ToolbarPlugin from './plugins/toolbar-plugin';
import ExampleTheme from './themes/theme';

const editorConfig = (portalUrl: string) => {
  LinkNode.setConfig({ portalUrl });
  SuperLinkNode.setConfig({ portalUrl });

  return {
    namespace: 'MyEditor',
    // The editor theme
    theme: ExampleTheme,
    // Any custom nodes go here
    nodes: [HeadingNode, ListNode, ListItemNode, LinkNode, SuperLinkNode, TableCellNode, TableNode, TableRowNode],
  };
};

export const RichTextField: FC<RichTextFieldDefinition> = (props) => {
  const t = useTranslations('components.richtexteditor');

  const updatedProps = {
    ...props,
    defaultValue: props.value,
  };

  const settings = useAppSettings();

  const [field, meta, helpers] = useField(updatedProps);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'text');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  const defaultValue = useMemo(() => {
    return (props.value as string) || props.defaultValue || props.initialValue || '';
  }, [props.value, props.defaultValue, props.initialValue]);

  const [floatingAnchorElem, setFloatingAnchorElem] = useState<HTMLDivElement | null>(null);

  const onRef = (_floatingAnchorElem: HTMLDivElement) => {
    if (_floatingAnchorElem !== null) {
      setFloatingAnchorElem(_floatingAnchorElem);
    }
  };

  return (
    <div onBlur={() => helpers.setTouched(true, true)}>
      <FormField definition={props} errorMessage={meta.touched ? meta.error : undefined} {...formControlProps}>
        <LexicalComposer
          initialConfig={{
            ...editorConfig(settings.portalUrl),
            onError(error: Error) {
              if (error instanceof DeserializationError) {
                showToast(`${t('err.deserializationError')} (${props.label})`);
              } else if (error instanceof SerializationError) {
                showToast(`${t('err.serializationError')} (${props.label})`);
              } else if (error instanceof InvalidClipboardError) {
                showToast(`${t('err.invalidClipboard')} (${props.label})`);
              } else {
                throw error;
              }
            },
          }}
        >
          <div
            className={cx(
              'rounded-input relative w-full',
              borderClassname(meta.touched ? meta.error : undefined, field.value)
            )}
          >
            <ToolbarPlugin enableSuperlink={props.enableSuperlink} />
            <div className="rounded-input relative w-full">
              <RichTextPlugin
                contentEditable={
                  <div ref={onRef} className="editor-input relative w-full overflow-auto">
                    <ContentEditable
                      id={inputProps.id}
                      ariaLabel={inputProps['aria-describedby']}
                      className={cx('editor-input w-full', props.contentEditorClass)}
                      name={field.name}
                      ariaLabelledBy={labelProps.id}
                    />
                  </div>
                }
                // Placeholder is required by the plugin
                placeholder={<div className="hidden" />}
                ErrorBoundary={LexicalErrorBoundary}
              />
              <HistoryPlugin />
              <ListPlugin />
              <SerializationPlugin
                onChange={(htmlString) => {
                  helpers.setValue(htmlString);
                }}
              />
              <DeserializationPlugin value={defaultValue} />
              <LinkPlugin />
              <PastePlugin />
              <TablePlugin />
            </div>
            {floatingAnchorElem && <TableActionMenuPlugin anchorElem={floatingAnchorElem} />}
          </div>
        </LexicalComposer>
      </FormField>
    </div>
  );
};

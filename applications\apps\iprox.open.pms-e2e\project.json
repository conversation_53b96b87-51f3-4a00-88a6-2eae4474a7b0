{"name": "iprox.open.pms-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/iprox.open.pms-e2e/src", "projectType": "application", "targets": {"e2e": {"executor": "@nx/cypress:cypress", "options": {"cypressConfig": "apps/iprox.open.pms-e2e/cypress.config.ts", "devServerTarget": "iprox.open.pms:serve:development", "testingType": "e2e"}, "configurations": {"production": {"devServerTarget": "iprox.open.pms:serve:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": [], "implicitDependencies": ["iprox.open.pms"]}
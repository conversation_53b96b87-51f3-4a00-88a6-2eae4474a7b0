import { put } from '@/http/fetcher-axios';
import { components } from '@/iprox-open.interface';
import { AxiosProgressEvent } from 'axios';
import { KyInstance } from 'ky/distribution/types/ky';

export async function createDossier(
  clientApi: KyInstance,
  dossier: components['schemas']['CreateDossierCommand']
): Promise<components['schemas']['CreateDossierResponse']> {
  try {
    return await clientApi.post('dossier', { json: dossier }).json<components['schemas']['CreateDossierResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function updateDossier(
  clientApi: KyInstance,
  updatedDossier: components['schemas']['UpdateDossierCommand']
): Promise<components['schemas']['UpdateDossierResponse']> {
  try {
    return await clientApi
      .put('dossier', { json: updatedDossier })
      .json<components['schemas']['UpdateDossierResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function publishDossier(
  clientApi: KyInstance,
  body: components['schemas']['PublishDossierCommand']
): Promise<components['schemas']['PublishDossierResponse']> {
  try {
    return await clientApi
      .post(`dossier/publish-dossier`, { json: body })
      .json<components['schemas']['PublishDossierResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function deleteDossier(
  clientApi: KyInstance,
  body: components['schemas']['DeleteDossierCommand']
): Promise<components['schemas']['DeleteDossierResponse']> {
  try {
    return await clientApi.delete(`dossier`, { json: body }).json<components['schemas']['DeleteDossierResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function unpublishDossier(
  clientApi: KyInstance,
  body: components['schemas']['UnpublishDossierCommand']
): Promise<components['schemas']['UnpublishDossierResponse']> {
  try {
    return await clientApi
      .post(`dossier/unpublish-dossier`, { json: body })
      .json<components['schemas']['UnpublishDossierResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function updateDossierTitle(
  clientApi: KyInstance,
  body: components['schemas']['UpdateDossierTitleCommand']
): Promise<components['schemas']['UpdateDossierTitleResponse']> {
  try {
    return await clientApi
      .put(`dossier/dossier-title`, { json: body })
      .json<components['schemas']['UpdateDossierTitleResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function updateDossierImage(
  apiUrl: string,
  id: string,
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<void> {
  const formData = new FormData();
  formData.append('file', file);

  return await put<void, FormData>(`${apiUrl}/dossier/${id}/image`, formData, {
    onUploadProgress,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function getDossierLatestVersion(
  clientApi: KyInstance,
  dossierId: string
): Promise<components['schemas']['GetLatestDossierVersionResponse']> {
  return await clientApi
    .get(`dossier/${dossierId}/latest-version`)
    .json<components['schemas']['GetLatestDossierVersionResponse']>();
}

export async function getDossierVersions(
  clientApi: KyInstance,
  dossierId: string
): Promise<components['schemas']['GetDossierAllVersionsResponse']> {
  return await clientApi
    .get(`dossier/${dossierId}/versions`)
    .json<components['schemas']['GetDossierAllVersionsResponse']>();
}

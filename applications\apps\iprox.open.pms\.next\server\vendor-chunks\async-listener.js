/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/async-listener";
exports.ids = ["vendor-chunks/async-listener"];
exports.modules = {

/***/ "(instrument)/../../node_modules/async-listener/es6-wrapped-promise.js":
/*!****************************************************************!*\
  !*** ../../node_modules/async-listener/es6-wrapped-promise.js ***!
  \****************************************************************/
/***/ ((module) => {

"use strict";
eval("\n\nmodule.exports = (Promise, ensureAslWrapper) => {\n  // Updates to this class should also be applied to the the ES3 version\n  // in index.js.\n  return class WrappedPromise extends Promise {\n    constructor(executor) {\n      var context, args;\n      super(wrappedExecutor);\n      var promise = this;\n\n      try {\n        executor.apply(context, args);\n      } catch (err) {\n        args[1](err);\n      }\n\n      return promise;\n      function wrappedExecutor(resolve, reject) {\n        context = this;\n        args = [wrappedResolve, wrappedReject];\n\n        // These wrappers create a function that can be passed a function and an argument to\n        // call as a continuation from the resolve or reject.\n        function wrappedResolve(val) {\n          ensureAslWrapper(promise, false);\n          return resolve(val);\n        }\n\n        function wrappedReject(val) {\n          ensureAslWrapper(promise, false);\n          return reject(val);\n        }\n      }\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9hc3luYy1saXN0ZW5lci9lczYtd3JhcHBlZC1wcm9taXNlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9hc3luYy1saXN0ZW5lci9lczYtd3JhcHBlZC1wcm9taXNlLmpzP2JkZGQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IChQcm9taXNlLCBlbnN1cmVBc2xXcmFwcGVyKSA9PiB7XG4gIC8vIFVwZGF0ZXMgdG8gdGhpcyBjbGFzcyBzaG91bGQgYWxzbyBiZSBhcHBsaWVkIHRvIHRoZSB0aGUgRVMzIHZlcnNpb25cbiAgLy8gaW4gaW5kZXguanMuXG4gIHJldHVybiBjbGFzcyBXcmFwcGVkUHJvbWlzZSBleHRlbmRzIFByb21pc2Uge1xuICAgIGNvbnN0cnVjdG9yKGV4ZWN1dG9yKSB7XG4gICAgICB2YXIgY29udGV4dCwgYXJncztcbiAgICAgIHN1cGVyKHdyYXBwZWRFeGVjdXRvcik7XG4gICAgICB2YXIgcHJvbWlzZSA9IHRoaXM7XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGV4ZWN1dG9yLmFwcGx5KGNvbnRleHQsIGFyZ3MpO1xuICAgICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIGFyZ3NbMV0oZXJyKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHByb21pc2U7XG4gICAgICBmdW5jdGlvbiB3cmFwcGVkRXhlY3V0b3IocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgICAgIGNvbnRleHQgPSB0aGlzO1xuICAgICAgICBhcmdzID0gW3dyYXBwZWRSZXNvbHZlLCB3cmFwcGVkUmVqZWN0XTtcblxuICAgICAgICAvLyBUaGVzZSB3cmFwcGVycyBjcmVhdGUgYSBmdW5jdGlvbiB0aGF0IGNhbiBiZSBwYXNzZWQgYSBmdW5jdGlvbiBhbmQgYW4gYXJndW1lbnQgdG9cbiAgICAgICAgLy8gY2FsbCBhcyBhIGNvbnRpbnVhdGlvbiBmcm9tIHRoZSByZXNvbHZlIG9yIHJlamVjdC5cbiAgICAgICAgZnVuY3Rpb24gd3JhcHBlZFJlc29sdmUodmFsKSB7XG4gICAgICAgICAgZW5zdXJlQXNsV3JhcHBlcihwcm9taXNlLCBmYWxzZSk7XG4gICAgICAgICAgcmV0dXJuIHJlc29sdmUodmFsKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGZ1bmN0aW9uIHdyYXBwZWRSZWplY3QodmFsKSB7XG4gICAgICAgICAgZW5zdXJlQXNsV3JhcHBlcihwcm9taXNlLCBmYWxzZSk7XG4gICAgICAgICAgcmV0dXJuIHJlamVjdCh2YWwpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/async-listener/es6-wrapped-promise.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/async-listener/glue.js":
/*!*************************************************!*\
  !*** ../../node_modules/async-listener/glue.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var wrap = (__webpack_require__(/*! shimmer */ \"(instrument)/../../node_modules/shimmer/index.js\").wrap);\n\n/*\n *\n * CONSTANTS\n *\n */\nvar HAS_CREATE_AL = 1 << 0;\nvar HAS_BEFORE_AL = 1 << 1;\nvar HAS_AFTER_AL = 1 << 2;\nvar HAS_ERROR_AL = 1 << 3;\n\n/**\n * There is one list of currently active listeners that is mutated in place by\n * addAsyncListener and removeAsyncListener. This complicates error-handling,\n * for reasons that are discussed below.\n */\nvar listeners = [];\n\n/**\n * There can be multiple listeners with the same properties, so disambiguate\n * them by assigning them an ID at creation time.\n */\nvar uid = 0;\n\n/**\n * Ensure that errors coming from within listeners are handed off to domains,\n * process._fatalException, or uncaughtException without being treated like\n * user errors.\n */\nvar inAsyncTick = false;\n\n/**\n * Because asynchronous contexts can be nested, and errors can come from anywhere\n * in the stack, a little extra work is required to keep track of where in the\n * nesting we are. Because JS arrays are frequently mutated in place\n */\nvar listenerStack = [];\n\n/**\n * The error handler on a listener can capture errors thrown during synchronous\n * execution immediately after the listener is added. To capture both\n * synchronous and asynchronous errors, the error handler just uses the\n * \"global\" list of active listeners, and the rest of the code ensures that the\n * listener list is correct by using a stack of listener lists during\n * asynchronous execution.\n */\nvar asyncCatcher;\n\n/**\n * The guts of the system -- called each time an asynchronous event happens\n * while one or more listeners are active.\n */\nvar asyncWrap;\n\n/**\n * Simple helper function that's probably faster than using Array\n * filter methods and can be inlined.\n */\nfunction union(dest, added) {\n  var destLength = dest.length;\n  var addedLength = added.length;\n  var returned = [];\n\n  if (destLength === 0 && addedLength === 0) return returned;\n\n  for (var j  = 0; j < destLength; j++) returned[j] = dest[j];\n\n  if (addedLength === 0) return returned;\n\n  for (var i = 0; i < addedLength; i++) {\n    var missing = true;\n    for (j = 0; j < destLength; j++) {\n      if (dest[j].uid === added[i].uid) {\n        missing = false;\n        break;\n      }\n    }\n    if (missing) returned.push(added[i]);\n  }\n\n  return returned;\n}\n\n/*\n * For performance, split error-handlers and asyncCatcher up into two separate\n * code paths.\n */\n\n// 0.9+\nif (process._fatalException) {\n  /**\n   * Error handlers on listeners can throw, the catcher needs to be able to\n   * discriminate between exceptions thrown by user code, and exceptions coming\n   * from within the catcher itself. Use a global to keep track of which state\n   * the catcher is currently in.\n   */\n  var inErrorTick = false;\n\n  /**\n   * Throwing always happens synchronously. If the current array of values for\n   * the current list of asyncListeners is put in a module-scoped variable right\n   * before a call that can throw, it will always be correct when the error\n   * handlers are run.\n   */\n  var errorValues;\n\n  asyncCatcher = function asyncCatcher(er) {\n    var length = listeners.length;\n    if (inErrorTick || length === 0) return false;\n\n    var handled = false;\n\n    /*\n     * error handlers\n     */\n    inErrorTick = true;\n    for (var i = 0; i < length; ++i) {\n      var listener = listeners[i];\n      if ((listener.flags & HAS_ERROR_AL) === 0) continue;\n\n      var value = errorValues && errorValues[listener.uid];\n      handled = listener.error(value, er) || handled;\n    }\n    inErrorTick = false;\n\n    /* Test whether there are any listener arrays on the stack. In the case of\n     * synchronous throws when the listener is active, there may have been\n     * none pushed yet.\n     */\n    if (listenerStack.length > 0) listeners = listenerStack.pop();\n    errorValues = undefined;\n\n    return handled && !inAsyncTick;\n  };\n\n  asyncWrap = function asyncWrap(original, list, length) {\n    var values = [];\n\n    /*\n     * listeners\n     */\n    inAsyncTick = true;\n    for (var i = 0; i < length; ++i) {\n      var listener = list[i];\n      values[listener.uid] = listener.data;\n\n      if ((listener.flags & HAS_CREATE_AL) === 0) continue;\n\n      var value = listener.create(listener.data);\n      if (value !== undefined) values[listener.uid] = value;\n    }\n    inAsyncTick = false;\n\n    /* One of the main differences between this polyfill and the core\n     * asyncListener support is that core avoids creating closures by putting a\n     * lot of the state managemnt on the C++ side of Node (and of course also it\n     * bakes support for async listeners into the Node C++ API through the\n     * AsyncWrap class, which means that it doesn't monkeypatch basically every\n     * async method like this does).\n     */\n    return function () {\n      // put the current values where the catcher can see them\n      errorValues = values;\n\n      /* More than one listener can end up inside these closures, so save the\n       * current listeners on a stack.\n       */\n      listenerStack.push(listeners);\n\n      /* Activate both the listeners that were active when the closure was\n       * created and the listeners that were previously active.\n       */\n      listeners = union(list, listeners);\n\n      /*\n       * before handlers\n       */\n      inAsyncTick = true;\n      for (var i = 0; i < length; ++i) {\n        if ((list[i].flags & HAS_BEFORE_AL) > 0) {\n          list[i].before(this, values[list[i].uid]);\n        }\n      }\n      inAsyncTick = false;\n\n      // save the return value to pass to the after callbacks\n      var returned = original.apply(this, arguments);\n\n      /*\n       * after handlers (not run if original throws)\n       */\n      inAsyncTick = true;\n      for (i = 0; i < length; ++i) {\n        if ((list[i].flags & HAS_AFTER_AL) > 0) {\n          list[i].after(this, values[list[i].uid]);\n        }\n      }\n      inAsyncTick = false;\n\n      // back to the previous listener list on the stack\n      listeners = listenerStack.pop();\n      errorValues = undefined;\n\n      return returned;\n    };\n  };\n\n  wrap(process, '_fatalException', function (_fatalException) {\n    return function _asyncFatalException(er) {\n      return asyncCatcher(er) || _fatalException(er);\n    };\n  });\n}\n// 0.8 and below\nelse {\n  /**\n   * If an error handler in asyncWrap throws, the process must die. Under 0.8\n   * and earlier the only way to put a bullet through the head of the process\n   * is to rethrow from inside the exception handler, so rethrow and set\n   * errorThrew to tell the uncaughtHandler what to do.\n   */\n  var errorThrew = false;\n\n  /**\n   * Under Node 0.8, this handler *only* handles synchronously thrown errors.\n   * This simplifies it, which almost but not quite makes up for the hit taken\n   * by putting everything in a try-catch.\n   */\n  asyncCatcher = function uncaughtCatcher(er) {\n    // going down hard\n    if (errorThrew) throw er;\n\n    var handled = false;\n\n    /*\n     * error handlers\n     */\n    var length = listeners.length;\n    for (var i = 0; i < length; ++i) {\n      var listener = listeners[i];\n      if ((listener.flags & HAS_ERROR_AL) === 0) continue;\n      handled = listener.error(null, er) || handled;\n    }\n\n    /* Rethrow if one of the before / after handlers fire, which will bring the\n     * process down immediately.\n     */\n    if (!handled && inAsyncTick) throw er;\n  };\n\n  asyncWrap = function asyncWrap(original, list, length) {\n    var values = [];\n\n    /*\n     * listeners\n     */\n    inAsyncTick = true;\n    for (var i = 0; i < length; ++i) {\n      var listener = list[i];\n      values[listener.uid] = listener.data;\n\n      if ((listener.flags & HAS_CREATE_AL) === 0) continue;\n\n      var value = listener.create(listener.data);\n      if (value !== undefined) values[listener.uid] = value;\n    }\n    inAsyncTick = false;\n\n    /* One of the main differences between this polyfill and the core\n     * asyncListener support is that core avoids creating closures by putting a\n     * lot of the state managemnt on the C++ side of Node (and of course also it\n     * bakes support for async listeners into the Node C++ API through the\n     * AsyncWrap class, which means that it doesn't monkeypatch basically every\n     * async method like this does).\n     */\n    return function () {\n      /*jshint maxdepth:4*/\n\n      // after() handlers don't run if threw\n      var threw = false;\n\n      // ...unless the error is handled\n      var handled = false;\n\n      /* More than one listener can end up inside these closures, so save the\n       * current listeners on a stack.\n       */\n      listenerStack.push(listeners);\n\n      /* Activate both the listeners that were active when the closure was\n       * created and the listeners that were previously active.\n       */\n      listeners = union(list, listeners);\n\n      /*\n       * before handlers\n       */\n      inAsyncTick = true;\n      for (var i = 0; i < length; ++i) {\n        if ((list[i].flags & HAS_BEFORE_AL) > 0) {\n          list[i].before(this, values[list[i].uid]);\n        }\n      }\n      inAsyncTick = false;\n\n      // save the return value to pass to the after callbacks\n      var returned;\n      try {\n        returned = original.apply(this, arguments);\n      }\n      catch (er) {\n        threw = true;\n        for (var i = 0; i < length; ++i) {\n          if ((listeners[i].flags & HAS_ERROR_AL) == 0) continue;\n          try {\n            handled = listeners[i].error(values[list[i].uid], er) || handled;\n          }\n          catch (x) {\n            errorThrew = true;\n            throw x;\n          }\n        }\n\n        if (!handled) {\n          // having an uncaughtException handler here alters crash semantics\n          process.removeListener('uncaughtException', asyncCatcher);\n          process._originalNextTick(function () {\n            process.addListener('uncaughtException', asyncCatcher);\n          });\n\n          throw er;\n        }\n      }\n      finally {\n        /*\n         * after handlers (not run if original throws)\n         */\n        if (!threw || handled) {\n          inAsyncTick = true;\n          for (i = 0; i < length; ++i) {\n            if ((list[i].flags & HAS_AFTER_AL) > 0) {\n              list[i].after(this, values[list[i].uid]);\n            }\n          }\n          inAsyncTick = false;\n        }\n\n        // back to the previous listener list on the stack\n        listeners = listenerStack.pop();\n      }\n\n\n      return returned;\n    };\n  };\n\n  // will be the first to fire if async-listener is the first module loaded\n  process.addListener('uncaughtException', asyncCatcher);\n}\n\n// for performance in the case where there are no handlers, just the listener\nfunction simpleWrap(original, list, length) {\n  inAsyncTick = true;\n  for (var i = 0; i < length; ++i) {\n    var listener = list[i];\n    if (listener.create) listener.create(listener.data);\n  }\n  inAsyncTick = false;\n\n  // still need to make sure nested async calls are made in the context\n  // of the listeners active at their creation\n  return function () {\n    listenerStack.push(listeners);\n    listeners = union(list, listeners);\n\n    var returned = original.apply(this, arguments);\n\n    listeners = listenerStack.pop();\n\n    return returned;\n  };\n}\n\n/**\n * Called each time an asynchronous function that's been monkeypatched in\n * index.js is called. If there are no listeners, return the function\n * unwrapped.  If there are any asyncListeners and any of them have callbacks,\n * pass them off to asyncWrap for later use, otherwise just call the listener.\n */\nfunction wrapCallback(original) {\n  var length = listeners.length;\n\n  // no context to capture, so avoid closure creation\n  if (length === 0) return original;\n\n  // capture the active listeners as of when the wrapped function was called\n  var list = listeners.slice();\n\n  for (var i = 0; i < length; ++i) {\n    if (list[i].flags > 0) return asyncWrap(original, list, length);\n  }\n\n  return simpleWrap(original, list, length);\n}\n\nfunction AsyncListener(callbacks, data) {\n  if (typeof callbacks.create === 'function') {\n    this.create = callbacks.create;\n    this.flags |= HAS_CREATE_AL;\n  }\n\n  if (typeof callbacks.before === 'function') {\n    this.before = callbacks.before;\n    this.flags |= HAS_BEFORE_AL;\n  }\n\n  if (typeof callbacks.after === 'function') {\n    this.after = callbacks.after;\n    this.flags |= HAS_AFTER_AL;\n  }\n\n  if (typeof callbacks.error === 'function') {\n    this.error = callbacks.error;\n    this.flags |= HAS_ERROR_AL;\n  }\n\n  this.uid = ++uid;\n  this.data = data === undefined ? null : data;\n}\nAsyncListener.prototype.create = undefined;\nAsyncListener.prototype.before = undefined;\nAsyncListener.prototype.after  = undefined;\nAsyncListener.prototype.error  = undefined;\nAsyncListener.prototype.data   = undefined;\nAsyncListener.prototype.uid    = 0;\nAsyncListener.prototype.flags  = 0;\n\nfunction createAsyncListener(callbacks, data) {\n  if (typeof callbacks !== 'object' || !callbacks) {\n    throw new TypeError('callbacks argument must be an object');\n  }\n\n  if (callbacks instanceof AsyncListener) {\n    return callbacks;\n  }\n  else {\n    return new AsyncListener(callbacks, data);\n  }\n}\n\nfunction addAsyncListener(callbacks, data) {\n  var listener;\n  if (!(callbacks instanceof AsyncListener)) {\n    listener = createAsyncListener(callbacks, data);\n  }\n  else {\n    listener = callbacks;\n  }\n\n  // Make sure the listener isn't already in the list.\n  var registered = false;\n  for (var i = 0; i < listeners.length; i++) {\n    if (listener === listeners[i]) {\n      registered = true;\n      break;\n    }\n  }\n\n  if (!registered) listeners.push(listener);\n\n  return listener;\n}\n\nfunction removeAsyncListener(listener) {\n  for (var i = 0; i < listeners.length; i++) {\n    if (listener === listeners[i]) {\n      listeners.splice(i, 1);\n      break;\n    }\n  }\n}\n\nprocess.createAsyncListener = createAsyncListener;\nprocess.addAsyncListener    = addAsyncListener;\nprocess.removeAsyncListener = removeAsyncListener;\n\nmodule.exports = wrapCallback;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/async-listener/glue.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/async-listener/index.js":
/*!**************************************************!*\
  !*** ../../node_modules/async-listener/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nif (process.addAsyncListener) throw new Error(\"Don't require polyfill unless needed\");\n\nvar shimmer      = __webpack_require__(/*! shimmer */ \"(instrument)/../../node_modules/shimmer/index.js\")\n  , semver       = __webpack_require__(/*! semver */ \"(instrument)/../../node_modules/async-listener/node_modules/semver/semver.js\")\n  , wrap         = shimmer.wrap\n  , massWrap     = shimmer.massWrap\n  , wrapCallback = __webpack_require__(/*! ./glue.js */ \"(instrument)/../../node_modules/async-listener/glue.js\")\n  , util         = __webpack_require__(/*! util */ \"util\")\n  ;\n\nvar v6plus = semver.gte(process.version, '6.0.0');\nvar v7plus = semver.gte(process.version, '7.0.0');\nvar v8plus = semver.gte(process.version, '8.0.0');\nvar v11plus = semver.gte(process.version, '11.0.0');\n\nvar net = __webpack_require__(/*! net */ \"net\");\n\n// From Node.js v7.0.0, net._normalizeConnectArgs have been renamed net._normalizeArgs\nif (v7plus && !net._normalizeArgs) {\n  // a polyfill in our polyfill etc so forth -- taken from node master on 2017/03/09\n  net._normalizeArgs = function (args) {\n    if (args.length === 0) {\n      return [{}, null];\n    }\n\n    var arg0 = args[0];\n    var options = {};\n    if (typeof arg0 === 'object' && arg0 !== null) {\n      // (options[...][, cb])\n      options = arg0;\n    } else if (isPipeName(arg0)) {\n      // (path[...][, cb])\n      options.path = arg0;\n    } else {\n      // ([port][, host][...][, cb])\n      options.port = arg0;\n      if (args.length > 1 && typeof args[1] === 'string') {\n        options.host = args[1];\n      }\n    }\n\n    var cb = args[args.length - 1];\n    if (typeof cb !== 'function')\n      return [options, null];\n    else\n      return [options, cb];\n  }\n} else if (!v7plus && !net._normalizeConnectArgs) {\n  // a polyfill in our polyfill etc so forth -- taken from node master on 2013/10/30\n  net._normalizeConnectArgs = function (args) {\n    var options = {};\n\n    function toNumber(x) { return (x = Number(x)) >= 0 ? x : false; }\n\n    if (typeof args[0] === 'object' && args[0] !== null) {\n      // connect(options, [cb])\n      options = args[0];\n    }\n    else if (typeof args[0] === 'string' && toNumber(args[0]) === false) {\n      // connect(path, [cb]);\n      options.path = args[0];\n    }\n    else {\n      // connect(port, [host], [cb])\n      options.port = args[0];\n      if (typeof args[1] === 'string') {\n        options.host = args[1];\n      }\n    }\n\n    var cb = args[args.length - 1];\n    return typeof cb === 'function' ? [options, cb] : [options];\n  };\n}\n\n// In https://github.com/nodejs/node/pull/11796 `_listen2` was renamed\n// `_setUpListenHandle`. It's still aliased as `_listen2`, and currently the\n// Node internals still call the alias - but who knows for how long. So better\n// make sure we use the new name instead if available.\nif ('_setUpListenHandle' in net.Server.prototype) {\n  wrap(net.Server.prototype, '_setUpListenHandle', wrapSetUpListenHandle);\n} else {\n  wrap(net.Server.prototype, '_listen2', wrapSetUpListenHandle);\n}\n\nfunction wrapSetUpListenHandle(original) {\n  return function () {\n    this.on('connection', function (socket) {\n      if (socket._handle) {\n        socket._handle.onread = wrapCallback(socket._handle.onread);\n      }\n    });\n\n    try {\n      return original.apply(this, arguments);\n    }\n    finally {\n      // the handle will only not be set in cases where there has been an error\n      if (this._handle && this._handle.onconnection) {\n        this._handle.onconnection = wrapCallback(this._handle.onconnection);\n      }\n    }\n  };\n}\n\nfunction patchOnRead(ctx) {\n  if (ctx && ctx._handle) {\n    var handle = ctx._handle;\n    if (!handle._originalOnread) {\n      handle._originalOnread = handle.onread;\n    }\n    handle.onread = wrapCallback(handle._originalOnread);\n  }\n}\n\nwrap(net.Socket.prototype, 'connect', function (original) {\n  return function () {\n    var args;\n    // Node core uses an internal Symbol here to guard against the edge-case\n    // where the user accidentally passes in an array. As we don't have access\n    // to this Symbol we resort to this hack where we just detect if there is a\n    // symbol or not. Checking for the number of Symbols is by no means a fool\n    // proof solution, but it catches the most basic cases.\n    if (v8plus &&\n        Array.isArray(arguments[0]) &&\n        Object.getOwnPropertySymbols(arguments[0]).length > 0) {\n      // already normalized\n      args = arguments[0];\n    } else {\n      // From Node.js v7.0.0, net._normalizeConnectArgs have been renamed net._normalizeArgs\n      args = v7plus\n        ? net._normalizeArgs(arguments)\n        : net._normalizeConnectArgs(arguments);\n    }\n    if (args[1]) args[1] = wrapCallback(args[1]);\n    var result = original.apply(this, args);\n    patchOnRead(this);\n    return result;\n  };\n});\n\nvar http = __webpack_require__(/*! http */ \"http\");\n\n// NOTE: A rewrite occurred in 0.11 that changed the addRequest signature\n// from (req, host, port, localAddress) to (req, options)\n// Here, I use the longer signature to maintain 0.10 support, even though\n// the rest of the arguments aren't actually used\nwrap(http.Agent.prototype, 'addRequest', function (original) {\n  return function (req) {\n    var onSocket = req.onSocket;\n    req.onSocket = wrapCallback(function (socket) {\n      patchOnRead(socket);\n      return onSocket.apply(this, arguments);\n    });\n    return original.apply(this, arguments);\n  };\n});\n\nvar childProcess = __webpack_require__(/*! child_process */ \"child_process\");\n\nfunction wrapChildProcess(child) {\n  if (Array.isArray(child.stdio)) {\n    child.stdio.forEach(function (socket) {\n      if (socket && socket._handle) {\n        socket._handle.onread = wrapCallback(socket._handle.onread);\n        wrap(socket._handle, 'close', activatorFirst);\n      }\n    });\n  }\n\n  if (child._handle) {\n    child._handle.onexit = wrapCallback(child._handle.onexit);\n  }\n}\n\n// iojs v2.0.0+\nif (childProcess.ChildProcess) {\n  wrap(childProcess.ChildProcess.prototype, 'spawn', function (original) {\n    return function () {\n      var result = original.apply(this, arguments);\n      wrapChildProcess(this);\n      return result;\n    };\n  });\n} else {\n  massWrap(childProcess, [\n    'execFile', // exec is implemented in terms of execFile\n    'fork',\n    'spawn'\n  ], function (original) {\n    return function () {\n      var result = original.apply(this, arguments);\n      wrapChildProcess(result);\n      return result;\n    };\n  });\n}\n\n// need unwrapped nextTick for use within < 0.9 async error handling\nif (!process._fatalException) {\n  process._originalNextTick = process.nextTick;\n}\n\nvar processors = [];\nif (process._nextDomainTick) processors.push('_nextDomainTick');\nif (process._tickDomainCallback) processors.push('_tickDomainCallback');\n\nmassWrap(\n  process,\n  processors,\n  activator\n);\nwrap(process, 'nextTick', activatorFirst);\n\nvar asynchronizers = [\n  'setTimeout',\n  'setInterval'\n];\nif (global.setImmediate) asynchronizers.push('setImmediate');\n\nvar timers = __webpack_require__(/*! timers */ \"timers\");\nvar patchGlobalTimers = global.setTimeout === timers.setTimeout;\n\nmassWrap(\n  timers,\n  asynchronizers,\n  activatorFirst\n);\n\nif (patchGlobalTimers) {\n  massWrap(\n    global,\n    asynchronizers,\n    activatorFirst\n  );\n}\n\nvar dns = __webpack_require__(/*! dns */ \"dns\");\nmassWrap(\n  dns,\n  [\n    'lookup',\n    'resolve',\n    'resolve4',\n    'resolve6',\n    'resolveCname',\n    'resolveMx',\n    'resolveNs',\n    'resolveTxt',\n    'resolveSrv',\n    'reverse'\n  ],\n  activator\n);\n\nif (dns.resolveNaptr) wrap(dns, 'resolveNaptr', activator);\n\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nmassWrap(\n  fs,\n  [\n    'watch',\n    'rename',\n    'truncate',\n    'chown',\n    'fchown',\n    'chmod',\n    'fchmod',\n    'stat',\n    'lstat',\n    'fstat',\n    'link',\n    'symlink',\n    'readlink',\n    'realpath',\n    'unlink',\n    'rmdir',\n    'mkdir',\n    'readdir',\n    'close',\n    'open',\n    'utimes',\n    'futimes',\n    'fsync',\n    'write',\n    'read',\n    'readFile',\n    'writeFile',\n    'appendFile',\n    'watchFile',\n    'unwatchFile',\n    \"exists\",\n  ],\n  activator\n);\n\n// only wrap lchown and lchmod on systems that have them.\nif (fs.lchown) wrap(fs, 'lchown', activator);\nif (fs.lchmod) wrap(fs, 'lchmod', activator);\n\n// only wrap ftruncate in versions of node that have it\nif (fs.ftruncate) wrap(fs, 'ftruncate', activator);\n\n// Wrap zlib streams\nvar zlib;\ntry { zlib = __webpack_require__(/*! zlib */ \"zlib\"); } catch (err) { }\nif (zlib && zlib.Deflate && zlib.Deflate.prototype) {\n  var proto = Object.getPrototypeOf(zlib.Deflate.prototype);\n  if (proto._transform) {\n    // streams2\n    wrap(proto, \"_transform\", activator);\n  }\n  else if (proto.write && proto.flush && proto.end) {\n    // plain ol' streams\n    massWrap(\n      proto,\n      [\n        'write',\n        'flush',\n        'end'\n      ],\n      activator\n    );\n  }\n}\n\n// Wrap Crypto\nvar crypto;\ntry { crypto = __webpack_require__(/*! crypto */ \"crypto\"); } catch (err) { }\nif (crypto) {\n\n  var toWrap = [\n      'pbkdf2',\n      'randomBytes',\n  ];\n  if (!v11plus) {\n    toWrap.push('pseudoRandomBytes');\n  }\n\n  massWrap(crypto, toWrap, activator);\n}\n\n// It is unlikely that any userspace promise implementations have a native\n// implementation of both Promise and Promise.toString.\nvar instrumentPromise = !!global.Promise &&\n    Promise.toString() === 'function Promise() { [native code] }' &&\n    Promise.toString.toString() === 'function toString() { [native code] }';\n\n// Check that global Promise is native\nif (instrumentPromise) {\n  // shoult not use any methods that have already been wrapped\n  var promiseListener = process.addAsyncListener({\n    create: function create() {\n      instrumentPromise = false;\n    }\n  });\n\n  // should not resolve synchronously\n  global.Promise.resolve(true).then(function notSync() {\n    instrumentPromise = false;\n  });\n\n  process.removeAsyncListener(promiseListener);\n}\n\n/*\n * Native promises use the microtask queue to make all callbacks run\n * asynchronously to avoid Zalgo issues. Since the microtask queue is not\n * exposed externally, promises need to be modified in a fairly invasive and\n * complex way.\n *\n * The async boundary in promises that must be patched is between the\n * fulfillment of the promise and the execution of any callback that is waiting\n * for that fulfillment to happen. This means that we need to trigger a create\n * when resolve or reject is called and trigger before, after and error handlers\n * around the callback execution. There may be multiple callbacks for each\n * fulfilled promise, so handlers will behave similar to setInterval where\n * there may be multiple before after and error calls for each create call.\n *\n * async-listener monkeypatching has one basic entry point: `wrapCallback`.\n * `wrapCallback` should be called when create should be triggered and be\n * passed a function to wrap, which will execute the body of the async work.\n * The resolve and reject calls can be modified fairly easily to call\n * `wrapCallback`, but at the time of resolve and reject all the work to be done\n * on fulfillment may not be defined, since a call to then, chain or fetch can\n * be made even after the promise has been fulfilled. To get around this, we\n * create a placeholder function which will call a function passed into it,\n * since the call to the main work is being made from within the wrapped\n * function, async-listener will work correctly.\n *\n * There is another complication with monkeypatching Promises. Calls to then,\n * chain and catch each create new Promises that are fulfilled internally in\n * different ways depending on the return value of the callback. When the\n * callback return a Promise, the new Promise is resolved asynchronously after\n * the returned Promise has been also been resolved. When something other than\n * a promise is resolved the resolve call for the new Promise is put in the\n * microtask queue and asynchronously resolved.\n *\n * Then must be wrapped so that its returned promise has a wrapper that can be\n * used to invoke further continuations. This wrapper cannot be created until\n * after the callback has run, since the callback may return either a promise\n * or another value. Fortunately we already have a wrapper function around the\n * callback we can use (the wrapper created by resolve or reject).\n *\n * By adding an additional argument to this wrapper, we can pass in the\n * returned promise so it can have its own wrapper appended. the wrapper\n * function can the call the callback, and take action based on the return\n * value. If a promise is returned, the new Promise can proxy the returned\n * Promise's wrapper (this wrapper may not exist yet, but will by the time the\n * wrapper needs to be invoked). Otherwise, a new wrapper can be create the\n * same way as in resolve and reject. Since this wrapper is created\n * synchronously within another wrapper, it will properly appear as a\n * continuation from within the callback.\n */\n\nif (instrumentPromise) {\n  wrapPromise();\n}\n\nfunction wrapPromise() {\n  var Promise = global.Promise;\n\n  // Updates to this class should also be applied to the the ES6 version\n  // in es6-wrapped-promise.js.\n  function wrappedPromise(executor) {\n    if (!(this instanceof wrappedPromise)) {\n      return Promise(executor);\n    }\n\n    if (typeof executor !== 'function') {\n      return new Promise(executor);\n    }\n\n    var context, args;\n    var promise = new Promise(wrappedExecutor);\n    promise.__proto__ = wrappedPromise.prototype;\n\n    try {\n      executor.apply(context, args);\n    } catch (err) {\n      args[1](err);\n    }\n\n    return promise;\n\n    function wrappedExecutor(resolve, reject) {\n      context = this;\n      args = [wrappedResolve, wrappedReject];\n\n      // These wrappers create a function that can be passed a function and an argument to\n      // call as a continuation from the resolve or reject.\n      function wrappedResolve(val) {\n        ensureAslWrapper(promise, false);\n        return resolve(val);\n      }\n\n      function wrappedReject(val) {\n        ensureAslWrapper(promise, false);\n        return reject(val);\n      }\n    }\n  }\n\n  util.inherits(wrappedPromise, Promise);\n\n  wrap(Promise.prototype, 'then', wrapThen);\n  // Node.js <v7 only, alias for .then\n  if (Promise.prototype.chain) {\n    wrap(Promise.prototype, 'chain', wrapThen);\n  }\n\n  if (v6plus) {\n    global.Promise = __webpack_require__(/*! ./es6-wrapped-promise.js */ \"(instrument)/../../node_modules/async-listener/es6-wrapped-promise.js\")(Promise, ensureAslWrapper);\n  } else {\n    var PromiseFunctions = [\n      'all',\n      'race',\n      'reject',\n      'resolve',\n      'accept',  // Node.js <v7 only\n      'defer'    // Node.js <v7 only\n    ];\n\n    PromiseFunctions.forEach(function(key) {\n      // don't break `in` by creating a key for undefined entries\n      if (typeof Promise[key] === 'function') {\n        wrappedPromise[key] = Promise[key];\n      }\n    });\n    global.Promise = wrappedPromise\n  }\n\n  function ensureAslWrapper(promise, overwrite) {\n    if (!promise.__asl_wrapper || overwrite) {\n      promise.__asl_wrapper = wrapCallback(propagateAslWrapper);\n    }\n  }\n\n  function propagateAslWrapper(ctx, fn, result, next) {\n    var nextResult;\n    try {\n      nextResult = fn.call(ctx, result);\n      return {returnVal: nextResult, error: false}\n    } catch (err) {\n      return {errorVal: err, error: true}\n    } finally {\n      // Wrap any resulting futures as continuations.\n      if (nextResult instanceof Promise) {\n        next.__asl_wrapper = function proxyWrapper() {\n          var aslWrapper = nextResult.__asl_wrapper || propagateAslWrapper;\n          return aslWrapper.apply(this, arguments);\n        }\n      } else {\n        ensureAslWrapper(next, true);\n      }\n    }\n  }\n\n  function wrapThen(original) {\n    return function wrappedThen() {\n      var promise = this;\n      var next = original.apply(promise, Array.prototype.map.call(arguments, bind));\n\n      next.__asl_wrapper = function proxyWrapper(ctx, fn, val, last) {\n        if (promise.__asl_wrapper) {\n          promise.__asl_wrapper(ctx, function () {}, null, next);\n          return next.__asl_wrapper(ctx, fn, val, last);\n        }\n        return propagateAslWrapper(ctx, fn, val, last);\n      }\n\n      return next;\n\n      // wrap callbacks (success, error) so that the callbacks will be called as a\n      // continuations of the resolve or reject call using the __asl_wrapper created above.\n      function bind(fn) {\n        if (typeof fn !== 'function') return fn;\n        return wrapCallback(function (val) {\n          var result = (promise.__asl_wrapper || propagateAslWrapper)(this, fn, val, next);\n          if (result.error) {\n            throw result.errorVal\n          } else {\n            return result.returnVal\n          }\n        });\n      }\n    }\n  }\n}\n\n// Shim activator for functions that have callback last\nfunction activator(fn) {\n  var fallback = function () {\n    var args;\n    var cbIdx = arguments.length - 1;\n    if (typeof arguments[cbIdx] === \"function\") {\n      args = Array(arguments.length)\n      for (var i = 0; i < arguments.length - 1; i++) {\n        args[i] = arguments[i];\n      }\n      args[cbIdx] = wrapCallback(arguments[cbIdx]);\n    }\n    return fn.apply(this, args || arguments);\n  };\n  // Preserve function length for small arg count functions.\n  switch (fn.length) {\n    case 1:\n      return function (cb) {\n        if (arguments.length !== 1) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, cb);\n      };\n    case 2:\n      return function (a, cb) {\n        if (arguments.length !== 2) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, a, cb);\n      };\n    case 3:\n      return function (a, b, cb) {\n        if (arguments.length !== 3) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, a, b, cb);\n      };\n    case 4:\n      return function (a, b, c, cb) {\n        if (arguments.length !== 4) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, a, b, c, cb);\n      };\n    case 5:\n      return function (a, b, c, d, cb) {\n        if (arguments.length !== 5) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, a, b, c, d, cb);\n      };\n    case 6:\n      return function (a, b, c, d, e, cb) {\n        if (arguments.length !== 6) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, a, b, c, d, e, cb);\n      };\n    default:\n      return fallback;\n  }\n}\n\n// Shim activator for functions that have callback first\nfunction activatorFirst(fn) {\n  var fallback = function () {\n    var args;\n    if (typeof arguments[0] === \"function\") {\n      args = Array(arguments.length)\n      args[0] = wrapCallback(arguments[0]);\n      for (var i = 1; i < arguments.length; i++) {\n        args[i] = arguments[i];\n      }\n    }\n    return fn.apply(this, args || arguments);\n  };\n  // Preserve function length for small arg count functions.\n  switch (fn.length) {\n    case 1:\n      return function (cb) {\n        if (arguments.length !== 1) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, cb);\n      };\n    case 2:\n      return function (cb, a) {\n        if (arguments.length !== 2) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, cb, a);\n      };\n    case 3:\n      return function (cb, a, b) {\n        if (arguments.length !== 3) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, cb, a, b);\n      };\n    case 4:\n      return function (cb, a, b, c) {\n        if (arguments.length !== 4) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, cb, a, b, c);\n      };\n    case 5:\n      return function (cb, a, b, c, d) {\n        if (arguments.length !== 5) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, cb, a, b, c, d);\n      };\n    case 6:\n      return function (cb, a, b, c, d, e) {\n        if (arguments.length !== 6) return fallback.apply(this, arguments);\n        if (typeof cb === \"function\") cb = wrapCallback(cb);\n        return fn.call(this, cb, a, b, c, d, e);\n      };\n    default:\n      return fallback;\n  }\n}\n\n// taken from node master on 2017/03/09\nfunction toNumber(x) {\n  return (x = Number(x)) >= 0 ? x : false;\n}\n\n// taken from node master on 2017/03/09\nfunction isPipeName(s) {\n  return typeof s === 'string' && toNumber(s) === false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/async-listener/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/async-listener/node_modules/semver/semver.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/async-listener/node_modules/semver/semver.js ***!
  \***********************************************************************/
/***/ ((module, exports) => {

eval("exports = module.exports = SemVer\n\nvar debug\n/* istanbul ignore next */\nif (typeof process === 'object' &&\n    process.env &&\n    process.env.NODE_DEBUG &&\n    /\\bsemver\\b/i.test(process.env.NODE_DEBUG)) {\n  debug = function () {\n    var args = Array.prototype.slice.call(arguments, 0)\n    args.unshift('SEMVER')\n    console.log.apply(console, args)\n  }\n} else {\n  debug = function () {}\n}\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nexports.SEMVER_SPEC_VERSION = '2.0.0'\n\nvar MAX_LENGTH = 256\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n  /* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nvar MAX_SAFE_COMPONENT_LENGTH = 16\n\nvar MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\n// The actual regexps go on exports.re\nvar re = exports.re = []\nvar safeRe = exports.safeRe = []\nvar src = exports.src = []\nvar R = 0\n\nvar LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nvar safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nfunction makeSafeRe (value) {\n  for (var i = 0; i < safeRegexReplacements.length; i++) {\n    var token = safeRegexReplacements[i][0]\n    var max = safeRegexReplacements[i][1]\n    value = value\n      .split(token + '*').join(token + '{0,' + max + '}')\n      .split(token + '+').join(token + '{1,' + max + '}')\n  }\n  return value\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\nvar NUMERICIDENTIFIER = R++\nsrc[NUMERICIDENTIFIER] = '0|[1-9]\\\\d*'\nvar NUMERICIDENTIFIERLOOSE = R++\nsrc[NUMERICIDENTIFIERLOOSE] = '\\\\d+'\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\nvar NONNUMERICIDENTIFIER = R++\nsrc[NONNUMERICIDENTIFIER] = '\\\\d*[a-zA-Z-]' + LETTERDASHNUMBER + '*'\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\nvar MAINVERSION = R++\nsrc[MAINVERSION] = '(' + src[NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[NUMERICIDENTIFIER] + ')\\\\.' +\n                   '(' + src[NUMERICIDENTIFIER] + ')'\n\nvar MAINVERSIONLOOSE = R++\nsrc[MAINVERSIONLOOSE] = '(' + src[NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[NUMERICIDENTIFIERLOOSE] + ')\\\\.' +\n                        '(' + src[NUMERICIDENTIFIERLOOSE] + ')'\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n\nvar PRERELEASEIDENTIFIER = R++\nsrc[PRERELEASEIDENTIFIER] = '(?:' + src[NUMERICIDENTIFIER] +\n                            '|' + src[NONNUMERICIDENTIFIER] + ')'\n\nvar PRERELEASEIDENTIFIERLOOSE = R++\nsrc[PRERELEASEIDENTIFIERLOOSE] = '(?:' + src[NUMERICIDENTIFIERLOOSE] +\n                                 '|' + src[NONNUMERICIDENTIFIER] + ')'\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\nvar PRERELEASE = R++\nsrc[PRERELEASE] = '(?:-(' + src[PRERELEASEIDENTIFIER] +\n                  '(?:\\\\.' + src[PRERELEASEIDENTIFIER] + ')*))'\n\nvar PRERELEASELOOSE = R++\nsrc[PRERELEASELOOSE] = '(?:-?(' + src[PRERELEASEIDENTIFIERLOOSE] +\n                       '(?:\\\\.' + src[PRERELEASEIDENTIFIERLOOSE] + ')*))'\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\nvar BUILDIDENTIFIER = R++\nsrc[BUILDIDENTIFIER] = LETTERDASHNUMBER + '+'\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\nvar BUILD = R++\nsrc[BUILD] = '(?:\\\\+(' + src[BUILDIDENTIFIER] +\n             '(?:\\\\.' + src[BUILDIDENTIFIER] + ')*))'\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\nvar FULL = R++\nvar FULLPLAIN = 'v?' + src[MAINVERSION] +\n                src[PRERELEASE] + '?' +\n                src[BUILD] + '?'\n\nsrc[FULL] = '^' + FULLPLAIN + '$'\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\nvar LOOSEPLAIN = '[v=\\\\s]*' + src[MAINVERSIONLOOSE] +\n                 src[PRERELEASELOOSE] + '?' +\n                 src[BUILD] + '?'\n\nvar LOOSE = R++\nsrc[LOOSE] = '^' + LOOSEPLAIN + '$'\n\nvar GTLT = R++\nsrc[GTLT] = '((?:<|>)?=?)'\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\nvar XRANGEIDENTIFIERLOOSE = R++\nsrc[XRANGEIDENTIFIERLOOSE] = src[NUMERICIDENTIFIERLOOSE] + '|x|X|\\\\*'\nvar XRANGEIDENTIFIER = R++\nsrc[XRANGEIDENTIFIER] = src[NUMERICIDENTIFIER] + '|x|X|\\\\*'\n\nvar XRANGEPLAIN = R++\nsrc[XRANGEPLAIN] = '[v=\\\\s]*(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:\\\\.(' + src[XRANGEIDENTIFIER] + ')' +\n                   '(?:' + src[PRERELEASE] + ')?' +\n                   src[BUILD] + '?' +\n                   ')?)?'\n\nvar XRANGEPLAINLOOSE = R++\nsrc[XRANGEPLAINLOOSE] = '[v=\\\\s]*(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:\\\\.(' + src[XRANGEIDENTIFIERLOOSE] + ')' +\n                        '(?:' + src[PRERELEASELOOSE] + ')?' +\n                        src[BUILD] + '?' +\n                        ')?)?'\n\nvar XRANGE = R++\nsrc[XRANGE] = '^' + src[GTLT] + '\\\\s*' + src[XRANGEPLAIN] + '$'\nvar XRANGELOOSE = R++\nsrc[XRANGELOOSE] = '^' + src[GTLT] + '\\\\s*' + src[XRANGEPLAINLOOSE] + '$'\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\nvar COERCE = R++\nsrc[COERCE] = '(?:^|[^\\\\d])' +\n              '(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '})' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:\\\\.(\\\\d{1,' + MAX_SAFE_COMPONENT_LENGTH + '}))?' +\n              '(?:$|[^\\\\d])'\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\nvar LONETILDE = R++\nsrc[LONETILDE] = '(?:~>?)'\n\nvar TILDETRIM = R++\nsrc[TILDETRIM] = '(\\\\s*)' + src[LONETILDE] + '\\\\s+'\nre[TILDETRIM] = new RegExp(src[TILDETRIM], 'g')\nsafeRe[TILDETRIM] = new RegExp(makeSafeRe(src[TILDETRIM]), 'g')\nvar tildeTrimReplace = '$1~'\n\nvar TILDE = R++\nsrc[TILDE] = '^' + src[LONETILDE] + src[XRANGEPLAIN] + '$'\nvar TILDELOOSE = R++\nsrc[TILDELOOSE] = '^' + src[LONETILDE] + src[XRANGEPLAINLOOSE] + '$'\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\nvar LONECARET = R++\nsrc[LONECARET] = '(?:\\\\^)'\n\nvar CARETTRIM = R++\nsrc[CARETTRIM] = '(\\\\s*)' + src[LONECARET] + '\\\\s+'\nre[CARETTRIM] = new RegExp(src[CARETTRIM], 'g')\nsafeRe[CARETTRIM] = new RegExp(makeSafeRe(src[CARETTRIM]), 'g')\nvar caretTrimReplace = '$1^'\n\nvar CARET = R++\nsrc[CARET] = '^' + src[LONECARET] + src[XRANGEPLAIN] + '$'\nvar CARETLOOSE = R++\nsrc[CARETLOOSE] = '^' + src[LONECARET] + src[XRANGEPLAINLOOSE] + '$'\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\nvar COMPARATORLOOSE = R++\nsrc[COMPARATORLOOSE] = '^' + src[GTLT] + '\\\\s*(' + LOOSEPLAIN + ')$|^$'\nvar COMPARATOR = R++\nsrc[COMPARATOR] = '^' + src[GTLT] + '\\\\s*(' + FULLPLAIN + ')$|^$'\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\nvar COMPARATORTRIM = R++\nsrc[COMPARATORTRIM] = '(\\\\s*)' + src[GTLT] +\n                      '\\\\s*(' + LOOSEPLAIN + '|' + src[XRANGEPLAIN] + ')'\n\n// this one has to use the /g flag\nre[COMPARATORTRIM] = new RegExp(src[COMPARATORTRIM], 'g')\nsafeRe[COMPARATORTRIM] = new RegExp(makeSafeRe(src[COMPARATORTRIM]), 'g')\nvar comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\nvar HYPHENRANGE = R++\nsrc[HYPHENRANGE] = '^\\\\s*(' + src[XRANGEPLAIN] + ')' +\n                   '\\\\s+-\\\\s+' +\n                   '(' + src[XRANGEPLAIN] + ')' +\n                   '\\\\s*$'\n\nvar HYPHENRANGELOOSE = R++\nsrc[HYPHENRANGELOOSE] = '^\\\\s*(' + src[XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s+-\\\\s+' +\n                        '(' + src[XRANGEPLAINLOOSE] + ')' +\n                        '\\\\s*$'\n\n// Star ranges basically just allow anything at all.\nvar STAR = R++\nsrc[STAR] = '(<|>)?=?\\\\s*\\\\*'\n\n// Compile to actual regexp objects.\n// All are flag-free, unless they were created above with a flag.\nfor (var i = 0; i < R; i++) {\n  debug(i, src[i])\n  if (!re[i]) {\n    re[i] = new RegExp(src[i])\n\n    // Replace all greedy whitespace to prevent regex dos issues. These regex are\n    // used internally via the safeRe object since all inputs in this library get\n    // normalized first to trim and collapse all extra whitespace. The original\n    // regexes are exported for userland consumption and lower level usage. A\n    // future breaking change could export the safer regex only with a note that\n    // all input should have extra whitespace removed.\n    safeRe[i] = new RegExp(makeSafeRe(src[i]))\n  }\n}\n\nexports.parse = parse\nfunction parse (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  if (version.length > MAX_LENGTH) {\n    return null\n  }\n\n  var r = options.loose ? safeRe[LOOSE] : safeRe[FULL]\n  if (!r.test(version)) {\n    return null\n  }\n\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    return null\n  }\n}\n\nexports.valid = valid\nfunction valid (version, options) {\n  var v = parse(version, options)\n  return v ? v.version : null\n}\n\nexports.clean = clean\nfunction clean (version, options) {\n  var s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\n\nexports.SemVer = SemVer\n\nfunction SemVer (version, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n  if (version instanceof SemVer) {\n    if (version.loose === options.loose) {\n      return version\n    } else {\n      version = version.version\n    }\n  } else if (typeof version !== 'string') {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  if (version.length > MAX_LENGTH) {\n    throw new TypeError('version is longer than ' + MAX_LENGTH + ' characters')\n  }\n\n  if (!(this instanceof SemVer)) {\n    return new SemVer(version, options)\n  }\n\n  debug('SemVer', version, options)\n  this.options = options\n  this.loose = !!options.loose\n\n  var m = version.trim().match(options.loose ? safeRe[LOOSE] : safeRe[FULL])\n\n  if (!m) {\n    throw new TypeError('Invalid Version: ' + version)\n  }\n\n  this.raw = version\n\n  // these are actually numbers\n  this.major = +m[1]\n  this.minor = +m[2]\n  this.patch = +m[3]\n\n  if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n    throw new TypeError('Invalid major version')\n  }\n\n  if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n    throw new TypeError('Invalid minor version')\n  }\n\n  if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n    throw new TypeError('Invalid patch version')\n  }\n\n  // numberify any prerelease numeric ids\n  if (!m[4]) {\n    this.prerelease = []\n  } else {\n    this.prerelease = m[4].split('.').map(function (id) {\n      if (/^[0-9]+$/.test(id)) {\n        var num = +id\n        if (num >= 0 && num < MAX_SAFE_INTEGER) {\n          return num\n        }\n      }\n      return id\n    })\n  }\n\n  this.build = m[5] ? m[5].split('.') : []\n  this.format()\n}\n\nSemVer.prototype.format = function () {\n  this.version = this.major + '.' + this.minor + '.' + this.patch\n  if (this.prerelease.length) {\n    this.version += '-' + this.prerelease.join('.')\n  }\n  return this.version\n}\n\nSemVer.prototype.toString = function () {\n  return this.version\n}\n\nSemVer.prototype.compare = function (other) {\n  debug('SemVer.compare', this.version, this.options, other)\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return this.compareMain(other) || this.comparePre(other)\n}\n\nSemVer.prototype.compareMain = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  return compareIdentifiers(this.major, other.major) ||\n         compareIdentifiers(this.minor, other.minor) ||\n         compareIdentifiers(this.patch, other.patch)\n}\n\nSemVer.prototype.comparePre = function (other) {\n  if (!(other instanceof SemVer)) {\n    other = new SemVer(other, this.options)\n  }\n\n  // NOT having a prerelease is > having one\n  if (this.prerelease.length && !other.prerelease.length) {\n    return -1\n  } else if (!this.prerelease.length && other.prerelease.length) {\n    return 1\n  } else if (!this.prerelease.length && !other.prerelease.length) {\n    return 0\n  }\n\n  var i = 0\n  do {\n    var a = this.prerelease[i]\n    var b = other.prerelease[i]\n    debug('prerelease compare', i, a, b)\n    if (a === undefined && b === undefined) {\n      return 0\n    } else if (b === undefined) {\n      return 1\n    } else if (a === undefined) {\n      return -1\n    } else if (a === b) {\n      continue\n    } else {\n      return compareIdentifiers(a, b)\n    }\n  } while (++i)\n}\n\n// preminor will bump the version up to the next minor release, and immediately\n// down to pre-release. premajor and prepatch work the same way.\nSemVer.prototype.inc = function (release, identifier) {\n  switch (release) {\n    case 'premajor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor = 0\n      this.major++\n      this.inc('pre', identifier)\n      break\n    case 'preminor':\n      this.prerelease.length = 0\n      this.patch = 0\n      this.minor++\n      this.inc('pre', identifier)\n      break\n    case 'prepatch':\n      // If this is already a prerelease, it will bump to the next version\n      // drop any prereleases that might already exist, since they are not\n      // relevant at this point.\n      this.prerelease.length = 0\n      this.inc('patch', identifier)\n      this.inc('pre', identifier)\n      break\n    // If the input is a non-prerelease version, this acts the same as\n    // prepatch.\n    case 'prerelease':\n      if (this.prerelease.length === 0) {\n        this.inc('patch', identifier)\n      }\n      this.inc('pre', identifier)\n      break\n\n    case 'major':\n      // If this is a pre-major version, bump up to the same major version.\n      // Otherwise increment major.\n      // 1.0.0-5 bumps to 1.0.0\n      // 1.1.0 bumps to 2.0.0\n      if (this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0) {\n        this.major++\n      }\n      this.minor = 0\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'minor':\n      // If this is a pre-minor version, bump up to the same minor version.\n      // Otherwise increment minor.\n      // 1.2.0-5 bumps to 1.2.0\n      // 1.2.1 bumps to 1.3.0\n      if (this.patch !== 0 || this.prerelease.length === 0) {\n        this.minor++\n      }\n      this.patch = 0\n      this.prerelease = []\n      break\n    case 'patch':\n      // If this is not a pre-release version, it will increment the patch.\n      // If it is a pre-release it will bump up to the same patch version.\n      // 1.2.0-5 patches to 1.2.0\n      // 1.2.0 patches to 1.2.1\n      if (this.prerelease.length === 0) {\n        this.patch++\n      }\n      this.prerelease = []\n      break\n    // This probably shouldn't be used publicly.\n    // 1.0.0 \"pre\" would become 1.0.0-0 which is the wrong direction.\n    case 'pre':\n      if (this.prerelease.length === 0) {\n        this.prerelease = [0]\n      } else {\n        var i = this.prerelease.length\n        while (--i >= 0) {\n          if (typeof this.prerelease[i] === 'number') {\n            this.prerelease[i]++\n            i = -2\n          }\n        }\n        if (i === -1) {\n          // didn't increment anything\n          this.prerelease.push(0)\n        }\n      }\n      if (identifier) {\n        // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n        // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n        if (this.prerelease[0] === identifier) {\n          if (isNaN(this.prerelease[1])) {\n            this.prerelease = [identifier, 0]\n          }\n        } else {\n          this.prerelease = [identifier, 0]\n        }\n      }\n      break\n\n    default:\n      throw new Error('invalid increment argument: ' + release)\n  }\n  this.format()\n  this.raw = this.version\n  return this\n}\n\nexports.inc = inc\nfunction inc (version, release, loose, identifier) {\n  if (typeof (loose) === 'string') {\n    identifier = loose\n    loose = undefined\n  }\n\n  try {\n    return new SemVer(version, loose).inc(release, identifier).version\n  } catch (er) {\n    return null\n  }\n}\n\nexports.diff = diff\nfunction diff (version1, version2) {\n  if (eq(version1, version2)) {\n    return null\n  } else {\n    var v1 = parse(version1)\n    var v2 = parse(version2)\n    var prefix = ''\n    if (v1.prerelease.length || v2.prerelease.length) {\n      prefix = 'pre'\n      var defaultResult = 'prerelease'\n    }\n    for (var key in v1) {\n      if (key === 'major' || key === 'minor' || key === 'patch') {\n        if (v1[key] !== v2[key]) {\n          return prefix + key\n        }\n      }\n    }\n    return defaultResult // may be undefined\n  }\n}\n\nexports.compareIdentifiers = compareIdentifiers\n\nvar numeric = /^[0-9]+$/\nfunction compareIdentifiers (a, b) {\n  var anum = numeric.test(a)\n  var bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nexports.rcompareIdentifiers = rcompareIdentifiers\nfunction rcompareIdentifiers (a, b) {\n  return compareIdentifiers(b, a)\n}\n\nexports.major = major\nfunction major (a, loose) {\n  return new SemVer(a, loose).major\n}\n\nexports.minor = minor\nfunction minor (a, loose) {\n  return new SemVer(a, loose).minor\n}\n\nexports.patch = patch\nfunction patch (a, loose) {\n  return new SemVer(a, loose).patch\n}\n\nexports.compare = compare\nfunction compare (a, b, loose) {\n  return new SemVer(a, loose).compare(new SemVer(b, loose))\n}\n\nexports.compareLoose = compareLoose\nfunction compareLoose (a, b) {\n  return compare(a, b, true)\n}\n\nexports.rcompare = rcompare\nfunction rcompare (a, b, loose) {\n  return compare(b, a, loose)\n}\n\nexports.sort = sort\nfunction sort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.compare(a, b, loose)\n  })\n}\n\nexports.rsort = rsort\nfunction rsort (list, loose) {\n  return list.sort(function (a, b) {\n    return exports.rcompare(a, b, loose)\n  })\n}\n\nexports.gt = gt\nfunction gt (a, b, loose) {\n  return compare(a, b, loose) > 0\n}\n\nexports.lt = lt\nfunction lt (a, b, loose) {\n  return compare(a, b, loose) < 0\n}\n\nexports.eq = eq\nfunction eq (a, b, loose) {\n  return compare(a, b, loose) === 0\n}\n\nexports.neq = neq\nfunction neq (a, b, loose) {\n  return compare(a, b, loose) !== 0\n}\n\nexports.gte = gte\nfunction gte (a, b, loose) {\n  return compare(a, b, loose) >= 0\n}\n\nexports.lte = lte\nfunction lte (a, b, loose) {\n  return compare(a, b, loose) <= 0\n}\n\nexports.cmp = cmp\nfunction cmp (a, op, b, loose) {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object')\n        a = a.version\n      if (typeof b === 'object')\n        b = b.version\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError('Invalid operator: ' + op)\n  }\n}\n\nexports.Comparator = Comparator\nfunction Comparator (comp, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (comp instanceof Comparator) {\n    if (comp.loose === !!options.loose) {\n      return comp\n    } else {\n      comp = comp.value\n    }\n  }\n\n  if (!(this instanceof Comparator)) {\n    return new Comparator(comp, options)\n  }\n\n  comp = comp.trim().split(/\\s+/).join(' ')\n  debug('comparator', comp, options)\n  this.options = options\n  this.loose = !!options.loose\n  this.parse(comp)\n\n  if (this.semver === ANY) {\n    this.value = ''\n  } else {\n    this.value = this.operator + this.semver.version\n  }\n\n  debug('comp', this)\n}\n\nvar ANY = {}\nComparator.prototype.parse = function (comp) {\n  var r = this.options.loose ? safeRe[COMPARATORLOOSE] : safeRe[COMPARATOR]\n  var m = comp.match(r)\n\n  if (!m) {\n    throw new TypeError('Invalid comparator: ' + comp)\n  }\n\n  this.operator = m[1]\n  if (this.operator === '=') {\n    this.operator = ''\n  }\n\n  // if it literally is just '>' or '' then allow anything.\n  if (!m[2]) {\n    this.semver = ANY\n  } else {\n    this.semver = new SemVer(m[2], this.options.loose)\n  }\n}\n\nComparator.prototype.toString = function () {\n  return this.value\n}\n\nComparator.prototype.test = function (version) {\n  debug('Comparator.test', version, this.options.loose)\n\n  if (this.semver === ANY) {\n    return true\n  }\n\n  if (typeof version === 'string') {\n    version = new SemVer(version, this.options)\n  }\n\n  return cmp(version, this.operator, this.semver, this.options)\n}\n\nComparator.prototype.intersects = function (comp, options) {\n  if (!(comp instanceof Comparator)) {\n    throw new TypeError('a Comparator is required')\n  }\n\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  var rangeTmp\n\n  if (this.operator === '') {\n    rangeTmp = new Range(comp.value, options)\n    return satisfies(this.value, rangeTmp, options)\n  } else if (comp.operator === '') {\n    rangeTmp = new Range(this.value, options)\n    return satisfies(comp.semver, rangeTmp, options)\n  }\n\n  var sameDirectionIncreasing =\n    (this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '>=' || comp.operator === '>')\n  var sameDirectionDecreasing =\n    (this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '<=' || comp.operator === '<')\n  var sameSemVer = this.semver.version === comp.semver.version\n  var differentDirectionsInclusive =\n    (this.operator === '>=' || this.operator === '<=') &&\n    (comp.operator === '>=' || comp.operator === '<=')\n  var oppositeDirectionsLessThan =\n    cmp(this.semver, '<', comp.semver, options) &&\n    ((this.operator === '>=' || this.operator === '>') &&\n    (comp.operator === '<=' || comp.operator === '<'))\n  var oppositeDirectionsGreaterThan =\n    cmp(this.semver, '>', comp.semver, options) &&\n    ((this.operator === '<=' || this.operator === '<') &&\n    (comp.operator === '>=' || comp.operator === '>'))\n\n  return sameDirectionIncreasing || sameDirectionDecreasing ||\n    (sameSemVer && differentDirectionsInclusive) ||\n    oppositeDirectionsLessThan || oppositeDirectionsGreaterThan\n}\n\nexports.Range = Range\nfunction Range (range, options) {\n  if (!options || typeof options !== 'object') {\n    options = {\n      loose: !!options,\n      includePrerelease: false\n    }\n  }\n\n  if (range instanceof Range) {\n    if (range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease) {\n      return range\n    } else {\n      return new Range(range.raw, options)\n    }\n  }\n\n  if (range instanceof Comparator) {\n    return new Range(range.value, options)\n  }\n\n  if (!(this instanceof Range)) {\n    return new Range(range, options)\n  }\n\n  this.options = options\n  this.loose = !!options.loose\n  this.includePrerelease = !!options.includePrerelease\n\n  // First reduce all whitespace as much as possible so we do not have to rely\n  // on potentially slow regexes like \\s*. This is then stored and used for\n  // future error messages as well.\n  this.raw = range\n    .trim()\n    .split(/\\s+/)\n    .join(' ')\n\n  // First, split based on boolean or ||\n  this.set = this.raw.split('||').map(function (range) {\n    return this.parseRange(range.trim())\n  }, this).filter(function (c) {\n    // throw out any that are not relevant for whatever reason\n    return c.length\n  })\n\n  if (!this.set.length) {\n    throw new TypeError('Invalid SemVer Range: ' + this.raw)\n  }\n\n  this.format()\n}\n\nRange.prototype.format = function () {\n  this.range = this.set.map(function (comps) {\n    return comps.join(' ').trim()\n  }).join('||').trim()\n  return this.range\n}\n\nRange.prototype.toString = function () {\n  return this.range\n}\n\nRange.prototype.parseRange = function (range) {\n  var loose = this.options.loose\n  // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n  var hr = loose ? safeRe[HYPHENRANGELOOSE] : safeRe[HYPHENRANGE]\n  range = range.replace(hr, hyphenReplace)\n  debug('hyphen replace', range)\n  // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n  range = range.replace(safeRe[COMPARATORTRIM], comparatorTrimReplace)\n  debug('comparator trim', range, safeRe[COMPARATORTRIM])\n\n  // `~ 1.2.3` => `~1.2.3`\n  range = range.replace(safeRe[TILDETRIM], tildeTrimReplace)\n\n  // `^ 1.2.3` => `^1.2.3`\n  range = range.replace(safeRe[CARETTRIM], caretTrimReplace)\n\n  // At this point, the range is completely trimmed and\n  // ready to be split into comparators.\n  var compRe = loose ? safeRe[COMPARATORLOOSE] : safeRe[COMPARATOR]\n  var set = range.split(' ').map(function (comp) {\n    return parseComparator(comp, this.options)\n  }, this).join(' ').split(/\\s+/)\n  if (this.options.loose) {\n    // in loose mode, throw out any that are not valid comparators\n    set = set.filter(function (comp) {\n      return !!comp.match(compRe)\n    })\n  }\n  set = set.map(function (comp) {\n    return new Comparator(comp, this.options)\n  }, this)\n\n  return set\n}\n\nRange.prototype.intersects = function (range, options) {\n  if (!(range instanceof Range)) {\n    throw new TypeError('a Range is required')\n  }\n\n  return this.set.some(function (thisComparators) {\n    return thisComparators.every(function (thisComparator) {\n      return range.set.some(function (rangeComparators) {\n        return rangeComparators.every(function (rangeComparator) {\n          return thisComparator.intersects(rangeComparator, options)\n        })\n      })\n    })\n  })\n}\n\n// Mostly just for testing and legacy API reasons\nexports.toComparators = toComparators\nfunction toComparators (range, options) {\n  return new Range(range, options).set.map(function (comp) {\n    return comp.map(function (c) {\n      return c.value\n    }).join(' ').trim().split(' ')\n  })\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nfunction parseComparator (comp, options) {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nfunction isX (id) {\n  return !id || id.toLowerCase() === 'x' || id === '*'\n}\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0\nfunction replaceTildes (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceTilde(comp, options)\n  }).join(' ')\n}\n\nfunction replaceTilde (comp, options) {\n  var r = options.loose ? safeRe[TILDELOOSE] : safeRe[TILDE]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('tilde', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0\n      ret = '>=' + M + '.' + m + '.' + p +\n            ' <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0\n// ^1.2.3 --> >=1.2.3 <2.0.0\n// ^1.2.0 --> >=1.2.0 <2.0.0\nfunction replaceCarets (comp, options) {\n  return comp.trim().split(/\\s+/).map(function (comp) {\n    return replaceCaret(comp, options)\n  }).join(' ')\n}\n\nfunction replaceCaret (comp, options) {\n  debug('caret', comp, options)\n  var r = options.loose ? safeRe[CARETLOOSE] : safeRe[CARET]\n  return comp.replace(r, function (_, M, m, p, pr) {\n    debug('caret', comp, _, M, m, p, pr)\n    var ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n      } else {\n        ret = '>=' + M + '.' + m + '.0 <' + (+M + 1) + '.0.0'\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p + '-' + pr +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + m + '.' + (+p + 1)\n        } else {\n          ret = '>=' + M + '.' + m + '.' + p +\n                ' <' + M + '.' + (+m + 1) + '.0'\n        }\n      } else {\n        ret = '>=' + M + '.' + m + '.' + p +\n              ' <' + (+M + 1) + '.0.0'\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nfunction replaceXRanges (comp, options) {\n  debug('replaceXRanges', comp, options)\n  return comp.split(/\\s+/).map(function (comp) {\n    return replaceXRange(comp, options)\n  }).join(' ')\n}\n\nfunction replaceXRange (comp, options) {\n  comp = comp.trim()\n  var r = options.loose ? safeRe[XRANGELOOSE] : safeRe[XRANGE]\n  return comp.replace(r, function (ret, gtlt, M, m, p, pr) {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    var xM = isX(M)\n    var xm = xM || isX(m)\n    var xp = xm || isX(p)\n    var anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        // >1.2.3 => >= 1.2.4\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      ret = gtlt + M + '.' + m + '.' + p\n    } else if (xm) {\n      ret = '>=' + M + '.0.0 <' + (+M + 1) + '.0.0'\n    } else if (xp) {\n      ret = '>=' + M + '.' + m + '.0 <' + M + '.' + (+m + 1) + '.0'\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nfunction replaceStars (comp, options) {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp.trim().replace(safeRe[STAR], '')\n}\n\n// This function is passed to string.replace(safeRe[HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0\nfunction hyphenReplace ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr, tb) {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = '>=' + fM + '.0.0'\n  } else if (isX(fp)) {\n    from = '>=' + fM + '.' + fm + '.0'\n  } else {\n    from = '>=' + from\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = '<' + (+tM + 1) + '.0.0'\n  } else if (isX(tp)) {\n    to = '<' + tM + '.' + (+tm + 1) + '.0'\n  } else if (tpr) {\n    to = '<=' + tM + '.' + tm + '.' + tp + '-' + tpr\n  } else {\n    to = '<=' + to\n  }\n\n  return (from + ' ' + to).trim()\n}\n\n// if ANY of the sets match ALL of its comparators, then pass\nRange.prototype.test = function (version) {\n  if (!version) {\n    return false\n  }\n\n  if (typeof version === 'string') {\n    version = new SemVer(version, this.options)\n  }\n\n  for (var i = 0; i < this.set.length; i++) {\n    if (testSet(this.set[i], version, this.options)) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction testSet (set, version, options) {\n  for (var i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        var allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n\nexports.satisfies = satisfies\nfunction satisfies (version, range, options) {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\n\nexports.maxSatisfying = maxSatisfying\nfunction maxSatisfying (versions, range, options) {\n  var max = null\n  var maxSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\n\nexports.minSatisfying = minSatisfying\nfunction minSatisfying (versions, range, options) {\n  var min = null\n  var minSV = null\n  try {\n    var rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach(function (v) {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\n\nexports.minVersion = minVersion\nfunction minVersion (range, loose) {\n  range = new Range(range, loose)\n\n  var minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    comparators.forEach(function (comparator) {\n      // Clone to avoid manipulating the comparator's semver object.\n      var compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!minver || gt(minver, compver)) {\n            minver = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error('Unexpected operation: ' + comparator.operator)\n      }\n    })\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\n\nexports.validRange = validRange\nfunction validRange (range, options) {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\n\n// Determine if version is less than all the versions possible in the range\nexports.ltr = ltr\nfunction ltr (version, range, options) {\n  return outside(version, range, '<', options)\n}\n\n// Determine if version is greater than all the versions possible in the range.\nexports.gtr = gtr\nfunction gtr (version, range, options) {\n  return outside(version, range, '>', options)\n}\n\nexports.outside = outside\nfunction outside (version, range, hilo, options) {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  var gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisifes the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (var i = 0; i < range.set.length; ++i) {\n    var comparators = range.set[i]\n\n    var high = null\n    var low = null\n\n    comparators.forEach(function (comparator) {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nexports.prerelease = prerelease\nfunction prerelease (version, options) {\n  var parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\n\nexports.intersects = intersects\nfunction intersects (r1, r2, options) {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2)\n}\n\nexports.coerce = coerce\nfunction coerce (version) {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  var match = version.match(safeRe[COERCE])\n\n  if (match == null) {\n    return null\n  }\n\n  return parse(match[1] +\n    '.' + (match[2] || '0') +\n    '.' + (match[3] || '0'))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/async-listener/node_modules/semver/semver.js\n");

/***/ })

};
;
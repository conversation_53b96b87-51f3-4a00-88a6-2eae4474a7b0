/* stylelint-disable scss/at-rule-no-unknown -- for now */
@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Inter';
  font-weight: 700;
  src: url('../../../libs/react-ui/src/assets/fonts/Inter/Inter-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-weight: 500;
  src: url('../../../libs/react-ui/src/assets/fonts/Inter/Inter-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-weight: 400;
  src: url('../../../libs/react-ui/src/assets/fonts/Inter/Inter-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter';
  font-weight: 300;
  src: url('../../../libs/react-ui/src/assets/fonts/Inter/Inter-Light.ttf') format('truetype');
}

@font-face {
  font-family: 'ArticulatCF';
  font-weight: 700;
  src: url('../../../libs/react-ui/src/assets/fonts/ArticulatCF/ArticulatCF-Bold.ttf') format('truetype');
}

/** scrollbar styles */
::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: theme('colors.base-15');
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background-color: theme('colors.base-25');
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: theme('colors.base-35');
}

body {
  font-size: 1.125rem;
}

.editor-table {
  border-spacing: 0;
  width: 100%;
  margin-bottom: 1rem;
  border-collapse: collapse;
  max-width: 100%;
  overflow-y: scroll;
  table-layout: fixed;

  thead,
  tbody {
    .editor-table-row {
      border: 1px solid theme('colors.base-85');
    }

    .editor-table-cell-header {
      background-color: theme('colors.base-10');
    }

    .editor-table-cell,
    .editor-table-header {
      text-align: start;
      border: 1px solid theme('colors.base-85');
      position: relative;
      padding: 8px 6px;
      vertical-align: top;

      .editor-paragraph {
        position: relative;
      }
    }
  }
}

.search-highlight {
  background-color: theme('colors.text-highlight');
  color: inherit;
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-popper";
exports.ids = ["vendor-chunks/react-popper"];
exports.modules = {

/***/ "(ssr)/../../node_modules/react-popper/lib/esm/Manager.js":
/*!**********************************************************!*\
  !*** ../../node_modules/react-popper/lib/esm/Manager.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Manager: () => (/* binding */ Manager),\n/* harmony export */   ManagerReferenceNodeContext: () => (/* binding */ ManagerReferenceNodeContext),\n/* harmony export */   ManagerReferenceNodeSetterContext: () => (/* binding */ ManagerReferenceNodeSetterContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar ManagerReferenceNodeContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext();\nvar ManagerReferenceNodeSetterContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext();\nfunction Manager(_ref) {\n  var children = _ref.children;\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n      referenceNode = _React$useState[0],\n      setReferenceNode = _React$useState[1];\n\n  var hasUnmounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      hasUnmounted.current = true;\n    };\n  }, []);\n  var handleSetReferenceNode = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (node) {\n    if (!hasUnmounted.current) {\n      setReferenceNode(node);\n    }\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(ManagerReferenceNodeContext.Provider, {\n    value: referenceNode\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(ManagerReferenceNodeSetterContext.Provider, {\n    value: handleSetReferenceNode\n  }, children));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LXBvcHBlci9saWIvZXNtL01hbmFnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0I7QUFDeEIsa0NBQWtDLGdEQUFtQjtBQUNyRCx3Q0FBd0MsZ0RBQW1CO0FBQzNEO0FBQ1A7O0FBRUEsd0JBQXdCLDJDQUFjO0FBQ3RDO0FBQ0E7O0FBRUEscUJBQXFCLHlDQUFZO0FBQ2pDLEVBQUUsNENBQWU7QUFDakI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILCtCQUErQiw4Q0FBaUI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHNCQUFzQixnREFBbUI7QUFDekM7QUFDQSxHQUFHLGVBQWUsZ0RBQW1CO0FBQ3JDO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9yZWFjdC1wb3BwZXIvbGliL2VzbS9NYW5hZ2VyLmpzPzM5MmMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IHZhciBNYW5hZ2VyUmVmZXJlbmNlTm9kZUNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KCk7XG5leHBvcnQgdmFyIE1hbmFnZXJSZWZlcmVuY2VOb2RlU2V0dGVyQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoKTtcbmV4cG9ydCBmdW5jdGlvbiBNYW5hZ2VyKF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbjtcblxuICB2YXIgX1JlYWN0JHVzZVN0YXRlID0gUmVhY3QudXNlU3RhdGUobnVsbCksXG4gICAgICByZWZlcmVuY2VOb2RlID0gX1JlYWN0JHVzZVN0YXRlWzBdLFxuICAgICAgc2V0UmVmZXJlbmNlTm9kZSA9IF9SZWFjdCR1c2VTdGF0ZVsxXTtcblxuICB2YXIgaGFzVW5tb3VudGVkID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgaGFzVW5tb3VudGVkLmN1cnJlbnQgPSB0cnVlO1xuICAgIH07XG4gIH0sIFtdKTtcbiAgdmFyIGhhbmRsZVNldFJlZmVyZW5jZU5vZGUgPSBSZWFjdC51c2VDYWxsYmFjayhmdW5jdGlvbiAobm9kZSkge1xuICAgIGlmICghaGFzVW5tb3VudGVkLmN1cnJlbnQpIHtcbiAgICAgIHNldFJlZmVyZW5jZU5vZGUobm9kZSk7XG4gICAgfVxuICB9LCBbXSk7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChNYW5hZ2VyUmVmZXJlbmNlTm9kZUNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZTogcmVmZXJlbmNlTm9kZVxuICB9LCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChNYW5hZ2VyUmVmZXJlbmNlTm9kZVNldHRlckNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZTogaGFuZGxlU2V0UmVmZXJlbmNlTm9kZVxuICB9LCBjaGlsZHJlbikpO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-popper/lib/esm/Manager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-popper/lib/esm/Popper.js":
/*!*********************************************************!*\
  !*** ../../node_modules/react-popper/lib/esm/Popper.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Popper: () => (/* binding */ Popper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Manager__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Manager */ \"(ssr)/../../node_modules/react-popper/lib/esm/Manager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/react-popper/lib/esm/utils.js\");\n/* harmony import */ var _usePopper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./usePopper */ \"(ssr)/../../node_modules/react-popper/lib/esm/usePopper.js\");\n\n\n\n\n\nvar NOOP = function NOOP() {\n  return void 0;\n};\n\nvar NOOP_PROMISE = function NOOP_PROMISE() {\n  return Promise.resolve(null);\n};\n\nvar EMPTY_MODIFIERS = [];\nfunction Popper(_ref) {\n  var _ref$placement = _ref.placement,\n      placement = _ref$placement === void 0 ? 'bottom' : _ref$placement,\n      _ref$strategy = _ref.strategy,\n      strategy = _ref$strategy === void 0 ? 'absolute' : _ref$strategy,\n      _ref$modifiers = _ref.modifiers,\n      modifiers = _ref$modifiers === void 0 ? EMPTY_MODIFIERS : _ref$modifiers,\n      referenceElement = _ref.referenceElement,\n      onFirstUpdate = _ref.onFirstUpdate,\n      innerRef = _ref.innerRef,\n      children = _ref.children;\n  var referenceNode = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_Manager__WEBPACK_IMPORTED_MODULE_1__.ManagerReferenceNodeContext);\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n      popperElement = _React$useState[0],\n      setPopperElement = _React$useState[1];\n\n  var _React$useState2 = react__WEBPACK_IMPORTED_MODULE_0__.useState(null),\n      arrowElement = _React$useState2[0],\n      setArrowElement = _React$useState2[1];\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    (0,_utils__WEBPACK_IMPORTED_MODULE_2__.setRef)(innerRef, popperElement);\n  }, [innerRef, popperElement]);\n  var options = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return {\n      placement: placement,\n      strategy: strategy,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: [].concat(modifiers, [{\n        name: 'arrow',\n        enabled: arrowElement != null,\n        options: {\n          element: arrowElement\n        }\n      }])\n    };\n  }, [placement, strategy, onFirstUpdate, modifiers, arrowElement]);\n\n  var _usePopper = (0,_usePopper__WEBPACK_IMPORTED_MODULE_3__.usePopper)(referenceElement || referenceNode, popperElement, options),\n      state = _usePopper.state,\n      styles = _usePopper.styles,\n      forceUpdate = _usePopper.forceUpdate,\n      update = _usePopper.update;\n\n  var childrenProps = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return {\n      ref: setPopperElement,\n      style: styles.popper,\n      placement: state ? state.placement : placement,\n      hasPopperEscaped: state && state.modifiersData.hide ? state.modifiersData.hide.hasPopperEscaped : null,\n      isReferenceHidden: state && state.modifiersData.hide ? state.modifiersData.hide.isReferenceHidden : null,\n      arrowProps: {\n        style: styles.arrow,\n        ref: setArrowElement\n      },\n      forceUpdate: forceUpdate || NOOP,\n      update: update || NOOP_PROMISE\n    };\n  }, [setPopperElement, setArrowElement, placement, state, styles, update, forceUpdate]);\n  return (0,_utils__WEBPACK_IMPORTED_MODULE_2__.unwrapArray)(children)(childrenProps);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-popper/lib/esm/Popper.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-popper/lib/esm/Reference.js":
/*!************************************************************!*\
  !*** ../../node_modules/react-popper/lib/esm/Reference.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Reference: () => (/* binding */ Reference)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! warning */ \"(ssr)/../../node_modules/warning/warning.js\");\n/* harmony import */ var warning__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(warning__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Manager */ \"(ssr)/../../node_modules/react-popper/lib/esm/Manager.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/react-popper/lib/esm/utils.js\");\n\n\n\n\nfunction Reference(_ref) {\n  var children = _ref.children,\n      innerRef = _ref.innerRef;\n  var setReferenceNode = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_Manager__WEBPACK_IMPORTED_MODULE_2__.ManagerReferenceNodeSetterContext);\n  var refHandler = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (node) {\n    (0,_utils__WEBPACK_IMPORTED_MODULE_3__.setRef)(innerRef, node);\n    (0,_utils__WEBPACK_IMPORTED_MODULE_3__.safeInvoke)(setReferenceNode, node);\n  }, [innerRef, setReferenceNode]); // ran on unmount\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    return function () {\n      return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.setRef)(innerRef, null);\n    };\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n    warning__WEBPACK_IMPORTED_MODULE_1___default()(Boolean(setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n  }, [setReferenceNode]);\n  return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.unwrapArray)(children)({\n    ref: refHandler\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LXBvcHBlci9saWIvZXNtL1JlZmVyZW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQStCO0FBQ0Q7QUFDZ0M7QUFDSjtBQUNuRDtBQUNQO0FBQ0E7QUFDQSx5QkFBeUIsNkNBQWdCLENBQUMsdUVBQWlDO0FBQzNFLG1CQUFtQiw4Q0FBaUI7QUFDcEMsSUFBSSw4Q0FBTTtBQUNWLElBQUksa0RBQVU7QUFDZCxHQUFHLGlDQUFpQztBQUNwQzs7QUFFQSxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsYUFBYSw4Q0FBTTtBQUNuQjtBQUNBLEdBQUc7QUFDSCxFQUFFLDRDQUFlO0FBQ2pCLElBQUksOENBQU87QUFDWCxHQUFHO0FBQ0gsU0FBUyxtREFBVztBQUNwQjtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsid2VicGFjazovLy8uLi8uLi9ub2RlX21vZHVsZXMvcmVhY3QtcG9wcGVyL2xpYi9lc20vUmVmZXJlbmNlLmpzPzcxYTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHdhcm5pbmcgZnJvbSAnd2FybmluZyc7XG5pbXBvcnQgeyBNYW5hZ2VyUmVmZXJlbmNlTm9kZVNldHRlckNvbnRleHQgfSBmcm9tICcuL01hbmFnZXInO1xuaW1wb3J0IHsgc2FmZUludm9rZSwgdW53cmFwQXJyYXksIHNldFJlZiB9IGZyb20gJy4vdXRpbHMnO1xuZXhwb3J0IGZ1bmN0aW9uIFJlZmVyZW5jZShfcmVmKSB7XG4gIHZhciBjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgICBpbm5lclJlZiA9IF9yZWYuaW5uZXJSZWY7XG4gIHZhciBzZXRSZWZlcmVuY2VOb2RlID0gUmVhY3QudXNlQ29udGV4dChNYW5hZ2VyUmVmZXJlbmNlTm9kZVNldHRlckNvbnRleHQpO1xuICB2YXIgcmVmSGFuZGxlciA9IFJlYWN0LnVzZUNhbGxiYWNrKGZ1bmN0aW9uIChub2RlKSB7XG4gICAgc2V0UmVmKGlubmVyUmVmLCBub2RlKTtcbiAgICBzYWZlSW52b2tlKHNldFJlZmVyZW5jZU5vZGUsIG5vZGUpO1xuICB9LCBbaW5uZXJSZWYsIHNldFJlZmVyZW5jZU5vZGVdKTsgLy8gcmFuIG9uIHVubW91bnRcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuXG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBzZXRSZWYoaW5uZXJSZWYsIG51bGwpO1xuICAgIH07XG4gIH0sIFtdKTtcbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICB3YXJuaW5nKEJvb2xlYW4oc2V0UmVmZXJlbmNlTm9kZSksICdgUmVmZXJlbmNlYCBzaG91bGQgbm90IGJlIHVzZWQgb3V0c2lkZSBvZiBhIGBNYW5hZ2VyYCBjb21wb25lbnQuJyk7XG4gIH0sIFtzZXRSZWZlcmVuY2VOb2RlXSk7XG4gIHJldHVybiB1bndyYXBBcnJheShjaGlsZHJlbikoe1xuICAgIHJlZjogcmVmSGFuZGxlclxuICB9KTtcbn0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-popper/lib/esm/Reference.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-popper/lib/esm/usePopper.js":
/*!************************************************************!*\
  !*** ../../node_modules/react-popper/lib/esm/usePopper.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePopper: () => (/* binding */ usePopper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _popperjs_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @popperjs/core */ \"(ssr)/../../node_modules/@popperjs/core/lib/popper.js\");\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-fast-compare */ \"(ssr)/../../node_modules/react-fast-compare/index.js\");\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_fast_compare__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/react-popper/lib/esm/utils.js\");\n\n\n\n\n\nvar EMPTY_MODIFIERS = [];\nvar usePopper = function usePopper(referenceElement, popperElement, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var prevOptions = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  var optionsWithDefaults = {\n    onFirstUpdate: options.onFirstUpdate,\n    placement: options.placement || 'bottom',\n    strategy: options.strategy || 'absolute',\n    modifiers: options.modifiers || EMPTY_MODIFIERS\n  };\n\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    styles: {\n      popper: {\n        position: optionsWithDefaults.strategy,\n        left: '0',\n        top: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      }\n    },\n    attributes: {}\n  }),\n      state = _React$useState[0],\n      setState = _React$useState[1];\n\n  var updateStateModifier = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    return {\n      name: 'updateState',\n      enabled: true,\n      phase: 'write',\n      fn: function fn(_ref) {\n        var state = _ref.state;\n        var elements = Object.keys(state.elements);\n        react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(function () {\n          setState({\n            styles: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.fromEntries)(elements.map(function (element) {\n              return [element, state.styles[element] || {}];\n            })),\n            attributes: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.fromEntries)(elements.map(function (element) {\n              return [element, state.attributes[element]];\n            }))\n          });\n        });\n      },\n      requires: ['computeStyles']\n    };\n  }, []);\n  var popperOptions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n    var newOptions = {\n      onFirstUpdate: optionsWithDefaults.onFirstUpdate,\n      placement: optionsWithDefaults.placement,\n      strategy: optionsWithDefaults.strategy,\n      modifiers: [].concat(optionsWithDefaults.modifiers, [updateStateModifier, {\n        name: 'applyStyles',\n        enabled: false\n      }])\n    };\n\n    if (react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(prevOptions.current, newOptions)) {\n      return prevOptions.current || newOptions;\n    } else {\n      prevOptions.current = newOptions;\n      return newOptions;\n    }\n  }, [optionsWithDefaults.onFirstUpdate, optionsWithDefaults.placement, optionsWithDefaults.strategy, optionsWithDefaults.modifiers, updateStateModifier]);\n  var popperInstanceRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  (0,_utils__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function () {\n    if (popperInstanceRef.current) {\n      popperInstanceRef.current.setOptions(popperOptions);\n    }\n  }, [popperOptions]);\n  (0,_utils__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(function () {\n    if (referenceElement == null || popperElement == null) {\n      return;\n    }\n\n    var createPopper = options.createPopper || _popperjs_core__WEBPACK_IMPORTED_MODULE_4__.createPopper;\n    var popperInstance = createPopper(referenceElement, popperElement, popperOptions);\n    popperInstanceRef.current = popperInstance;\n    return function () {\n      popperInstance.destroy();\n      popperInstanceRef.current = null;\n    };\n  }, [referenceElement, popperElement, options.createPopper]);\n  return {\n    state: popperInstanceRef.current ? popperInstanceRef.current.state : null,\n    styles: state.styles,\n    attributes: state.attributes,\n    update: popperInstanceRef.current ? popperInstanceRef.current.update : null,\n    forceUpdate: popperInstanceRef.current ? popperInstanceRef.current.forceUpdate : null\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-popper/lib/esm/usePopper.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-popper/lib/esm/utils.js":
/*!********************************************************!*\
  !*** ../../node_modules/react-popper/lib/esm/utils.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEntries: () => (/* binding */ fromEntries),\n/* harmony export */   safeInvoke: () => (/* binding */ safeInvoke),\n/* harmony export */   setRef: () => (/* binding */ setRef),\n/* harmony export */   unwrapArray: () => (/* binding */ unwrapArray),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nvar unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nvar safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === 'function') {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nvar setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === 'function') {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n      ref.current = node;\n    }\n};\n/**\n * Simple ponyfill for Object.fromEntries\n */\n\nvar fromEntries = function fromEntries(entries) {\n  return entries.reduce(function (acc, _ref) {\n    var key = _ref[0],\n        value = _ref[1];\n    acc[key] = value;\n    return acc;\n  }, {});\n};\n/**\n * Small wrapper around `useLayoutEffect` to get rid of the warning on SSR envs\n */\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && window.document && window.document.createElement ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-popper/lib/esm/utils.js\n");

/***/ })

};
;
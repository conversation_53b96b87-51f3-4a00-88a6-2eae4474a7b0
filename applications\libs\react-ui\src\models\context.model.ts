'use client';

import { Dispatch, ReactNode } from 'react';

export type StateAction<T, K, U = void> = U extends void
  ? {
      type: T;
      reducer?: StateReducer<K>;
    }
  : {
      type: T;
      payload: U;
      reducer?: StateReducer<K, U>;
    };

export type StateReducer<T, U = void> = U extends void ? (state: T) => T : (state: T, payload: U) => T;

export interface StateContext<T, U> {
  state: T;
  dispatch: Dispatch<U>;
}

export type ContextProvider = {
  children: ReactNode;
};

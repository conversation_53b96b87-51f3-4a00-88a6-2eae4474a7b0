/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[13].use[2]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[13].use[3]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[14].oneOf[13].use[4]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[14].oneOf[13].use[5]!./app/globals.scss ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* stylelint-disable scss/at-rule-no-unknown -- for now */
/* ! tailwindcss v3.4.3 | MIT License | https://tailwindcss.com */
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: currentColor; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}
[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}
[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}
input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}
input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}
::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}
::-webkit-date-and-time-value {
  min-height: 1.5em;
}
::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}
[multiple],[size]:where(select:not([size="1"])) {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}
[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}
[type='checkbox'] {
  border-radius: 0px;
}
[type='radio'] {
  border-radius: 100%;
}
[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}
[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}
[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}
[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}
[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}
[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}
:root {
    --ref_color_orange_100_rgb: #ff9900;
    --ref_color_orange_75_rgb: #ffad33;
    --ref_color_orange_50_rgb: #ffc266;
    --ref_color_orange_25_rgb: #ffe0b3;
    --ref_color_darkblue_100_rgb: #000033;
    --ref_color_darkblue_75_rgb: #33335c;
    --ref_color_darkblue_50_rgb: #666685;
    --ref_color_darkblue_25_rgb: #9999ad;
    --ref_color_darkblue_10_rgb: #e5e5eb;
    --ref_color_green_100_rgb: #33cc66;
    --ref_color_green_75_rgb: #5cd685;
    --ref_color_green_50_rgb: #85e0a3;
    --ref_color_green_25_rgb: #adebc2;
    --ref_color_lightblue_100_rgb: #3399cc;
    --ref_color_lightblue_75_rgb: #5cadd6;
    --ref_color_lightblue_50_rgb: #85c2e0;
    --ref_color_lightblue_25_rgb: #add6eb;
    --ref_color_lightgrey_rgb: #dbdbdb;
    --ref_color_darkorange_100_rgb: #df8500;
    --ref_font_articulat_cf: "ArticulatCF";
    --ref_font_inter: "Inter";
    --sys_color_base_100: #000000;
    --sys_color_base_85: #4a5568;
    --sys_color_base_75: #676767;
    --sys_color_base_35: #848484;
    --sys_color_base_25: #bebebe;
    --sys_color_base_15: #e4e4e4;
    --sys_color_base_10: #ebebeb;
    --sys_color_base_05: #f2f2f2;
    --sys_color_base_00: #ffffff;
    --sys_color_primary: var(--ref_color_orange_100_rgb);
    --sys_color_primary_content: var(--ref_color_darkblue_100_rgb);
    --sys_color_primary_hover: var(--ref_color_darkorange_100_rgb);
    --sys_color_primary_hover_content: var(--ref_color_darkblue_100_rgb);
    --sys_color_secondary: var(--ref_color_lightgrey_rgb);
    --sys_color_secondary_content: var(--ref_color_darkblue_100_rgb);
    --sys_color_secondary_hover: #bebebe;
    --sys_color_secondary_hover_content: var(--ref_color_darkblue_100_rgb);
    --sys_color_tertiary: #f4f5f6;
    --sys_color_tertiary_content: #ffffff;
    --sys_color_tertiary_hover: var(--ref_color_darkorange_100_rgb);
    --sys_color_tertiary_hover_content: var(--ref_color_darkblue_100_rgb);
    --sys_color_heading_text: #000033;
    --sys_color_body_text: #000000;
    --sys_color_anchor_text: #216aa1;
    --sys_color_content_lite: var(--ref_color_darkblue_50_rgb);
    --sys_color_content_extra_lite: var(--ref_color_darkblue_25_rgb);
    --sys_color_accent: var(--ref_color_lightblue_100_rgb);
    --sys_color_accent_medium: var(--ref_color_lightblue_75_rgb);
    --sys_color_accent_light: var(--ref_color_lightblue_50_rgb);
    --sys_color_accent_lighter: var(--ref_color_lightblue_25_rgb);
    --sys_color_light_grey: var(--ref_color_lightgrey_rgb);
    --sys_color_error: #de0a0a;
    --sys_color_navigation_background: #ffffff;
    --sys_color_navigation_text: #00365f;
    --sys_color_superlink_background: #dddddd;
    --sys_color_superlink_text: #00365f;
    --sys_font_family_heading: var(--ref_font_articulat_cf);
    --sys_font_family_text: var(--ref_font_inter);
    --sys_font_size_h1: 2rem;
    --sys_font_size_h2: 1.75rem;
    --sys_font_size_h3: 1.5rem;
    --sys_font_size_h4: 1.375rem;
    --sys_font_size_h5: 1.25rem;
    --sys_font_size_h6: 1rem;
    --sys_font_size_base: 1rem;
    --sys_border_radius_input: 10px;
    --sys_border_radius_medium: 10px;
    --sys_height_input: 50px;
    --sys_color_file_structure_expanded: #f4f5f6;
    --sys_color_button_primary-border: transparent;
    --sys_color_button_secondary-border: transparent;
    --sys_color_button_tertiary-border: transparent;
    --sys_color_highlight: var(--ref_color_darkblue_100_rgb);
    --sys_color_progress_frame: var(--ref_color_green_50_rgb);
    /** These are @iprox/iprox-ui only colors. */
    --sys_color_secondary_medium: var(--ref_color_darkblue_75_rgb);
    --sys_color_secondary_extra_light: var(--ref_color_darkblue_10_rgb);
    --sys_color_published: #00856a;
    --sys_color_not_published: #de0a0a;
    --sys_color_unused: #ffad33;
  }
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.-top-3 {
  top: -0.75rem;
}
.bottom-0 {
  bottom: 0px;
}
.left-0 {
  left: 0px;
}
.left-\[-20px\] {
  left: -20px;
}
.left-full {
  left: 100%;
}
.right-0 {
  right: 0px;
}
.right-0\.5 {
  right: 0.125rem;
}
.right-4 {
  right: 1rem;
}
.top-0 {
  top: 0px;
}
.top-1\/2 {
  top: 50%;
}
.top-16 {
  top: 4rem;
}
.top-6 {
  top: 1.5rem;
}
.top-\[110px\] {
  top: 110px;
}
.top-\[32px\] {
  top: 32px;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-3 {
  grid-column: span 3 / span 3;
}
.col-span-6 {
  grid-column: span 6 / span 6;
}
.col-span-8 {
  grid-column: span 8 / span 8;
}
.-m-2 {
  margin: -0.5rem;
}
.-m-2\.5 {
  margin: -0.625rem;
}
.m-0 {
  margin: 0px;
}
.m-1 {
  margin: 0.25rem;
}
.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-11 {
  margin-bottom: 2.75rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-14 {
  margin-bottom: 3.5rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-2\.5 {
  margin-bottom: 0.625rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-3\.5 {
  margin-bottom: 0.875rem;
}
.mb-32 {
  margin-bottom: 8rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-9 {
  margin-bottom: 2.25rem;
}
.mb-96 {
  margin-bottom: 24rem;
}
.mb-\[72px\] {
  margin-bottom: 72px;
}
.ml-0 {
  margin-left: 0px;
}
.ml-0\.5 {
  margin-left: 0.125rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-1\.5 {
  margin-left: 0.375rem;
}
.ml-12 {
  margin-left: 3rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-10 {
  margin-right: 2.5rem;
}
.mr-16 {
  margin-right: 4rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-7 {
  margin-right: 1.75rem;
}
.mt-0 {
  margin-top: 0px;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-14 {
  margin-top: 3.5rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-7 {
  margin-top: 1.75rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-9 {
  margin-top: 2.25rem;
}
.mt-\[56px\] {
  margin-top: 56px;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.\!inline {
  display: inline !important;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.\!h-11 {
  height: 2.75rem !important;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-60 {
  height: 15rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[200px\] {
  height: 200px;
}
.h-\[25px\] {
  height: 25px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[320px\] {
  height: 320px;
}
.h-\[3px\] {
  height: 3px;
}
.h-\[454px\] {
  height: 454px;
}
.h-\[50px\] {
  height: 50px;
}
.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}
.h-full {
  height: 100%;
}
.h-input {
  height: var(--sys_height_input);
}
.h-min {
  height: -moz-min-content;
  height: min-content;
}
.h-screen {
  height: 100vh;
}
.max-h-32 {
  max-height: 8rem;
}
.max-h-\[60vh\] {
  max-height: 60vh;
}
.max-h-fit {
  max-height: -moz-fit-content;
  max-height: fit-content;
}
.min-h-40 {
  min-height: 10rem;
}
.min-h-\[50px\] {
  min-height: 50px;
}
.min-h-full {
  min-height: 100%;
}
.min-h-screen {
  min-height: 100vh;
}
.\!w-10 {
  width: 2.5rem !important;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-2\/5 {
  width: 40%;
}
.w-20 {
  width: 5rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\/5 {
  width: 60%;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-5\/6 {
  width: 83.333333%;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-60 {
  width: 15rem;
}
.w-64 {
  width: 16rem;
}
.w-8 {
  width: 2rem;
}
.w-9 {
  width: 2.25rem;
}
.w-96 {
  width: 24rem;
}
.w-\[115px\] {
  width: 115px;
}
.w-\[200px\] {
  width: 200px;
}
.w-\[220px\] {
  width: 220px;
}
.w-\[25px\] {
  width: 25px;
}
.w-\[70vw\] {
  width: 70vw;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-min {
  width: -moz-min-content;
  width: min-content;
}
.w-px {
  width: 1px;
}
.w-screen {
  width: 100vw;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[150px\] {
  min-width: 150px;
}
.min-w-\[20px\] {
  min-width: 20px;
}
.min-w-\[220px\] {
  min-width: 220px;
}
.min-w-\[250px\] {
  min-width: 250px;
}
.min-w-full {
  min-width: 100%;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-\[128px\] {
  max-width: 128px;
}
.max-w-\[475px\] {
  max-width: 475px;
}
.max-w-\[500px\] {
  max-width: 500px;
}
.max-w-\[534px\] {
  max-width: 534px;
}
.max-w-\[55px\] {
  max-width: 55px;
}
.max-w-fit {
  max-width: -moz-fit-content;
  max-width: fit-content;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.shrink-0 {
  flex-shrink: 0;
}
.grow {
  flex-grow: 1;
}
.origin-top-right {
  transform-origin: top right;
}
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0\.5 {
  --tw-translate-y: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 1;
  }
  20% {
    transform: scale(1.3, 1.3);
    opacity: 1;
  }
  100% {
    transform: scale(2, 2);
    opacity: 0;
  }
}
.animate-ripple {
  animation: ripple 0.7s cubic-bezier(0, 0, 0.2, 1) 1;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-pointer {
  cursor: pointer;
}
.resize-none {
  resize: none;
}
.resize {
  resize: both;
}
.grid-flow-col {
  grid-auto-flow: column;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.grid-cols-\[24rem_1fr\] {
  grid-template-columns: 24rem 1fr;
}
.grid-cols-\[auto_auto_1fr\] {
  grid-template-columns: auto auto 1fr;
}
.grid-cols-\[max-content_3fr_minmax\(0\2c _20\%\)\] {
  grid-template-columns: max-content 3fr minmax(0, 20%);
}
.grid-cols-\[min\(85\%\)_1fr\] {
  grid-template-columns: min(85%) 1fr;
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.place-items-center {
  place-items: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-9 {
  gap: 2.25rem;
}
.gap-x-1 {
  -moz-column-gap: 0.25rem;
       column-gap: 0.25rem;
}
.gap-x-1\.5 {
  -moz-column-gap: 0.375rem;
       column-gap: 0.375rem;
}
.gap-x-10 {
  -moz-column-gap: 2.5rem;
       column-gap: 2.5rem;
}
.gap-x-16 {
  -moz-column-gap: 4rem;
       column-gap: 4rem;
}
.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.gap-x-2\.5 {
  -moz-column-gap: 0.625rem;
       column-gap: 0.625rem;
}
.gap-x-3 {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}
.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-x-5 {
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
}
.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-x-7 {
  -moz-column-gap: 1.75rem;
       column-gap: 1.75rem;
}
.gap-x-\[15px\] {
  -moz-column-gap: 15px;
       column-gap: 15px;
}
.gap-y-1 {
  row-gap: 0.25rem;
}
.gap-y-2 {
  row-gap: 0.5rem;
}
.gap-y-4 {
  row-gap: 1rem;
}
.gap-y-5 {
  row-gap: 1.25rem;
}
.gap-y-7 {
  row-gap: 1.75rem;
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.place-self-center {
  place-self: center;
}
.self-start {
  align-self: flex-start;
}
.self-center {
  align-self: center;
}
.self-stretch {
  align-self: stretch;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-y-scroll {
  overflow-y: scroll;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.overflow-ellipsis {
  text-overflow: ellipsis;
}
.hyphens-auto {
  -webkit-hyphens: auto;
          hyphens: auto;
}
.\!whitespace-normal {
  white-space: normal !important;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre {
  white-space: pre;
}
.whitespace-pre-line {
  white-space: pre-line;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[10px\] {
  border-radius: 10px;
}
.rounded-\[1px\] {
  border-radius: 1px;
}
.rounded-\[40px\] {
  border-radius: 40px;
}
.rounded-\[5px\] {
  border-radius: 5px;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-input {
  border-radius: var(--sys_border_radius_input);
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-medium {
  border-radius: var(--sys_border_radius_medium);
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-tl-\[10px\] {
  border-top-left-radius: 10px;
}
.rounded-tr-input {
  border-top-right-radius: var(--sys_border_radius_input);
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.\!border-base-25 {
  border-color: var(--sys_color_base_25) !important;
}
.\!border-content-extra-lite {
  border-color: var(--sys_color_content_extra_lite) !important;
}
.\!border-error {
  border-color: var(--sys_color_error) !important;
}
.\!border-highlight {
  border-color: var(--sys_color_highlight) !important;
}
.\!border-primary {
  border-color: var(--sys_color_primary) !important;
}
.border-base-10 {
  border-color: var(--sys_color_base_10);
}
.border-base-25 {
  border-color: var(--sys_color_base_25);
}
.border-base-35 {
  border-color: var(--sys_color_base_35);
}
.border-base-75 {
  border-color: var(--sys_color_base_75);
}
.border-button-primary-border {
  border-color: var(--sys_color_button_primary_border);
}
.border-button-secondary-border {
  border-color: var(--sys_color_button_secondary_border);
}
.border-content-extra-lite {
  border-color: var(--sys_color_content_extra_lite);
}
.border-content-lite {
  border-color: var(--sys_color_content_lite);
}
.border-error {
  border-color: var(--sys_color_error);
}
.border-highlight {
  border-color: var(--sys_color_highlight);
}
.border-primary {
  border-color: var(--sys_color_primary);
}
.border-primary-content {
  border-color: var(--sys_color_primary_content);
}
.border-progress-frame {
  border-color: var(--sys_color_progress_frame);
}
.border-published {
  border-color: var(--sys_color_published);
}
.border-transparent {
  border-color: transparent;
}
.border-b-highlight {
  border-bottom-color: var(--sys_color_highlight);
}
.\!bg-highlight {
  background-color: var(--sys_color_highlight) !important;
}
.bg-base-00 {
  background-color: var(--sys_color_base_00);
}
.bg-base-10 {
  background-color: var(--sys_color_base_10);
}
.bg-base-100 {
  background-color: var(--sys_color_base_100);
}
.bg-base-15 {
  background-color: var(--sys_color_base_15);
}
.bg-base-25 {
  background-color: var(--sys_color_base_25);
}
.bg-base-35 {
  background-color: var(--sys_color_base_35);
}
.bg-content-extra-lite {
  background-color: var(--sys_color_content_extra_lite);
}
.bg-content-lite {
  background-color: var(--sys_color_content_lite);
}
.bg-error {
  background-color: var(--sys_color_error);
}
.bg-expanded-background {
  background-color: var(--sys_color_file_structure_expanded);
}
.bg-file-tag-background {
  background-color: var(--sys_color_secondary);
}
.bg-highlight {
  background-color: var(--sys_color_highlight);
}
.bg-light-grey {
  background-color: var(--sys_color_light_grey);
}
.bg-primary {
  background-color: var(--sys_color_primary);
}
.bg-primary-content {
  background-color: var(--sys_color_primary_content);
}
.bg-progress-frame {
  background-color: var(--sys_color_progress_frame);
}
.bg-secondary {
  background-color: var(--sys_color_secondary);
}
.bg-secondary-extra-light {
  background-color: var(--sys_color_secondary_extra_light);
}
.bg-tertiary {
  background-color: var(--sys_color_tertiary);
}
.bg-transparent {
  background-color: transparent;
}
.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-center {
  -o-object-position: center;
     object-position: center;
}
.\!p-0 {
  padding: 0px !important;
}
.p-0 {
  padding: 0px;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-32 {
  padding: 8rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-\[2px\] {
  padding: 2px;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-16 {
  padding-left: 4rem;
  padding-right: 4rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-\[10px\] {
  padding-left: 10px;
  padding-right: 10px;
}
.px-\[30px\] {
  padding-left: 30px;
  padding-right: 30px;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}
.py-9 {
  padding-top: 2.25rem;
  padding-bottom: 2.25rem;
}
.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}
.pb-20 {
  padding-bottom: 5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-\[10px\] {
  padding-bottom: 10px;
}
.pb-\[48px\] {
  padding-bottom: 48px;
}
.pb-\[61px\] {
  padding-bottom: 61px;
}
.pb-\[72px\] {
  padding-bottom: 72px;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-6 {
  padding-left: 1.5rem;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-2\.5 {
  padding-right: 0.625rem;
}
.pr-20 {
  padding-right: 5rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-5 {
  padding-right: 1.25rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-8 {
  padding-top: 2rem;
}
.pt-\[40px\] {
  padding-top: 40px;
}
.pt-\[43px\] {
  padding-top: 43px;
}
.pt-\[45px\] {
  padding-top: 45px;
}
.pt-\[50px\] {
  padding-top: 50px;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.align-top {
  vertical-align: top;
}
.align-middle {
  vertical-align: middle;
}
.font-heading {
  font-family: var(--sys_font_family_heading);
}
.font-text {
  font-family: var(--sys_font_family_text);
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[8px\] {
  font-size: 8px;
}
.text-base {
  font-size: var(--sys_font_size_base);
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.leading-3 {
  line-height: .75rem;
}
.leading-4 {
  line-height: 1rem;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-none {
  line-height: 1;
}
.leading-normal {
  line-height: 1.5;
}
.leading-snug {
  line-height: 1.375;
}
.\!text-base-00 {
  color: var(--sys_color_base_00) !important;
}
.text-accent {
  color: var(--sys_color_accent);
}
.text-anchor {
  color: var(--sys_color_anchor_text);
}
.text-base-00 {
  color: var(--sys_color_base_00);
}
.text-base-10 {
  color: var(--sys_color_base_10);
}
.text-base-100 {
  color: var(--sys_color_base_100);
}
.text-base-25 {
  color: var(--sys_color_base_25);
}
.text-base-35 {
  color: var(--sys_color_base_35);
}
.text-base-75 {
  color: var(--sys_color_base_75);
}
.text-base-85 {
  color: var(--sys_color_base_85);
}
.text-body {
  color: var(--sys_color_body_text);
}
.text-content-extra-lite {
  color: var(--sys_color_content_extra_lite);
}
.text-content-lite {
  color: var(--sys_color_content_lite);
}
.text-error {
  color: var(--sys_color_error);
}
.text-heading {
  color: var(--sys_color_heading_text);
}
.text-highlight {
  color: var(--sys_color_highlight);
}
.text-not-published {
  color: var(--sys_color_not_published);
}
.text-primary {
  color: var(--sys_color_primary);
}
.text-primary-content {
  color: var(--sys_color_primary_content);
}
.text-published {
  color: var(--sys_color_published);
}
.text-secondary-content {
  color: var(--sys_color_secondary_content);
}
.text-secondary-medium {
  color: var(--sys_color_secondary_medium);
}
.text-tertiary-content {
  color: var(--sys_color_tertiary_content);
}
.text-unused {
  color: var(--sys_color_unused);
}
.underline {
  text-decoration-line: underline;
}
.underline-offset-2 {
  text-underline-offset: 2px;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-80 {
  opacity: 0.8;
}
.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-base-25 {
  --tw-ring-color: var(--sys_color_base_25);
}
.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-100 {
  transition-duration: 100ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-75 {
  transition-duration: 75ms;
}
.duration-standard {
  transition-duration: 260ms;
}
.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-iprox {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
@font-face {
  font-family: "ArticulatCF";
  font-weight: 700;
  src: url(/_next/static/media/ArticulatCF-Bold.918da6da.ttf) format("truetype");
}
@font-face {
  font-family: "ArticulatCF";
  font-weight: 600;
  src: url(/_next/static/media/ArticulatCF-DemiBold.5abb5b86.ttf) format("truetype");
}
@font-face {
  font-family: "ArticulatCF";
  font-weight: 500;
  src: url(/_next/static/media/ArticulatCF-Medium.616ce39d.ttf) format("truetype");
}
@font-face {
  font-family: "ArticulatCF";
  font-weight: 400;
  src: url(/_next/static/media/ArticulatCF-Normal.7b26c8b2.ttf) format("truetype");
}
@font-face {
  font-family: "Inter";
  font-weight: 700;
  src: url(/_next/static/media/Inter-Bold.1e3e4a31.ttf) format("truetype");
}
@font-face {
  font-family: "Inter";
  font-weight: 500;
  src: url(/_next/static/media/Inter-Medium.04937818.ttf) format("truetype");
}
@font-face {
  font-family: "Inter";
  font-weight: 400;
  src: url(/_next/static/media/Inter-Regular.8c0fe73b.ttf) format("truetype");
}
@font-face {
  font-family: "Inter";
  font-weight: 300;
  src: url(/_next/static/media/Inter-Light.8be0a11c.ttf) format("truetype");
}
/* TODO place refer to reference design tokens. */
@layer theme;
pre {
  padding: 8px;
  background-color: #f3f3f9;
}
.after\:pointer-events-none::after {
  content: var(--tw-content);
  pointer-events: none;
}
.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}
.after\:inset-0::after {
  content: var(--tw-content);
  inset: 0px;
}
.after\:block::after {
  content: var(--tw-content);
  display: block;
}
.after\:flex::after {
  content: var(--tw-content);
  display: flex;
}
.after\:contents::after {
  content: var(--tw-content);
  display: contents;
}
.after\:h-full::after {
  content: var(--tw-content);
  height: 100%;
}
.after\:w-full::after {
  content: var(--tw-content);
  width: 100%;
}
.after\:items-center::after {
  content: var(--tw-content);
  align-items: center;
}
.after\:justify-center::after {
  content: var(--tw-content);
  justify-content: center;
}
.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:rounded-lg::after {
  content: var(--tw-content);
  border-radius: 0.5rem;
}
.after\:bg-base-100::after {
  content: var(--tw-content);
  background-color: var(--sys_color_base_100);
}
.after\:bg-highlight::after {
  content: var(--tw-content);
  background-color: var(--sys_color_highlight);
}
.after\:text-opacity-100::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
}
.after\:opacity-0::after {
  content: var(--tw-content);
  opacity: 0;
}
.after\:opacity-75::after {
  content: var(--tw-content);
  opacity: 0.75;
}
.after\:outline::after {
  content: var(--tw-content);
  outline-style: solid;
}
.after\:outline-3::after {
  content: var(--tw-content);
  outline-width: 3px;
}
.after\:outline-\[3px\]::after {
  content: var(--tw-content);
  outline-width: 3px;
}
.after\:outline-offset-\[-4px\]::after {
  content: var(--tw-content);
  outline-offset: -4px;
}
.after\:outline-base-00::after {
  content: var(--tw-content);
  outline-color: var(--sys_color_base_00);
}
.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:duration-300::after {
  content: var(--tw-content);
  transition-duration: 300ms;
}
.after\:ease-in-out::after {
  content: var(--tw-content);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.last\:mb-0:last-child {
  margin-bottom: 0px;
}
.checked\:bg-highlight:checked {
  background-color: var(--sys_color_highlight);
}
.hover\:rounded-medium:hover {
  border-radius: var(--sys_border_radius_medium);
}
.hover\:\!border-base-75:hover {
  border-color: var(--sys_color_base_75) !important;
}
.hover\:\!border-highlight:hover {
  border-color: var(--sys_color_highlight) !important;
}
.hover\:border-highlight:hover {
  border-color: var(--sys_color_highlight);
}
.hover\:bg-base-10:hover {
  background-color: var(--sys_color_base_10);
}
.hover\:bg-base-15:hover {
  background-color: var(--sys_color_base_15);
}
.hover\:bg-base-25:hover {
  background-color: var(--sys_color_base_25);
}
.hover\:bg-base-35:hover {
  background-color: var(--sys_color_base_35);
}
.hover\:bg-highlight:hover {
  background-color: var(--sys_color_highlight);
}
.hover\:bg-primary-hover:hover {
  background-color: var(--sys_color_primary_hover);
}
.hover\:bg-secondary:hover {
  background-color: var(--sys_color_secondary);
}
.hover\:bg-secondary-hover:hover {
  background-color: var(--sys_color_secondary_hover);
}
.hover\:bg-tertiary-hover:hover {
  background-color: var(--sys_color_tertiary_hover);
}
.hover\:text-base-00:hover {
  color: var(--sys_color_base_00);
}
.hover\:text-primary-hover-content:hover {
  color: var(--sys_color_primary_hover_content);
}
.hover\:text-secondary-hover-content:hover {
  color: var(--sys_color_secondary_hover_content);
}
.hover\:text-tertiary-hover-content:hover {
  color: var(--sys_color_tertiary_hover_content);
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:\!shadow-input-error:hover {
  --tw-shadow: 0 0 0 2px rgba(var(--sys_color_error) / 0.3) !important;
  --tw-shadow-colored: 0 0 0 2px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.hover\:\!shadow-input-hover:hover {
  --tw-shadow: 0 0 0 2px rgba(var(--sys_color_secondary) / 0.3) !important;
  --tw-shadow-colored: 0 0 0 2px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.hover\:after\:opacity-75:hover::after {
  content: var(--tw-content);
  opacity: 0.75;
}
.focus\:rounded-medium:focus {
  border-radius: var(--sys_border_radius_medium);
}
.focus\:border-2:focus {
  border-width: 2px;
}
.focus\:border-y:focus {
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.focus\:\!border-base-75:focus {
  border-color: var(--sys_color_base_75) !important;
}
.focus\:\!border-highlight:focus {
  border-color: var(--sys_color_highlight) !important;
}
.focus\:border-highlight:focus {
  border-color: var(--sys_color_highlight);
}
.focus\:border-secondary:focus {
  border-color: var(--sys_color_secondary);
}
.focus\:\!shadow-none:focus {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:\!outline:focus {
  outline-style: solid !important;
}
.focus\:\!outline-2:focus {
  outline-width: 2px !important;
}
.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-offset-0:focus {
  --tw-ring-offset-width: 0px;
}
.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}
.focus\:first\:border-t-0:first-child:focus {
  border-top-width: 0px;
}
.focus\:last\:border-b-0:last-child:focus {
  border-bottom-width: 0px;
}
.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.disabled\:cursor-progress:disabled {
  cursor: progress;
}
.disabled\:\!border-base-100:disabled {
  border-color: var(--sys_color_base_100) !important;
}
.disabled\:bg-light-grey:disabled {
  background-color: var(--sys_color_light_grey);
}
.disabled\:font-bold:disabled {
  font-weight: 700;
}
.disabled\:text-base-75:disabled {
  color: var(--sys_color_base_75);
}
.group:hover .group-hover\:bg-secondary {
  background-color: var(--sys_color_secondary);
}
.group:hover .group-hover\:text-base-100 {
  color: var(--sys_color_base_100);
}
.group:hover .group-hover\:underline {
  text-decoration-line: underline;
}
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
@media (min-width: 640px) {
  .sm\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
  .sm\:mt-0 {
    margin-top: 0px;
  }
  .sm\:block {
    display: block;
  }
  .sm\:flex {
    display: flex;
  }
  .sm\:w-1\/2 {
    width: 50%;
  }
  .sm\:w-full {
    width: 100%;
  }
  .sm\:max-w-lg {
    max-width: 32rem;
  }
  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .sm\:flex-row {
    flex-direction: row;
  }
  .sm\:items-start {
    align-items: flex-start;
  }
  .sm\:items-center {
    align-items: center;
  }
  .sm\:gap-x-6 {
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }
  .sm\:p-0 {
    padding: 0px;
  }
  .sm\:p-6 {
    padding: 1.5rem;
  }
  .sm\:p-8 {
    padding: 2rem;
  }
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .sm\:text-left {
    text-align: left;
  }
  .sm\:duration-700 {
    transition-duration: 700ms;
  }
}
@media (min-width: 768px) {
  .md\:order-1 {
    order: 1;
  }
  .md\:order-2 {
    order: 2;
  }
  .md\:mb-0 {
    margin-bottom: 0px;
  }
  .md\:flex {
    display: flex;
  }
  .md\:grid {
    display: grid;
  }
  .md\:hidden {
    display: none;
  }
  .md\:w-2\/3 {
    width: 66.666667%;
  }
  .md\:flex-row {
    flex-direction: row;
  }
  .md\:items-center {
    align-items: center;
  }
  .md\:gap-4 {
    gap: 1rem;
  }
  .md\:p-9 {
    padding: 2.25rem;
  }
  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }
}
@media (min-width: 1024px) {
  .lg\:fixed {
    position: fixed;
  }
  .lg\:bottom-0 {
    bottom: 0px;
  }
  .lg\:top-16 {
    top: 4rem;
  }
  .lg\:z-50 {
    z-index: 50;
  }
  .lg\:flex {
    display: flex;
  }
  .lg\:hidden {
    display: none;
  }
  .lg\:w-72 {
    width: 18rem;
  }
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\:flex-row {
    flex-direction: row;
  }
  .lg\:flex-col {
    flex-direction: column;
  }
  .lg\:items-center {
    align-items: center;
  }
  .lg\:justify-center {
    justify-content: center;
  }
  .lg\:gap-x-0 {
    -moz-column-gap: 0px;
         column-gap: 0px;
  }
  .lg\:gap-x-10 {
    -moz-column-gap: 2.5rem;
         column-gap: 2.5rem;
  }
  .lg\:gap-x-4 {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }
  .lg\:gap-x-6 {
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }
  .lg\:p-10 {
    padding: 2.5rem;
  }
  .lg\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
  .lg\:pl-72 {
    padding-left: 18rem;
  }
}
@media (min-width: 1280px) {
  .xl\:w-2\/5 {
    width: 40%;
  }
  .xl\:gap-x-12 {
    -moz-column-gap: 3rem;
         column-gap: 3rem;
  }
}

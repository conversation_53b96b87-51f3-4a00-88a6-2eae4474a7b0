import { BellIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';

import { toggleSidePanel } from '../context/application-shell.actions';
import { useApplicationShellContext } from '../context/application-shell.context';

export function NotificationButton() {
  const { dispatch } = useApplicationShellContext();
  const t = useTranslations('components');

  return (
    <button
      type="button"
      className="bg-base-00 rounded-full p-1.5 text-gray-400 hover:text-gray-500"
      onClick={() => dispatch(toggleSidePanel())}
    >
      <span className="sr-only">{t('notificationCTA.notificationButtonAlt')}</span>
      <BellIcon className="h-6 w-6" aria-hidden="true" />
    </button>
  );
}

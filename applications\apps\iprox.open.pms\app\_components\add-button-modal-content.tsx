import { Button } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import React from 'react';

type AddButtoModalContentProps = {
  onAddFolder: () => void;
  onUploadDocuments: () => void;
};

export function AddButtoModalContent({ onAddFolder, onUploadDocuments }: AddButtoModalContentProps) {
  const t = useTranslations('dossier');

  return (
    <>
      <h3 className="font-heading text-heading mb-5 block text-2xl font-bold">{t('chooseOption')}</h3>
      <div className="mt-4 grid grid-flow-col justify-end gap-2">
        <Button type="button" variant="primary" onClick={onAddFolder}>
          {t('addFolder')}
        </Button>
        <Button type="button" variant="primary" onClick={onUploadDocuments}>
          {t('uploadDocuments')}
        </Button>
      </div>
    </>
  );
}

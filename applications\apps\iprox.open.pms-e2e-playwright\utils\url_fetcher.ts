import axios from 'axios';

import { API_ENDPOINT, basePortalUrl, getUrlsEndpoint } from '../config';

export async function fetchUrls() {
  let URLs: string[] = [];
  try {
    const response = await axios.get(`${API_ENDPOINT}${getUrlsEndpoint}`);
    const data = response.data;
    URLs = data.pageList.map((page: { slug: string }) => basePortalUrl + '/' + page.slug);
    return URLs;
  } catch (error) {
    console.error('Error fetching URLs:', error);
  }
}

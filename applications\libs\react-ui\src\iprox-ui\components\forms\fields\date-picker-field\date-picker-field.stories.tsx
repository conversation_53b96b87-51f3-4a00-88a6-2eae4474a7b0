import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { DatePickerField } from './date-picker-field';

const meta: Meta<typeof DatePickerField> = {
  title: 'iprox-ui/forms/fields/datefield',
  component: DatePickerField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof DatePickerField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'date-field',
    fieldType: FieldType.Date,
  },
};

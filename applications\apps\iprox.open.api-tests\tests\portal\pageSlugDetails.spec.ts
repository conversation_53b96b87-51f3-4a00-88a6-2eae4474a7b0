import { faker } from '@faker-js/faker';

import * as http from '../../enums/httpEnums';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';
import { PortalHelpers } from '../../helpers/portalHelpers';

describe('Public page slug details test', () => {
  let bearerToken: string;
  let pageSlug: string;
  let createPageResponse: any;
  let pageId: any;
  let addCategoryResponse: any;
  let publishPageResponse: any;

  beforeAll(async () => {
    pageSlug = faker.lorem.word();
    bearerToken = await loginAndGetToken();

    // Create a new page
    createPageResponse = await DossierHelpersNew.createSearchPage(pageSlug, bearerToken);
    pageId = await createPageResponse.body.page.id;

    // Update the page - add a category
    addCategoryResponse = await DossierHelpersNew.updateSearchPage(pageId, bearerToken, pageSlug);

    // Publish the page
    publishPageResponse = await DossierHelpersNew.publishSearchPage(pageId, bearerToken);
  });

  it('PORTAL - should get the published page slug details ', async () => {
    const responsePageSlug: any = await PortalHelpers.getPageSlugDetails(pageSlug);

    const responseLabel = await responsePageSlug.body.page.label;
    const responsePageState = await responsePageSlug.body.page.pageState;

    expect(responsePageSlug.status).toBe(http.StatusCode.OK_200);
    expect(responseLabel).toBe(`${pageSlug}`);
    expect(responsePageState).toBe('Published');
  });

  afterAll(async () => {
    // clean up - delete the page
    await DossierHelpersNew.deleteSearchPage(pageId, bearerToken);
  });
});

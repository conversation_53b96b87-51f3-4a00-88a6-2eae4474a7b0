import Link from 'next/link';
import { ReactNode } from 'react';

import styles from './application-shell.module.scss';
import { MenuItem } from './models/application-shell.models';
import { ExpandableSidebar } from './parts/expandable-sidebar';
import { HamburgerButton } from './parts/hamburger-button';
import { NavigationMenu } from './parts/navigation-menu';
import { SidePanel } from './parts/side-panel';
import { StaticSidebar } from './parts/static-sidebar';

interface ApplicationShellProps {
  children: ReactNode;
  quickbar?: ReactNode;
  sidePanel?: ReactNode;
  logo?: ReactNode;
  menuItems: MenuItem[];
  bottomMenuItems?: MenuItem[];
  loggedIn?: boolean;
}

export function ApplicationShell(props: ApplicationShellProps) {
  return (
    <div className={`${styles['ipx-application-shell']} h-full`}>
      <ExpandableSidebar logo={props.logo}>
        <NavigationMenu topItems={props.menuItems} bottomItems={props.bottomMenuItems} />
      </ExpandableSidebar>

      <StaticSidebar>
        <NavigationMenu topItems={props.menuItems} bottomItems={props.bottomMenuItems} />
      </StaticSidebar>

      <SidePanel>{props.sidePanel}</SidePanel>

      <div className="flex h-full flex-col">
        <div className="bg-base-10 fixed top-0 z-40 flex h-16 w-full shrink-0 items-center gap-x-4 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-5">
          <div className="flex h-16 shrink-0 items-center">
            <HamburgerButton />
            <Link href="/">{props.logo ? props.logo : null}</Link>
          </div>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            <div className="ml-auto flex items-center gap-x-4 lg:gap-x-0">
              <div className="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true" />
              {props.quickbar && props.quickbar}
            </div>
          </div>
        </div>

        <div className="mt-16 h-full lg:pl-72">
          <div className={`h-full ${styles['content-wrapper']}`}>{props.children}</div>
        </div>
      </div>
    </div>
  );
}

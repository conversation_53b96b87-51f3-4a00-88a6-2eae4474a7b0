'use client';

import { components } from '@/iprox-open.interface';
import { usePrevious } from '@/utils/previous.hook';
import { FieldType, Pagination, SelectField, Text, TextField } from '@iprox/iprox-ui';
import { Form, Formik, useFormikContext } from 'formik';
import { useTranslations } from 'next-intl';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo } from 'react';
import { useDebounce } from 'usehooks-ts';

import NewDossierCta from '@/components/new-dossier-cta';

interface DossierSearchWrapperProps {
  data: {
    dossierCategories: components['schemas']['DossierCategoryDto'][];
    count: number;
    start: number;
    totalCount: number;
  };
  children?: React.ReactNode;
}

export interface DossierSearchForm {
  query: string;
  type?: string;
  status?: string;
}

const SearchFormObserver: React.FC = () => {
  const { values } = useFormikContext<DossierSearchForm>();
  const debouncedValues = useDebounce(values, 250);
  const previousValues = usePrevious<DossierSearchForm>(debouncedValues);

  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const queryParams = new URLSearchParams({
      ...Object.fromEntries(searchParams),
      ...debouncedValues,
      ...{
        sortField: searchParams.get('sortField') || 'modified.dateTime',
        sortDirection: searchParams.get('sortDirection') || 'desc',
      },
    });

    if (
      previousValues === undefined ||
      previousValues.query !== debouncedValues.query ||
      previousValues.status !== debouncedValues.status ||
      previousValues.type !== debouncedValues.type
    ) {
      queryParams.delete('count');
      queryParams.delete('start');
    }

    router.push(`${pathname}?${queryParams.toString()}`);
  }, [debouncedValues, previousValues, router, pathname, searchParams]);

  return null;
};

export function DossierSearchWrapper({ data, children }: DossierSearchWrapperProps) {
  const { dossierCategories, count, start, totalCount } = data;

  const t = useTranslations('dossiersList');
  const searchParams = useSearchParams();

  const initialState = useMemo(() => {
    return {
      query: searchParams.get('query') || '',
      type: searchParams.get('type') || '',
      status: searchParams.get('status') || '',
    };
  }, [searchParams]);

  const typeFilterOptions = useMemo(() => {
    const allOption = { label: t('type.all'), value: '' };
    const options = dossierCategories
      .map((item) => ({
        label: item.label,
        value: item.id,
      }))
      .sort((a, b) => a.label.localeCompare(b.label)); // Sort by label name
    return [allOption, ...options];
  }, [dossierCategories, t]);

  const handleSubmit = () => {
    // do nothing
  };

  return (
    <div className="flex h-full flex-col">
      <Text className="font-heading text-heading mb-6 text-4xl font-bold">{t('title')}</Text>
      <Formik initialValues={initialState} onSubmit={handleSubmit}>
        <Form>
          <SearchFormObserver />
          <div className="flex w-full items-end gap-x-4">
            <div className="flex-1">
              <TextField name="query" label={t('search')} fieldType={FieldType.Text} validationRules={[]} />
            </div>
            <NewDossierCta />
          </div>
          <div className="mt-4 grid grid-cols-4 flex-row items-center gap-x-4">
            <div className="col-span-2">
              <SelectField
                name="type"
                label={t('type.title')}
                fieldType={FieldType.Select}
                validationRules={[]}
                options={typeFilterOptions}
              />
            </div>
            <div className="col-span-2">
              <SelectField
                name="status"
                label={t('status.title')}
                fieldType={FieldType.Select}
                validationRules={[]}
                options={[
                  { label: t('status.all'), value: '' },
                  { label: t('published'), value: 'published' },
                  { label: t('unpublished'), value: 'unpublished' },
                ]}
              />
            </div>
          </div>
        </Form>
      </Formik>
      {totalCount === 0 ? (
        <Text className="font-text text-body mt-8 text-center text-sm">{t('noResults')}</Text>
      ) : (
        <>
          {children}
          <div className="mt-10 flex flex-row justify-center">
            <Pagination count={count} start={start} totalCount={totalCount} maxVisiblePages={5} />
          </div>
        </>
      )}
    </div>
  );
}

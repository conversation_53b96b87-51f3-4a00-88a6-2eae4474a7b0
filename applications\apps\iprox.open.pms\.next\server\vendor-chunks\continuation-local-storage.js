"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/continuation-local-storage";
exports.ids = ["vendor-chunks/continuation-local-storage"];
exports.modules = {

/***/ "(instrument)/../../node_modules/continuation-local-storage/context.js":
/*!****************************************************************!*\
  !*** ../../node_modules/continuation-local-storage/context.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar assert      = __webpack_require__(/*! assert */ \"assert\");\nvar wrapEmitter = __webpack_require__(/*! emitter-listener */ \"(instrument)/../../node_modules/emitter-listener/listener.js\");\n\n/*\n *\n * CONSTANTS\n *\n */\nvar CONTEXTS_SYMBOL = 'cls@contexts';\nvar ERROR_SYMBOL = 'error@context';\n\n// load polyfill if native support is unavailable\nif (!process.addAsyncListener) __webpack_require__(/*! async-listener */ \"(instrument)/../../node_modules/async-listener/index.js\");\n\nfunction Namespace(name) {\n  this.name   = name;\n  // changed in 2.7: no default context\n  this.active = null;\n  this._set   = [];\n  this.id     = null;\n}\n\nNamespace.prototype.set = function (key, value) {\n  if (!this.active) {\n    throw new Error(\"No context available. ns.run() or ns.bind() must be called first.\");\n  }\n\n  this.active[key] = value;\n  return value;\n};\n\nNamespace.prototype.get = function (key) {\n  if (!this.active) return undefined;\n\n  return this.active[key];\n};\n\nNamespace.prototype.createContext = function () {\n  return Object.create(this.active);\n};\n\nNamespace.prototype.run = function (fn) {\n  var context = this.createContext();\n  this.enter(context);\n  try {\n    fn(context);\n    return context;\n  }\n  catch (exception) {\n    if (exception) {\n      exception[ERROR_SYMBOL] = context;\n    }\n    throw exception;\n  }\n  finally {\n    this.exit(context);\n  }\n};\n\nNamespace.prototype.runAndReturn = function (fn) {\n  var value;\n  this.run(function (context) {\n    value = fn(context);\n  });\n  return value;\n};\n\nNamespace.prototype.bind = function (fn, context) {\n  if (!context) {\n    if (!this.active) {\n      context = this.createContext();\n    }\n    else {\n      context = this.active;\n    }\n  }\n\n  var self = this;\n  return function () {\n    self.enter(context);\n    try {\n      return fn.apply(this, arguments);\n    }\n    catch (exception) {\n      if (exception) {\n        exception[ERROR_SYMBOL] = context;\n      }\n      throw exception;\n    }\n    finally {\n      self.exit(context);\n    }\n  };\n};\n\nNamespace.prototype.enter = function (context) {\n  assert.ok(context, \"context must be provided for entering\");\n\n  this._set.push(this.active);\n  this.active = context;\n};\n\nNamespace.prototype.exit = function (context) {\n  assert.ok(context, \"context must be provided for exiting\");\n\n  // Fast path for most exits that are at the top of the stack\n  if (this.active === context) {\n    assert.ok(this._set.length, \"can't remove top context\");\n    this.active = this._set.pop();\n    return;\n  }\n\n  // Fast search in the stack using lastIndexOf\n  var index = this._set.lastIndexOf(context);\n\n  assert.ok(index >= 0, \"context not currently entered; can't exit\");\n  assert.ok(index,      \"can't remove top context\");\n\n  this._set.splice(index, 1);\n};\n\nNamespace.prototype.bindEmitter = function (emitter) {\n  assert.ok(emitter.on && emitter.addListener && emitter.emit, \"can only bind real EEs\");\n\n  var namespace  = this;\n  var thisSymbol = 'context@' + this.name;\n\n  // Capture the context active at the time the emitter is bound.\n  function attach(listener) {\n    if (!listener) return;\n    if (!listener[CONTEXTS_SYMBOL]) listener[CONTEXTS_SYMBOL] = Object.create(null);\n\n    listener[CONTEXTS_SYMBOL][thisSymbol] = {\n      namespace : namespace,\n      context   : namespace.active\n    };\n  }\n\n  // At emit time, bind the listener within the correct context.\n  function bind(unwrapped) {\n    if (!(unwrapped && unwrapped[CONTEXTS_SYMBOL])) return unwrapped;\n\n    var wrapped  = unwrapped;\n    var contexts = unwrapped[CONTEXTS_SYMBOL];\n    Object.keys(contexts).forEach(function (name) {\n      var thunk = contexts[name];\n      wrapped = thunk.namespace.bind(wrapped, thunk.context);\n    });\n    return wrapped;\n  }\n\n  wrapEmitter(emitter, attach, bind);\n};\n\n/**\n * If an error comes out of a namespace, it will have a context attached to it.\n * This function knows how to find it.\n *\n * @param {Error} exception Possibly annotated error.\n */\nNamespace.prototype.fromException = function (exception) {\n  return exception[ERROR_SYMBOL];\n};\n\nfunction get(name) {\n  return process.namespaces[name];\n}\n\nfunction create(name) {\n  assert.ok(name, \"namespace must be given a name!\");\n\n  var namespace = new Namespace(name);\n  namespace.id = process.addAsyncListener({\n    create : function () { return namespace.active; },\n    before : function (context, storage) { if (storage) namespace.enter(storage); },\n    after  : function (context, storage) { if (storage) namespace.exit(storage); },\n    error  : function (storage) { if (storage) namespace.exit(storage); }\n  });\n\n  process.namespaces[name] = namespace;\n  return namespace;\n}\n\nfunction destroy(name) {\n  var namespace = get(name);\n\n  assert.ok(namespace,    \"can't delete nonexistent namespace!\");\n  assert.ok(namespace.id, \"don't assign to process.namespaces directly!\");\n\n  process.removeAsyncListener(namespace.id);\n  process.namespaces[name] = null;\n}\n\nfunction reset() {\n  // must unregister async listeners\n  if (process.namespaces) {\n    Object.keys(process.namespaces).forEach(function (name) {\n      destroy(name);\n    });\n  }\n  process.namespaces = Object.create(null);\n}\nif (!process.namespaces) reset(); // call immediately to set up\n\nmodule.exports = {\n  getNamespace     : get,\n  createNamespace  : create,\n  destroyNamespace : destroy,\n  reset            : reset\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/continuation-local-storage/context.js\n");

/***/ })

};
;
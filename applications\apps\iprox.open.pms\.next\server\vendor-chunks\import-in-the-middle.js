/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/import-in-the-middle";
exports.ids = ["vendor-chunks/import-in-the-middle"];
exports.modules = {

/***/ "(instrument)/../../node_modules/import-in-the-middle/index.js":
/*!********************************************************!*\
  !*** ../../node_modules/import-in-the-middle/index.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst parse = __webpack_require__(/*! module-details-from-path */ \"(instrument)/../../node_modules/module-details-from-path/index.js\")\nconst { fileURLToPath } = __webpack_require__(/*! url */ \"url\")\n\nconst {\n  importHooks,\n  specifiers,\n  toHook\n} = __webpack_require__(/*! ./lib/register */ \"(instrument)/../../node_modules/import-in-the-middle/lib/register.js\")\n\nfunction addHook(hook) {\n  importHooks.push(hook)\n  toHook.forEach(([name, namespace]) => hook(name, namespace))\n}\n\nfunction removeHook(hook) {\n  const index = importHooks.indexOf(hook)\n  if (index > -1) {\n    importHooks.splice(index, 1)\n  }\n}\n\nfunction callHookFn(hookFn, namespace, name, baseDir) {\n  const newDefault = hookFn(namespace, name, baseDir)\n  if (newDefault && newDefault !== namespace) {\n    namespace.default = newDefault\n  }\n}\n\nfunction Hook(modules, options, hookFn) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, hookFn)\n  if (typeof modules === 'function') {\n    hookFn = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    hookFn = options\n    options = null\n  }\n  const internals = options ? options.internals === true : false\n\n  this._iitmHook = (name, namespace) => {\n    const filename = name\n    const isBuiltin = name.startsWith('node:')\n    let baseDir\n\n    if (isBuiltin) {\n      name = name.replace(/^node:/, '')\n    } else {\n      if (name.startsWith('file://')) {\n        try {\n          name = fileURLToPath(name)\n        } catch (e) {}\n      }\n      const details = parse(name)\n      if (details) {\n        name = details.name\n        baseDir = details.basedir\n      }\n    }\n\n    if (modules) {\n      for (const moduleName of modules) {\n        if (moduleName === name) {\n          if (baseDir) {\n            if (internals) {\n              name = name + path.sep + path.relative(baseDir, fileURLToPath(filename))\n            } else {\n              if (!baseDir.endsWith(specifiers.get(filename))) continue\n            }\n          }\n          callHookFn(hookFn, namespace, name, baseDir)\n        }\n      }\n    } else {\n      callHookFn(hookFn, namespace, name, baseDir)\n    }\n  }\n\n  addHook(this._iitmHook)\n}\n\nHook.prototype.unhook = function () {\n  removeHook(this._iitmHook)\n}\n\nmodule.exports = Hook\nmodule.exports.addHook = addHook\nmodule.exports.removeHook = removeHook\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/import-in-the-middle/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/import-in-the-middle/lib/register.js":
/*!***************************************************************!*\
  !*** ../../node_modules/import-in-the-middle/lib/register.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\n\nconst importHooks = [] // TODO should this be a Set?\nconst setters = new WeakMap()\nconst specifiers = new Map()\nconst toHook = []\n\nconst proxyHandler = {\n  set(target, name, value) {\n    return setters.get(target)[name](value)\n  },\n\n  defineProperty(target, property, descriptor) {\n    if ((!('value' in descriptor))) {\n      throw new Error('Getters/setters are not supported for exports property descriptors.')\n    }\n\n    return setters.get(target)[property](descriptor.value)\n  }\n}\n\nfunction register(name, namespace, set, specifier) {\n  specifiers.set(name, specifier)\n  setters.set(namespace, set)\n  const proxy = new Proxy(namespace, proxyHandler)\n  importHooks.forEach(hook => hook(name, proxy))\n  toHook.push([name, proxy])\n}\n\nexports.register = register\nexports.importHooks = importHooks\nexports.specifiers = specifiers\nexports.toHook = toHook\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/import-in-the-middle/lib/register.js\n");

/***/ })

};
;
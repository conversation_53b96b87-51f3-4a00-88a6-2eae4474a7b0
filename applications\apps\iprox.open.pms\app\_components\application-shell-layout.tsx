import { usePermissionAuthorizer } from '@/auth/permission-authorizer.hook';
import { Cog8ToothIcon, Square3Stack3DIcon, Squares2X2Icon } from '@heroicons/react/24/outline';
import { ApplicationShell, MenuItem } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';
import { use } from 'react';

import { IproxOpenLogo } from './logo';
import { NotificationPanel } from './notification-panel';
import { Quickbar } from './quick-bar';

export function ApplicationShellLayout({ children }: { children: React.ReactNode }) {
  const t = useTranslations('navigation');

  const permissions = use(usePermissionAuthorizer(['admin:access']));

  const managementSubMenu: MenuItem[] = [
    {
      name: t('session'),
      route: '/session',
      activeRouteMatch: {
        match: '/session',
        mode: 'exact',
      },
    },
    {
      name: t('sessionClient'),
      route: '/session-client',
      activeRouteMatch: {
        match: '/session-client',
        mode: 'exact',
      },
    },
    {
      name: t('siteConfiguration'),
      route: '/site-configuration',
      activeRouteMatch: {
        match: '/site-configuration',
      },
    },
    {
      name: t('homepage'),
      route: '/homepage',
      activeRouteMatch: {
        match: '/homepage',
      },
    },
    {
      name: t('footer'),
      route: '/footer',
      activeRouteMatch: {
        match: '/footer',
      },
    },
    {
      name: t('fonts'),
      route: '/fonts',
      activeRouteMatch: {
        match: '/fonts',
      },
    },
    {
      name: t('pages'),
      route: '/page/list',
      activeRouteMatch: {
        match: '/page',
      },
    },
    {
      name: t('siteNavigation'),
      route: '/site-navigation',
      activeRouteMatch: {
        match: '/site-navigation',
      },
    },
  ];

  const navigation: MenuItem[] = [
    {
      name: t('dashboard'),
      route: '/dashboard',
      activeRouteMatch: {
        match: '/dashboard',
        mode: 'exact',
      },
      icon: <Squares2X2Icon />,
    },
    {
      name: t('dossiers'),
      route: '/dossier/list?sortField=modified.dateTime&sortDirection=desc',
      activeRouteMatch: {
        match: '/dossier',
      },
      icon: <Square3Stack3DIcon />,
    },
  ];

  if (permissions['admin:access']) {
    navigation.push({
      name: t('management'),
      route: '#', // TODO Remove this route: '#'. If the anchor doesn't have a page it should not be an anchor but a <span>
      activeRouteMatch: {
        match: '/session',
        mode: 'exact',
      },
      icon: <Cog8ToothIcon />,
      subMenu: managementSubMenu.filter((item: MenuItem) => {
        if (item.route === '/session' || item.route === '/session-client') {
          return process.env.DEVELOPMENT === 'true';
        }

        return true;
      }),
    });
  }

  return (
    <>
      <header>{/* <NavBar /> */}</header>
      <main className="flex-1">
        <ApplicationShell
          menuItems={navigation}
          quickbar={<Quickbar />}
          sidePanel={<NotificationPanel />}
          logo={<IproxOpenLogo />}
        >
          {children}
        </ApplicationShell>
      </main>
      <footer></footer>
    </>
  );
}

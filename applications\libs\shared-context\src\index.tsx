'use client';

import { ReactNode, createContext, useContext } from 'react';

export interface IproxOpenClientSettings {
  apiUrl: string;
  portalUrl: string;
}

export type AppSettingsProviderProps = {
  children: ReactNode;
  settings: IproxOpenClientSettings;
};

export const AppSettingsContext = createContext<IproxOpenClientSettings>({
  apiUrl: '',
  portalUrl: '',
});

export function AppSettingsProvider({ children, settings }: AppSettingsProviderProps) {
  return <AppSettingsContext.Provider value={settings}>{children}</AppSettingsContext.Provider>;
}

/**
 * @returns IproxOpenClientSettings
 */
export function useAppSettings() {
  const context = useContext(AppSettingsContext);

  if (context === undefined) {
    throw new Error('useAppSettings must be used within a AppSettingsProvider');
  }

  return context;
}

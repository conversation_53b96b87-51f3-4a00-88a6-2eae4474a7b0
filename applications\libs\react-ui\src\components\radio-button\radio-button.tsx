import cx from 'classnames';

interface RadioButtonProps {
  label: string;
  handleSelect: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isSelected: boolean;
  value?: string;
}

export function RadioButton({ label, value, isSelected, handleSelect }: RadioButtonProps) {
  return (
    <label className="flex cursor-pointer items-center">
      <div className="relative h-5 w-5">
        <input
          type="radio"
          value={value}
          checked={isSelected}
          onChange={handleSelect}
          className={cx(
            'relative flex h-5 w-5 cursor-pointer items-center justify-center align-top',
            isSelected
              ? "text-body after:outline-base-00 after:bg-base-100 after:contents[''] after:outline-3 after:absolute after:inset-0 after:block after:rounded-full after:outline after:outline-[3px] after:outline-offset-[-4px]"
              : 'text-base-25'
          )}
        />
      </div>
      <span className="text-body ml-2 text-base">{label}</span>
    </label>
  );
}

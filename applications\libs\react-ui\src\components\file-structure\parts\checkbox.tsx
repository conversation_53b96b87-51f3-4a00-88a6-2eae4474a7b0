import cx from 'classnames';
import { RefObject } from 'react';

interface CheckboxProps {
  ariaLabel?: string;
  handleSelect: (event: React.ChangeEvent<HTMLInputElement>) => void;
  inputRef?: RefObject<HTMLInputElement>;
  isSelected: boolean;
  isDisabled?: boolean;
  value?: string;
}

export function Checkbox({ ariaLabel, handleSelect, inputRef, isSelected, isDisabled, value }: CheckboxProps) {
  return (
    <input
      ref={inputRef}
      aria-label={ariaLabel}
      type="checkbox"
      className={cx(
        '!border-base-25 hover:!border-base-75 mr-3 h-[25px] w-[25px] cursor-pointer rounded-[5px] border',
        isSelected ? 'text-primary !border-primary' : 'text-base-25'
      )}
      value={value}
      checked={isSelected}
      onChange={handleSelect}
      disabled={isDisabled}
    />
  );
}

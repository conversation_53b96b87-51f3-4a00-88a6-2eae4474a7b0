import { Locator, Page } from '@playwright/test';
import { loadStory } from 'apps/react-ui-e2e/src/utils/common-utils';

import { RichTextFieldArgs } from './rich-text-field.spec';

const storyId = 'iprox-ui-forms-fields-richtext--default';

export const richTextProps: RichTextFieldArgs = {
  label: 'Label',
  description: 'Richtext Description',
  defaultValue: '',
};

/**
 * create a table with given rows and columns inside the rich text field
 * @param page page object
 * @param rows row count
 * @param columns column count
 * @returns
 */
export const createTable = async (page: Page, rows: number, columns: number) => {
  const columnsKey = `${columns}`;
  const rowsKey = `${rows}`;

  await loadStory<RichTextFieldArgs>(page, storyId, richTextProps);

  await page.getByTitle('Insert Table').click();

  await page
    .getByLabel('Columns')
    .selectText()
    .then(() => page.keyboard.press(columnsKey));
  await page
    .getByLabel('Rows')
    .selectText()
    .then(() => page.keyboard.press(rowsKey));

  await page.getByRole('button', { name: 'Insert Table' }).click();

  return page.getByRole('table');
};

/**
 * iterate through each cell of the table
 * @param table table locator object
 * @param rowCount row count
 * @param cellCount cell count
 * @param callback callback function for run on each cell
 */
export const iterateThroughTableCells = async (
  table: Locator,
  rowCount: number,
  cellCount: number,
  callback: (cell: Locator, rowIndex: number, cellIndex: number) => void
) => {
  const tableRows = await table.locator('tr').all();

  for (let i = 0; i < Math.min(rowCount, tableRows.length); i++) {
    const tdCells = await tableRows[i].locator('td').all();
    const thCells = await tableRows[i].locator('th').all();
    const cells = [...tdCells, ...thCells];

    for (let j = 0; j < Math.min(cellCount, cells.length); j++) {
      const cell = cells[j];
      await callback(cell, i, j);
    }
  }
};

/**
 * perform action on the cell of the table
 * @param page Page object
 * @param table table locator object
 * @param rowIndex row index to perform action
 * @param cellIndex cell index to perform action
 * @param action action to perform
 * @param cellText text to add inside the cell
 */
export const performActionOnCell = async (
  page: Page,
  table: Locator,
  rowIndex: number,
  cellIndex: number,
  action: string,
  cellText = undefined
) => {
  const thCellLocator = await table.locator('tr').nth(rowIndex).locator('th').nth(cellIndex);
  const tdCellLocator = await table.locator('tr').nth(rowIndex).locator('td').nth(cellIndex);
  const cell = (await thCellLocator.count()) > 0 ? thCellLocator : tdCellLocator;

  await cell.click();
  const inerText = await cell.innerText();

  if (cellText && inerText !== cellText) {
    await page.keyboard.type(cellText);
  }

  await page.getByRole('button', { name: 'action-menu' }).click();
  await page.getByRole('button', { name: action }).click();
};

/**
 * add heading to the rich text field
 * @param page Page object
 * @param type text element type
 * @param inputValue text to add inside the element
 */
export const addHeading = async (page: Page, type: 'Heading 1' | 'Heading 2' | 'Heading 3', inputValue: string) => {
  await page.getByLabel('Formatting options for text').click();
  await page.getByRole('button', { name: type }).click();

  const editorInput = page.locator('.editor-input').first();

  editorInput.click();
  await page.keyboard.type(inputValue);
};

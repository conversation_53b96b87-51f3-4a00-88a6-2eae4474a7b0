import { colors } from '../../config';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';

describe('Dossier Un-Publish Tests', () => {
  let dossierId: string;
  let bearerToken: any;
  let dossierCategoryId: string | null;

  beforeAll(async () => {
    bearerToken = await loginAndGetToken();
    dossierCategoryId = await DossierHelpersNew.getRandomCategoryUuid(false, bearerToken);

    const response: any = await DossierHelpersNew.createDossier('Un-Publish dossier', bearerToken, dossierCategoryId);
    dossierId = await response.body.dossier.dossierId;
  }, 90000);

  it('should un-publish a dossier', async () => {
    // update and publish dossier
    await DossierHelpersNew.updateDossier(dossierId, bearerToken);
    await DossierHelpersNew.publishDossier(dossierId, bearerToken);
    const dossierLatestVersionResponse1 = await DossierHelpersNew.latestVersionOfDossier(dossierId, bearerToken);
    expect(await dossierLatestVersionResponse1.body.dossier.isPublished).toBe(true);

    // un-publish dossier
    await DossierHelpersNew.unpublishDossier(dossierId, bearerToken);
    const dossierLatestVersionResponse2 = await DossierHelpersNew.latestVersionOfDossier(dossierId, bearerToken);
    expect(await dossierLatestVersionResponse2.body.dossier.isPublished).toBe(false);
  }, 90000);

  afterAll(async () => {
    await DossierHelpersNew.deleteDossier(dossierId, bearerToken);
  });
});

import nl from 'date-fns/locale/nl';
import { FieldHookConfig, useField } from 'formik';
import { useLocale } from 'next-intl';
import { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import { FormField } from '../../form-field/form-field';
import { useFormField } from '../../hooks/use-form-field.hook';
import { DateFieldDefinition } from '../../models/form.models';
import './react-datepicker.scss';

export function DatePickerField(props: DateFieldDefinition) {
  const locale = useLocale();

  const [startDate, setStartDate] = useState<Date | undefined>(
    typeof props.value === 'string' ? new Date(props.value) : new Date()
  );
  const updatedProps = {
    ...props,
    defaultValue: startDate?.toISOString(),
  };
  const [field, meta, helpers] = useField(updatedProps as FieldHookConfig<Date | null | undefined>);
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'date');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  const handleDateChange = (date?: Date | null) => {
    setStartDate(date ?? undefined);
    helpers.setValue(date, true);
  };

  const subDates = (date: Date, count: number) => {
    const newDate = date.setDate(date.getDate() - count);
    return new Date(newDate);
  };

  const addDates = (date: Date, count: number) => {
    const newDate = date.setDate(date.getDate() + count);
    return new Date(newDate);
  };

  return (
    <FormField definition={props} errorMessage={meta.touched ? meta.error : undefined} {...formControlProps}>
      <DatePicker
        selected={startDate}
        onChange={handleDateChange}
        id={inputProps?.id}
        ariaDescribedBy={inputProps?.['aria-describedby']}
        name={field.name}
        onBlur={() => helpers.setTouched(true, true)}
        minDate={subDates(new Date(), 120)}
        maxDate={addDates(new Date(), 120)}
        locale={locale === 'nl' ? nl : undefined}
        dateFormat={locale === 'nl' ? 'dd-MM-yyyy' : 'dd/MM/yyyy'}
      />
    </FormField>
  );
}

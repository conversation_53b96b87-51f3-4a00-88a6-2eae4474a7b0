import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { DateRangePickerField } from './date-range-picker-field';

const meta: Meta<typeof DateRangePickerField> = {
  title: 'iprox-ui/forms/fields/date-range-picker-field',
  component: DateRangePickerField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof DateRangePickerField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'date-range-field',
    fieldType: FieldType.Date,
  },
};

import { test } from '@playwright/test';
import { chromium } from 'playwright';
import { playAudit } from 'playwright-lighthouse';

import { options, thresholds } from '../config';
import { portalUrls, tenant } from '../data/portalUrls';
import { generateSummaryReport } from '../utils/generate_summary_report';

let URLs: string[] = portalUrls;

function formatReportFileName(slug: string): string {
  return false
    ? `${slug}-${new Date()
        .toISOString()
        .replace(/[^0-9]/g, '')
        .slice(0, 14)}`
    : slug;
}

const outputFolder = `${process.cwd()}/lighthouse-report/${tenant}`;

for (const url of URLs) {
  const parts = url.replace('https://', '').split('/');
  const slug = parts.length > 1 ? parts.slice(1).join('_') : 'portal';

  test(`Lighthouse Accessibility test for ${url}`, async () => {
    test.slow();
    const browser = await chromium.launch({
      args: ['--remote-debugging-port=9222'],
      headless: true,
    });
    const page = await browser.newPage();
    await page.goto(url);
    await playAudit({
      page: page,
      port: 9222,
      thresholds: thresholds,
      opts: options,
      reports: {
        formats: {
          html: true,
          json: true,
        },
        name: formatReportFileName(slug),
        directory: outputFolder,
      },
      ignoreError: true,
    });
    await page.close();
    await browser.close();
  });
}

test.afterAll(async () => {
  await generateSummaryReport(outputFolder);
});

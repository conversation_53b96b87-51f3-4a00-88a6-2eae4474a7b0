import createMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';

import { WebStatsDomain } from '@/components/web-stats';

const intlMiddleware = createMiddleware({
  // Supported locales. English is only used for development purposes.
  locales: process.env.NODE_ENV === 'development' ? ['nl', 'en'] : ['nl'],

  // If this locale is matched, pathnames work without a prefix (e.g. `/about`)
  defaultLocale: 'nl',

  // Locale will always default to nl or used locale in URL.
  localeDetection: false,
  alternateLinks: false,
  localePrefix: 'as-needed',
});

export async function middleware(request: NextRequest) {
  const publicPathnameRegex = RegExp('^/public/', 'i');

  /* If the path starts with /public its an api route. */
  if (publicPathnameRegex.test(request.nextUrl.pathname)) {
    return NextResponse.rewrite(
      new URL(
        `${process.env.IPROX_OPEN_API_URL}${request.nextUrl.pathname.replace('/public', '')}${request.nextUrl.search}`
      ),
      { request }
    );
  }

  const response = intlMiddleware(request);

  if (process.env.IPROX_OPEN_API_URL) {
    const apiUrl = new URL(process.env.IPROX_OPEN_API_URL);
    const unsafeEval = process.env.NODE_ENV === 'development' ? `'unsafe-eval'` : '';
    const { analyticsImgSrc, analyticsScriptSrc } = await WebStatsDomain();

    response.headers.set(
      'Content-Security-Policy',
      [
        `default-src 'self'`,
        `connect-src 'self' ${apiUrl.origin}`,
        `img-src 'self' ${apiUrl.origin} ${analyticsImgSrc} data:`,
        `script-src 'unsafe-inline' ${unsafeEval} 'self' ${analyticsScriptSrc}`,
        `font-src 'self' https://fonts.googleapis.com ${apiUrl.origin}`,
        `style-src 'self' 'unsafe-inline'`,
        `base-uri 'self'`,
        `form-action 'self'`,
        `frame-ancestors 'none'`,
      ].join('; ')
    );
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Permissions-Policy', 'payment=()');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('Strict-Transport-Security', 'max-age=31536000;includeSubDomains;preload');
  }

  return response;
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|images/chevron-right.svg|robots.txt|sitemap|.well-known/security.txt).*)',
  ],
};

import { INSERT_TABLE_COMMAND } from '@lexical/table';
import { LexicalEditor } from 'lexical';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';

import { Button } from '../../../../button/button';
import { Modal } from '../../../../modal/modal';
import { Text } from '../../../../text/text';
import { FormBuilder } from '../../../form-builder/form-builder';
import { FieldType, FormSubmitValues } from '../../../models/form.models';
import { ValidationRuleType } from '../../../models/validator.models';

export function InsertTableModal({
  isOpen,
  activeEditor,
  onClose,
}: {
  isOpen: boolean;
  activeEditor: LexicalEditor;
  onClose: () => void;
}): JSX.Element {
  const t = useTranslations('components.richtexteditor');

  const [isModalOpen, setIsModalOpen] = useState(isOpen);

  useEffect(() => {
    setIsModalOpen(isOpen);
  }, [isOpen]);

  const onClick = (values: FormSubmitValues) => {
    activeEditor.dispatchCommand(INSERT_TABLE_COMMAND, {
      columns: typeof values.columns === 'string' ? values.columns : `${values.columns}`,
      rows: typeof values.rows === 'string' ? values.rows : `${values.rows}`,
      includeHeaders: {
        rows: true,
        columns: false,
      },
    });

    onClose();
  };

  const fields = useMemo(() => {
    return [
      {
        name: 'columns',
        label: t('columns'),
        fieldType: FieldType.Integer,
        value: 5,
        step: 1,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateNumberRange,
            ruleValue: { minimumValue: 1, maximumValue: 50 },
          },
          {
            ruleType: ValidationRuleType.IntegerOnly,
            ruleValue: {},
          },
        ],
      },
      {
        name: 'rows',
        label: t('rows'),
        fieldType: FieldType.Integer,
        value: 5,
        step: 1,
        validationRules: [
          {
            ruleType: ValidationRuleType.RequiredProperty,
            ruleValue: {},
          },
          {
            ruleType: ValidationRuleType.ValidateNumberRange,
            ruleValue: { minimumValue: 1, maximumValue: 50 },
          },
          {
            ruleType: ValidationRuleType.IntegerOnly,
            ruleValue: {},
          },
        ],
      },
    ];
  }, [t]);

  return (
    <Modal
      isOpen={isModalOpen}
      onClose={() => {
        setIsModalOpen(false);
        onClose();
      }}
    >
      <Text className="font-heading text-heading mb-4 text-4xl font-semibold">{t('insertTable')}</Text>
      <FormBuilder
        fields={fields}
        onSubmit={onClick}
        buttons={
          <div className="flex justify-center">
            <Button type="submit" variant="primary">
              {t('insertTable')}
            </Button>
          </div>
        }
      />
    </Modal>
  );
}

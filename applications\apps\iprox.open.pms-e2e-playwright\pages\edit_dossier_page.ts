import { type Locator, type Page, expect } from '@playwright/test';

import { IntegerCountExtractor } from '../utils/integer_count_extractor';

export class EditDossierPage {
  readonly page: Page;
  readonly categoryElement: Locator;
  readonly fileChooserLink: Locator;
  readonly imageChooser: Locator;
  readonly dossierTitle: Locator;
  readonly titleEditButton: Locator;
  readonly titleSaveButton: Locator;
  readonly dossierTitleInputField: Locator;
  readonly childFileName = 'ylsqmqskwj.csv';
  readonly locators: Locator[];
  addZipButton: Locator;
  zipDeleteButton: Locator;
  dossierStatus: Locator;
  dossierViewLink: Locator;
  fileCount: number | null;

  constructor(page: Page) {
    this.page = page;
    this.dossierTitle = page.locator(`//span[contains(text(),'Titel')]/following-sibling::div[1]/span[1]`);
    this.categoryElement = page.locator(':text("Categorie") + span');
    this.imageChooser = page.getByRole('button', { name: 'Wij<PERSON> de afbeeldingsmap' });
    this.fileChooserLink = page.getByRole('button', { name: 'klik om te bladeren', exact: true }).nth(1);
    this.titleEditButton = page.getByRole('button', { name: 'Titel wijzigen', exact: true });
    this.dossierTitleInputField = page.getByLabel('Title');
    this.titleSaveButton = page.getByRole('button', { name: 'Titel opslaan', exact: true });
  }

  async editTitle(titleUpdateText: string) {
    await this.titleEditButton.click();
    const titleOriginalValue = await this.dossierTitleInputField.inputValue();
    const titleUpdatedValue = `${titleOriginalValue}-${titleUpdateText}`;
    await this.dossierTitleInputField.click();
    await this.dossierTitleInputField.fill(titleUpdatedValue);
    await this.titleSaveButton.click();
    await this.titleEditButton.click();
    const titleCurrentValue = await this.dossierTitleInputField.inputValue();
    await this.titleSaveButton.click();
    expect(titleCurrentValue).toMatch(titleUpdatedValue);
  }

  async uploadImage(imageName: any) {
    await this.page.evaluate(() => {
      window.scrollTo(0, 0);
    });
    const fileChooserPromise = this.page.waitForEvent('filechooser');
    await this.imageChooser.click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(`data/images/${imageName}`); //'data/images/image.jpg'
    expect(await this.page.getByRole('alert', { name: 'Afbeelding bijgewerkt' }).isVisible());
    expect(
      await this.page
        .getByRole('button', { name: 'Wijzig de afbeeldingsmap Voorbeeld van dossierafbeelding' })
        .isVisible()
    );
  }

  async inputDynamicFields() {
    await this.page.getByLabel('Samenvatting').click();
    await this.page.getByLabel('Samenvatting').fill('description rich text');

    await this.page.getByRole('textbox', { name: 'Auteur' }).fill('Test author');
    await this.page.getByRole('button', { name: 'Toevoegen', exact: true }).click();

    await this.page.getByLabel('Documentnummer').click();
    await this.page.getByLabel('Documentnummer').fill('Document number 1/A/#123/007');

    await this.page.getByLabel('Annotatie').click();
    await this.page.getByLabel('Annotatie').fill('Annotation @EditTest');

    await this.page.getByLabel('Uitgever').click({ modifiers: ['Control'] });
    await this.page.getByLabel('Uitgever').fill('Publisher 1 test');

    await this.page.getByLabel('Inhoud').click();
    await this.page.getByLabel('Inhoud').fill('Here are some content');

    await this.page.getByLabel('Contact').click();
    await this.page.getByLabel('Contact').fill('+100 200300400');
  }

  async getZipUploadElementLocators(page: Page) {
    await expect(this.page.getByRole('status').locator('svg')).toBeHidden();
    this.addZipButton = page.getByRole('button', { name: 'Zip toevoegen', exact: true });
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForLoadState('networkidle', { timeout: 1000 * 60 * 10 });
    await expect(this.addZipButton).toBeVisible();
    await expect.soft(this.addZipButton).toBeEnabled();
    this.zipDeleteButton = page.getByRole('button', { name: 'Verwijderen', exact: true });
    await expect(this.zipDeleteButton).toBeVisible();
  }

  async uploadZipFile(zipName: string) {
    // zipNames : root_with_sub_dirs | root_dir_and_files
    await this.fileChooserLink.isVisible();
    const fileChooserPromise = this.page.waitForEvent('filechooser');
    await this.fileChooserLink.click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(`data/zipFiles/${zipName}.zip`);
    await this.page.waitForLoadState('domcontentloaded');
    await this.page.waitForLoadState('networkidle', { timeout: 1000 * 60 * 10 });
    // await this.getZipUploadElementLocators(this.page);
  }

  // expect file structure to be available, zipNames : root_with_sub_dirs | root_dir_and_files
  async checkFileStructure(zipName: string) {
    await this.getZipUploadElementLocators(this.page);
    await this.page.waitForLoadState('domcontentloaded');

    await this.page.getByLabel(`Map ${zipName}`).waitFor({ state: 'visible' });
    await this.page.getByLabel(`Map ${zipName}`).click({ force: true });
    await expect(this.page.getByLabel(`Map ${zipName}`)).toBeVisible();
    await this.page.getByLabel(`Map ${zipName}`).click({ force: true });

    // await this.page.getByLabel('Map subdir1').waitFor({ state: 'attached'});
    await expect(this.page.getByLabel('Map subdir1')).toBeVisible();
    await this.page.getByRole('checkbox', { name: 'Selecteer subdir1' }).check();
    await this.page.getByLabel('Map subdir1').click({ force: true });

    // await this.page.getByLabel('Map subsubdir1').waitFor({ state: 'visible'});
    // await this.page.getByText('Aantal bestanden:').click();
    // await expect(this.page.getByLabel('Map subsubdir1')).toBeVisible();
    await this.page.getByLabel('Map subsubdir1').click({ force: true });
  }

  async uploadSecondZip(zipName: string) {
    // await this.page.waitForTimeout(1000 * 60 * 3);
    await this.getZipUploadElementLocators(this.page);
    await this.page.waitForLoadState('domcontentloaded');
    await expect(this.addZipButton).toBeEnabled();
    this.addZipButton.click();
    this.uploadZipFile(zipName);
  }
  async expandZip(zipName: string) {}
  async selectChildFile() {}
  async deleteChildFile() {}

  async getZipFileCount() {
    let fileCountValue: string | null = await this.page
      .locator(`//span[contains(text(),'Aantal bestanden')]`)
      .textContent();
    this.fileCount = IntegerCountExtractor.extractNumberFromString(fileCountValue);
    return this.fileCount;
  }

  async clickActionButton(action: string) {
    await this.page.waitForLoadState('domcontentloaded');
    await expect(this.page.getByLabel('Toon opties')).toBeVisible();
    await expect(this.page.getByLabel('Toon opties')).toBeEnabled();
    await this.page.getByLabel('Toon opties').focus();
    await this.page.getByLabel('Toon opties').click();

    const actionButton = this.page.getByRole('button', { name: `${action}`, exact: true });
    await actionButton.focus();
    await expect(actionButton).toBeVisible();
    await actionButton.click({}); //Opslaan,Publiceren,Verbergen,Verwijderen

    await this.page.getByRole('button', { name: 'Bevestigen', exact: true }).click();

    // wait for the alert toasts
    if (action === 'Opslaan') {
      if (await this.page.getByRole('alert', { name: 'Dossier opgeslagen' }).isVisible()) {
        await expect(this.page.getByRole('alert', { name: 'Dossier opgeslagen' })).toBeVisible({ visible: false });
      }
    } else if (action === 'Publiceren') {
      if (await this.page.getByRole('alert', { name: 'Dossier gepubliceerd' }).isVisible()) {
        await expect(this.page.getByRole('alert', { name: 'Dossier gepubliceerd' })).toBeVisible({ visible: false });
      }
    } else if (action === 'Verbergen') {
      if (await this.page.getByRole('alert', { name: 'Dossier verborgen' }).isVisible()) {
        await expect(this.page.getByRole('alert', { name: 'Dossier verborgen' })).toBeVisible({ visible: false });
      }
    } else if (action === 'Verwijderen') {
      if (await this.page.getByRole('alert', { name: 'Dossier verwijderd' }).isVisible()) {
        await expect(this.page.getByRole('alert', { name: 'Dossier verwijderd' })).toBeVisible({ visible: false });
      }
    } else {
      console.log('Invalid selection');
    }
  }

  async checkPublishedDossierViewLink() {
    await expect(this.page.getByRole('link', { name: 'Bekijk' })).toBeVisible({ visible: true });
  }

  async clickPublishedDossierViewLink() {
    expect(await this.page.getByRole('link', { name: 'Bekijk' }).isVisible());
    await this.page.getByRole('link', { name: 'Bekijk' }).click();
  }

  async checkStatusBoxStatus(status: string) {
    const dossierStatus: Locator = this.page.getByText(status).getByTestId(status);
    expect(await dossierStatus.isVisible()); //Nieuw,Gepubliceerd,Gewijzigd
  }

  async editRichTextField() {
    // todo: all 12 types of editing
  }

  async verifyUpdatedDynamicFields(page: Page, locators: Locator[], textToAppend: string) {
    const originalValues: string[] = [];
    const updatedValues: string[] = [];

    // Append text to each element and store the original values
    for (const locator of locators) {
      const currentValue: any = await locator.inputValue();
      originalValues.push(currentValue);
      const updatedValue = `${currentValue}-${textToAppend}`;
      updatedValues.push(updatedValue);
      await locator.fill(updatedValue);
    }

    // Saving the dossier
    await this.clickActionButton('Opslaan');

    // Check updated values
    for (let i = 0; i < locators.length; i++) {
      await locators[i].isVisible();
      const currentValue: any = await locators[i].inputValue();
      await locators[i].focus();
      expect(updatedValues[i]).toEqual(currentValue);
    }
  }
}

export default EditDossierPage;

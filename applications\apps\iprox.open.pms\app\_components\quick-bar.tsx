'use client';

import { useNotificationManager } from '@/context/notifications-context';
import { DropdownMenuItem, NotificationButton, UserMenu } from '@iprox/react-ui';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import { useMemo, useState } from 'react';

import { LogoutModal } from './logout-modal';
import { NotificationBadge } from './notification-badge';

export function Quickbar() {
  const { notifications } = useNotificationManager();

  const [openModal, setOpenModal] = useState(false);
  const { data: session } = useSession({
    required: true,
    onUnauthenticated() {
      redirect('/login');
    },
  });

  const menu: DropdownMenuItem[] = useMemo(
    () => [
      {
        label: 'Logout',
        onClick: () => {
          setOpenModal(true);
        },
      },
    ],
    []
  );

  return (
    <>
      <div className="flex flex-row items-center">
        <div className="relative px-2">
          <NotificationButton />
          <div className="absolute right-0.5 top-6">
            <NotificationBadge count={notifications.length} />
          </div>
        </div>
        <UserMenu displayName={session?.user.name ?? ''} menuItems={menu} />
      </div>
      <LogoutModal isOpen={openModal} onClose={() => setOpenModal(false)} />
    </>
  );
}

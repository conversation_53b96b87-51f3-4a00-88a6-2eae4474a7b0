import { components } from '@/iprox-open.interface';
import { CommonPageDto } from '@/models/page-dto.model';
import { PageListParams } from '@/models/page-list-filter.model';
import { KyInstance } from 'ky/distribution/types/ky';

function getPageTypeSlug(pageType: 'Search' | 'Announcement' | 'Simple'): string {
  switch (pageType) {
    case 'Search':
      return 'search-page';
    case 'Announcement':
      return 'announcement-page';
    case 'Simple':
      return 'simple-page';
    default:
      throw new Error('Invalid page type');
  }
}

export async function managePage(
  clientApi: KyInstance,
  id: string,
  pageType: 'Search' | 'Announcement' | 'Simple',
  action: 'delete' | 'publish' | 'unpublish'
): Promise<void> {
  const endpointMap = {
    delete: `page/${getPageTypeSlug(pageType)}/${id}`,
    publish: `page/${getPageTypeSlug(pageType)}/${id}/publish`,
    unpublish: `page/${getPageTypeSlug(pageType)}/${id}/unpublish`,
  };

  const endpoint = endpointMap[action];

  if (!endpoint) {
    throw new Error('Invalid action');
  }

  if (action === 'delete') {
    await clientApi.delete(endpoint);
  } else {
    await clientApi.get(endpoint);
  }
}

export async function createPage(
  clientApi: KyInstance,
  pageType: 'Search' | 'Announcement' | 'Simple',
  body:
    | components['schemas']['CreateSearchPageCommand']
    | components['schemas']['CreateAnnouncementPageCommand']
    | components['schemas']['CreateSimplePageCommand']
): Promise<{
  page: CommonPageDto;
}> {
  return await clientApi.post(`page/${getPageTypeSlug(pageType)}`, { json: body }).json<{
    page: CommonPageDto;
  }>();
}

export async function getPagesPaged(
  clientApi: KyInstance,
  params: PageListParams
): Promise<components['schemas']['GetPagedPagesResponse']> {
  const paramsString = new URLSearchParams(
    Object.entries(params).reduce<Record<string, string>>((acc, [key, value]: [string, string | undefined]) => {
      if (value) {
        acc[key] = value;
      }

      return acc;
    }, {})
  ).toString();

  return await clientApi.get(`page/paged?${paramsString}`).json<components['schemas']['GetPagedPagesResponse']>();
}

export async function updateSearchPage(
  clientApi: KyInstance,
  id: string,
  body: components['schemas']['UpdateSearchPageCommand']
): Promise<{
  page: components['schemas']['SearchPageDto'];
}> {
  return await clientApi.put(`page/search-page/${id}`, { json: body }).json<{
    page: components['schemas']['SearchPageDto'];
  }>();
}

export async function updateAnnouncementPage(
  clientApi: KyInstance,
  id: string,
  body: components['schemas']['UpdateAnnouncementPageCommand']
): Promise<{
  page: components['schemas']['AnnouncementPageDto'];
}> {
  return await clientApi.put(`page/announcement-page/${id}`, { json: body }).json<{
    page: components['schemas']['AnnouncementPageDto'];
  }>();
}

export async function updateSimplePage(
  clientApi: KyInstance,
  id: string,
  body: components['schemas']['UpdateSimplePageCommand']
): Promise<{
  page: components['schemas']['SimplePageDto'];
}> {
  return await clientApi.put(`page/simple-page/${id}`, { json: body }).json<{
    page: components['schemas']['SimplePageDto'];
  }>();
}

export async function getPage(clientApi: KyInstance, id: string): Promise<components['schemas']['GetPageResponse']> {
  return await clientApi.get(`page/${id}`).json<components['schemas']['GetPageResponse']>();
}

export async function updateHomePage(
  clientApi: KyInstance,
  body: components['schemas']['UpdateHomePageCommand']
): Promise<components['schemas']['UpdateHomePageResponse']> {
  return await clientApi.put(`page/home-page`, { json: body }).json<components['schemas']['UpdateHomePageResponse']>();
}

export async function updateFooterComponent(
  clientApi: KyInstance,
  body: components['schemas']['UpdateFooterComponentCommand']
): Promise<components['schemas']['UpdateFooterComponentResponse']> {
  return await clientApi
    .put(`page/component/footer`, { json: body })
    .json<components['schemas']['UpdateFooterComponentResponse']>();
}

## Install the dependencies

Run `yarn install` from the root (applications)

Run `npx playwright install` from the root (applications)

## How to create an user.ts credentials files.

Create a user.ts file under `\applications\.auth` and `\applications\apps\iprox.open.pms-e2e-playwright\.auth` dir
similar to the given format in the 'user-template.ts'

## Running the tests

Run `yarn run playwright:pms` from the root (applications)

or

Run `npx playwright test tests --project=chromium --project=login-chromium --headed` from the
iprox.open.pms-e2e-playwright folder

## Lighthouse tests

Before running the portal lighthouse test, please run the `.\scripts\fetch_portal_published_urls.ps1 [tenant]` from
the iprox.open.pms-e2e-playwright folder. This will write the published portal urls for the specific tenant to the
data/portalUrls.ts file.

Run `npx playwright test tests --project=a11y-chromium --headed` from the iprox.open.pms-e2e-playwright folder

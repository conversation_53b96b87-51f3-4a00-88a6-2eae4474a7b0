import { Dropdown, DropdownMenuItem } from '../dropdown/dropdown';
import { User } from './parts/user';

export interface UserMenuProps {
  displayName: string;
  avatarSrc?: string;
  menuItems: DropdownMenuItem[];
}

export function UserMenu({ displayName, avatarSrc, menuItems }: UserMenuProps) {
  return (
    <Dropdown menu={menuItems}>
      <User displayName={displayName} avatar={avatarSrc} />
    </Dropdown>
  );
}

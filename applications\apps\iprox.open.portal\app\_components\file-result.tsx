import { components } from '@/iprox-open.interface';
import { getExtensionFromMimeType } from '@/utils/get-file-types';
import { FileTypeLabel, Text } from '@iprox/react-ui';
import { filesize } from 'filesize';
import { useFormatter, useTranslations } from 'next-intl';
import Link from 'next/link';
import { useMemo } from 'react';

import { FileDownloadLink } from './file-download-link';
import { RenderSearchHighlights } from './render-search-highlights';

export interface FileResultProps {
  pageSlug: string;
  item: components['schemas']['PublicCognitiveFileSearchResultDto'];
}

export function FileResult({ pageSlug, item }: FileResultProps) {
  const format = useFormatter();
  const t = useTranslations('search');

  const fileType = useMemo(() => {
    return getExtensionFromMimeType(item.contentType) ?? '';
  }, [item.contentType]);

  return (
    <>
      <div className="mb-4 flex items-center gap-x-4">
        <FileTypeLabel label={fileType.toUpperCase()} />
        <Text className="font-heading text-heading w-fit text-2xl font-semibold">
          <RenderSearchHighlights highlightedContent={item.highlights?.NodeName} fallbackContent={item.nodeName} />
        </Text>
      </div>
      <div className="overflow-hidden">
        <Text className="font-text text-body mb-4 text-sm">
          <span className="capitalize">
            {format.dateTime(new Date(item.publishFromDate as string), {
              dateStyle: 'long',
            })}
          </span>
          <span aria-hidden>{` | `}</span>
          {filesize(item.size, { base: 2, standard: 'jedec' }).toString()}
          <span aria-hidden>{` | `}</span>
          {t('belongsTo')}&nbsp;
          <Link
            href={`/${pageSlug}/${item.dossierId}`}
            className="editor-link !p-0 hover:cursor-pointer hover:underline"
          >
            <RenderSearchHighlights
              highlightedContent={item.highlights?.DossierTitle}
              fallbackContent={item.dossierTitle}
            />
          </Link>
        </Text>
      </div>
      <FileDownloadLink link={`/public/download/${item.nodeId}`} fileType={fileType.toUpperCase()} />
    </>
  );
}

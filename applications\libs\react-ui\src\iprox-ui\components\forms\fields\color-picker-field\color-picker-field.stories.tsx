import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { ColorPickerField } from './color-picker-field';

const meta: Meta<typeof ColorPickerField> = {
  title: 'iprox-ui/forms/fields/color-picker-field',
  component: ColorPickerField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof ColorPickerField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Select a color',
    description: 'help text',
    name: 'color-picker-field',
    fieldType: FieldType.Color,
  },
};

import { expect, test } from '@playwright/test';

import { baseUrl } from '../config';
import { DashboardPage } from '../pages/dashboard_page';
import { EditDossierPage } from '../pages/edit_dossier_page';
import { CreateNewDossierPage } from '../pages/new_dossier_page';
import { DateTimeFormatter } from '../utils/date_time_formatter';

let dashboardPage: DashboardPage;
let newDossierPage: CreateNewDossierPage;
let editDossierPage: EditDossierPage;
let updatedText = 'updated text';

test.beforeEach(async ({ page }) => {
  await page.goto(baseUrl);
  dashboardPage = new DashboardPage(page);
  newDossierPage = new CreateNewDossierPage(page);
  editDossierPage = new EditDossierPage(page);
});

test.describe.skip('Verify the dossier zip uploads', () => {
  test('Verify a Dossier upload and structure', async ({ page }) => {
    test.slow();
    const zipName = 'root_dir_and_files';
    const dossierFileName = 'ylsqmqskwj.csv';
    const title = `Playwright dossier with publish dates - ${DateTimeFormatter.getFormattedDateTime()}`;

    // Create new dossier
    await dashboardPage.clickNewDossierButton();
    // create a dossier with publish From To dates.
    await newDossierPage.createNewDossier(title, true);
    await expect(page).toHaveURL(new RegExp('/dossier/.*'));

    // Input dynamic fields
    await editDossierPage.inputDynamicFields();

    // uploadImageFile
    // await editDossierPage.uploadImage('iprox_1.png');

    // save dossier
    await editDossierPage.clickActionButton('Opslaan');
    await editDossierPage.uploadZipFile(zipName);
    await editDossierPage.checkFileStructure(zipName);
  });

  test('Verify the Dossier reuploading gives the proper warnings', async ({ page }) => {
    test.slow();
    test.setTimeout(1000 * 60 * 5);

    const zipName = 'root_dir_and_files';
    const dossierFileName = 'ylsqmqskwj.csv';
    const title = `Playwright dossier reuploading - ${DateTimeFormatter.getFormattedDateTime()}`;

    // Create new dossier
    await dashboardPage.clickNewDossierButton();
    // create a dossier with publish From To dates.
    await newDossierPage.createNewDossier(title, true);
    await expect(page).toHaveURL(new RegExp('/dossier/.*'));

    // Upload zip
    await editDossierPage.uploadZipFile(zipName);

    // upload Image
    // await editDossierPage.uploadImage('iprox_1.png');

    // Input dynamic fields
    await editDossierPage.inputDynamicFields();

    // save dossier
    await editDossierPage.clickActionButton('Opslaan');

    // upload zip and verify the zip structure
    await editDossierPage.checkFileStructure(zipName);

    await editDossierPage.uploadSecondZip(zipName);
    expect(await page.locator(`//span[contains(text(),'Geblokkeerde bestanden')]`).isVisible());
    expect(
      await page
        .locator(`//span[contains(text(),"ylsqmqskwj.csv")]//preceding::span[contains(text(),"FileExist")]`)
        .isVisible()
    );
    expect(await page.getByRole('button', { name: 'OK' }).isEnabled());
    await page.getByRole('button', { name: 'OK' }).click();
    expect(await page.locator(`//span[contains(text(),'Geblokkeerde bestanden')]`).isHidden());
    expect(await page.locator(`//span[contains(text(),'Aantal bestanden')]`).isVisible());
  });
});

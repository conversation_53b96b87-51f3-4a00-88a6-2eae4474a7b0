'use client';

import { ALLOWED_IMAGE_FILE_TYPES } from '@/config/allowed-file-types';
import { useClientApi } from '@/http/fetcher-api.client';
import { updateLogo } from '@/services/image-asset-service.client';
import { getPublicAssets } from '@/services/public-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { ImageUploader } from '@iprox/iprox-ui';
import { showToast } from '@iprox/react-ui';
import { useAppSettings } from '@iprox/shared-context';
import { AxiosProgressEvent } from 'axios';
import { useTranslations } from 'next-intl';

interface LogoUploaderProps {
  logoAssetPath?: string | null;
}

export function LogoUploader({ logoAssetPath }: LogoUploaderProps) {
  const settings = useAppSettings();
  const clientApi = useClientApi();
  const t = useTranslations('site');

  const upload = async (image: File, handleProgress: (progressEvent: AxiosProgressEvent) => void) => {
    try {
      await updateLogo(settings.apiUrl, image, handleProgress);
      showToast(t('logoUpdated'), { type: 'success' });

      const publicAssets = await getPublicAssets(clientApi);
      return `${settings.apiUrl}/public/asset${publicAssets.siteAssets.logo}`;
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    }
  };

  return (
    <ImageUploader
      label={t('logoSettings')}
      allowedFileTypes={ALLOWED_IMAGE_FILE_TYPES}
      uploadPromise={upload}
      overflowText={t('changeLogo')}
      initialImageSrc={logoAssetPath ? `${settings.apiUrl}/public/asset${logoAssetPath}` : undefined}
      wide
    />
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(authenticated)/(user)/dossier/[id]/page",{

/***/ "(app-pages-browser)/./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx":
/*!***************************************************************************!*\
  !*** ./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx ***!
  \***************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PageContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/http/fetcher-api.client */ \"(app-pages-browser)/./app/_http/fetcher-api.client.ts\");\n/* harmony import */ var _services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/dossier-service.client */ \"(app-pages-browser)/./app/_services/dossier-service.client.ts\");\n/* harmony import */ var _utils_dossier_form_changed__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/dossier.form-changed */ \"(app-pages-browser)/./app/_utils/dossier.form-changed.ts\");\n/* harmony import */ var _utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dossier.form-definition.hook */ \"(app-pages-browser)/./app/_utils/dossier.form-definition.hook.ts\");\n/* harmony import */ var _utils_error_handler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/error-handler */ \"(app-pages-browser)/./app/_utils/error-handler.ts\");\n/* harmony import */ var _iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @iprox/iprox-ui */ \"(app-pages-browser)/../../libs/react-ui/src/iprox-ui/index.ts\");\n/* harmony import */ var _iprox_react_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @iprox/react-ui */ \"(app-pages-browser)/../../libs/react-ui/src/index.ts\");\n/* harmony import */ var _iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @iprox/shared-context */ \"(app-pages-browser)/../../libs/shared-context/src/index.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/../../node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _components_dossier_file_manager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dossier-file-manager */ \"(app-pages-browser)/./app/_components/dossier-file-manager.tsx\");\n/* harmony import */ var _components_dossier_image_field__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dossier-image-field */ \"(app-pages-browser)/./app/_components/dossier-image-field.tsx\");\n/* harmony import */ var _components_dossier_status_controls__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dossier-status-controls */ \"(app-pages-browser)/./app/_components/dossier-status-controls.tsx\");\n/* harmony import */ var _components_dossier_title__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dossier-title */ \"(app-pages-browser)/./app/_components/dossier-title.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PageContent(param) {\n    let { dossier: initialDossier, versions: initialVersions, pages } = param;\n    var _dossier_created, _dossier_published, _dossier_unpublished, _dossier_modified, _dossier_category, _dossier_decorativeImageNode;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations)(\"dossier\");\n    const settings = (0,_iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__.useAppSettings)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const clientApi = (0,_http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__.useClientApi)();\n    const formId = \"dossier-creation-form\";\n    const { showDialog } = (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.useConfirmDialog)();\n    const [dossier, setDossier] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(initialDossier);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(initialDossier.status);\n    const [isSaveAndPublish, setIsSaveAndPublish] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false);\n    const [versions, setVersions] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(initialVersions);\n    const [lastSavedValues, setLastSavedValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [formValues, setFormValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(null);\n    const [dateFormValues, setDateFormValues] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(dossier.publishDates);\n    const [isNew, setIsNew] = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(dossier.version === 1);\n    const formDefinition = (0,_utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__.useDossierFormDefinition)(dossier);\n    const editStatus = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        switch(true){\n            case status === \"Published\":\n                var _dossier_published;\n                return {\n                    status: \"published\",\n                    date: (_dossier_published = dossier.published) === null || _dossier_published === void 0 ? void 0 : _dossier_published.dateTime\n                };\n            case status === \"Unpublished\":\n                var _dossier_unpublished;\n                return {\n                    status: \"unpublished\",\n                    date: (_dossier_unpublished = dossier.unpublished) === null || _dossier_unpublished === void 0 ? void 0 : _dossier_unpublished.dateTime\n                };\n            case isNew:\n                var _dossier_created;\n                return {\n                    status: \"new\",\n                    date: (_dossier_created = dossier.created) === null || _dossier_created === void 0 ? void 0 : _dossier_created.dateTime\n                };\n            default:\n                var _versions__modified, _versions_;\n                return {\n                    status: \"modified\",\n                    date: (_versions_ = versions[0]) === null || _versions_ === void 0 ? void 0 : (_versions__modified = _versions_.modified) === null || _versions__modified === void 0 ? void 0 : _versions__modified.dateTime\n                };\n        }\n    }, [\n        status,\n        isNew,\n        (_dossier_created = dossier.created) === null || _dossier_created === void 0 ? void 0 : _dossier_created.dateTime,\n        (_dossier_published = dossier.published) === null || _dossier_published === void 0 ? void 0 : _dossier_published.dateTime,\n        (_dossier_unpublished = dossier.unpublished) === null || _dossier_unpublished === void 0 ? void 0 : _dossier_unpublished.dateTime,\n        versions\n    ]);\n    const dossierLiveStatus = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(()=>{\n        var _version_published, _version_unpublished, _version_deleted;\n        const version = versions.find((version)=>version.status !== \"Draft\");\n        if (!version) {\n            var _dossier_modified;\n            return {\n                status: \"Draft\",\n                date: (_dossier_modified = dossier.modified) === null || _dossier_modified === void 0 ? void 0 : _dossier_modified.dateTime\n            };\n        }\n        const dates = [\n            {\n                status: \"Published\",\n                date: (_version_published = version.published) === null || _version_published === void 0 ? void 0 : _version_published.dateTime\n            },\n            {\n                status: \"Unpublished\",\n                date: (_version_unpublished = version.unpublished) === null || _version_unpublished === void 0 ? void 0 : _version_unpublished.dateTime\n            },\n            {\n                status: \"Deleted\",\n                date: (_version_deleted = version.deleted) === null || _version_deleted === void 0 ? void 0 : _version_deleted.dateTime\n            }\n        ].filter((item)=>item.date !== null);\n        const mostRecent = dates.reduce((latest, current)=>{\n            if (!latest || current.date && new Date(current.date) > new Date(latest.date)) {\n                return current;\n            }\n            return latest;\n        }, null);\n        return mostRecent;\n    }, [\n        versions,\n        (_dossier_modified = dossier.modified) === null || _dossier_modified === void 0 ? void 0 : _dossier_modified.dateTime\n    ]);\n    const refetchVersions = async ()=>{\n        try {\n            const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.getDossierVersions)(clientApi, dossier.dossierId);\n            if (response === null || response === void 0 ? void 0 : response.dossiers) {\n                setVersions(response.dossiers);\n            }\n        } catch (error) {\n            console.error(\"Failed to refetch versions:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(()=>{\n        const handleBeforeUnload = (e)=>{\n            if ((0,_utils_dossier_form_changed__WEBPACK_IMPORTED_MODULE_3__.hasFormChanged)(formDefinition, formValues, lastSavedValues)) {\n                e.returnValue = \"\";\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>{\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        t,\n        formDefinition,\n        formValues,\n        lastSavedValues\n    ]);\n    /**\r\n   * format form value object to match the API request body required to save form data\r\n   * @param values form values\r\n   * @returns formatted form values object\r\n   */ const formatValues = (values)=>{\n        return Object.keys(values).filter((key)=>key !== \"summary\" && key !== \"fromDate\" && key !== \"toDate\").reduce((acc, value)=>{\n            var _formDefinition_find;\n            return {\n                ...acc,\n                [(_formDefinition_find = formDefinition.find((field)=>field.name === value)) === null || _formDefinition_find === void 0 ? void 0 : _formDefinition_find.id]: values[value]\n            };\n        }, {});\n    };\n    const handleFormChange = (values)=>{\n        setFormValues(values);\n    };\n    const handleDateFormChange = (values)=>{\n        setDateFormValues(values);\n    };\n    /**\r\n   * handle save form data\r\n   * @param values form values\r\n   */ const handleSaveForm = async (values)=>{\n        showDialog({\n            message: isSaveAndPublish ? t(\"confirmation.publish.message\") : t(\"confirmation.save.message\"),\n            onConfirm: async ()=>{\n                setIsLoading(true);\n                const updateDossierRequest = {\n                    dossierId: dossier.dossierId,\n                    summary: typeof values.summary === \"string\" ? values.summary : \"\",\n                    dynamicFieldValues: formatValues(values),\n                    publishFromDate: (dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.fromDate) ? dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.fromDate : new Date().toISOString(),\n                    publishToDate: (dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.toDate) ? dateFormValues === null || dateFormValues === void 0 ? void 0 : dateFormValues.toDate : null\n                };\n                try {\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.updateDossier)(clientApi, updateDossierRequest);\n                    if (response) {\n                        setIsNew(response.dossier.version === 1);\n                        setLastSavedValues(formValues);\n                        await refetchVersions();\n                        if (isSaveAndPublish) {\n                            handlePublishDossier();\n                        } else {\n                            setStatus(\"Draft\");\n                            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.save\"), {\n                                type: \"success\"\n                            });\n                        }\n                    }\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    const handleDeleteDossier = ()=>{\n        showDialog({\n            message: t(\"confirmation.delete.message\"),\n            onConfirm: async ()=>{\n                try {\n                    setIsLoading(true);\n                    const reqBody = {\n                        dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n                    };\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.deleteDossier)(clientApi, reqBody);\n                    if (response && response.success) {\n                        // Refetch versions to get updated delete timestamp (though we're navigating away)\n                        await refetchVersions();\n                        router.push(\"/dossier/list\");\n                    }\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.delete\"), {\n                        type: \"success\"\n                    });\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    /**\r\n   * handle publish dossier functionality\r\n   */ const handlePublishDossier = async ()=>{\n        try {\n            const reqBody = {\n                dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n            };\n            const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.publishDossier)(clientApi, reqBody);\n            if (response && response.success) {\n                setStatus(\"Published\");\n                // Refetch versions to get updated publish timestamp\n                await refetchVersions();\n            }\n            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.publish\"), {\n                type: \"success\"\n            });\n        } catch (error) {\n            const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n            (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                type: \"error\"\n            });\n        }\n    };\n    const handleUnpublishDossier = ()=>{\n        showDialog({\n            message: t(\"confirmation.unpublish.message\"),\n            onConfirm: async ()=>{\n                try {\n                    setIsLoading(true);\n                    const reqBody = {\n                        dossierId: dossier === null || dossier === void 0 ? void 0 : dossier.dossierId\n                    };\n                    const response = await (0,_services_dossier_service_client__WEBPACK_IMPORTED_MODULE_2__.unpublishDossier)(clientApi, reqBody);\n                    if (response) {\n                        setStatus(\"Draft\");\n                        setIsNew(false);\n                        // Refetch versions to get updated unpublish timestamp\n                        await refetchVersions();\n                    }\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(t(\"success.unpublish\"), {\n                        type: \"success\"\n                    });\n                } catch (error) {\n                    const errorMessages = await (0,_utils_error_handler__WEBPACK_IMPORTED_MODULE_5__.getErrorMessages)(error);\n                    (0,_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.showToast)(errorMessages, {\n                        type: \"error\"\n                    });\n                } finally{\n                    setIsLoading(false);\n                }\n            }\n        });\n    };\n    if (!dossier.rootFolderNode) {\n        console.error(\"Root folder node is missing\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid h-full grid-cols-3 gap-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        className: \"font-heading text-heading mb-9 truncate text-5xl\",\n                        children: dossier.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_title__WEBPACK_IMPORTED_MODULE_14__.DossierTitle, {\n                            title: dossier.title,\n                            dossierId: dossier.dossierId,\n                            onTitleUpdate: (updatedDossier)=>setDossier(updatedDossier)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                className: \"font-heading text-heading mb-2 text-lg font-bold\",\n                                children: t(\"category\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                className: \"font-text-regular text-content-lite text-sm\",\n                                children: (dossier === null || dossier === void 0 ? void 0 : (_dossier_category = dossier.category) === null || _dossier_category === void 0 ? void 0 : _dossier_category.label) || \"\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b-highlight mb-10 border-b\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 mt-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_image_field__WEBPACK_IMPORTED_MODULE_12__.DossierImageField, {\n                                    dossierId: dossier.dossierId,\n                                    imagePath: (_dossier_decorativeImageNode = dossier.decorativeImageNode) === null || _dossier_decorativeImageNode === void 0 ? void 0 : _dossier_decorativeImageNode.fullName\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.FormBuilder, {\n                                    fields: formDefinition,\n                                    onChange: handleFormChange,\n                                    onSubmit: handleSaveForm,\n                                    formId: formId,\n                                    buttons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            dossier.rootFolderNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_file_manager__WEBPACK_IMPORTED_MODULE_11__.DossierFileManager, {\n                                dossierId: dossier.dossierId,\n                                dossierName: dossier.title,\n                                apiRootNode: dossier.rootFolderNode\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_iprox_react_ui__WEBPACK_IMPORTED_MODULE_7__.StatusBox, {\n                    editStatus: editStatus,\n                    dossierLiveStatus: dossierLiveStatus,\n                    publishDates: dateFormValues,\n                    pages: pages,\n                    dossierId: dossier.dossierId,\n                    categoryId: dossier.categoryId,\n                    portalUrl: settings.portalUrl,\n                    submitForm: handleDateFormChange,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dossier_status_controls__WEBPACK_IMPORTED_MODULE_13__.DossierStatusControls, {\n                        dossierId: dossier.dossierId,\n                        formId: formId,\n                        disabled: isLoading,\n                        status: status,\n                        handleUnpublishDossier: handleUnpublishDossier,\n                        handleDeleteDossier: handleDeleteDossier,\n                        setIsSaveAndPublish: setIsSaveAndPublish\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Producten\\\\iprox-open\\\\projects\\\\iprox.open\\\\applications\\\\apps\\\\iprox.open.pms\\\\app\\\\[locale]\\\\(authenticated)\\\\(user)\\\\dossier\\\\[id]\\\\page-content.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, this);\n}\n_s(PageContent, \"/lC7UfztqxrJSzmMMERWsRc0Evk=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_15__.useTranslations,\n        _iprox_shared_context__WEBPACK_IMPORTED_MODULE_8__.useAppSettings,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _http_fetcher_api_client__WEBPACK_IMPORTED_MODULE_1__.useClientApi,\n        _iprox_iprox_ui__WEBPACK_IMPORTED_MODULE_6__.useConfirmDialog,\n        _utils_dossier_form_definition_hook__WEBPACK_IMPORTED_MODULE_4__.useDossierFormDefinition\n    ];\n});\n_c = PageContent;\nvar _c;\n$RefreshReg$(_c, \"PageContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/(authenticated)/(user)/dossier/[id]/page-content.tsx\n"));

/***/ })

});
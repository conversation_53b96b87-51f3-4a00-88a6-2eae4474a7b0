import { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { ReactNode, useEffect, useState } from 'react';

import { Button } from '../button/button';
import { Modal } from './modal';

const meta: Meta<typeof Modal> = {
  title: 'iprox-ui/components/modal',
  component: Modal,
  argTypes: {
    children: {
      control: 'text',
    },
  },
};

export default meta;

type Story = StoryObj<typeof Modal>;

const TemplateModal = ({ open, children }: { open: boolean; children: ReactNode }) => {
  const [isOpen, setIsOpen] = useState(open);

  useEffect(() => {
    setIsOpen(open);
  }, [open]);

  return (
    <>
      <button data-testid="open-modal" onClick={() => setIsOpen(true)}>
        Click to open modal
      </button>
      <Modal isOpen={isOpen} onClose={() => setIsOpen(false)}>
        {children}
        <div className="mt-4 grid grid-flow-col justify-end gap-2">
          <Button type="button" variant="primary" onClick={() => setIsOpen(false)}>
            OK
          </Button>
          <Button type="button" variant="secondary" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
        </div>
      </Modal>
    </>
  );
};

export const Default: Story = {
  name: 'default',
  args: {
    isOpen: false,
    children: (
      <div className="sm:flex sm:items-start">
        <div className="mt-3 text-center sm:mt-0 sm:text-left">
          <h3 className="font-heading text-primary-content mb-5 block text-2xl font-bold">Title</h3>
          <p className="font-text text-primary-content block text-lg">Some message that you need to conway.</p>
        </div>
      </div>
    ),
  },
  render: (args) => <TemplateModal open={args.isOpen} children={args.children} />,
  decorators: [
    (Story) => (
      <div className="w-100 flex flex-row justify-center">
        <Story />
      </div>
    ),
  ],
};

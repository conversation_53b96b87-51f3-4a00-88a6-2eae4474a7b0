# Sample env config

# For prisma studio
PMS_DATABASE_URL=sqlserver://sql-iprox-open-dev.database.windows.net:1433;database=sqldb-iprox-open-dev-pms-auth;user=sql-iprox-open@sql-iprox-open-dev;password=********************;encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;

# Next Configuration
PMS_NEXT_DATABASE_URL=sqlserver://sql-iprox-open-dev.database.windows.net:1433;database=sqldb-iprox-open-dev-pms-auth;user=sql-iprox-open@sql-iprox-open-dev;password=********************;encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;
PMS_DATABASE_URL_SHADOW=sqlserver://sql-iprox-open-dev.database.windows.net:1433;database=sqldb-iprox-open-dev-pms-auth-shadow;user=sql-iprox-open@sql-iprox-open-dev;password=********************;encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;
IPROX_DEBUG=true

IPROX_OPEN_API_URL=https://app-iprox-open-dev.azurewebsites.net/api/v1
# IPROX_OPEN_API_URL=http://localhost:25419/api/v1
#IPROX_OPEN_API_URL=http://localhost:5257/api/v1
IPROX_OPEN_CREDENTIALS_PROVIDER_URL=https://tf-test-open-overheid-prod-cms.azurewebsites.net
IPROX_OPEN_PORTAL_URL=https://app-iprox-open-portal-dev.azurewebsites.net

APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=1531c510-bacf-46ed-a8bc-476f75d8bbd8;IngestionEndpoint=https://westeurope-5.in.applicationinsights.azure.com/;LiveEndpoint=https://westeurope.livediagnostics.monitor.azure.com/

NEXTAUTH_URL=http://localhost:4200
NEXTAUTH_SECRET=ebb432a48ce1aaad95aefbb704386d02

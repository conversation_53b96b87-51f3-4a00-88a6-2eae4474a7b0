# Portal Application Developer Guide

The portal is the website where the public user will use to search and download dossiers.

This application is built using Next 14 and requires specific prerequisites and setup steps.

## Prerequisites

Ensure the following software is installed:

1. Node.js 18+
2. Yarn (Package Manager)

## Setup Guide

### Installing NPM Packages

Navigate to the application directory and execute `yarn` to install the necessary libraries, as this project utilizes Yarn as its package manager.

### Configuring Environment Variables

Ensure proper configuration in the .env.development.local file to run the application.

### Running the Project

From the application directory, execute `yarn nx server iprox.open.portal`.

Alternatively, consider using the "NX Console" Visual Studio Code extension. Find the "NX Console" extension in the marketplace or [install it here](https://marketplace.visualstudio.com/items?itemName=nrwl.angular-console).

## Installing Additional Dependencies

To include new npm libraries, access the `application` directory. Once there, employ `yarn add` to install the desired libraries.

For ex..

`yarn add react-dom`

import { getSearchPages } from '@/services/page-service';
import { RedirectType, notFound, redirect } from 'next/navigation';

type Props = {
  params: {
    page: string;
    id: string;
    locale: string;
  };
};

export default async function Page({ params }: Props) {
  const searchPages = await getSearchPages(params.id);

  if (searchPages.length === 0) {
    notFound();
  }

  redirect(`/${searchPages[0].slug}/${params.id}`, 'replace' as RedirectType);
}

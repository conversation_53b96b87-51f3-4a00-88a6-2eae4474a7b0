'use client';

import { components } from '@/iprox-open.interface';
import { DateRangePickerField, FieldType, SelectField, Text } from '@iprox/iprox-ui';
import { format } from 'date-fns';
import { Form, Formik, useFormikContext } from 'formik';
import { useTranslations } from 'next-intl';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo } from 'react';

interface StatisticsFilterForm {
  publicationType: string;
  publishedFromTill: [Date | null, Date | null];
  publishedBy: string;
}

const FormObserver: React.FC = () => {
  const { values } = useFormikContext<StatisticsFilterForm>();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const { publishedFromTill, ...restValues } = values;
    const [publishedFrom, publishedTill] = publishedFromTill || [null, null];

    const formattedValues = {
      ...restValues,
      ...{
        publishedFrom: publishedFrom && publishedTill ? format(new Date(publishedFrom), 'yyyy-MM-dd') : '',
        publishedTill: publishedFrom && publishedTill ? format(new Date(publishedTill), 'yyyy-MM-dd') : '',
      },
    };

    const newSearchParams = new URLSearchParams({
      ...Object.fromEntries(searchParams),
      ...formattedValues,
    });

    Object.entries(formattedValues).forEach(([key, value]) => {
      if (value === '') {
        newSearchParams.delete(key);
      }
    });

    router.push(`${pathname}?${newSearchParams.toString()}`);
  }, [pathname, router, searchParams, values]);

  return null;
};

interface StatisticsFilterWrapperProps {
  dossierCategories: components['schemas']['DossierCategoryDto'][];
  children?: React.ReactNode;
}

export function StatisticsFilterWrapper({ dossierCategories, children }: StatisticsFilterWrapperProps) {
  const t = useTranslations('dashboard');
  const searchParams = useSearchParams();

  const initialValues: StatisticsFilterForm = useMemo(() => {
    const publishedFrom = searchParams.get('publishedFrom');
    const publishedTill = searchParams.get('publishedTill');

    return {
      publicationType: searchParams.get('publicationType') || '',
      publishedFromTill: [
        publishedFrom ? new Date(publishedFrom) : null,
        publishedTill ? new Date(publishedTill) : null,
      ],
      publishedBy: searchParams.get('publishedBy') || '',
    };
  }, [searchParams]);

  const categoryOptions = useMemo(() => {
    const allOption = { label: t('allPublicationTypes'), value: '' };
    const options = dossierCategories.map((item) => ({
      label: item.label,
      value: item.id,
    }));
    return [allOption, ...options];
  }, [dossierCategories, t]);

  const handleSubmit = () => {
    // do nothing
  };

  return (
    <div className="mb-96">
      <Text className="font-heading text-heading mb-6 text-4xl font-bold">{t('statistics')}</Text>
      <Formik initialValues={initialValues} onSubmit={handleSubmit}>
        <Form className="mb-4 grid grid-cols-1 gap-y-1 lg:grid-cols-3 lg:gap-x-4 xl:gap-x-12">
          <FormObserver />
          <div>
            <SelectField
              name="publicationType"
              label={t('typeOfPublication')}
              fieldType={FieldType.Select}
              validationRules={[]}
              options={categoryOptions}
            />
          </div>
          <div>
            <DateRangePickerField
              name="publishedFromTill"
              label={t('publishedFromTill')}
              fieldType={FieldType.Date}
              validationRules={[]}
              initialDateRange={initialValues.publishedFromTill}
            />
          </div>
          {/* TODO: implement publishedBy */}
          {/* <div>
            <SelectField
              name="publishedBy"
              label={t('publishedBy')}
              fieldType={FieldType.Select}
              validationRules={[]}
              options={[]}
            />
          </div> */}
        </Form>
      </Formik>
      {children}
    </div>
  );
}

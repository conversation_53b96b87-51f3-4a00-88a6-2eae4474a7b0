import useLexicalEditable from '@lexical/react/useLexicalEditable';
import { ReactPortal } from 'react';
import { createPortal } from 'react-dom';

import { TableCellActionMenuContainer } from '../components/table-action-menu-container';

export function TableActionMenuPlugin({
  anchorElem = document.body,
}: {
  anchorElem?: HTMLElement;
}): null | ReactPortal {
  const isEditable = useLexicalEditable();
  return createPortal(isEditable ? <TableCellActionMenuContainer anchorElem={anchorElem} /> : null, anchorElem);
}

export async function InstagramIcon() {
  return (
    <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="25" cy="25" r="25" fill="white" />
      <path
        d="M26.542 10.0018C27.6366 9.99765 28.7311 10.0086 29.8253 10.0348L30.1163 10.0453C30.4523 10.0573 30.7838 10.0723 31.1843 10.0903C32.7802 10.1653 33.8691 10.4173 34.8246 10.7878C35.8145 11.1688 36.6485 11.6848 37.4825 12.5188C38.245 13.2681 38.8351 14.1746 39.2119 15.1752C39.5823 16.1306 39.8343 17.2211 39.9093 18.817C39.9273 19.216 39.9423 19.549 39.9543 19.885L39.9633 20.176C39.99 21.2698 40.0015 22.3638 39.9978 23.4579L39.9993 24.5769V26.5418C40.003 27.6364 39.9915 28.7309 39.9648 29.8252L39.9558 30.1162C39.9438 30.4522 39.9288 30.7836 39.9108 31.1841C39.8358 32.7801 39.5808 33.869 39.2119 34.8245C38.8364 35.8261 38.2461 36.7333 37.4825 37.4824C36.7324 38.2449 35.8255 38.835 34.8246 39.2119C33.8691 39.5823 32.7802 39.8343 31.1843 39.9093C30.7838 39.9273 30.4523 39.9423 30.1163 39.9543L29.8253 39.9633C28.7311 39.99 27.6366 40.0015 26.542 39.9978L25.4231 39.9993H23.4597C22.3651 40.003 21.2706 39.9915 20.1763 39.9648L19.8854 39.9558C19.5293 39.9429 19.1733 39.9279 18.8174 39.9108C17.2215 39.8358 16.1326 39.5808 15.1756 39.2119C14.1747 38.8359 13.2681 38.2456 12.5192 37.4824C11.7558 36.7329 11.1651 35.8258 10.7883 34.8245C10.4178 33.869 10.1659 32.7801 10.0909 31.1841C10.0742 30.8282 10.0592 30.4722 10.0459 30.1162L10.0384 29.8252C10.0107 28.7309 9.99821 27.6364 10.0009 26.5418V23.4579C9.99668 22.3638 10.0077 21.2698 10.0339 20.176L10.0444 19.885C10.0564 19.549 10.0714 19.216 10.0894 18.817C10.1644 17.2196 10.4163 16.1321 10.7868 15.1752C11.1639 14.1741 11.7557 13.2679 12.5207 12.5203C13.269 11.7564 14.1751 11.1652 15.1756 10.7878C16.1326 10.4173 17.22 10.1653 18.8174 10.0903L19.8854 10.0453L20.1763 10.0378C21.2701 10.0102 22.3641 9.9977 23.4582 10.0003L26.542 10.0018ZM25.0001 17.5016C24.0064 17.4875 23.0198 17.6711 22.0977 18.0417C21.1756 18.4122 20.3363 18.9624 19.6286 19.6601C18.921 20.3578 18.359 21.1893 17.9755 22.1061C17.5919 23.0229 17.3944 24.0068 17.3944 25.0006C17.3944 25.9944 17.5919 26.9783 17.9755 27.8951C18.359 28.8119 18.921 29.6433 19.6286 30.3411C20.3363 31.0388 21.1756 31.5889 22.0977 31.9595C23.0198 32.3301 24.0064 32.5136 25.0001 32.4996C26.9891 32.4996 28.8967 31.7094 30.3031 30.303C31.7096 28.8965 32.4997 26.9889 32.4997 24.9998C32.4997 23.0108 31.7096 21.1032 30.3031 19.6967C28.8967 18.2902 26.9891 17.5016 25.0001 17.5016ZM25.0001 20.5015C25.5978 20.4905 26.1918 20.5987 26.7472 20.8198C27.3026 21.0409 27.8085 21.3705 28.2351 21.7893C28.6617 22.2081 29.0007 22.7077 29.232 23.2589C29.4634 23.8102 29.5827 24.402 29.5828 24.9998C29.5829 25.5977 29.4638 26.1895 29.2326 26.7409C29.0014 27.2922 28.6627 27.7919 28.2362 28.2108C27.8097 28.6298 27.304 28.9595 26.7486 29.1808C26.1932 29.4021 25.5993 29.5105 25.0016 29.4997C23.8082 29.4997 22.6636 29.0256 21.8198 28.1817C20.9759 27.3378 20.5018 26.1933 20.5018 24.9998C20.5018 23.8064 20.9759 22.6618 21.8198 21.818C22.6636 20.9741 23.8082 20.5 25.0016 20.5L25.0001 20.5015ZM32.8747 15.2517C32.3908 15.271 31.9332 15.4769 31.5977 15.8261C31.2621 16.1753 31.0748 16.6408 31.0748 17.1251C31.0748 17.6094 31.2621 18.0749 31.5977 18.4241C31.9332 18.7733 32.3908 18.9792 32.8747 18.9985C33.3719 18.9985 33.8488 18.801 34.2005 18.4494C34.5521 18.0978 34.7496 17.6209 34.7496 17.1236C34.7496 16.6263 34.5521 16.1494 34.2005 15.7978C33.8488 15.4462 33.3719 15.2487 32.8747 15.2487V15.2517Z"
        fill="#1A327B"
      />
    </svg>
  );
}

import cx from 'classnames';
import React from 'react';

export interface TextProps {
  children?: React.ReactNode;
  className?: string;
  role?: React.AriaRole;
  tooltipId?: string;
  tooltipContent?: string;
  inline?: boolean;
}

export const Text = React.forwardRef<HTMLSpanElement, TextProps>((props, ref) => {
  const { children, className, role, tooltipId, tooltipContent, inline } = props;
  return (
    <span
      className={cx(className, {
        block: !inline,
      })}
      role={role}
      data-tooltip-id={tooltipId}
      data-tooltip-content={tooltipContent}
      ref={ref}
    >
      {children}
    </span>
  );
});

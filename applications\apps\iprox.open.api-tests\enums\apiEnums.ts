export enum EndpointsDossier {
  GET_DOSSIERS = '/dossiers',
  GET_DOSSIER_BY_ID = '/dossiers/{ID}',
  CREATE_DOSSIER = '/api/v1/dossier',
  UPDATE_DOSSIER = '/api/v1/dossier',
  DELETE_DOSSIER = '/api/v1/dossier',
  PUBLISH_DOSSIER = '/api/v1/dossier/publish-dossier',
  UNPUBLISH_DOSSIER = '/api/v1/dossier/unpublish-dossier',
  LATEST_PUBLISHED_VERSION = `/api/v1/dossier/{ID}/latest-published-version`,
  LATEST_VERSION = `/api/v1/dossier/{ID}/latest-version`,
}

export enum EndpointsPage {
  CREATE_SEARCH_PAGE = '/api/v1/page/search-page',
  UPDATE_SEARCH_PAGE = '/api/v1/page/search-page/{ID}',
  DELETE_SEARCH_PAGE = '/api/v1/page/search-page/{ID}',
  PUBLISH_SEARCH_PAGE = '/api/v1/page/search-page/{ID}/publish',
  UN_PUBLISH_SEARCH_PAGE = '/api/v1/page/search-page/{ID}/unpublish',
}

export enum EndpointsDossierCategory {
  GET_DOSSIER_CATEGORIES = '/api/v1/dossier-category',
  CREATE_DOSSIER_CATEGORY = '/dossier-categories',
}

export enum EndpointsPortal {
  GET_WOO_CATEGORIES = '/api/v1/public/woo-categories',
  GET_PUBLIC_PAGE_LIST = '/api/v1/public/page/list',
  GET_PUBLIC_PAGE_SLUG_DETAILS = '/api/v1/public/page/{SLUG}',
  GET_PUBLIC_HOMEPAGE = '/api/v1/public/homepage',
  GET_PUBLIC_DOSSIER = '/api/v1/public/dossier/{ID}',
  GET_NAVIGATION_STRUCTURE = '/api/v1/public/navigation-structure',
}

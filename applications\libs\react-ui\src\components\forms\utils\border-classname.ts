export const borderClassname = (errorMessage: string | undefined, value: unknown) => {
  if (errorMessage) {
    return 'border !border-error hover:!shadow-input-error';
  }

  const isEmpty =
    value == null ||
    ((typeof value === 'string' || Array.isArray(value)) && value.length === 0) ||
    typeof value === 'boolean' ||
    value;

  if (!isEmpty) {
    return 'border !border-highlight';
  }

  return 'border border-content-extra-lite hover:!border-highlight hover:!shadow-input-hover';
};

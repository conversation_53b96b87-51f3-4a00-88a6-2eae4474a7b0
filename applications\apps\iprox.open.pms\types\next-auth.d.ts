// eslint-disable-next-line @typescript-eslint/no-unused-vars
import NextAuth from 'next-auth/next';

import { components } from './../api/generated/iprox-open.interface';

declare module 'next-auth' {
  interface Session {
    user: User;
    error: unknown;
  }

  type IproxUser = components['schemas']['UserDto'];
  type UserRole = string;

  interface User extends IproxUser {
    provider: string;
    id: string;
    name: string;
    email: string;
    access_token: string | null;
    refresh_token: string | null;
    errorMessage?: string;
    exp: number;
    userRoles: UserRole[] | null;
  }
}

declare module 'next-auth/jwt' {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    id: string;
    email: string;
    unique_name: string;
    access_token: string;
    expires_at: number;
    refresh_token: string;
    exp: number;
  }
}

import { components } from '@/iprox-open.interface';
import { KyInstance } from 'ky/distribution/types/ky';

export async function updateSiteParameters(
  clientApi: KyInstance,
  siteParametersSettings: components['schemas']['UpdateSiteParametersSettingsCommand']
): Promise<void> {
  return await clientApi.put('settings/site-parameters', { json: siteParametersSettings }).json();
}

export async function updateNavigation(
  clientApi: KyInstance,
  content: components['schemas']['UpdateNavigationSettingsCommand']
): Promise<void> {
  return await clientApi.put('settings/navigation', { json: content }).json();
}

import { sanitize } from 'isomorphic-dompurify';

import { ValidationRule, ValidationRuleType, ValidatorFn } from '../models/validator.models';

export interface StringLengthValidationRule extends ValidationRule<ValidationRuleType.ValidateStringLength> {
  ruleValue: {
    maximumLength: number | string;
    minimumLength: number | string;
  };
}

export const isStringLengthRule = (rule: ValidationRule): rule is StringLengthValidationRule => {
  return rule.ruleType === ValidationRuleType.ValidateStringLength;
};

export const stringLengthValidator = (ruleValue: {
  minimumLength: number | string;
  maximumLength: number | string;
}): ValidatorFn => {
  return ({ value }) => {
    const errors: { stringMaxLength?: boolean; stringRangeLength?: boolean } = {};

    if (typeof value !== 'string') {
      return errors;
    }

    const sanitizedValue = sanitize(value, {
      ALLOWED_ATTR: [],
      ALLOWED_TAGS: [],
    }).trim();

    const minLength = Number(ruleValue.minimumLength);
    const maxLength = Number(ruleValue.maximumLength);

    if ((isNaN(minLength) || minLength === 0) && sanitizedValue.length > maxLength) {
      errors.stringMaxLength = true;
      return errors;
    }

    if (sanitizedValue.length < minLength || sanitizedValue.length > maxLength) {
      errors.stringRangeLength = true;
      return errors;
    }

    return errors;
  };
};

import { components } from '@/iprox-open.interface';
import { StatisticsCard } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';
import React from 'react';

interface StatisticsProps {
  dossierMetrics?: components['schemas']['DossierMetrics'];
}

export function Statistics({ dossierMetrics }: StatisticsProps) {
  const t = useTranslations('dashboard');

  return (
    <div className="flex flex-col gap-y-4 lg:flex-row lg:gap-x-4">
      <div className="max-w-xs flex-1">
        <StatisticsCard icon="FolderIcon" label={t('numberOfDossiers')} value={dossierMetrics?.dossierCount || 0} />
      </div>
      <div className="max-w-xs flex-1">
        <StatisticsCard icon="DocumentIcon" label={t('numberOfFiles')} value={dossierMetrics?.fileCount || 0} />
      </div>
      <div className="max-w-xs flex-1">
        <StatisticsCard
          icon="CircleStackIcon"
          label={t('data')}
          value={dossierMetrics?.totalStorageConsumption || 0}
          formatterType="filesize"
        />
      </div>
    </div>
  );
}

import { AxiosProgressEvent } from 'axios';
import cx from 'classnames';
import { useTranslations } from 'next-intl';
import { useRef, useState } from 'react';

import { Spinner } from '../../../components/spinner/spinner';
import { calculateUploadProgress } from '../../../utils/upload-utils';
import CloudUploadIcon from '../file-upload/cloud-upload.icon';
import { Image } from '../image/image';
import { ProgressBar } from '../progress-bar/progress-bar';
import { Text } from '../text/text';

interface ImageUploaderProps {
  label: string;
  allowedFileTypes: string[];
  overflowText?: string;
  initialImageSrc?: string;
  uploadPromise: (
    image: File,
    handleProgress: (progressEvent: AxiosProgressEvent) => void
  ) => Promise<string | undefined>;
  wide?: boolean;
  placeholder?: string;
}

export function ImageUploader({
  label,
  allowedFileTypes,
  overflowText,
  initialImageSrc,
  uploadPromise,
  wide,
  placeholder,
}: ImageUploaderProps) {
  const tImage = useTranslations('components.imageUploader');
  const tFile = useTranslations('components.fileUpload');

  const [imageSrc, setImageSrc] = useState<string | undefined>(initialImageSrc);
  const [isLoadingImage, setIsLoadingImage] = useState(initialImageSrc ? true : false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file) {
      handleUpload(file);
      setIsLoadingImage(true);
    }
  };

  const handleInputClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const clearSelectedImage = () => {
    setImageSrc(undefined);
    fileInputRef.current?.value && (fileInputRef.current.value = '');
  };

  const handleProgress = (progressEvent: AxiosProgressEvent) => {
    const percentage = calculateUploadProgress(progressEvent);
    setUploadProgress(percentage);
  };

  const handleUpload = async (imageFile: File) => {
    if (!imageFile) {
      return;
    }

    setIsUploading(true);

    try {
      const uploadedImageSrc = await uploadPromise(imageFile, handleProgress);

      if (uploadedImageSrc) {
        clearSelectedImage();
        setImageSrc(uploadedImageSrc);
      }
    } catch (error) {
      clearSelectedImage();
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);

    const file = event.dataTransfer.files?.[0];
    if (!file || !allowedFileTypes.includes(file.type)) {
      alert(tFile('fileTypeWarning'));
      return;
    }

    handleUpload(file);
    setIsLoadingImage(true);
  };

  const handleOnImageLoad = () => {
    setIsLoadingImage(false);
  };

  return (
    <div className="w-full">
      <Text className="font-heading text-heading mb-4 text-lg font-bold">{label}</Text>
      <input type="file" accept="image/*" ref={fileInputRef} className="hidden" onChange={handleImageChange} />
      <div
        role="button"
        onClick={handleInputClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={cx(
          { 'w-full': wide },
          { 'w-60': !wide },
          `border-highlight bg-secondary-extra-light rounded-input after:bg-base-100 text-base-00 after:z-9 group relative mb-6 h-60 overflow-hidden border border-dashed text-lg after:absolute after:inset-0 after:flex after:items-center after:justify-center after:text-opacity-100 after:opacity-0 after:transition-all after:duration-300 after:ease-in-out`,
          { 'hover:after:opacity-75': imageSrc && !isUploading },
          { 'after:opacity-75': isDragging }
        )}
      >
        <span
          className={cx(
            'text-base-00 absolute inset-0 flex items-center justify-center opacity-0 duration-300 ease-in-out',
            {
              'hover:underline group-hover:opacity-100': imageSrc && !isUploading,
            }
          )}
        >
          {overflowText}
        </span>
        {isUploading ? (
          <div className="mx-auto flex h-full w-5/6 flex-col justify-center">
            <ProgressBar title={tImage('uploading')} progress={uploadProgress} />
          </div>
        ) : imageSrc ? (
          <>
            <Image
              src={imageSrc}
              fill
              alt={tImage('imageAlt')}
              onLoad={handleOnImageLoad}
              className="object-contain object-center"
            />
            {isLoadingImage && (
              <div className="flex h-full w-full items-center justify-center">
                <Spinner variant="secondary" />
              </div>
            )}
          </>
        ) : (
          <div className="flex h-full w-full flex-col items-center justify-center gap-y-1">
            <CloudUploadIcon className="text-secondary-light h-14 w-14 text-center" />
            <Text className="font-heading text-heading px-3 text-center text-base font-bold">
              {placeholder ?? tImage('dropFileHere')}
              <br />
              <span className="cursor-pointer underline" tabIndex={0}>
                {tImage('clickHereToBrowse')}
              </span>
            </Text>
          </div>
        )}
      </div>
    </div>
  );
}

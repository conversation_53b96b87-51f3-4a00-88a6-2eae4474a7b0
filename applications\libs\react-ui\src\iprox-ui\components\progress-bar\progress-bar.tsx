import { Text } from '../text/text';

interface ProgressBarProps {
  title: string;
  progress: number;
}

export function ProgressBar({ title, progress }: ProgressBarProps) {
  return (
    <div className="bg-highlight rounded-lg px-10 py-7">
      <Text className="font-text text-base-00 mb-3.5 text-center text-sm">{title}</Text>
      <div className="border-progress-frame mb-3 h-3 w-full rounded-3xl border-2">
        <div className="bg-progress-frame h-full rounded-3xl" style={{ width: `${progress}%` }} />
      </div>
      <Text className="font-text text-content-extra-lite text-center text-xs">{progress}%</Text>
    </div>
  );
}

'use client';

import { ErrorComponent } from '@iprox/react-ui';
import { useEffect } from 'react';

import { ContentWrapper } from '@/components/content-wrapper';

export default function Error({ error, reset }: { error: Error & { digest?: string }; reset: () => void }) {
  useEffect(() => {
    console.error('error', error);
  }, [error]);

  return (
    <ContentWrapper>
      <ErrorComponent error={error} reset={reset} serviceDeskLink={process.env.NEXT_PUBLIC_SERVICE_DESK_URL ?? ''} />
    </ContentWrapper>
  );
}

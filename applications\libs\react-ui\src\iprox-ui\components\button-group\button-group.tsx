import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'next-intl';
import { useEffect, useId, useRef, useState } from 'react';

interface Option extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  text: string;
}

interface ButtonGroupProps {
  label: string;
  options: Option[];
  disabled?: boolean;
}

export function ButtonGroup({ label, options, disabled }: ButtonGroupProps) {
  const t = useTranslations('components.buttonGroup');

  const [isOpen, setIsOpen] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div ref={dropdownRef} className="font-heading relative w-[220px] font-bold">
      <button
        id={useId()}
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`rounded-medium bg-base-00 flex min-h-[50px] w-full flex-row items-center justify-between border px-[30px] py-[10px] text-lg ${
          disabled ? 'border-content-extra-lite text-content-extra-lite' : 'border-highlight text-primary-content'
        }`}
        aria-label={t('openDropdown')}
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-controls="dropdown-menu"
        disabled={disabled}
      >
        {label}
        {isOpen ? <ChevronUpIcon className="ml-auto h-4 w-4" /> : <ChevronDownIcon className="ml-auto h-4 w-4" />}
      </button>
      <div
        id="dropdown-menu"
        className={`border-highlight rounded-medium bg-base-00 absolute z-50 mt-1 w-full overflow-hidden border ${
          !isOpen || !options.length ? 'hidden' : ''
        }`}
      >
        {options.map((option, index) => {
          const { text, ...rest } = option;
          return (
            <button
              key={index}
              className={`focus:border-highlight focus:rounded-medium hover:rounded-medium w-full px-[30px] py-[10px] text-left text-base focus:border-y focus:outline-none focus:first:border-t-0 focus:last:border-b-0 ${
                option.disabled
                  ? 'text-content-extra-lite'
                  : 'text-primary-content hover:bg-highlight hover:text-base-00'
              }`}
              {...rest}
              onClick={(event) => {
                if (option.onClick) {
                  option.onClick(event);
                }
                setIsOpen(false);
              }}
            >
              {text}
            </button>
          );
        })}
      </div>
    </div>
  );
}

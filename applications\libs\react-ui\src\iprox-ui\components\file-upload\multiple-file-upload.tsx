import cx from 'classnames';
import { useTranslations } from 'next-intl';
import React, { useRef, useState } from 'react';

import { Text } from '../text/text';
import CloudUploadIcon from './cloud-upload.icon';

export interface FileWithDisallowed extends File {
  disallowed?: boolean;
}

interface MultipleFileUploadProps {
  allowedFileExtensions?: string[];
  maxFileCount?: number;
  onFilesSelect: (files: FileWithDisallowed[]) => void;
}

export function MultipleFileUpload({ allowedFileExtensions, maxFileCount, onFilesSelect }: MultipleFileUploadProps) {
  const t = useTranslations('components.fileUpload');
  const [isDragging, setDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setDragging(false);

    const files = event.dataTransfer.files;
    if (files.length) {
      handleFileSelect(files);
    }
  };

  const handleOnChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files?.length) {
      handleFileSelect(files);
    }
  };

  const isFileAllowed = (fileName: string): boolean => {
    const fileExtension = `*.${fileName.split('.').pop()}`;
    return allowedFileExtensions?.includes(fileExtension) || false;
  };

  const handleDisallowedFileTypes = (files: FileList) => {
    return Array.from(files).map((file) => {
      return Object.assign(file, {
        disallowed: !isFileAllowed(file.name),
      });
    });
  };

  const handleFileSelect = (files: FileList) => {
    if (maxFileCount && files.length > maxFileCount) {
      alert(
        t('maxFileCountWarning', {
          maxFileCount,
        })
      );
      return;
    }

    const filesWithDisallowed = handleDisallowedFileTypes(files);
    onFilesSelect(filesWithDisallowed);
  };

  const handleIconClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div
      className={cx(
        'rounded-input border-highlight bg-secondary-extra-light after:bg-base-100 after:z-9 relative flex items-center justify-center border border-dashed px-6 py-6 after:pointer-events-none after:absolute after:inset-0 after:h-full after:w-full after:rounded-lg after:opacity-0 after:transition-all after:duration-300 after:ease-in-out',
        { 'after:opacity-75': isDragging }
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      aria-label="File drop area"
      role="group"
    >
      <div>
        <div className="px-16 py-7">
          <input
            type="file"
            multiple
            ref={fileInputRef}
            className="hidden"
            onChange={handleOnChange}
            aria-label="File input"
          />
          <div className="flex justify-center">
            <CloudUploadIcon className="text-content-lite h-14 w-14 text-center" />
          </div>
          <Text className="font-heading text-heading text-center text-base font-bold">
            {t('dropFileHere')}
            <br />
            <span
              className="cursor-pointer underline"
              onClick={handleIconClick}
              role="button"
              tabIndex={0}
              onKeyDown={(event) => {
                if (event.key === 'Enter') {
                  handleIconClick();
                }
              }}
            >
              {t('clickHereToBrowse')}
            </span>
          </Text>
        </div>
      </div>
    </div>
  );
}

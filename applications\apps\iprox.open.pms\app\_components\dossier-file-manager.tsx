import { ALLOWED_ZIP_FILE_TYPES } from '@/config/allowed-file-types';
import { UploadState, useDossierUploadService } from '@/context/dossier-upload-context';
import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { getDossierLatestVersion } from '@/services/dossier-service.client';
import {
  createFolder,
  deleteSelectedNodes,
  downloadFile,
  getNodeChildren,
  renameSelectedNode,
} from '@/services/file-structure-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { saveFile } from '@/utils/save-file';
import { XCircleIcon } from '@heroicons/react/24/outline';
import { Button, FileUpload, Modal, showToast, useConfirmDialog } from '@iprox/iprox-ui';
import {
  FileNode,
  FileStructure,
  FolderNode,
  Node,
  Spinner,
  Text,
  mapFolderNode,
  mapNode,
  removeNodesById,
  updateNodeAndChildren,
} from '@iprox/react-ui';
import { useAppSettings } from '@iprox/shared-context';
import { useTranslations } from 'next-intl';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { AddButtoModalContent } from './add-button-modal-content';
import { BlockedFilesList } from './blocked-files-list';
import { CreateFolderForm } from './create-folder-form';
import { FileUploadItem } from './file-upload-item';
import { FileUploader } from './file-uploader';
import styles from './styles/file-uploader.module.scss';

interface DossierFileManagerProps {
  dossierId: string;
  dossierName: string;
  apiRootNode: components['schemas']['DossierFileStructureViewDto'];
}

export function DossierFileManager({ dossierId, dossierName, apiRootNode }: DossierFileManagerProps) {
  const t = useTranslations('dossier');

  const clientApi = useClientApi();
  const { apiUrl } = useAppSettings();
  const { showDialog } = useConfirmDialog();

  const { dossierUploads, uploadZip, resetNotUploadedFiles, startStopProcess, clearUpload } =
    useDossierUploadService(dossierId);

  // Note(Frank): Why are we only tracking progress of the first item?
  const zipUploadingItem =
    dossierUploads
      .map((uploadItem) => {
        const uploadId = Object.keys(uploadItem)[0];
        return { ...uploadItem[uploadId], uploadId };
      })
      .find((uploadItem) => uploadItem.isZipUpload === true) ?? ({} as UploadState & { uploadId: string });

  const { status, fileName, fileSize, progress, uploadResponse, estimated, loaded, uploadId, error } = zipUploadingItem;

  const [shouldUpdateRootNode, setShouldUpdateRootNode] = useState(false);

  const blockedFiles = uploadResponse?.notUploadedFiles ?? [];

  const [rootFolderNode, setRootFolderNode] = useState<FolderNode | null>(mapFolderNode(apiRootNode));

  const [isAddZipEnabled, setIsAddZipEnabled] = useState<boolean>(false);
  const [selectedNodes, setSelectedNodes] = useState<Node[]>([]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [modalContentTypes, setModalContentType] = useState<'addFolder' | 'uploadFiles' | 'combinedModal'>(
    'combinedModal'
  );
  const [clickedFolderNode, setClickedFolderNode] = useState<FolderNode | undefined>();
  const [createLoading, setCreateLoading] = useState(false);

  const [uniqueUploads, setUniqueUploads] = useState(new Set<UploadState>());

  const updateRoot = useCallback(async () => {
    try {
      const response = await getDossierLatestVersion(clientApi, dossierId);
      if (response.dossier.rootFolderNode) {
        setRootFolderNode(mapFolderNode(response.dossier.rootFolderNode));
      }
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    }
  }, [clientApi, dossierId]);

  const calculateNewUploadCount = useCallback(() => {
    const successfulFileUploads = dossierUploads
      .flatMap((upload) => Object.values(upload))
      .filter((uploadState) => {
        return !uploadState.error && !uploadState.isZipUpload && !uniqueUploads.has(uploadState);
      });

    return successfulFileUploads;
  }, [dossierUploads, uniqueUploads]);

  const addUniqueUpload = useCallback((successfulFileUploads: UploadState[]) => {
    // Add the dossierId of the new uploads to the uniqueUploads set
    successfulFileUploads.forEach((uploadState) => {
      setUniqueUploads((prevState) => new Set(prevState.add(uploadState)));
    });
  }, []);

  /**
   * get the children from the api call and update the root node
   * @param item FolderNode
   */
  const handleOnLoadChildren = useCallback(
    async (item: FolderNode): Promise<void> => {
      let successfulFileUploads = new Array<UploadState>();
      try {
        const response = await getNodeChildren(clientApi, item.nodeId);

        item.children = response.nodes.map<Node>((child) => mapNode(child, item));

        if (rootFolderNode) {
          successfulFileUploads = calculateNewUploadCount();
          const updatedRootNode = updateNodeAndChildren(rootFolderNode, item.nodeId, item) as FolderNode;
          if (successfulFileUploads) {
            updatedRootNode.numberOfDescendantFiles =
              updatedRootNode.numberOfDescendantFiles + successfulFileUploads.length;
          }
          setRootFolderNode(updatedRootNode);
        }
      } catch (error) {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      }
      addUniqueUpload(successfulFileUploads);
    },
    [addUniqueUpload, calculateNewUploadCount, clientApi, rootFolderNode]
  );

  /**
   * toggle folder node which already got children from api call
   * @param item FolderNode
   */
  const handleOnToggleFolderNode = useCallback(
    (item: FolderNode) => {
      const updatedNode: FolderNode = {
        ...item,
        isExpanded: !item.isExpanded,
      };
      let successfulFileUploads = new Array<UploadState>();

      if (rootFolderNode) {
        const updatedRootNode = updateNodeAndChildren(rootFolderNode, item.nodeId, updatedNode) as FolderNode;
        successfulFileUploads = calculateNewUploadCount();
        if (successfulFileUploads) {
          updatedRootNode.numberOfDescendantFiles =
            updatedRootNode.numberOfDescendantFiles + successfulFileUploads.length;
        }
        setRootFolderNode(updatedRootNode);
      }
      addUniqueUpload(successfulFileUploads);
    },
    [addUniqueUpload, calculateNewUploadCount, rootFolderNode]
  );

  const handleCreateFolderSubmit = useCallback(
    async (folderName: string) => {
      if (clickedFolderNode) {
        setCreateLoading(true);
        try {
          await createFolder(clientApi, clickedFolderNode.nodeId, { name: folderName });

          const isRootFolder = clickedFolderNode.nodeId === rootFolderNode?.nodeId;

          showToast(
            t('folderCreated', {
              folderName,
              parentFolderName: isRootFolder ? 'root' : clickedFolderNode.nodeName,
            }),
            { type: 'success' }
          );
          setIsModalOpen(false);

          if (isRootFolder) {
            await updateRoot();
          } else {
            await handleOnLoadChildren(clickedFolderNode);
            if (!clickedFolderNode.isExpanded) {
              handleOnToggleFolderNode(clickedFolderNode);
            }
          }
        } catch (error) {
          const errorMessages = await getErrorMessages(error);
          showToast(errorMessages, { type: 'error' });
        } finally {
          setCreateLoading(false);
        }
      }
    },
    [
      clientApi,
      handleOnLoadChildren,
      handleOnToggleFolderNode,
      rootFolderNode?.nodeId,
      clickedFolderNode,
      t,
      updateRoot,
    ]
  );

  /**
   * update the root node or expand the relevent folder after uploading the files
   */
  const handleOnFileUpload = useCallback(async () => {
    if (isModalOpen) {
      setLoadingFiles(false);
      setIsModalOpen(false);
    }

    if (clickedFolderNode) {
      const isRootFolder = clickedFolderNode?.nodeId === rootFolderNode?.nodeId;

      if (isRootFolder) {
        await updateRoot();
      } else {
        await handleOnLoadChildren(clickedFolderNode);
        if (!clickedFolderNode.isExpanded) {
          handleOnToggleFolderNode(clickedFolderNode);
        }
      }
    }
  }, [
    clickedFolderNode,
    handleOnLoadChildren,
    handleOnToggleFolderNode,
    isModalOpen,
    rootFolderNode?.nodeId,
    updateRoot,
  ]);

  /**
   * map the model content based on the modalContentTypes
   */
  const modalContent = useMemo(() => {
    switch (modalContentTypes) {
      case 'addFolder':
        return (
          <CreateFolderForm
            createLoading={createLoading}
            onSubmit={handleCreateFolderSubmit}
            onCancel={() => setIsModalOpen(false)}
          />
        );
      case 'uploadFiles':
        return (
          <FileUploader
            dossierId={dossierId}
            clickedFolderNodeId={clickedFolderNode?.nodeId}
            onUploadFiles={handleOnFileUpload}
            onClose={(isInProgress) => {
              if (isInProgress) {
                setIsModalOpen(false);
              }
              handleOnFileUpload();
            }}
            loading={loadingFiles}
          />
        );
      default:
        return (
          <AddButtoModalContent
            onAddFolder={() => setModalContentType('addFolder')}
            onUploadDocuments={() => {
              startStopProcess(false);
              setModalContentType('uploadFiles');
            }}
          />
        );
    }
  }, [
    modalContentTypes,
    createLoading,
    handleCreateFolderSubmit,
    dossierId,
    clickedFolderNode?.nodeId,
    handleOnFileUpload,
    loadingFiles,
    startStopProcess,
  ]);

  const handleAddFiles = () => {
    startStopProcess(true);
    setIsAddZipEnabled(true);
  };

  const handleUpload = (file: File) => {
    startStopProcess(true);
    setSelectedNodes([]);

    uploadZip(dossierName, file, () => {
      setIsAddZipEnabled(false);
    });
  };

  /**
   * handle selecting items from the file structure
   * @param updatedRoot FolderNode
   * @param items Node[]
   */
  const handleSelectItems = (updatedRoot: FolderNode, items: Node[]) => {
    setRootFolderNode(updatedRoot);
    setSelectedNodes(items);
  };

  /**
   * download clicked file
   * @param item FileNode
   */
  const downloadFileNode = async (item: FileNode) => {
    if (item.nodeName) {
      try {
        const response = await downloadFile(clientApi, item.nodeId);
        const url = `${apiUrl}/download/${response.token}`;

        saveFile(url);
      } catch (error) {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      }
    }
  };

  const handleAddButtonClick = (item: FolderNode) => {
    setClickedFolderNode(item);
    setModalContentType('combinedModal');
    setIsModalOpen(true);
  };

  /**
   * delete selected multiple nodes
   */
  const deleteSelectedItems = async () => {
    if (rootFolderNode && selectedNodes.length) {
      showDialog({
        message: t('confirmation.deleteSelectedNodes.message'),
        onConfirm: async () => {
          try {
            const response = await deleteSelectedNodes(clientApi, dossierId, {
              nodeIds: selectedNodes.map(({ nodeId }) => nodeId),
            });

            showToast(t('deleteSuccess'), { type: 'success' });

            const updatedRoot = removeNodesById(rootFolderNode, response.removedNodeIds);

            const { deletedFileNodesCount, deletedFolderNodesCount } = selectedNodes.reduce(
              (counts, node) => {
                if (node.nodeType === 'File') {
                  counts.deletedFileNodesCount++;
                } else {
                  counts.deletedFolderNodesCount += node.numberOfDescendantFiles;
                }
                return counts;
              },
              { deletedFileNodesCount: 0, deletedFolderNodesCount: 0 }
            );

            const deletedNodesCount = deletedFileNodesCount + deletedFolderNodesCount;

            updatedRoot.numberOfDescendantFiles = updatedRoot.numberOfDescendantFiles - deletedNodesCount;
            setRootFolderNode(updatedRoot.children.length ? { ...updatedRoot } : null);
          } catch (error) {
            const errorMessages = await getErrorMessages(error);
            showToast(errorMessages, { type: 'error' });
          } finally {
            setSelectedNodes([]);
          }
        },
      });
    }
  };

  const handleRenamingNode = async (item: Node, newName: string): Promise<boolean> => {
    try {
      await renameSelectedNode(clientApi, item.nodeId, { name: newName });
      showToast(t('renameSuccess', { nodeName: item.nodeName, newName }), { type: 'success' });
      return Promise.resolve(true);
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
      return Promise.reject();
    }
  };

  useEffect(() => {
    if (status === 'uploading') {
      setShouldUpdateRootNode(true);
    }
  }, [status]);

  useEffect(() => {
    if (status === 'uploadComplete' && shouldUpdateRootNode) {
      const rootNode = uploadResponse?.dossier.rootFolderNode;

      if (rootNode) {
        setRootFolderNode(mapFolderNode(rootNode));
      }

      setShouldUpdateRootNode(false);
      clearUpload(uploadId);
    }
  }, [uploadResponse, status, shouldUpdateRootNode, clearUpload, uploadId]);

  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (status === 'uploading') {
        event.preventDefault();
        event.returnValue = '';

        return '';
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [status]);

  return (
    <>
      <div className="mb-5">
        <div className="mb-2 flex flex-row items-center justify-between">
          <Text className="font-heading text-heading text-lg font-bold">{t('uploadFiles')}</Text>
          <div className="grid grid-flow-col justify-end gap-2">
            <Button
              type="button"
              variant="secondary"
              onClick={deleteSelectedItems}
              disabled={selectedNodes.length <= 0}
            >
              {t('delete')}
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={handleAddFiles}
              icon="ArchiveBoxIcon"
              iconPosition="left"
            >
              {t('zipUpload')}
            </Button>
          </div>
        </div>
        <div className="border-base-25 rounded-input relative flex h-[454px] items-center justify-center overflow-hidden border">
          {isAddZipEnabled ? (
            <button
              className="absolute right-0 top-0 z-10 mr-1 mt-1 cursor-pointer disabled:cursor-progress"
              onClick={() => setIsAddZipEnabled(false)}
              disabled={status === 'uploading'}
            >
              <XCircleIcon className="text-heading h-5 w-5" />
              <span className="sr-only">{t('closeZip')}</span>
            </button>
          ) : null}
          {status === 'uploading' || blockedFiles.length ? (
            <div className="flex h-full w-full flex-col items-center justify-center">
              {blockedFiles.length ? (
                <div className={`${styles['file-list-scroll-container']} mt-7 w-full pl-5 pr-1`}>
                  <BlockedFilesList
                    files={blockedFiles}
                    onOk={() => {
                      resetNotUploadedFiles(uploadId);
                    }}
                  />
                </div>
              ) : (
                <>
                  {progress < 100 ? (
                    <div className="mt-6 h-full w-full p-2">
                      <FileUploadItem
                        fileId={uploadId}
                        fileName={typeof fileName === 'string' ? fileName : ''}
                        fileSize={typeof fileSize === 'number' ? fileSize : 0}
                        progress={progress}
                        estimated={estimated}
                        loaded={loaded}
                        status={status}
                        error={error}
                      />
                    </div>
                  ) : (
                    <div>
                      <Spinner variant={'secondary'} message={t('finalizingUpload')} />
                    </div>
                  )}
                </>
              )}
            </div>
          ) : !isAddZipEnabled ? (
            <div className={`${styles['file-list-scroll-container']} h-full w-full p-3`}>
              <div className="h-full w-full overflow-y-auto p-3">
                {rootFolderNode && (
                  <FileStructure
                    rootNode={rootFolderNode}
                    onFileClick={downloadFileNode}
                    onLoadChildren={handleOnLoadChildren}
                    onToggleFolderNode={handleOnToggleFolderNode}
                    onSelectItem={handleSelectItems}
                    onAddButtonClick={handleAddButtonClick}
                    selectedNodes={selectedNodes}
                    setRootFolderNode={setRootFolderNode}
                    editableNodes={true}
                    onAddFolderButtonClick={() => {
                      setClickedFolderNode(rootFolderNode);
                      setModalContentType('addFolder');
                      setIsModalOpen(true);
                    }}
                    enableAddFolder={true}
                    onRenaming={handleRenamingNode}
                    enableUploadFiles={true}
                    onUploadFilesButtonClick={() => {
                      startStopProcess(false);
                      setClickedFolderNode(rootFolderNode);
                      setModalContentType('uploadFiles');
                      setIsModalOpen(true);
                    }}
                  />
                )}
              </div>
            </div>
          ) : (
            <FileUpload allowedFileTypes={ALLOWED_ZIP_FILE_TYPES} onFileSelect={handleUpload} />
          )}
        </div>
      </div>
      <Modal isOpen={isModalOpen} onClose={() => null}>
        {modalContent}
      </Modal>
    </>
  );
}

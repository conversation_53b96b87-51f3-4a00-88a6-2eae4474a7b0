import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { TextField } from './text-field';

const meta: Meta<typeof TextField> = {
  title: 'textfield',
  component: TextField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof TextField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'text-aria-field',
    fieldType: FieldType.Text,
  },
};

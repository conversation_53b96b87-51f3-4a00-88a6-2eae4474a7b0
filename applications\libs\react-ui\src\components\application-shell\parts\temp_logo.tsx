import * as React from 'react';

export const IproxOpenLogo = () => (
  <svg
    id="Layer_1"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 192.3 36.7"
    width={160}
    xmlSpace="preserve"
  >
    <style type="text/css">
      {
        '\n\t.st0{fill:#000033;}\n\t.st1{fill:#3399CC;}\n\t.st2{fill:none;stroke:#666685;stroke-linecap:round;stroke-linejoin:round;}\n\t.st3{fill:#666685;}\n\t.st4{fill:#676767;}\n\t.st5{fill:none;stroke:#FF9900;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;}\n\t.st6{fill:#A21D21;}\n\t.st7{fill:#FF9900;}\n\t.st8{fill:none;stroke:#FFFFFF;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;}\n'
      }
    </style>
    <g>
      <path
        className="st0"
        d="M0.9,5.3C0.3,4.7,0,3.9,0,3.1c0-0.8,0.3-1.6,0.9-2.2C1.6,0.3,2.3,0,3.2,0s1.6,0.3,2.2,0.9s0.9,1.3,0.9,2.2 C6.3,4,6,4.7,5.4,5.3C4.7,5.9,4,6.2,3.2,6.2C2.3,6.2,1.6,5.9,0.9,5.3z M0.3,8.6h5.8v20.6H0.3V8.6z"
      />
      <path
        className="st0"
        d="M26.3,9.4c1.4,0.8,2.5,1.9,3.4,3.6s1.3,3.6,1.3,6s-0.4,4.4-1.3,6c-0.9,1.6-2,2.8-3.4,3.6 c-1.4,0.8-2.8,1.2-4.4,1.2c-1.5,0-2.7-0.3-3.8-1s-1.8-1.4-2.2-2.4v10.5h-5.8V8.6h5.8v3c0.4-0.9,1.1-1.8,2.2-2.4 c1.1-0.6,2.4-1,3.8-1C23.5,8.2,25,8.6,26.3,9.4z M24.1,23.3c1-1,1.4-2.5,1.4-4.4c0-1.9-0.5-3.4-1.4-4.4c-1-1-2.1-1.5-3.5-1.5 c-1.4,0-2.5,0.5-3.5,1.5c-1,1-1.4,2.5-1.4,4.4s0.5,3.4,1.4,4.4s2.1,1.5,3.5,1.5C22,24.8,23.2,24.3,24.1,23.3z"
      />
      <path
        className="st0"
        d="M47.6,8.6v4.9c-0.6-0.1-1.2-0.1-1.6-0.1c-1.7,0-3.2,0.5-4.3,1.6s-1.7,2.8-1.7,5.1v9.1h-5.8V8.6h5.8v5H40 c0.4-1.6,1-2.8,2.1-3.8c1-0.9,2.3-1.4,3.9-1.4C46.5,8.4,47.1,8.5,47.6,8.6z"
      />
      <path
        className="st0"
        d="M53.9,28.4c-1.6-0.8-2.8-2-3.8-3.6c-1-1.6-1.4-3.6-1.4-6s0.5-4.4,1.4-6s2.2-2.8,3.8-3.6 c1.6-0.8,3.3-1.1,5.1-1.1s3.5,0.4,5.1,1.1c1.6,0.8,2.8,1.9,3.8,3.6s1.4,3.6,1.4,6s-0.5,4.4-1.4,6c-1,1.6-2.2,2.8-3.8,3.6 c-1.6,0.8-3.3,1.1-5.1,1.1C57.2,29.6,55.5,29.2,53.9,28.4z M62.6,23.3c0.9-1,1.3-2.5,1.3-4.4s-0.5-3.4-1.3-4.4S60.5,13,59,13 s-2.7,0.5-3.6,1.5s-1.3,2.5-1.3,4.4s0.5,3.4,1.3,4.4c0.9,1,2.1,1.5,3.6,1.5S61.7,24.3,62.6,23.3z"
      />
      <path
        className="st0"
        d="M76.8,18.8L70.1,8.6h6.2l3.8,6l3.8-6H90l-6.7,10.2l7.1,10.4h-6.2l-4.2-6.2l-4.2,6.2h-6.2L76.8,18.8z"
      />
      <path
        className="st0"
        d="M94,28.6c-0.7-0.7-1-1.5-1-2.4s0.3-1.8,1-2.4s1.5-1,2.4-1c1,0,1.8,0.3,2.5,1c0.7,0.7,1,1.5,1,2.4 c0,1-0.3,1.8-1,2.5c-0.7,0.7-1.5,1-2.5,1C95.5,29.6,94.7,29.2,94,28.6z"
      />
      <path
        className="st1"
        d="M107.8,28.4c-1.6-0.8-2.8-2-3.8-3.6c-1-1.6-1.4-3.6-1.4-6s0.5-4.4,1.4-6s2.2-2.8,3.8-3.6 c1.6-0.8,3.3-1.1,5.1-1.1s3.5,0.4,5.1,1.1c1.6,0.8,2.8,1.9,3.8,3.6s1.4,3.6,1.4,6s-0.5,4.4-1.4,6c-1,1.6-2.2,2.8-3.8,3.6 c-1.6,0.8-3.3,1.1-5.1,1.1C111,29.6,109.3,29.2,107.8,28.4z M116.4,23.3c0.9-1,1.3-2.5,1.3-4.4s-0.4-3.4-1.3-4.4s-2.1-1.5-3.6-1.5 c-1.5,0-2.7,0.5-3.6,1.5c-0.9,1-1.3,2.5-1.3,4.4s0.4,3.4,1.3,4.4c0.9,1,2.1,1.5,3.6,1.5C114.3,24.8,115.5,24.3,116.4,23.3z"
      />
      <path
        className="st1"
        d="M142.4,9.4c1.4,0.8,2.5,1.9,3.4,3.6s1.3,3.6,1.3,6s-0.4,4.4-1.3,6c-0.9,1.6-2,2.8-3.4,3.6 c-1.4,0.8-2.8,1.2-4.4,1.2c-1.4,0-2.7-0.3-3.8-1s-1.8-1.4-2.2-2.4v10.5h-5.8V8.6h5.8v3c0.4-0.9,1.1-1.8,2.2-2.4 c1.1-0.6,2.4-1,3.8-1C139.6,8.2,141.1,8.6,142.4,9.4z M140.2,23.3c0.9-1,1.4-2.5,1.4-4.4c0-1.9-0.5-3.4-1.4-4.4 c-0.9-1-2.1-1.5-3.5-1.5c-1.4,0-2.5,0.5-3.5,1.5s-1.4,2.5-1.4,4.4s0.5,3.4,1.4,4.4s2.1,1.5,3.5,1.5 C138.1,24.8,139.3,24.3,140.2,23.3z"
      />
      <path
        className="st1"
        d="M169.4,20.4h-14.5c0.2,1.8,0.7,3,1.6,3.8s2.1,1.2,3.6,1.2c1.2,0,2.1-0.2,2.7-0.7c0.6-0.5,1.1-1.1,1.3-2.1h5.1 c-0.5,2.3-1.6,4.1-3.2,5.3s-3.7,1.8-6.2,1.8c-1.9,0-3.6-0.4-5.1-1.1c-1.6-0.7-2.8-1.9-3.8-3.5c-1-1.6-1.4-3.6-1.4-6.1 c0-2.5,0.5-4.5,1.4-6.1c0.9-1.6,2.2-2.8,3.7-3.5c1.5-0.8,3.3-1.1,5.2-1.1c2.9,0,5.2,0.9,7.1,2.6c1.8,1.8,2.7,4.5,2.7,8.2 C169.5,19.5,169.5,19.9,169.4,20.4z M164.1,17.2c-0.1-1.4-0.4-2.5-1.1-3.4c-0.7-0.8-1.8-1.3-3.3-1.3c-1.4,0-2.5,0.4-3.3,1.2 c-0.8,0.8-1.3,1.9-1.4,3.5L164.1,17.2L164.1,17.2z"
      />
      <path
        className="st1"
        d="M190.3,10.2c1.3,1.4,2,3.3,2,5.9v13.1h-5.8V17.5c0-3-1.3-4.5-3.8-4.5c-1.3,0-2.3,0.4-3.1,1.1 c-0.8,0.8-1.2,1.9-1.2,3.4v11.7h-5.8V8.6h5.8v4.3h0.2c0.1-0.7,0.5-1.4,1-2.2c0.5-0.7,1.2-1.3,2.2-1.8c0.9-0.5,2.1-0.7,3.4-0.7 C187.3,8.2,189,8.9,190.3,10.2z"
      />
    </g>
    <text
      transform="matrix(1 0 0 1 -1.7788 -66.585)"
      className="st0"
      style={{
        fontFamily: "'ArticulatCF-Bold'",
        fontSize: '39.5332px',
      }}
    >
      {'iprox.'}
    </text>
    <text
      transform="matrix(1 0 0 1 103.8512 -66.585)"
      className="st1"
      style={{
        fontFamily: "'ArticulatCF-Bold'",
        fontSize: '39.5332px',
      }}
    >
      {'open'}
    </text>
    <text
      transform="matrix(1 0 0 1 196.5512 -66.585)"
      className="st0"
      style={{
        fontFamily: "'ArticulatCF-Bold'",
        fontSize: '39.5332px',
      }}
    />
    <text
      transform="matrix(1 0 0 1 205.6512 -66.585)"
      style={{
        fill: '#808285',
        fontFamily: "'ArticulatCF-Bold'",
        fontSize: '39.5332px',
      }}
    >
      {'| demo'}
    </text>
    <path className="st2" d="M-151.1-229.5v-3.4h-6.2v12.7h9.5v-9.2H-151.1z" />
    <path className="st2" d="M-147.9-229.7l-3.2-3.2v3.2H-147.9z" />
    <polygon
      className="st2"
      points="-136.7,-230.4 -139.2,-232.9 -143.9,-232.9 -143.9,-230.4 -143.9,-228.7 -143.9,-220.2 -130.2,-220.2  -130.2,-230.4 "
    />
    <line className="st2" x1={-135.6} y1={-230.4} x2={-143.9} y2={-230.4} />
    <path
      className="st3"
      d="M-113.6-228.3l1.6-1.6c0.6-0.6,0.6-1.6,0-2.1c-0.6-0.6-1.6-0.6-2.1,0l-1.6,1.6L-113.6-228.3z"
    />
    <polygon className="st3" points="-116.4,-229.8 -123.1,-223 -123.1,-220.8 -120.9,-220.9 -114.2,-227.6 " />
    <path
      className="st2"
      d="M-162.6-226.8c0-0.2,0-0.3,0-0.5l1.3-1.4l-1.3-2.3l-1.8,0.4c-0.3-0.3-0.7-0.5-1.1-0.6l-0.5-1.8h-2.6l-0.5,1.8 c-0.4,0.2-0.7,0.4-1.1,0.6l-1.8-0.4l-1.3,2.3l1.3,1.4c0,0.2,0,0.3,0,0.5c0,0.2,0,0.4,0.1,0.6l-1.4,1.4l1.3,2.3l2-0.5 c0.3,0.2,0.5,0.4,0.8,0.5l0.6,2h2.6l0.6-2c0.3-0.1,0.6-0.3,0.8-0.5l2,0.5l1.3-2.3l-1.4-1.4C-162.6-226.3-162.6-226.5-162.6-226.8z"
    />
    <circle className="st2" cx={-167.4} cy={-226.7} r={2} />
    <path
      className="st2"
      d="M-192.5-221.9c0,1,3.3,1.9,7.3,1.9c4,0,7.3-0.8,7.3-1.9v-15.1c0-1-3.3-1.9-7.3-1.9c-4,0-7.3,0.8-7.3,1.9V-221.9 z"
    />
    <path className="st2" d="M-177.9-236.9c0,1-3.3,1.9-7.3,1.9c-4,0-7.3-0.8-7.3-1.9" />
    <path className="st2" d="M-177.9-232c0,1-3.3,1.9-7.3,1.9c-4,0-7.3-0.8-7.3-1.9" />
    <path className="st2" d="M-177.9-226.9c0,1-3.3,1.9-7.3,1.9c-4,0-7.3-0.8-7.3-1.9" />
    <path className="st2" d="M-192.5-260.2v15.1" />
    <path className="st2" d="M-177.9-245.1v-15.1" />
    <path className="st2" d="M-177.9-255.2c0,1-3.3,1.9-7.3,1.9c-4,0-7.3-0.8-7.3-1.9" />
    <path
      className="st3"
      d="M-170.9-257.7c0-0.1,0.1-0.2,0.2-0.1c0.8,0.8,3.6,1.4,7.1,1.4s6.3-0.6,7.1-1.4c0.1-0.1,0.2,0,0.2,0.1v3.6 c0,1-3.3,1.9-7.3,1.9s-7.3-0.8-7.3-1.9V-257.7z"
    />
    <path
      className="st3"
      d="M-170.9-262.8c0-0.1,0.1-0.2,0.2-0.1c0.8,0.8,3.6,1.4,7.1,1.4s6.3-0.6,7.1-1.4c0.1-0.1,0.2,0,0.2,0.1v3.6 c0,1-3.3,1.9-7.3,1.9s-7.3-0.8-7.3-1.9V-262.8z"
    />
    <path
      className="st3"
      d="M-170.9-252.3c0-0.1,0.1-0.2,0.2-0.1c0.8,0.8,3.6,1.4,7.1,1.4s6.3-0.6,7.1-1.4c0.1-0.1,0.2,0,0.2,0.1v3.6 c0,1-3.3,1.9-7.3,1.9s-7.3-0.8-7.3-1.9V-252.3z"
    />
    <path
      className="st2"
      d="M-207.6-234.8c0-1.9-1.1-3.4-2.7-4.1v2.9l-1.8,1.6l-1.8-1.6v-2.9c-1.6,0.7-2.7,2.3-2.7,4.1s1.1,3.4,2.7,4.1v8.7 c0,1,0.8,1.8,1.8,1.8s1.8-0.8,1.8-1.8v-8.7C-208.8-231.4-207.6-232.9-207.6-234.8z"
    />
    <path
      className="st3"
      d="M-220.6-234.8c0-1.7-1-3.2-2.4-4c-0.1-0.1-0.3,0-0.3,0.2l0,2.4c0,0.1,0,0.2-0.1,0.2l-1.5,1.3 c-0.1,0.1-0.3,0.1-0.4,0l-1.5-1.3c-0.1-0.1-0.1-0.1-0.1-0.2v-2.4c0-0.2-0.2-0.3-0.3-0.2c-1.4,0.8-2.4,2.3-2.4,4 c0,1.9,1.1,3.4,2.7,4.1v8.6c0,0.9,0.6,1.7,1.5,1.8c1.1,0.2,2.1-0.7,2.1-1.7v-8.7C-221.7-231.4-220.6-232.9-220.6-234.8z"
    />
    <ellipse className="st3" cx={-163.6} cy={-264.1} rx={6.8} ry={1.6} />
    <circle className="st3" cx={-138.8} cy={-260.5} r={5.6} />
    <path
      className="st3"
      d="M-138.3-254h-1c-4.7,0-8.5,3.8-8.5,8.5v1.2c0,1,0.8,1.8,1.8,1.8h14.3c1,0,1.8-0.8,1.8-1.8v-1.2 C-129.8-250.2-133.6-254-138.3-254z"
    />
    <path
      className="st4"
      d="M-119-241.5c0-0.3-0.1-0.6-0.1-0.9l2.5-2.6l-2.4-4.3l-3.3,0.8c-0.6-0.5-1.2-0.9-1.9-1.2l-1-3.3h-4.8l-1,3.3 c-0.7,0.3-1.3,0.7-1.9,1.2l-3.3-0.8l-2.4,4.3l2.5,2.6c0,0.3-0.1,0.6-0.1,0.9c0,0.4,0.1,0.8,0.1,1.1l-2.5,2.7l2.4,4.3l3.6-0.9 c0.5,0.4,1,0.6,1.6,0.9l1.1,3.6h4.8l1.1-3.6c0.5-0.2,1.1-0.6,1.6-0.9l3.6,0.9l2.4-4.3l-2.5-2.7C-119-240.7-119-241.1-119-241.5z  M-127.7-237.6c-2,0-3.7-1.7-3.7-3.7c0-2.1,1.6-3.7,3.7-3.7c2,0,3.7,1.7,3.7,3.7C-124-239.2-125.6-237.6-127.7-237.6z"
    />
    <path className="st5" d="M-89.6-259.5v-0.7c0-0.6,0.4-1,1-1h17.5c0.6,0,1,0.4,1,1v11.1c0,0.6-0.4,1-1,1h-6.7" />
    <circle className="st6" cx={-94} cy={-243.7} r={1.8} />
    <g>
      <path className="st5" d="M-87.6-248c0-1.1-0.9-2-2-2" />
      <path className="st5" d="M-84.5-248c0-2.8-2.3-5.1-5.1-5.1" />
      <path className="st5" d="M-89.6-256.2c4.5,0,8.2,3.7,8.2,8.2" />
    </g>
    <path
      className="st7"
      d="M-59.5-259.5v-0.7c0-0.6,0.4-1,1-1H-41c0.6,0,1,0.4,1,1v11.1c0,0.6-0.4,1-1,1h-6.7h-10.7 c-0.6,0-1.1-0.5-1.1-1.1V-259.5z"
    />
    <g>
      <path className="st8" d="M-57.5-248.2c0-1.1-0.9-2-2-2" />
      <path className="st8" d="M-54.3-248.2c0-2.8-2.3-5.1-5.1-5.1" />
      <path className="st8" d="M-59.5-256.4c4.5,0,8.2,3.7,8.2,8.2" />
    </g>
    <path
      className="st7"
      d="M-11.3-260.5h-18.6c-0.3,0-0.5,0.2-0.5,0.5v4.6c0,0.3,0.2,0.5,0.5,0.5h18.6c0.3,0,0.5-0.2,0.5-0.5v-4.6 C-10.8-260.3-11.1-260.5-11.3-260.5z M-27.4-256.3c-0.8,0-1.4-0.6-1.4-1.4c0-0.8,0.6-1.4,1.4-1.4s1.4,0.6,1.4,1.4 C-26-256.9-26.6-256.3-27.4-256.3z M-12.3-257.5c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0-0.1,0-0.2,0H-19c-0.1,0-0.1,0-0.2,0 c-0.1-0.1-0.2-0.2-0.3-0.3c0-0.1,0-0.1,0-0.2c0-0.3,0.2-0.5,0.5-0.5h6.3c0.3,0,0.5,0.2,0.5,0.5C-12.2-257.6-12.3-257.5-12.3-257.5z"
    />
    <path
      className="st7"
      d="M-11.3-254.1h-18.6c-0.3,0-0.5,0.2-0.5,0.5v4.6c0,0.3,0.2,0.5,0.5,0.5h18.6c0.3,0,0.5-0.2,0.5-0.5v-4.6 C-10.8-253.8-11.1-254.1-11.3-254.1z M-27.4-249.8c-0.8,0-1.4-0.6-1.4-1.4c0-0.8,0.6-1.4,1.4-1.4s1.4,0.6,1.4,1.4 C-26-250.5-26.6-249.8-27.4-249.8z M-12.2-251.2c0,0.1,0,0.1,0,0.2c-0.1,0.2-0.3,0.3-0.5,0.3H-19c-0.2,0-0.4-0.1-0.5-0.3 c0-0.1,0-0.1,0-0.2v0c0-0.3,0.2-0.5,0.5-0.5h6.3C-12.5-251.8-12.2-251.5-12.2-251.2L-12.2-251.2z"
    />
  </svg>
);

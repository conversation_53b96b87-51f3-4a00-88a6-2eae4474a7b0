//@ts-check

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');

const nextIntl = 'next-intl/plugin';
const i18nPath = './i18n.ts';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const withNextIntl = require(nextIntl)(i18nPath);

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  experimental: {
    esmExternals: true,
    instrumentationHook: true,
  },
  typescript: {
    tsconfigPath: process.env.NODE_ENV === 'production' ? './tsconfig.prod.json' : './tsconfig.json',
  },
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: '/dashboard',
        permanent: true,
      },
    ];
  },
  output: 'standalone',
  images: {
    domains: [process.env.IPROX_OPEN_API_URL ? new URL(process.env.IPROX_OPEN_API_URL).hostname : '127.0.0.1'],
  },
  webpack: (config, { isServer }) => {
    // Ignore warnings related to 'applicationinsights' and 'instrumentation.js'
    config.module.rules.push({
      test: /instrumentation\.js$/,
      parser: { requireEnsure: false },
    });

    if (isServer) {
      config.ignoreWarnings = [{ module: /applicationinsights/ }, { module: /instrumentation\.js/ }];
    }

    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
  withNextIntl,
];

module.exports = composePlugins(...plugins)(nextConfig);

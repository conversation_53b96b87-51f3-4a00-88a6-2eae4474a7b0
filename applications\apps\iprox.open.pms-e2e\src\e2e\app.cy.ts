import { getGreeting } from '../support/app.po';

describe('iprox.open.pms', () => {
  beforeEach(() => cy.visit('/dashboard'));

  it('should display welcome message', () => {
    // Custom command example, see `../support/commands.ts` file
    cy.login('<EMAIL>', 'myPassword');

    // Function helper example, see `../support/app.po.ts` file
    getGreeting().contains('Welcome iprox.open.pms');
  });
});

import { type Locator, type Page, expect } from '@playwright/test';

import { userFullname } from '../../../.auth/user';

export class Menu {
  readonly page: Page;
  readonly dashboardMenuButton: Locator;
  readonly dossierListButton: Locator;
  readonly settingsButton: Locator;
  readonly settingsHomePageButton: Locator;
  private userNameDropdown: Locator;
  private logoutButton: Locator;
  private logoutConfirmButton: Locator;
  private beheerMenuButton: Locator;
  private pagesMenuButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.dashboardMenuButton = page.getByRole('link', { name: 'Dashboard' });
    this.dossierListButton = page.getByRole('button', { name: 'Dossiers' });
    this.beheerMenuButton = page.getByRole('link', { name: 'Beheer' });
  }

  async clickDashboardMenuButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.dashboardMenuButton.click();
  }
  async clickDossierListButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.dashboardMenuButton.click();
  }
  async clickSettingsButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.dashboardMenuButton.click();
  }
  async clickLogoutButton() {
    await this.page.waitForLoadState('domcontentloaded');
    this.userNameDropdown = this.page.locator(`//span[contains(text(),'${userFullname}')]`, { hasNotText: 'Welkom' });
    await this.userNameDropdown.waitFor({ state: 'attached' });
    await expect(this.userNameDropdown).toBeVisible();
    await this.userNameDropdown.click();
    await this.page.waitForTimeout(2000);
    this.logoutButton = this.page.getByRole('menuitem', { name: 'Logout' });
    await expect(this.logoutButton).toBeVisible();
    await this.logoutButton.click();
    this.logoutConfirmButton = this.page.getByRole('button', { name: 'Uitloggen' });
    await expect(this.logoutConfirmButton).toBeVisible();
    await this.logoutConfirmButton.click();
    await this.page.waitForTimeout(2000);
    await expect(this.page).toHaveURL(/login/);
  }
  async clickBeheerMenuButton() {
    await this.page.waitForLoadState('domcontentloaded');
    await this.beheerMenuButton.click();
  }
  async clickPagesMenuButton() {
    await this.page.waitForLoadState('domcontentloaded');
    this.pagesMenuButton = this.page.getByRole('link', { name: "Pagina's" });
    await this.pagesMenuButton.click();
  }
}

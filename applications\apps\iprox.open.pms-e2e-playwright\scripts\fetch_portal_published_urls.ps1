param (
  [string]$tenant = "woo"
)

Write-Host $tenant

$outputFile = "data/portalUrls.ts"

# Define the URLs for each tenant
$tenantUrls = @{
  woo = @{
    apiEndpoint      = "https://api.iprox-open.nl"
    getUrlsEndpoint = "/api/v1/public/page/list"
    basePortalUrl   = "https://portaal.iprox-open.nl"
  }
  wsl = @{
    apiEndpoint      = "https://api-waterschaplimburg.iprox-open.nl"
    getUrlsEndpoint = "/api/v1/public/page/list"
    basePortalUrl   = "https://open.waterschaplimburg.nl"
  }
  wve = @{
    apiEndpoint      = "https://api-vechtstromen.iprox-open.nl"
    getUrlsEndpoint = "/api/v1/public/page/list"
    basePortalUrl   = "https://open.vechtstromen.nl"
  }
  pnb = @{
    apiEndpoint      = "https://api-brabant.iprox-open.nl"
    getUrlsEndpoint = "/api/v1/public/page/list"
    basePortalUrl   = "https://open.brabant.nl"
  }
  pdr = @{
    apiEndpoint      = "https://api-drenthe.iprox-open.nl"
    getUrlsEndpoint = "/api/v1/public/page/list"
    basePortalUrl   = "https://portal-drenthe.iprox-open.nl"
  }
  vmb = @{
    apiEndpoint      = "https://api-vrmwb.iprox-open.nl"
    getUrlsEndpoint = "/api/v1/public/page/list"
    basePortalUrl   = "https://portal-vrmwb.iprox-open.nl"
  }
}

# Get the URLs for the selected tenant
$apiEndpoint = $tenantUrls[$tenant].apiEndpoint
$getUrlsEndpoint = $tenantUrls[$tenant].getUrlsEndpoint
$basePortalUrl = $tenantUrls[$tenant].basePortalUrl

$response = Invoke-RestMethod -Uri "$apiEndpoint$getUrlsEndpoint" -Method Get

if ($?) {
  # Parse the JSON response and extract the slugs
  $urls = $response.pageList.slug

  # Open the file for writing
  $file = [System.IO.StreamWriter]::new($outputFile, $false, [System.Text.Encoding]::UTF8)

  try {
    # Write the header to the output file
    $file.WriteLine("// The portal published URLs you want to test for accessibility.")
    $file.WriteLine("// This file is auto generated.")
    $file.WriteLine("export const tenant = '$tenant';")
    $file.WriteLine("")
    $file.WriteLine("export const portalUrls = [")

    # Build the full URLs and append them to the file
    foreach ($slug in $urls) {
      $file.WriteLine("  '$basePortalUrl/$slug',")
    }

    # Close the array declaration in the file
    $file.WriteLine("];")

    Write-Output "URLs have been successfully written to $outputFile"
  } finally {
    # Ensure the file is closed even if an error occurs
    $file.Close()
  }
} else {
  Write-Output "Failed to fetch data from the API."
  exit 1
}

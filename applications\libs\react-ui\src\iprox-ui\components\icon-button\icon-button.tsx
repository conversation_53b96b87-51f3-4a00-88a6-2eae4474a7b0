import cx from 'classnames';
import { FC, ReactNode, useMemo } from 'react';

interface IconButtonProps {
  className?: string;
  title?: string;
  disabled?: boolean;
  'aria-label'?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'highlight' | 'default' | 'base-light';
  children: ReactNode;
  onClick?: () => void;
}

export const IconButton: FC<IconButtonProps> = ({
  children,
  size = 'md',
  color = 'default',
  disabled,
  className,
  title,
  'aria-label': ariaLabel,
  onClick,
}) => {
  const sizeClass = useMemo(() => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'md':
        return 'h-6 w-6';
      case 'lg':
        return 'h-8 w-8';
      case 'xl':
        return 'h-10 w-10';
    }
  }, [size]);

  const colorClass = useMemo(() => {
    if (color === 'base-light') {
      return disabled ? 'text-base-35' : 'text-base-00';
    }

    if (color === 'default') {
      return disabled ? 'text-content-extra-lite' : 'text-secondary-medium';
    }

    if (disabled) {
      return `text-${color}-lighter`;
    }

    return `text-${color}`;
  }, [color, disabled]);

  const hoverColorClass = useMemo(() => {
    if (color === 'base-light') {
      return disabled ? '' : 'hover:bg-base-35';
    }

    return 'hover:bg-base-10';
  }, [color, disabled]);

  return (
    <button
      onClick={onClick}
      title={title}
      aria-label={ariaLabel}
      disabled={disabled}
      className={cx(colorClass, sizeClass, hoverColorClass, 'flex flex-row items-center justify-center', className)}
      type="button"
    >
      {children}
    </button>
  );
};

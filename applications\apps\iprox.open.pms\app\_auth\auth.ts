import jwtDecode from 'jwt-decode';
import { NextAuthOptions, SessionOptions, UserRole } from 'next-auth';

import {
  AZUREAD_PROVIDERS,
  azureAdProvidersConfig,
  azureAdRefreshToken,
  azureAuthProviderFactory,
} from './azure-ad.provider';
import { prismaAdapter, prismaClient } from './prisma';

const session: Partial<SessionOptions> = {
  strategy: 'database',
  maxAge: 30 * 24 * 60 * 60, // 30 days
  updateAge: 24 * 60 * 60, // 24 hours
};

if (azureAdProvidersConfig.length === 0) {
  console.info('No Azure AD providers configured');
}

export const azureAdProviders = azureAdProvidersConfig.map((provider) => {
  return azureAuthProviderFactory(provider);
});

export const authOptions: NextAuthOptions = {
  // Uncomment this to see nextauth debug logs
  // debug: true,
  session,
  adapter: prismaAdapter,
  providers: azureAdProviders,
  callbacks: {
    async signIn({ user }) {
      return Boolean(user.email);
    },
    // Potential improvement: memoizing the fetch to the db.
    async session({ session, user }) {
      const [storedUser] = await prismaClient.account.findMany({
        where: { userId: user.id, provider: user.provider },
      });

      if (AZUREAD_PROVIDERS.includes(storedUser.provider as (typeof AZUREAD_PROVIDERS)[number])) {
        if ((storedUser.expires_at || 0) * 1000 < Date.now() && storedUser.access_token) {
          try {
            const tokens = await azureAdRefreshToken(storedUser.access_token, storedUser.refresh_token || '');

            await prismaClient.account.update({
              data: {
                access_token: tokens.access_token,
                expires_at: Math.floor(Date.now() / 1000 + tokens.expires_in),
                refresh_token: tokens.refresh_token || storedUser.refresh_token,
              },
              where: {
                provider_providerAccountId: {
                  provider: storedUser.provider,
                  providerAccountId: storedUser.providerAccountId,
                },
              },
            });

            session.user = {
              ...user,
              ...storedUser,
              userRoles:
                tokens.access_token !== null ? jwtDecode<{ roles: UserRole[] }>(tokens.access_token).roles ?? [] : [],
            };
          } catch (error) {
            console.error('Error refreshing access token', error);
            // The error property will be used client-side to handle the refresh token error
            session.error = 'RefreshAccessTokenError';
          }
        } else {
          session.user = {
            ...user,
            ...storedUser,
            userRoles:
              storedUser.access_token !== null
                ? jwtDecode<{ roles: UserRole[] }>(storedUser.access_token)?.roles ?? []
                : [],
          };
        }
      }

      return session;
    },
  },
};

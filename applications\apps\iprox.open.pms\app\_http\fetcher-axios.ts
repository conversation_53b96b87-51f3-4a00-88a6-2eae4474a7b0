'use client';

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { getSession } from 'next-auth/react';
import { redirect } from 'next/navigation';

const axiosInstance: AxiosInstance = axios.create({
  headers: {
    'Content-Type': 'application/json',
  },
});

axiosInstance.interceptors.request.use(
  async (config: InternalAxiosRequestConfig<unknown>) => {
    const session = await getSession();
    const token = session?.user?.access_token;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const isUnauthorized = error?.response?.status === 401;

    if (isUnauthorized) {
      redirect('/login');
    }

    return Promise.reject(error);
  }
);

/** Only use this for uploading files in the client. Axios supports progress callbacks. */
export const post = async <T, P>(url: string, data?: P, config?: AxiosRequestConfig<P>): Promise<T> => {
  const response: AxiosResponse<T> = await axiosInstance.post(url, data, config);
  return response.data;
};

export const put = async <T, P>(url: string, data?: P, config?: AxiosRequestConfig<P>): Promise<T> => {
  const response: AxiosResponse<T> = await axiosInstance.put(url, data, config);
  return response.data;
};

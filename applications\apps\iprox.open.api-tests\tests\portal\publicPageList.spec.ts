import { faker } from '@faker-js/faker';

import * as http from '../../enums/httpEnums';
import { DossierHelpersNew } from '../../helpers/dossierHelpersNew';
import { loginAndGetToken } from '../../helpers/getTokenPlaywright';
import { PortalHelpers } from '../../helpers/portalHelpers';

describe('Public page list test', () => {
  let bearerToken: string;
  let pageSlug: string;
  let createPageResponse: any;
  let pageId: any;
  let addCategoryResponse: any;
  let publishPageResponse: any;

  beforeAll(async () => {
    // Create a page and publish it via PMS side to get the list of published pages
    pageSlug = faker.lorem.word();
    bearerToken = await loginAndGetToken();

    // Create a new page
    createPageResponse = await DossierHelpersNew.createSearchPage(pageSlug, bearerToken);
    pageId = await createPageResponse.body.page.id;

    // Update the page - add a category
    addCategoryResponse = await DossierHelpersNew.updateSearchPage(pageId, bearerToken, pageSlug);

    // Publish the page
    publishPageResponse = await DossierHelpersNew.publishSearchPage(pageId, bearerToken);
  });

  it('PORTAL - should get the published page list', async () => {
    const response: any = await PortalHelpers.getPublicPageList();
    const pageListLength = await response.body.pageList.length;

    expect(response.status).toBe(http.StatusCode.OK_200);
    expect(pageListLength).toBeGreaterThan(0);
  });

  afterAll(async () => {
    // clean up - delete the page
    await DossierHelpersNew.deleteSearchPage(pageId, bearerToken);
  });
});

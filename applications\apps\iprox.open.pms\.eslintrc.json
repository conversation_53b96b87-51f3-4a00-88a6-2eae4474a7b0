{"extends": ["plugin:@nx/react-typescript", "next", "next/core-web-vitals", "../../.eslintrc.json"], "ignorePatterns": ["!**/*", ".next/**/*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@next/next/no-html-link-for-pages": ["error", "apps/iprox.open.pms/app"]}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}], "rules": {"@next/next/no-html-link-for-pages": "off"}, "env": {"jest": true}}
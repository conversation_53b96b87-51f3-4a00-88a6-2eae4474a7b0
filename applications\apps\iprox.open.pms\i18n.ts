import { getRequestConfig } from 'next-intl/server';

export const timeZone = 'Europe/Amsterdam';

export default getRequestConfig(async ({ locale }) => {
  return {
    now: new Date(),
    timeZone,
    messages:
      locale === 'en'
        ? {
            ...(await import('@iprox/react-ui-i18n/en.json')).default,
            ...(await import('./i18n/en.json')).default,
          }
        : {
            ...(await import('@iprox/react-ui-i18n/nl.json')).default,
            ...(await import('./i18n/nl.json')).default,
          },
  };
});

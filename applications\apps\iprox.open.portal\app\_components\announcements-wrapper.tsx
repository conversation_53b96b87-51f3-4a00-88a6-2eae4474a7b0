'use client';

import { Button, FieldType, TextField } from '@iprox/react-ui';
import { Form, Formik } from 'formik';
import { useTranslations } from 'next-intl';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useMemo } from 'react';

interface AnnouncementsWrapperProps {
  children?: React.ReactNode;
}

export function AnnouncementsWrapper({ children }: AnnouncementsWrapperProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const t = useTranslations('search');

  const initialState = useMemo(() => {
    return {
      query: searchParams.get('query') || '',
    };
  }, [searchParams]);

  const handleSubmit = (values: typeof initialState) => {
    router.push(`${pathname}?query=${values.query}`);
  };

  return (
    <div className="flex h-full w-full flex-col">
      <Formik initialValues={initialState} onSubmit={handleSubmit}>
        <Form>
          <TextField name="query" label={t('form.queryLabel')} fieldType={FieldType.Text} validationRules={[]} />
          <div className="mt-4 flex justify-end">
            <Button type="submit" variant="primary">
              {t('form.submit')}
            </Button>
          </div>
        </Form>
      </Formik>
      {children}
    </div>
  );
}

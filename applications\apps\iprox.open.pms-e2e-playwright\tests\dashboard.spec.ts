import { expect, test } from '@playwright/test';

import { baseUrl } from '../config.ts';
import { DashboardPage } from '../pages/dashboard_page';
import { EditDossierPage } from '../pages/edit_dossier_page';
import { CreateNewDossierPage } from '../pages/new_dossier_page';
import { userFullname } from '../user.ts';
import { DateTimeFormatter } from '../utils/date_time_formatter';

let dashboardPage: DashboardPage;
let newDossierPage: CreateNewDossierPage;
let editDossierPage: EditDossierPage;
let publishedDossiersCount: number;
let publishedFilesCount: number;

const zipFileCount = 3;
const title = `Playwright dossier - ${DateTimeFormatter.getFormattedDateTime()}`;

test.beforeEach(async ({ page }) => {
  await page.goto(baseUrl);
  dashboardPage = new DashboardPage(page);
  newDossierPage = new CreateNewDossierPage(page);
  editDossierPage = new EditDossierPage(page);
});

test.describe('Dashboard page test verifications', () => {
  test('Verify the page URL', async ({ page }) => {
    await dashboardPage.assertPageUrl();
  });

  test('Verify to show dashboard as homepage', async ({ page }) => {
    await expect(page.getByText(`Welkom ${userFullname}`)).toBeVisible();
  });

  test('Verify the dashboard page widget tests', async ({ page }) => {
    test.slow();
    await dashboardPage.goToDashboardPage();
    const publicationType = await dashboardPage.getPublicationType();
    expect(publicationType).toBe('Alle publicatietypen');

    publishedDossiersCount = parseInt(await dashboardPage.getPublishedDossierWidgetText());
    console.log('Published Dossiers Count: ', publishedDossiersCount);

    publishedFilesCount = parseInt(await dashboardPage.getPublishedDossierFileWidgetText());
    console.log('Published Files Count: ', publishedFilesCount);

    // Create new dossier & upload a zip
    await dashboardPage.clickNewDossierButton();
    await newDossierPage.createNewDossier(title, false);
    await expect(page).toHaveURL(new RegExp('/dossier/.*'));
    await page.getByLabel('File input').setInputFiles('data/zipFiles/root_dir_and_files.zip'); // upload zip file
    await editDossierPage.inputDynamicFields(); // Input dynamic fields
    await editDossierPage.clickActionButton('Publiceren'); // click publish button
    await editDossierPage.checkPublishedDossierViewLink(); // wait publish event get completed
    await page.goto('/dashboard'); // go to the dashboard page

    // Verify the count of published dossiers is increased by 1
    const newPublishedDossiersCount = parseInt(await dashboardPage.getPublishedDossierWidgetText());
    console.log('\nNew Published Dossiers Count:', newPublishedDossiersCount);
    expect(newPublishedDossiersCount).toBe(publishedDossiersCount + 1);

    // Verify the count of published files is increased by the number of files in the zip
    const newPublishedFilesCount = parseInt(await dashboardPage.getPublishedDossierFileWidgetText());
    console.log('New Published Files Count:', newPublishedFilesCount);
    expect(newPublishedFilesCount).toBe(publishedFilesCount + zipFileCount);

    await page.waitForTimeout(2000);
    await page.close();
  });
  test.afterEach(async ({ page }) => {
    await page.close();
  });
});

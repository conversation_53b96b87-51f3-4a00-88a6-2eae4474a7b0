import { ValidationRule, ValidationRuleType, ValidatorFn, ValidatorFnFactory } from '../models/validator.models';

export interface DisallowedCharsValidationRule extends ValidationRule<ValidationRuleType.ValidateDisallowedCharacters> {
  ruleValue: {
    regExp: string;
  };
}

export const isDisallowedCharsRule = (rule: ValidationRule): rule is DisallowedCharsValidationRule => {
  return rule.ruleType === ValidationRuleType.ValidateDisallowedCharacters;
};

export const disallowedCharsValidatorFactory: ValidatorFnFactory<{
  regExp: string;
}> = (ruleValue): ValidatorFn => {
  return ({ value }) => {
    if (typeof value !== 'string') {
      return null;
    }

    if (ruleValue.regExp && new RegExp(ruleValue.regExp).test(value.trim())) {
      return { disallowedCharacters: true };
    }

    return null;
  };
};

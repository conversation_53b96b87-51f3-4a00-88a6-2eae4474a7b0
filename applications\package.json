{"name": "applications", "version": "0.0.0", "license": "MIT", "scripts": {"format": "prettier --write .", "lint": "yarn lint:prettier && yarn lint:stylelint && yarn lint:eslint", "lint:prettier": "prettier --check .", "lint:stylelint": "stylelint {apps,libs}/**/*.scss", "lint:eslint": "yarn nx run-many --target=lint --projects=\"iprox.open.pms,iprox.open.portal,react-ui\"", "playwright:dev": "npx playwright install chromium && npx playwright test --project=e2e_chromium -c apps/react-ui-e2e --ui", "playwright:e2e": "npx playwright install chromium && npx playwright test --project=e2e_chromium -c apps/react-ui-e2e", "playwright:snapshots": "npx playwright install chromium && npx playwright test --project=snapshots_chromium -c apps/react-ui-e2e", "playwright:pms": "npx playwright install chromium && npx playwright test tests --project=chromium --project=login-chromium -c apps/iprox.open.pms-e2e-playwright/playwright.config.ts", "playwright:pms-dev": "npx playwright install chromium && npx playwright test tests --project=chromium --project=login-chromium -c apps/iprox.open.pms-e2e-playwright/playwright.config.ts --ui", "storybook": "yarn nx run react-ui:storybook", "generate:openapi": "npx openapi-typescript https://app-iprox-open-1887.azurewebsites.net/swagger/v1/swagger.json -o ./libs/iprox-open-api/generated/iprox-open.interface.ts && yarn format && yarn lint --fix", "generate:openapi:local": "npx openapi-typescript http://localhost:5257/swagger/v1/swagger.json -o ./libs/iprox-open-api/generated/iprox-open.interface.ts && yarn format && yarn lint --fix", "postinstall": "patch-package", "build:pms": "nx build iprox.open.pms --skip-nx-cache=true", "copy-static:pms": "copyfiles -u 4 \"./dist/apps/iprox.open.pms/public/*/**\" ./dist/apps/iprox.open.pms/.next/standalone/dist/apps/iprox.open.pms/public && copyfiles -u 5 \"./dist/apps/iprox.open.pms/.next/static/*/**\" ./dist/apps/iprox.open.pms/.next/standalone/dist/apps/iprox.open.pms/.next/static", "start:pms": "npx rimraf ./dist/apps/iprox.open.pms && yarn build:pms && yarn copy-static:pms && set PORT=4200&& node ./dist/apps/iprox.open.pms/.next/standalone/apps/iprox.open.pms/server.js"}, "private": true, "dependencies": {"@faker-js/faker": "^8.0.2", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.17", "@lexical/react": "^0.12.0", "@next-auth/prisma-adapter": "^1.0.7", "@nx/devkit": "20.4.0", "@prisma/client": "6", "@storybook/addon-interactions": "^8.2.8", "@swc/helpers": "0.5.15", "@types/debounce": "^1.2.1", "@types/dompurify": "^3.0.3", "@types/react-datepicker": "^4.11.2", "@typescript-eslint/scope-manager": "6.7.3", "@uiw/react-color": "^2.3.3", "applicationinsights": "2.7.3", "axe-playwright": "^2.0.1", "axios": "^1.4.0", "date-fns": "^2.30.0", "debounce": "^1.2.1", "dompurify": "^3.0.6", "file-saver": "^2.0.5", "filesize": "^10.0.8", "formik": "^2.2.9", "isomorphic-dompurify": "1.9.0", "jwt-decode": "^3.1.2", "ky": "^1.2.0", "lexical": "^0.12.0", "lighthouse": "^12.1.0", "next": "14.2.16", "next-auth": "^4.24.11", "next-intl": "3.1.2", "next-sitemap": "4.2.3", "patch-package": "^8.0.0", "playwright-lighthouse": "^4.0.0", "react": "18.3.1", "react-arborist": "3.4.0", "react-datepicker": "^4.13.0", "react-dom": "18.3.1", "react-select": "^5.7.3", "react-toastify": "^10.0.6", "react-tooltip": "^5.14.0", "storybook": "7.5.3", "storybook-react-context": "^0.6.0", "tailwind-merge": "^1.13.0", "tslib": "^2.3.0", "usehooks-ts": "^2.9.1", "xml2js": "0.6.2"}, "stylelint": {"extends": ["@infoprojects/stylelint-config-scss", "stylelint-config-prettier-scss"]}, "devDependencies": {"@axe-core/playwright": "^4.7.2", "@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@bbbtech/storybook-formik": "^3.0.0", "@infoprojects/eslint-config": "^1.1.0", "@infoprojects/prettier-config": "^1.0.0", "@infoprojects/stylelint-config-scss": "^1.0.0", "@nx/cypress": "20.4.0", "@nx/eslint": "20.4.0", "@nx/eslint-plugin": "20.4.0", "@nx/jest": "20.4.0", "@nx/next": "20.4.0", "@nx/plugin": "20.4.0", "@nx/react": "20.4.0", "@nx/storybook": "^20.4.0", "@nx/workspace": "20.4.0", "@playwright/test": "^1.41.2", "@storybook/addon-a11y": "7.5.3", "@storybook/addon-essentials": "7.5.3", "@storybook/addon-webpack5-compiler-babel": "^3.0.3", "@storybook/core-common": "7.5.3", "@storybook/core-server": "7.5.3", "@storybook/nextjs": "^7.5.3", "@storybook/node-logger": "^8.4.7", "@swc-node/register": "1.9.2", "@swc/cli": "0.3.14", "@swc/core": "1.5.7", "@swc/jest": "0.2.37", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.4", "@testing-library/react": "15.0.6", "@trivago/prettier-plugin-sort-imports": "^4.1.1", "@types/file-saver": "^2.0.5", "@types/jest": "29.5.14", "@types/node": "^18.16.9", "@types/react-dom": "18.3.0", "@types/uuid": "^9.0.1", "@types/xml2js": "0.4.14", "@typescript-eslint/eslint-plugin": "7.16.0", "@typescript-eslint/parser": "7.16.0", "autoprefixer": "10.4.13", "babel-jest": "29.7.0", "classnames": "^2.3.2", "copyfiles": "^2.4.1", "cypress": "13.13.0", "eslint": "8.57.1", "eslint-config-next": "14.2.3", "eslint-config-prettier": "9.0.0", "eslint-plugin-cypress": "2.13.4", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-storybook": "^0.11.1", "http-server": "^14.1.1", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jsonc-eslint-parser": "^2.1.0", "nx": "20.4.0", "openapi-typescript": "^6.3.9", "postcss": "8.4.38", "prettier": "3.6.2", "prettier-plugin-tailwindcss": "0.6.13", "prisma": "6", "sass": "1.61.0", "stylelint": "^15.6.1", "stylelint-config-prettier-scss": "^1.0.0", "stylelint-config-recommended-scss": "^11.0.0", "tailwindcss": "3.4.14", "ts-jest": "29.1.0", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "5.6.3"}}
import * as OutlineIcons from '@heroicons/react/24/outline';
import { Text } from '@iprox/react-ui';

interface OptionCardProps {
  isActive: boolean;
  onClick: () => void;
  icon: keyof typeof OutlineIcons;
  text: string;
}

export function OptionCard({ isActive, onClick, icon, text }: OptionCardProps) {
  const Icon = OutlineIcons[icon];

  return (
    <label
      htmlFor={text}
      className={`bg-light-grey rounded-medium hover:bg-base-25 relative flex h-[200px] w-[200px] cursor-pointer flex-col items-center justify-center gap-y-5 ${
        isActive ? 'border-primary-content border-2' : ''
      }`}
      aria-checked={isActive}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === ' ' || e.key === 'Enter') {
          onClick();
        }
      }}
    >
      <input
        id={text}
        type="checkBox"
        checked={isActive}
        className="invisible absolute inset-0 opacity-0"
        onChange={onClick}
      />
      <Icon className="text-primary-content h-8 w-8" />
      <Text className="text-primary-content font-heading text-lg font-bold">{text}</Text>
    </label>
  );
}

'use client';

import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { useTranslations } from 'use-intl';

interface FileDownloadLinkProps {
  fileType: string;
  link: string;
}

export function FileDownloadLink({ link, fileType }: FileDownloadLinkProps) {
  const t = useTranslations('search');
  return (
    <a href={link} className="flex items-center hover:cursor-pointer hover:underline" download target="_blank">
      <ArrowDownTrayIcon className="text-primary mr-2 h-6 w-6" strokeWidth={3} />
      <span className="text-body w-fit leading-5">{t('download', { fileType })}</span>
    </a>
  );
}

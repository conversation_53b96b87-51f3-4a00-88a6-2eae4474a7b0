import { ToastContainer, ToastOptions, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export function Toast() {
  return <ToastContainer position="bottom-center" autoClose={5000} pauseOnFocusLoss={false} pauseOnHover={false} />;
}

export function showToast(message: string[] | string, options?: ToastOptions) {
  if (Array.isArray(message)) {
    message.forEach((msg) => {
      toast(msg, options);
    });
  } else {
    toast(message, options);
  }
}

import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Button } from '../button/button';
import { ConfirmDialog } from './confirm-dialog';
import { ConfirmDialogProvider, useConfirmDialog } from './context/confirm-dialog.context';

const meta: Meta<typeof ConfirmDialog> = {
  title: 'iprox-ui/components/confirm-dialog',
  component: ConfirmDialog,
};

export default meta;
type Story = StoryObj<typeof Template>;

const Template = ({ message }: { message: string }) => {
  const { showDialog } = useConfirmDialog();

  return (
    <Button
      variant="primary"
      onClick={() => {
        showDialog({
          message,
          onConfirm: () => null,
        });
      }}
    >
      Open
    </Button>
  );
};

export const Default: Story = {
  name: 'default',
  args: {
    message: 'Are you sure you want to perform this action?',
  },
  render: (args) => {
    return (
      <ConfirmDialogProvider>
        <Template message={args.message} />
      </ConfirmDialogProvider>
    );
  },
};

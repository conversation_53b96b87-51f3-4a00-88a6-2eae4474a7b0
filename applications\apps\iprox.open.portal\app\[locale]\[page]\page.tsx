import { components } from '@/iprox-open.interface';
import { SearchParams } from '@/models/search.model';
import { getPage } from '@/services/page-service';
import { HTTPError } from 'ky';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { AnnouncementPage } from './announcements-page';
import { SearchPage } from './search-page';
import { SimplePage } from './simple-page';

type PageProps = {
  params: { page: string };
  searchParams: SearchParams;
};

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  try {
    const data = await getPage(params.page);

    if (!data) {
      notFound();
    }

    return {
      title: data?.page?.label,
    };
  } catch (error) {
    if (error instanceof HTTPError && error.response.status === 404) {
      notFound();
    }
  }
  return {
    title: 'Not Found',
  };
}

export default async function Page({ params, searchParams }: PageProps) {
  try {
    const pageData = await getPage(params.page);
    const page = pageData?.page;

    if (!page) {
      notFound();
    }

    if ('categories' in page) {
      return <SearchPage page={page} searchParams={searchParams} />;
    }

    if ('organisationName' in page) {
      return <AnnouncementPage page={page} searchParams={searchParams} />;
    }

    if (page.pageType === 'Simple') {
      return <SimplePage page={page as components['schemas']['SimplePageDto']} />;
    }
  } catch (error) {
    if (error instanceof HTTPError && error.response.status === 404) {
      notFound();
    }
  }

  return null;
}

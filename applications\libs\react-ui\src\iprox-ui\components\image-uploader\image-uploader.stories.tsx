import { Meta, StoryObj } from '@storybook/react';

import { ImageUploader } from './image-uploader';

const meta: Meta<typeof ImageUploader> = {
  title: 'iprox-ui/components/image-uploader',
  component: ImageUploader,
  argTypes: {
    uploadPromise: {
      action: 'uploadPromise',
      table: {
        disable: true,
      },
    },
  },
};

export default meta;

type Story = StoryObj<typeof ImageUploader>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Upload your image',
  },
};

import { type Locator, type Page, expect } from '@playwright/test';

import { PageType } from '../utils/enums';

export class BekendmakingenPage {
  private page: Page;
  private pageName: Locator;
  private pageStatus: Locator;
  private inputTextField: Locator;
  private dropdownOptions: Locator;
  private dropdownPublishAction: Locator;
  private dropdownUnPublishAction: Locator;
  private dropdownDeleteAction: Locator;
  private modalConfirmationButton: Locator;
  private modalCancelButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.dropdownOptions = this.page.getByRole('button', { name: 'Toon opties' });
    this.dropdownUnPublishAction = this.page.getByRole('button', { name: 'Verbergen' });
    this.dropdownDeleteAction = this.page.getByRole('button', { name: 'Verwijderen' });
  }

  async clickSearchPageButton() {
    await this.page.waitForLoadState('domcontentloaded');
  }

  async fillTitleField(title: string) {
    await this.page.getByTestId('modal').getByLabel('Titel').fill(title);
  }

  async clickNextButton() {
    await this.page.getByRole('button', { name: 'Verder' }).click();
  }

  async selectDossierCategory(string: string) {
    await this.page.getByText(string, { exact: true }).click();
  }

  async clickDropdownOptions() {
    await this.dropdownOptions.click();
  }

  async selectZone() {
    await this.page.getByText('4,4,4').click();
  }

  async clickDropdownPublishAction() {
    this.dropdownPublishAction = this.page.getByRole('button', { name: 'Publiceren' });
    await this.dropdownPublishAction.click();
  }

  async clickDropdownUnpublishPage() {
    await this.dropdownUnPublishAction.click();
  }

  async clickDropdownDeletePage() {
    await this.dropdownDeleteAction.click();
  }

  async clickModalConfirmationButton() {
    this.modalConfirmationButton = this.page.getByRole('button', { name: 'Bevestigen' });
    await this.modalConfirmationButton.click();
  }

  async clickModalCancelButton() {
    this.modalCancelButton = this.page.getByRole('button', { name: 'Annuleren' });
    await this.modalCancelButton.click();
  }

  async assertPagePublishedSuccessfully() {
    await this.page.getByText('Pagina gepubliceerd').click();
  }

  async assertPublishButtonIsDisabled() {
    await this.clickDropdownOptions();
    const publishButton = this.page.getByRole('button', { name: 'Publiceren' });
    await expect(publishButton).toBeDisabled();
  }

  async assertPageStatus(status: string) {
    this.pageStatus = this.page.locator(
      `//span[text()="${PageType.AnnouncementsPage}"]/following-sibling::span//child::span`
    );
    let pageStatus = await this.pageStatus.innerText();
    expect(pageStatus).toBe(status);
  }

  async inputText(locator: string, text: string) {
    await this.page.waitForLoadState('domcontentloaded');
    this.inputTextField = this.page.getByRole('textbox', { name: `${locator}` });
    await this.inputTextField.fill(text);
  }
}

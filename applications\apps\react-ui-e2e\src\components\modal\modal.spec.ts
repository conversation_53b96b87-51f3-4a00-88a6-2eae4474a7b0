import AxeBuilder from '@axe-core/playwright';
import { expect, test } from '@playwright/test';

import { loadStory } from '../../utils/common-utils';

type ModalArgs = {
  isOpen: boolean;
  onClose: () => void;
  children?: unknown;
};

const storyId = 'components-modal--default';

test.describe('<Modal />', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await loadStory<ModalArgs>(page, storyId);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should open the modal on click of the click to open modal button', async ({ page }) => {
    await loadStory<ModalArgs>(page, storyId, {
      isOpen: false,
      onClose: () => null,
    });

    await page.click('data-testid=open-modal');
    expect(await page.getByTestId('modal').isVisible());
  });

  test('should close the modal on click of the close button on the modal', async ({ page }) => {
    await loadStory<ModalArgs>(page, storyId, {
      isOpen: true,
      onClose: () => null,
    });

    await page.click(`data-testid=close-button`);
    expect(await page.getByTestId('modal').isHidden());
  });

  test('should display the children passed into the modal', async ({ page }) => {
    await loadStory<ModalArgs>(page, storyId, {
      isOpen: true,
      onClose: () => null,
      children: 'Some placeholder text',
    });

    expect(await page.getByText('Some placeholder text').isVisible());
  });
});

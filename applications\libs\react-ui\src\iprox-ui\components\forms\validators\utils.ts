import { FieldDefinition, FieldType, ValueTypes } from '../models/form.models';
import { ObjectValidationErrors, ValidationErrors, ValidatorFn, ValidatorSchema } from '../models/validator.models';
import { Validators } from './validators';

export const createValidatorSchema = (fields: FieldDefinition<FieldType, ValueTypes>[]): ValidatorSchema => {
  const validatorSchema = fields.reduce<ValidatorSchema>(
    (acc, field) => ({
      ...acc,
      [field.name]: createValidators(field),
    }),
    {}
  );

  return validatorSchema;
};

export const createValidators = (field: FieldDefinition<FieldType, ValueTypes>): ValidatorFn[] => {
  return field.validationRules
    .map((rule) => {
      const validatorFactory = Validators.getValidatorFactory(rule.ruleType);

      if (validatorFactory) {
        return validatorFactory(rule.ruleValue);
      }

      return null;
    })
    .filter((value: ValidatorFn | null): value is ValidatorFn => value !== null);
};

export const validateObject = (values: Record<string, unknown>, schema: ValidatorSchema): ObjectValidationErrors => {
  const errors: ObjectValidationErrors = {};

  Object.entries(values).forEach(([alias, value]) => {
    const validators = schema[alias];

    const fieldErrors: ValidationErrors = validators.reduce(
      (errors, validator) => ({ ...errors, ...validator({ value }) }),
      {}
    );

    errors[alias] = fieldErrors;
  });

  return errors;
};

export const formikValidation = (values: Record<string, unknown>, schema: ValidatorSchema): Record<string, string> => {
  const errors = validateObject(values, schema);
  const readableErrors: Record<string, string> = {};

  Object.entries(errors).forEach(([alias, errors]) => {
    const firstErrorKey = Object.keys(errors)[0];

    if (firstErrorKey) {
      readableErrors[alias] = firstErrorKey;
    }
  });

  return readableErrors;
};

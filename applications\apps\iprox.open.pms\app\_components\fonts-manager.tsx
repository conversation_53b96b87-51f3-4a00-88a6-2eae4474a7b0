'use client';

import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import {
  createFontAsset,
  deleteFontAsset,
  deleteFontFiles,
  updateFontAsset,
  uploadFontFiles,
} from '@/services/fonts-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { DocumentMinusIcon } from '@heroicons/react/24/outline';
import {
  Button,
  FontUploader,
  FormSubmitValues,
  ProgressBar,
  Text,
  showToast,
  useConfirmDialog,
} from '@iprox/iprox-ui';
import { Modal, Spinner, calculateUploadProgress } from '@iprox/react-ui';
import { useAppSettings } from '@iprox/shared-context';
import { AxiosProgressEvent } from 'axios';
import { useTranslations } from 'next-intl';
import React, { useId, useState } from 'react';

import { PageHeader } from '@/components/page-header';

import { FontCreateUpdateForm } from './font-create-update-form';
import { FontItem } from './font-item';

interface FontsManagerProps {
  fontAssets: components['schemas']['FontAssetDto'][];
}

export function FontsManager({ fontAssets }: FontsManagerProps) {
  const t = useTranslations('fonts');
  const clientApi = useClientApi();
  const settings = useAppSettings();

  const formId = useId();
  const { showDialog } = useConfirmDialog();

  const [formKey, setFormKey] = useState(formId); /* using this key to reset the form after api call success */
  const [fonts, setFonts] = useState(fontAssets);
  const [updatingFontId, setUpdatingFontId] = useState('');
  const [selectedFontId, setSelectedFontId] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [notUploadedFiles, setNotUploadedFiles] = useState<components['schemas']['NotUploadedFile'][] | undefined>();

  const handleCloseModal = () => {
    setNotUploadedFiles(undefined);
    setIsModalOpen(false);
  };

  const getUpdatedFonts = (
    fonts: components['schemas']['FontAssetDto'][],
    updatedFont: components['schemas']['FontAssetDto']
  ) => {
    const updatedFonts = [...fonts];
    const index = updatedFonts.findIndex(({ id }) => id === updatedFont.id);

    if (index > -1) {
      updatedFonts[index] = updatedFont;
    }

    return updatedFonts;
  };

  function handleProgressEvent(progressEvent: AxiosProgressEvent) {
    const percentage = calculateUploadProgress(progressEvent);
    setUploadProgress(percentage);
  }

  const handleUpload = async (files: FileList) => {
    if (selectedFontId) {
      setUploading(true);
      try {
        const response = await uploadFontFiles(settings.apiUrl, selectedFontId, files, handleProgressEvent);

        if (response?.fontAsset) {
          setFonts([...getUpdatedFonts(fonts, response.fontAsset)]);
        }

        if (response.notUploadedFiles?.length) {
          setNotUploadedFiles(response.notUploadedFiles);
        } else {
          handleCloseModal();
        }
      } catch (error) {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      } finally {
        setUploading(false);
      }
    }
  };

  const handleCreateFontAsset = async (values: FormSubmitValues) => {
    setSubmitting(true);
    try {
      const updatedFonts = fonts;
      const response = await createFontAsset(clientApi, values as components['schemas']['CreateFontAssetCommand']);

      updatedFonts.push(response.fontAsset);
      setFonts([...updatedFonts]);

      const newId = `${formId}-${Math.random()}`;
      setFormKey(newId);
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    } finally {
      setSubmitting(false);
    }
  };

  const handleAddFile = (id: string) => {
    setSelectedFontId(id);
    setIsModalOpen(true);
  };

  const handleUpdateFontAsset = async (values: FormSubmitValues) => {
    setSubmitting(true);
    const fontConfig: components['schemas']['UpdateFontAssetCommand'] = {
      descriptors: {},
      fontAssetId: updatingFontId,
      fontCategory: values.fontCategory as components['schemas']['FontCategory'],
      fontFamily: values.fontFamily as string,
      fontStyle: values.fontStyle as components['schemas']['FontStyle'],
      fontWeight: values.fontWeight as components['schemas']['FontWeight'],
    };
    try {
      const response = await updateFontAsset(clientApi, fontConfig);

      setFonts([...getUpdatedFonts(fonts, response.fontAsset)]);
      setUpdatingFontId('');
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteFont = (fontAssetId: string) => {
    showDialog({
      message: t('confirmation.deleteFontAsset.message'),
      onConfirm: async () => {
        try {
          const updatedFonts = fonts;
          const response = await deleteFontAsset(clientApi, { fontAssetId });

          if (response.success) {
            setFonts([...updatedFonts.filter(({ id }) => id !== fontAssetId)]);
          }
        } catch (error) {
          const errorMessages = await getErrorMessages(error);
          showToast(errorMessages, { type: 'error' });
        }
      },
    });
  };

  const handleDeleteFontFile = (fontAssetId: string, fontFileId: string) => {
    showDialog({
      message: t('confirmation.deleteFontFile.message'),
      onConfirm: async () => {
        try {
          const updatedFonts = fonts;
          const response = await deleteFontFiles(clientApi, { fontAssetId, fontIds: [fontFileId] });

          const index = updatedFonts.findIndex(({ id }) => id === fontAssetId);

          if (response.deletedNodes && index > -1) {
            updatedFonts[index].fontFiles = updatedFonts[index].fontFiles.filter(
              ({ id: fileId }) => !response.deletedNodes.includes(fileId)
            );
            setFonts([...updatedFonts]);
          }
        } catch (error) {
          const errorMessages = await getErrorMessages(error);
          showToast(errorMessages, { type: 'error' });
        }
      },
    });
  };

  return (
    <div>
      <PageHeader title={t('fonts')} />
      <FontCreateUpdateForm key={formKey} onSubmitForm={handleCreateFontAsset} disableButton={submitting} />

      {fonts.map((font) =>
        updatingFontId === font.id ? (
          <FontCreateUpdateForm
            key={font.id}
            initialValues={font}
            onSubmitForm={handleUpdateFontAsset}
            onCancel={() => setUpdatingFontId('')}
            submitButtonText="saveFont"
            disableButton={submitting}
          />
        ) : (
          <FontItem
            key={font.id}
            font={font}
            onClickDelete={handleDeleteFont}
            onClickUpdate={(id) => setUpdatingFontId(id)}
            onClickaddFile={handleAddFile}
            onClickDeleteFile={handleDeleteFontFile}
          />
        )
      )}
      <Modal isOpen={isModalOpen} onClose={handleCloseModal}>
        <div className="py-5">
          {uploading ? (
            uploadProgress < 100 ? (
              <div className="w-full max-w-[475px]">
                <ProgressBar title={t('uploadingFonts')} progress={uploadProgress} />
              </div>
            ) : (
              <div className="w-100 flex justify-center">
                <Spinner variant="secondary" message={t('finalizingUpload')} />
              </div>
            )
          ) : notUploadedFiles ? (
            <div className="flex w-full flex-col justify-center">
              {notUploadedFiles.map((item, index) => (
                <div key={`${item.name}-${index}`} className="mb-2 flex items-center last:mb-0">
                  <DocumentMinusIcon className="text-error h-4 w-4" />
                  <Text className="font-text text-secondary-medium mr-3">{item.name}</Text>
                  <Text className="font-text text-error">{`(${item.reason})`}</Text>
                </div>
              ))}
              <div className="mt-4 grid grid-flow-col justify-end gap-2">
                <Button type="button" variant="primary" onClick={() => setNotUploadedFiles(undefined)}>
                  {t('retry')}
                </Button>
                <Button type="button" variant="secondary" onClick={handleCloseModal}>
                  {t('cancel')}
                </Button>
              </div>
            </div>
          ) : (
            <FontUploader onFileSelect={handleUpload} />
          )}
        </div>
      </Modal>
    </div>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/diagnostic-channel";
exports.ids = ["vendor-chunks/diagnostic-channel"];
exports.modules = {

/***/ "(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/diagnostic-channel/dist/src/channel.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.channel = exports.ContextPreservingEventEmitter = exports.trueFilter = exports.makePatchingRequire = void 0;\r\nvar patchRequire_1 = __webpack_require__(/*! ./patchRequire */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/patchRequire.js\");\r\nvar patchRequire_2 = __webpack_require__(/*! ./patchRequire */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/patchRequire.js\");\r\nObject.defineProperty(exports, \"makePatchingRequire\", ({ enumerable: true, get: function () { return patchRequire_2.makePatchingRequire; } }));\r\nvar trueFilter = function (publishing) { return true; };\r\nexports.trueFilter = trueFilter;\r\nvar ContextPreservingEventEmitter = /** @class */ (function () {\r\n    function ContextPreservingEventEmitter() {\r\n        this.version = (__webpack_require__(/*! ./../../package.json */ \"(instrument)/../../node_modules/diagnostic-channel/package.json\").version); // Allow for future versions to replace things?\r\n        this.subscribers = {};\r\n        this.contextPreservationFunction = function (cb) { return cb; };\r\n        this.knownPatches = {};\r\n        this.modulesPatched = [];\r\n        this.currentlyPublishing = false;\r\n    }\r\n    ContextPreservingEventEmitter.prototype.shouldPublish = function (name) {\r\n        var listeners = this.subscribers[name];\r\n        if (listeners) {\r\n            return listeners.some(function (_a) {\r\n                var filter = _a.filter;\r\n                return !filter || filter(false);\r\n            });\r\n        }\r\n        return false;\r\n    };\r\n    ContextPreservingEventEmitter.prototype.publish = function (name, event) {\r\n        if (this.currentlyPublishing) {\r\n            return; // Avoid reentrancy\r\n        }\r\n        var listeners = this.subscribers[name];\r\n        // Note: Listeners called synchronously to preserve context\r\n        if (listeners) {\r\n            var standardEvent_1 = {\r\n                timestamp: Date.now(),\r\n                data: event\r\n            };\r\n            this.currentlyPublishing = true;\r\n            listeners.forEach(function (_a) {\r\n                var listener = _a.listener, filter = _a.filter;\r\n                try {\r\n                    if (filter && filter(true)) {\r\n                        listener(standardEvent_1);\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    // Subscriber threw an error\r\n                }\r\n            });\r\n            this.currentlyPublishing = false;\r\n        }\r\n    };\r\n    ContextPreservingEventEmitter.prototype.subscribe = function (name, listener, filter, patchCallback) {\r\n        if (filter === void 0) { filter = exports.trueFilter; }\r\n        if (!this.subscribers[name]) {\r\n            this.subscribers[name] = [];\r\n        }\r\n        this.subscribers[name].push({ listener: listener, filter: filter, patchCallback: patchCallback });\r\n        var patched = this.checkIfModuleIsAlreadyPatched(name);\r\n        if (patched && patchCallback) {\r\n            patchCallback(patched.name, patched.version);\r\n        }\r\n    };\r\n    ContextPreservingEventEmitter.prototype.unsubscribe = function (name, listener, filter) {\r\n        if (filter === void 0) { filter = exports.trueFilter; }\r\n        var listeners = this.subscribers[name];\r\n        if (listeners) {\r\n            for (var index = 0; index < listeners.length; ++index) {\r\n                if (listeners[index].listener === listener && listeners[index].filter === filter) {\r\n                    listeners.splice(index, 1);\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n        return false;\r\n    };\r\n    // Used for tests\r\n    ContextPreservingEventEmitter.prototype.reset = function () {\r\n        var _this = this;\r\n        this.subscribers = {};\r\n        this.contextPreservationFunction = function (cb) { return cb; };\r\n        // Modify the knownPatches object rather than replace, since a reference will be used in the require patcher\r\n        Object.getOwnPropertyNames(this.knownPatches).forEach(function (prop) { return delete _this.knownPatches[prop]; });\r\n    };\r\n    ContextPreservingEventEmitter.prototype.bindToContext = function (cb) {\r\n        return this.contextPreservationFunction(cb);\r\n    };\r\n    ContextPreservingEventEmitter.prototype.addContextPreservation = function (preserver) {\r\n        var previousPreservationStack = this.contextPreservationFunction;\r\n        this.contextPreservationFunction = (function (cb) { return preserver(previousPreservationStack(cb)); });\r\n    };\r\n    ContextPreservingEventEmitter.prototype.registerMonkeyPatch = function (packageName, patcher) {\r\n        if (!this.knownPatches[packageName]) {\r\n            this.knownPatches[packageName] = [];\r\n        }\r\n        this.knownPatches[packageName].push(patcher);\r\n    };\r\n    ContextPreservingEventEmitter.prototype.getPatchesObject = function () {\r\n        return this.knownPatches;\r\n    };\r\n    ContextPreservingEventEmitter.prototype.addPatchedModule = function (name, version) {\r\n        for (var _i = 0, _a = this.modulesPatched; _i < _a.length; _i++) {\r\n            var module_1 = _a[_i];\r\n            if (module_1.name === name) {\r\n                return;\r\n            }\r\n        }\r\n        // If new patch notify listeners\r\n        this.modulesPatched.push({ name: name, version: version });\r\n        var listeners = this.subscribers[name];\r\n        if (listeners) {\r\n            listeners.forEach(function (listener) {\r\n                if (listener.patchCallback) {\r\n                    listener.patchCallback(name, version);\r\n                }\r\n            });\r\n        }\r\n    };\r\n    ContextPreservingEventEmitter.prototype.checkIfModuleIsAlreadyPatched = function (name) {\r\n        for (var _i = 0, _a = this.modulesPatched; _i < _a.length; _i++) {\r\n            var module_2 = _a[_i];\r\n            if (module_2.name === name) {\r\n                return module_2;\r\n            }\r\n        }\r\n        return null;\r\n    };\r\n    return ContextPreservingEventEmitter;\r\n}());\r\nexports.ContextPreservingEventEmitter = ContextPreservingEventEmitter;\r\nif (!global.diagnosticsSource) {\r\n    global.diagnosticsSource = new ContextPreservingEventEmitter();\r\n    // TODO: should this only patch require after at least one monkey patch is registered?\r\n    /* tslint:disable-next-line:no-var-requires */\r\n    var moduleModule = __webpack_require__(/*! module */ \"module\");\r\n    // Note: We pass in the object now before any patches are registered, but the object is passed by reference\r\n    // so any updates made to the object will be visible in the patcher.\r\n    moduleModule.prototype.require = patchRequire_1.makePatchingRequire(global.diagnosticsSource.getPatchesObject());\r\n}\r\nexports.channel = global.diagnosticsSource;\r\n//# sourceMappingURL=channel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel/dist/src/patchRequire.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/diagnostic-channel/dist/src/patchRequire.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\n// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license. See LICENSE file in the project root for details.\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.makePatchingRequire = void 0;\r\nvar path = __webpack_require__(/*! path */ \"path\");\r\nvar semver = __webpack_require__(/*! semver */ \"(instrument)/../../node_modules/semver/index.js\");\r\nvar channel_1 = __webpack_require__(/*! ./channel */ \"(instrument)/../../node_modules/diagnostic-channel/dist/src/channel.js\");\r\n/* tslint:disable-next-line:no-var-requires */\r\nvar moduleModule = __webpack_require__(/*! module */ \"module\");\r\nvar nativeModules = Object.keys(process.binding(\"natives\"));\r\nvar originalRequire = moduleModule.prototype.require;\r\nfunction makePatchingRequire(knownPatches) {\r\n    var patchedModules = {};\r\n    return function patchedRequire(moduleId) {\r\n        var originalModule = originalRequire.apply(this, arguments);\r\n        if (knownPatches[moduleId]) {\r\n            // Fetch the specific path of the module\r\n            var modulePath = moduleModule._resolveFilename(moduleId, this);\r\n            if (patchedModules.hasOwnProperty(modulePath)) {\r\n                // This module has already been patched, no need to reapply\r\n                return patchedModules[modulePath];\r\n            }\r\n            var moduleVersion = void 0;\r\n            if (nativeModules.indexOf(moduleId) < 0) {\r\n                try {\r\n                    moduleVersion = originalRequire.call(this, path.join(moduleId, \"package.json\")).version;\r\n                }\r\n                catch (e) {\r\n                    // This should only happen if moduleId is actually a path rather than a module\r\n                    // This is not a supported scenario\r\n                    return originalModule;\r\n                }\r\n            }\r\n            else {\r\n                // This module is implemented natively so we cannot find a package.json\r\n                // Instead, take the version of node itself\r\n                moduleVersion = process.version.substring(1);\r\n            }\r\n            var prereleaseTagIndex = moduleVersion.indexOf(\"-\");\r\n            if (prereleaseTagIndex >= 0) {\r\n                // We ignore prerelease tags to avoid impossible to fix gaps in support\r\n                // e.g. supporting console in >= 4.0.0 would otherwise not include\r\n                // 8.0.0-pre\r\n                moduleVersion = moduleVersion.substring(0, prereleaseTagIndex);\r\n            }\r\n            var modifiedModule = originalModule;\r\n            for (var _i = 0, _a = knownPatches[moduleId]; _i < _a.length; _i++) {\r\n                var modulePatcher = _a[_i];\r\n                if (semver.satisfies(moduleVersion, modulePatcher.versionSpecifier)) {\r\n                    modifiedModule = modulePatcher.patch(modifiedModule, modulePath);\r\n                    if (channel_1.channel) {\r\n                        var name_1 = modulePatcher.publisherName || moduleId;\r\n                        channel_1.channel.addPatchedModule(name_1, moduleVersion);\r\n                    }\r\n                }\r\n            }\r\n            return patchedModules[modulePath] = modifiedModule;\r\n        }\r\n        return originalModule;\r\n    };\r\n}\r\nexports.makePatchingRequire = makePatchingRequire;\r\n//# sourceMappingURL=patchRequire.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/diagnostic-channel/dist/src/patchRequire.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel/package.json":
/*!**********************************************************!*\
  !*** ../../node_modules/diagnostic-channel/package.json ***!
  \**********************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"diagnostic-channel","version":"1.1.1","main":"./dist/src/channel.js","types":"./dist/src/channel.d.ts","scripts":{"build":"tsc","lint":"eslint ./ --fix","clean":"rimraf ./dist","test":"mocha ./dist/tests/**/*.js","debug":"mocha --inspect-brk ./dist/tests/**/*.js"},"homepage":"https://github.com/Microsoft/node-diagnostic-channel","bugs":{"url":"https://github.com/Microsoft/node-diagnostic-channel/issues"},"repository":{"type":"git","url":"https://github.com/Microsoft/node-diagnostic-channel.git"},"description":"Provides a context-saving pub/sub channel to connect diagnostic event publishers and subscribers","dependencies":{"semver":"^7.5.3"},"devDependencies":{"@types/mocha":"^2.2.40","@types/node":"~8.0.0","mocha":"^3.2.0","rimraf":"^2.6.1","sinon":"1.17.6","typescript":"4.1.2"},"files":["dist/src/**/*.d.ts","dist/src/**/*.js","LICENSE","README.md","package.json"],"license":"MIT"}');

/***/ })

};
;
import AuthProvider from '@/auth/auth-provider';
import { ApplicationShellProvider } from '@iprox/react-ui';
import { ReactNode } from 'react';

import { ApplicationShellLayout } from '@/components/application-shell-layout';

const Layout = ({ children }: { children: ReactNode }) => {
  return (
    <AuthProvider>
      <ApplicationShellProvider>
        <ApplicationShellLayout>{children}</ApplicationShellLayout>
      </ApplicationShellProvider>
    </AuthProvider>
  );
};

export default Layout;

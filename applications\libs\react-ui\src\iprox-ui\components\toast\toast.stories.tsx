import type { Meta, StoryObj } from '@storybook/react';

import { Toast, showToast } from './toast';

const meta: Meta<typeof Toast> = {
  title: 'iprox-ui/components/toast',
  component: Toast,
};

export default meta;
type Story = StoryObj<typeof Toast>;

export const Default: Story = {
  name: 'default',
  render: () => (
    <>
      <Toast />
      <button onClick={() => showToast('Default message', { type: 'default' })}>Show Toast</button>
    </>
  ),
};

export const Error: Story = {
  name: 'error',
  render: () => (
    <>
      <Toast />
      <button onClick={() => showToast('Error message', { type: 'error' })}>Show Toast</button>
    </>
  ),
};

export const Info: Story = {
  name: 'info',
  render: () => (
    <>
      <Toast />
      <button onClick={() => showToast('Info message', { type: 'info' })}>Show Toast</button>
    </>
  ),
};

export const Success: Story = {
  name: 'success',
  render: () => (
    <>
      <Toast />
      <button onClick={() => showToast('Success message', { type: 'success' })}>Show Toast</button>
    </>
  ),
};

export const Warning: Story = {
  name: 'warning',
  render: () => (
    <>
      <Toast />
      <button onClick={() => showToast('Warning message', { type: 'warning' })}>Show Toast</button>
    </>
  ),
};

import cx from 'classnames';
import { FieldHookConfig, FieldInputProps, useField } from 'formik';
import { useId, useMemo } from 'react';

import { DescriptionAttributes, InputAttributes, useFormField } from '../../hooks/use-form-field.hook';
import { CheckboxFieldDefinition } from '../../models/form.models';
import { borderClassname } from '../../utils/border-classname';

export function CheckboxField(props: CheckboxFieldDefinition) {
  const [field, meta, _helpers] = useField<boolean | string[]>(props as unknown as FieldHookConfig<boolean | string[]>);
  const [_labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'checkbox');

  return (
    <fieldset className="mb-5">
      <Checkbox
        value={props.name}
        inputProps={inputProps}
        field={field}
        description={props.description}
        error={meta?.touched ? meta.error : undefined}
        descriptionProps={descriptionProps}
      >
        {props.label}
      </Checkbox>
      {meta?.touched && (
        <div className="font-text text-error mt-1.5 text-sm" {...errorMessageProps}>
          {meta.error}
        </div>
      )}
    </fieldset>
  );
}

interface CheckboxProps {
  children: string;
  inputProps: InputAttributes;
  value: string;
  field: FieldInputProps<Array<string> | boolean>;
  description?: string;
  error?: string;
  descriptionProps?: DescriptionAttributes;
  displayModeRow?: boolean;
}

export function Checkbox({
  inputProps,
  field,
  value,
  children,
  description,
  error,
  descriptionProps,
  displayModeRow = false,
}: CheckboxProps) {
  const id = useId();

  const fieldValue = useMemo(
    () => (typeof field?.value === 'boolean' ? field.value : field.value?.indexOf(value) !== -1),
    [field?.value, value]
  );

  return (
    <label
      htmlFor={id}
      className={cx(
        'font-text text-body relative flex w-fit cursor-pointer flex-row text-sm font-medium last:mb-0',
        {
          'mb-0': displayModeRow,
        },
        { 'mb-3.5': !displayModeRow }
      )}
    >
      <div className="relative mr-3 h-5 w-5">
        <span
          className={cx('bg-content-extra-lite absolute inset-0 scale-0 rounded-md opacity-50', {
            'animate-ripple': fieldValue,
          })}
        ></span>
        <input
          {...inputProps}
          id={id}
          {...field}
          className={cx(
            'relative z-10 h-full w-full cursor-pointer rounded-md align-top focus:ring-0 focus:ring-offset-0',
            fieldValue ? 'text-body !border-highlight' : 'text-base-25',
            borderClassname(error, value)
          )}
          value={value}
          checked={fieldValue}
        />
      </div>
      <div>
        {children}
        {description && (
          <p {...descriptionProps} className="font-text text-base-75 text-sm">
            {description}
          </p>
        )}
      </div>
    </label>
  );
}

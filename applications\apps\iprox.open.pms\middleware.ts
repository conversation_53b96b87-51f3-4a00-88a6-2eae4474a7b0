import { PrismaClient } from '@prisma/client';
import { withAuth } from 'next-auth/middleware';
import createMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';

export const prismaClient = new PrismaClient({
  datasourceUrl: process.env.PMS_NEXT_DATABASE_URL,
});

// Supported locales. English is only used for development purposes.
const locales = process.env.NODE_ENV === 'development' ? ['nl', 'en'] : ['nl'];
const publicPages = ['/login', '/environment-public'];

const intlMiddleware = createMiddleware({
  locales,

  // If this locale is matched, pathnames work without a prefix (e.g. `/about`)
  defaultLocale: 'nl',

  // Locale will always default to nl or used locale in URL.
  localeDetection: false,
  alternateLinks: false,
  localePrefix: 'as-needed',
});

const authMiddleware = withAuth(
  function onSuccess(req) {
    return intlMiddleware(req);
  },
  {
    callbacks: {
      authorized: async ({ req }) => {
        const sessionToken =
          req.cookies.get('__Secure-next-auth.session-token')?.value ||
          req.cookies.get('next-auth.session-token')?.value;
        return Boolean(sessionToken);
      },
    },
    pages: {
      signIn: '/login',
    },
  }
);

export default async function middleware(req: NextRequest) {
  const publicPathnameRegex = RegExp(`^(/(${locales.join('|')}))?(${publicPages.join('|')})?/?$`, 'i');

  const isPublicPage = publicPathnameRegex.test(req.nextUrl.pathname);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const response: NextResponse = isPublicPage ? intlMiddleware(req) : await (authMiddleware as any)(req);

  if (process.env.IPROX_OPEN_API_URL) {
    const apiUrl = new URL(process.env.IPROX_OPEN_API_URL);

    response.headers.set(
      'Content-Security-Policy',
      `default-src 'self'; connect-src 'self' ${apiUrl.origin}; img-src 'self' ${apiUrl.origin} data:; script-src 'unsafe-inline' 'unsafe-eval' 'self' https://www.google-analytics.com; font-src 'self' https://fonts.googleapis.com; style-src 'self' 'unsafe-inline';`
    );
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Permissions-Policy', 'payment=()');
    response.headers.set('Referrer-Policy', 'origin-when-cross-origin');
    response.headers.set('Strict-Transport-Security', 'max-age=31536000;includeSubDomains;preload');
  }

  return response;
}

export const config = {
  matcher: ['/((?!api|_next|.*\\..*).*)'],
};

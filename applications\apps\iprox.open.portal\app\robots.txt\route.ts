import { getWooCategories } from '@/services/woo-service';
import { padZero } from '@/utils/pad-zero';

export const dynamic = 'force-dynamic';

const generateRobotTxtFile = async () => {
  const baseUrl = process.env.BASE_URL || '';

  const sitemaps =
    baseUrl !== ''
      ? (await getWooCategories()).wooCategories.map(
          (wooCategory) => `${baseUrl}/sitemap/sitemapindex-diwoo-infocat${padZero(wooCategory.wooCategoryId, 3)}.xml`
        )
      : [];

  const rootAllow =
    baseUrl === '' || baseUrl.endsWith('.azurewebsites.net') || baseUrl.endsWith('.iprox-open.nl')
      ? 'Disallow: /'
      : 'Allow: /';

  // prettier-ignore
  const lines = [
    'User-agent: *',
    rootAllow,
    'Disallow: /api/',
    ...sitemaps.map((url) => `Sitemap: ${url}`)
  ];

  return lines.join('\n');
};

export async function GET() {
  try {
    const fileContent = await generateRobotTxtFile();

    return new Response(fileContent, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Content-Disposition': 'inline; filename="robots.txt"',
        'X-Frame-Options': 'DENY',
      },
    });
  } catch (e) {
    return new Response('File not found', {
      status: 404,
    });
  }
}

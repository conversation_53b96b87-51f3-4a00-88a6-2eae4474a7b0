import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import cx from 'classnames';
import Link from 'next/link';
import { cloneElement, isValidElement, useEffect, useMemo, useState } from 'react';

export interface NavigationLinkProps {
  name: string;
  icon?: React.ReactElement<React.SVGProps<SVGSVGElement>>;
  route: string;
  isActive: boolean;
  subMenu?: NavigationLinkProps[];
}

export function NavigationLink({ name, route, isActive, icon, subMenu }: NavigationLinkProps) {
  const [isOpenSubMenu, setIsOpenSubMenu] = useState(false);
  const isSubLinkActive = useMemo(() => subMenu && subMenu?.findIndex((item) => item.isActive) > -1, [subMenu]);

  useEffect(() => {
    setIsOpenSubMenu(isSubLinkActive ?? false);
  }, [isSubLinkActive]);

  const renderSubIcon = (icon: React.ReactElement<React.SVGProps<SVGSVGElement>>, isSubIcon = false) => {
    if (isValidElement(icon)) {
      return cloneElement(icon, {
        className: cx({ 'h-4 w-4': isSubIcon }, { 'h-6 w-6': !isSubIcon }, 'shrink-0'),
        'aria-hidden': true,
      });
    }
    return undefined;
  };

  return (
    <li
      className={cx(
        { 'is-active': isActive || isSubLinkActive },
        { 'is-open': isOpenSubMenu && !(isActive || isSubLinkActive) },
        'rounded-md'
      )}
    >
      <Link
        prefetch={false}
        href={route}
        onClick={(e) => {
          if (subMenu) {
            e.preventDefault();
            setIsOpenSubMenu(isSubLinkActive ? true : !isOpenSubMenu);
          }
        }}
        className={cx(
          { 'is-active': isActive },
          'group flex gap-x-3 rounded-md bg-transparent p-2 text-sm font-semibold leading-6'
        )}
      >
        {icon ? renderSubIcon(icon) : null}
        {name}
        {subMenu && (
          <span className="font-text ml-auto h-5 w-5 self-center text-sm font-light">
            {isOpenSubMenu ? <ChevronUpIcon /> : <ChevronDownIcon />}
          </span>
        )}
      </Link>
      {subMenu && isOpenSubMenu && (
        <ul>
          {subMenu?.map((subMenuItem) => (
            <li key={subMenuItem.name}>
              <Link
                prefetch={false}
                href={subMenuItem.route}
                className={cx(
                  {
                    'text-primary font-semibold': subMenuItem.isActive,
                  },
                  { 'text-base-00': isSubLinkActive },
                  'font-text-default group ml-4 flex items-center gap-x-3 bg-transparent p-2 text-sm capitalize leading-6'
                )}
              >
                {subMenuItem.icon ? renderSubIcon(subMenuItem.icon, true) : null}
                {subMenuItem.name}
              </Link>
            </li>
          ))}
        </ul>
      )}
    </li>
  );
}

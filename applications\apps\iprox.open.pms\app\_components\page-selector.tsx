import { useTranslations } from 'next-intl';
import { useId } from 'react';

interface PageSelectorProps {
  page: string;
  checked?: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export function PageSelector({ page, checked, onChange }: PageSelectorProps) {
  const t = useTranslations('pages');

  return (
    <input
      id={useId()}
      type="checkbox"
      checked={checked}
      onChange={onChange}
      className="text-primary-content rounded focus:ring-0 focus:ring-offset-0"
      aria-label={
        checked
          ? t('deselectPage', {
              page,
            })
          : t('selectPage', {
              page,
            })
      }
    />
  );
}

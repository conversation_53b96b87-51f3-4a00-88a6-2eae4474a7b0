import { Meta } from '@storybook/react';

import { DropdownMenuItem } from '../dropdown/dropdown';
import { UserMenu, UserMenuProps } from './user-menu';

const mockAvatar =
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80';

const menu: DropdownMenuItem[] = [
  {
    label: 'Logout',
    route: '/logout',
  },
];

const meta: Meta<typeof UserMenu> = {
  title: 'components/usermenu',
  component: UserMenu,
  argTypes: {},
};

export default meta;

export const Default = (args: UserMenuProps) => (
  <div className="w-100 flex flex-row justify-center">
    <UserMenu {...args} />
  </div>
);

Default.args = {
  displayName: 'Tom Cock',
  avatarSrc: mockAvatar,
  menuItems: menu,
};

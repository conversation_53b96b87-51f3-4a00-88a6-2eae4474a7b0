import { Button } from '@iprox/iprox-ui';
import { Modal } from '@iprox/react-ui';
import { signOut } from 'next-auth/react';
import { useTranslations } from 'next-intl';

interface LogoutModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function LogoutModal({ isOpen, onClose }: LogoutModalProps) {
  const t = useTranslations('logout');

  const handleLogout = async () => {
    signOut();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="sm:flex sm:items-start">
        <div className="mt-3 text-center sm:mt-0 sm:text-left">
          <h3 className="font-heading text-heading mb-5 block text-2xl font-bold">{t('logout')}</h3>
          <p className="font-text text-body block text-lg">{t('warningMessage')}</p>
        </div>
      </div>
      <div className="mt-4 grid grid-flow-col justify-end gap-2">
        <Button type="button" variant="primary" onClick={handleLogout}>
          {t('logout')}
        </Button>
        <Button type="button" variant="secondary" onClick={onClose}>
          {t('cancel')}
        </Button>
      </div>
    </Modal>
  );
}

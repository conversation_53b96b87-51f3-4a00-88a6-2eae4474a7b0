import { ALLOWED_FILE_EXTENSIONS } from '@/config/allowed-file-types';
import { useDossierUploadService } from '@/context/dossier-upload-context';
import { Button, FileWithDisallowed, MultipleFileUpload, Text } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import React, { useEffect, useMemo, useState } from 'react';

import { FileUploadItem } from './file-upload-item';

interface FileUploaderProps {
  dossierId: string;
  clickedFolderNodeId?: string;
  loading?: boolean;
  onUploadFiles: () => void;
  onClose: (inProgress: boolean) => void;
}

export function FileUploader({ dossierId, clickedFolderNodeId, loading, onUploadFiles, onClose }: FileUploaderProps) {
  const t = useTranslations('dossier');

  const { dossierUploads, uploadFiles, startStopProcess, clearUpload } = useDossierUploadService(dossierId);

  const [hasDisallowedFiles, setHasDisallowedFiles] = useState(false);

  const { selectedFiles, filesInProgress, includeDisallowedFiles } = useMemo(() => {
    let hasDisallowedFiles = false;
    const files =
      dossierUploads
        .map((uploadItem) => {
          const uploadId = Object.keys(uploadItem)[0];
          const file = { ...uploadItem[uploadId], uploadId };
          if (file.disallowed) hasDisallowedFiles = true;
          return file;
        })
        .filter((uploadItem) => uploadItem.isZipUpload === false) ?? [];

    const inProgress = files && (files.length <= 0 || files.some((file) => file.status === 'uploading'));

    return { selectedFiles: files, filesInProgress: inProgress, includeDisallowedFiles: hasDisallowedFiles };
  }, [dossierUploads]);

  useEffect(() => {
    setHasDisallowedFiles(includeDisallowedFiles);
  }, [includeDisallowedFiles]);

  return (
    <>
      <h3 className="font-heading text-heading block text-2xl font-bold">{t('uploadFiles')}</h3>
      {selectedFiles.length > 0 && <Text className="font-text text-base-35 mb-5 text-sm">{t('clearToUpload')}</Text>}

      {selectedFiles.length > 0 && (
        <div className="w-100 flex justify-end px-2">
          <Button
            type="button"
            variant="tertiary"
            className="text-sm"
            disabled={filesInProgress}
            onClick={() => {
              selectedFiles.forEach((file) => clearUpload(file.uploadId));
              startStopProcess(false);
              setHasDisallowedFiles(false);
            }}
          >
            {t('clearAll')}
          </Button>
        </div>
      )}

      <div className="max-h-[60vh] overflow-auto">
        {selectedFiles.length > 0 ? (
          selectedFiles.map((file, index) => (
            <div key={`${file.fileName}-${index}`} className="mb-2 p-2">
              <FileUploadItem
                fileId={file.uploadId}
                fileName={file.fileName}
                fileSize={file.fileSize}
                progress={file.progress}
                error={file.disallowed ? t('disallowedFileType') : file.error}
                status={file.status}
                loaded={file.loaded}
                estimated={file.estimated}
                onClickRemove={(fileId) => clearUpload(fileId)}
              />
            </div>
          ))
        ) : (
          <MultipleFileUpload
            onFilesSelect={(files) => {
              if (files.some((file) => file.disallowed)) {
                setHasDisallowedFiles(true);
              } else {
                setHasDisallowedFiles(false);
              }
              uploadFiles(files as FileWithDisallowed[], () => null, clickedFolderNodeId);
            }}
            maxFileCount={50}
            allowedFileExtensions={ALLOWED_FILE_EXTENSIONS}
          />
        )}
      </div>

      {hasDisallowedFiles && <Text className="text-error mt-4 text-sm">{t('disallowedFiles')}</Text>}

      <div className="mt-8 flex flex-row gap-8">
        <Button
          variant="primary"
          type="submit"
          onClick={() => {
            if (selectedFiles.every((file) => file.status === 'uploadComplete')) {
              onUploadFiles();
            }
            startStopProcess(true);
          }}
          disabled={filesInProgress || loading || hasDisallowedFiles}
        >
          {selectedFiles.length && selectedFiles.every((file) => file.status === 'uploadComplete')
            ? t('done')
            : t('uploadFiles')}
        </Button>
        <Button
          type="button"
          variant="secondary"
          onClick={() => {
            onClose(filesInProgress);
          }}
          disabled={loading}
        >
          {t('close')}
        </Button>
      </div>
    </>
  );
}

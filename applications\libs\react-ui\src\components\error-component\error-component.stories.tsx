import { Meta, StoryObj } from '@storybook/react';

import { ErrorComponent } from './error-component';

const meta: Meta<typeof ErrorComponent> = {
  title: 'components/errorComponent',
  component: ErrorComponent,
  argTypes: {
    error: { message: 'This is a dummy error message' },
  },
};

export default meta;

type Story = StoryObj<typeof ErrorComponent>;

export const Default: Story = {
  name: 'default',
  args: {
    error: { name: '404', message: 'This is a dummy error message' },
    serviceDeskLink: 'https://www.example.com',
  },
  decorators: [
    (Story) => (
      <div className="w-100 flex h-screen flex-row items-center justify-center">
        <Story />
      </div>
    ),
  ],
};

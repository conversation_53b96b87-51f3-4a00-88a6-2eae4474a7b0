import { components } from '@/iprox-open.interface';
import { FieldDefinition, FieldType, ValueTypes } from '@iprox/iprox-ui';
import { useMemo } from 'react';

import { getValidationRulesMapping } from './get-validation-rules-mapping';

export function useDynamicFieldMapper(
  dynamicFields: components['schemas']['DynamicFieldDto'][],
  dynamicFieldValues: components['schemas']['DossierDynamicFieldValueDto'][]
) {
  return useMemo<FieldDefinition<FieldType, ValueTypes>[]>(() => {
    if (dynamicFields && dynamicFields.length) {
      return dynamicFields?.map<FieldDefinition<FieldType, ValueTypes>>((field) => ({
        id: field?.id,
        name: field?.alias,
        label: field?.label,
        description: field?.helpText,
        fieldType: field?.dynamicFieldType as unknown as FieldType,
        isRequired: field?.isRequired,
        ...(field?.options
          ? { options: Object.entries(field?.options).map(([key, value]) => ({ label: value, value: key })) }
          : {}),
        // TODO: Fix this not matching OpenAPI interface
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        value: dynamicFieldValues.find((item) => item?.dynamicFieldId === field?.id)?.value as any,
        defaultValues: field.defaultValue,
        validationRules: getValidationRulesMapping(field.isRequired, field.validationRules),
      }));
    }

    return [];
  }, [dynamicFields, dynamicFieldValues]);
}

import { Meta, StoryObj } from '@storybook/react';

import { Spinner } from './spinner';

const meta: Meta<typeof Spinner> = {
  title: 'components/spinner',
  component: Spinner,
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof Spinner>;

export const Default: Story = {
  name: 'default',
  args: {},
};

export const Primary: Story = {
  name: 'primary',
  args: {
    color: 'primary',
  },
};

'use client';

import { sanitizeHtml } from '@iprox/react-ui';

export function RenderSearchHighlights({
  highlightedContent,
  fallbackContent,
}: {
  highlightedContent?: string[];
  fallbackContent: string;
}) {
  if (highlightedContent && highlightedContent.length > 0) {
    return highlightedContent.map((highlight, index) => (
      <span key={index} dangerouslySetInnerHTML={{ __html: sanitizeHtml(highlight) }} />
    ));
  }

  return <span dangerouslySetInnerHTML={{ __html: sanitizeHtml(fallbackContent) }} />;
}

import { ArrowDownCircleIcon } from '@heroicons/react/24/outline';
import { Meta, StoryObj } from '@storybook/react';

import { IconButton } from './icon-button';

const meta: Meta<typeof IconButton> = {
  title: 'iprox-ui/components/icon-button',
  component: IconButton,
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof IconButton>;

export const Default: Story = {
  name: 'default',
  args: {
    children: <ArrowDownCircleIcon />,
  },
};

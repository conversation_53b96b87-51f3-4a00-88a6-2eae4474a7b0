import { components } from '@/iprox-open.interface';

import { BaseNode, FileNode, FolderNode, Node } from './../models/file-structure';

/**
 * map the api response for rootNode into Node type type
 * @param apiNode DossierFileStructureViewDto api response for rootNode
 * @returns Node type
 */
export const mapNode = (apiNode: components['schemas']['DossierFileStructureViewDto'], parentNode?: BaseNode): Node => {
  if (apiNode.nodeType === 'File') {
    return mapFileNode(apiNode, parentNode);
  }

  if (apiNode.nodeType === 'Folder') {
    return mapFolderNode(apiNode, parentNode);
  }

  throw Error('NodeType unrecognized');
};

/**
 * map the folderNodes
 * @param apiNode DossierFileStructureViewDto api response for rootNode
 * @returns FolderNode type
 */
export const mapFolderNode = (
  apiNode: components['schemas']['DossierFileStructureViewDto'],
  parentNode?: BaseNode
): FolderNode => {
  const folderNode: FolderNode = {
    nodeId: apiNode.nodeId,
    parentId: parentNode?.nodeId,
    isSelected: parentNode?.isSelected ?? false,
    children: [],
    nodeName: apiNode.nodeName ?? '', // WHY.
    nodeType: 'Folder',
    totalDescendantSize: apiNode.totalDescendantSize ?? 0, // why.
    isExpanded: false,
    numberOfDescendantFiles: apiNode.numberOfDescendantFiles ?? 0,
    numberOfDescendantFolders: apiNode.numberOfDescendantFolders ?? 0,
  };

  if (apiNode.children) {
    folderNode.children = apiNode.children.map((child) => mapNode(child, folderNode));
  }

  return folderNode;
};

/**
 * map the fileNodes
 * @param apiNode DossierFileStructureViewDto api response for rootNode
 * @returns FileNode type
 */
export const mapFileNode = (
  apiNode: components['schemas']['DossierFileStructureViewDto'],
  parentNode?: BaseNode
): FileNode => {
  return {
    nodeId: apiNode.nodeId,
    parentId: parentNode?.nodeId,
    isSelected: parentNode?.isSelected ?? false,
    nodeName: apiNode.nodeName ?? '', // WHY.
    nodeType: 'File',
    size: apiNode.size ?? 0,
    blobReference: apiNode.blobReference ?? undefined,
  };
};

/**
 * Update a node.
 * @param node Node type
 * @param nodeIdToUpdate nodeId (string)
 * @param updatedNode node to be add to the rootNode
 * @returns updated RootNode match to Node type type
 */
export const updateNodeAndChildren = (node: Node, nodeIdToUpdate: string, updatedNode: Node): Node => {
  if (node.nodeId === nodeIdToUpdate) {
    return updatedNode;
  }

  if (node.nodeType === 'Folder') {
    return {
      ...node,
      children: node.children.map((child) => updateNodeAndChildren(child, nodeIdToUpdate, updatedNode)),
    };
  }

  return node;
};

/**
 * update selection state of all children/grand children of passed node
 * @param node Node type
 * @param isSelected boolean
 * @returns Updated Node
 */
export const updateNodeAndChildrenSelection = (node: Node, isSelected: boolean): Node => {
  const updatedNode: Node = {
    ...node,
    isSelected,
    ...(node.nodeType === 'Folder'
      ? {
          children: node.children.map((child) => updateNodeAndChildrenSelection(child, isSelected)),
        }
      : {}),
  };

  return updatedNode;
};

export const isNodeSelectedInHierarchy = (node: Node, currentItem: Node): boolean => {
  if (currentItem.isSelected) {
    return true;
  }

  if (
    currentItem.nodeType === 'Folder' &&
    currentItem?.children?.some((child) => isNodeSelectedInHierarchy(node, child))
  ) {
    return true;
  }

  return false;
};

/**
 * find the parent/grandparent nodes of selected node
 * @param currentNode Node type node
 * @param selectedNodeId selected nodeID
 * @param parentNodes founded parent Nodes array
 * @returns Node type type array of founded parent nodes
 */
export const findParentNodes = (
  currentNode: Node | null,
  selectedNodeId: string,
  parentNodes: Node[] = []
): Node[] | null => {
  if (currentNode) {
    if (currentNode.nodeId === selectedNodeId) {
      return parentNodes;
    }

    const newHierarchy = [...parentNodes, currentNode];

    if (currentNode.nodeType === 'Folder' && currentNode?.children.length > 0) {
      for (const childNode of currentNode.children) {
        const result = findParentNodes(childNode, selectedNodeId, newHierarchy);

        if (result) {
          return result;
        }
      }
    }
  }

  return null;
};

/**
 * deselecting children and descendants of a node
 * @param node Node type
 * @param selectedNodes Set<string>
 */
const deselectChildrenAndDescendants = (node: Node, selectedNodes: Set<Node>) => {
  if (node.nodeType === 'Folder') {
    node.children.forEach((child) => {
      deselectChildrenAndDescendants(child, selectedNodes);
    });
  }

  selectedNodes = removeNodeFromSet(selectedNodes, node);
};

export const toggleSelectionForAllNodes = (
  root: FolderNode,
  isSelected: boolean
): { updatedRoot: Node; selectedNodes: Node[] } => {
  const selectedNodes = new Set<Node>();

  const toggleNodeSelection = (node: Node): Node => {
    const updatedNode = updateNodeAndChildrenSelection(node, isSelected);

    const isRootNode = root.nodeId === node.nodeId;

    if (!isRootNode && isSelected) {
      selectedNodes.add(updatedNode);
    }

    if (isRootNode && updatedNode.nodeType === 'Folder') {
      updatedNode.children?.forEach((child) => toggleNodeSelection(child));
    }

    return updatedNode;
  };

  const updatedRoot = toggleNodeSelection(root);

  return {
    updatedRoot,
    selectedNodes: Array.from(selectedNodes),
  };
};

const isNodeSelected = (nodeId: string, nodeSet: Set<Node>) =>
  Array.from(nodeSet).some((existingNode) => existingNode.nodeId === nodeId);

const addNodeIfNotExists = (node: Node, nodeSet: Set<Node>) => {
  if (!isNodeSelected(node.nodeId, nodeSet)) {
    nodeSet.add(node);
  }
};

const removeChildNodes = (children: Node[], nodeSet: Set<Node>) => {
  children.forEach((child) => {
    nodeSet = removeNodeFromSet(nodeSet, child);
  });
  return nodeSet;
};

/**
 * handle updating ancestors of selected node
 * @param root Node type
 * @param nodeToUpdate selected Node to update
 * @returns Node tree
 */
export const updateAncestorsRecursively = (
  root: FolderNode,
  nodeToUpdate: Node,
  currentSelectedNodes: Node[]
): { updatedRoot: Node; selectedNodes: Node[] } => {
  let updatedRoot: Node = { ...root };
  let selectedNodes = new Set(currentSelectedNodes);

  const updatedTree = updateNodeAndChildrenSelection(nodeToUpdate, nodeToUpdate.isSelected);
  updatedRoot = updateNodeAndChildren(updatedRoot, updatedTree.nodeId, updatedTree);

  const updateAncestors = (node: Node) => {
    if (node.isSelected) {
      addNodeIfNotExists(node, selectedNodes);

      if (node.nodeType === 'Folder') {
        removeChildNodes(node.children, selectedNodes);
      }
    } else {
      selectedNodes = removeNodeFromSet(selectedNodes, node);
    }

    const parentNodes = findParentNodes(updatedRoot, node.nodeId);

    parentNodes?.forEach((parentNode) => {
      const updatedParentNode = { ...parentNode };

      updatedParentNode.isSelected =
        updatedParentNode.nodeType === 'Folder' && updatedParentNode.children.every((child) => child.isSelected);

      updatedRoot = updateNodeAndChildren(updatedRoot, updatedParentNode.nodeId, updatedParentNode);

      if (parentNode.nodeType === 'Folder') {
        if (updatedParentNode.isSelected) {
          addNodeIfNotExists(updatedParentNode, selectedNodes);

          selectedNodes = removeChildNodes(parentNode.children, selectedNodes);
        } else {
          parentNode.children.forEach((child) => {
            if (child.isSelected && !isNodeSelected(child.nodeId, selectedNodes)) {
              selectedNodes.add(child);
            }
          });
        }
      }

      updateAncestors(updatedParentNode);
    });
  };

  deselectChildrenAndDescendants(nodeToUpdate, selectedNodes);
  updateAncestors(nodeToUpdate);

  return { updatedRoot, selectedNodes: [...selectedNodes].map((id) => id) };
};

function removeNodeFromSet(nodeSet: Set<Node>, node: Node): Set<Node> {
  return new Set([...nodeSet].filter(({ nodeId }) => nodeId !== node.nodeId));
}

export function updateFileSizes(rootNode: FolderNode, nodeIdsToRemove: string[]) {
  for (const nodeId of nodeIdsToRemove) {
    const node = rootNode.children.find((i) => i.nodeId === nodeId);
    const parents = findParentNodes(rootNode, nodeId, []);

    if (node && parents) {
      for (const parent of parents) {
        if (parent.nodeType === 'Folder') {
          if (node.nodeType === 'Folder') {
            parent.totalDescendantSize = parent.totalDescendantSize - node.totalDescendantSize;
          } else {
            parent.totalDescendantSize = parent.totalDescendantSize - node.size;
          }
        }
      }
    }
  }
}

/**
 * remove selected nodes from the tree
 * @param rootNode Node type
 * @param nodeIdsToRemove array of NodeIds:string
 */
export function removeNodesById(rootNode: FolderNode, nodeIdsToRemove: string[]): FolderNode {
  updateFileSizes(rootNode, nodeIdsToRemove);
  rootNode.children = rootNode.children.filter((node) => !nodeIdsToRemove.includes(node.nodeId));

  for (const childNode of rootNode.children) {
    if (childNode.nodeType === 'Folder') {
      removeNodesById(childNode, nodeIdsToRemove);
    }
  }

  return rootNode;
}

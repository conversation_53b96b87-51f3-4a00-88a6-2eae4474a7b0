{"name": "react-ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/react-ui/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint", "options": {"maxWarnings": 0}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/react-ui/jest.config.ts"}}, "storybook": {"executor": "@nx/storybook:storybook", "options": {"port": 4400, "configDir": "libs/react-ui/.storybook"}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@nx/storybook:build", "outputs": ["{options.outputDir}"], "options": {"outputDir": "dist/storybook/react-ui", "configDir": "libs/react-ui/.storybook"}, "configurations": {"ci": {"quiet": true}}}}}
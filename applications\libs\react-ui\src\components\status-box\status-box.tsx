import { components } from '@/iprox-open.interface';
import { Form, Formik, useFormikContext } from 'formik';
import { useFormatter, useTranslations } from 'next-intl';
import { ReactNode, useEffect, useMemo } from 'react';

import { FieldType } from '../../iprox-ui';
import { DateTimePickerField } from '../../iprox-ui/components/forms/fields/date-time-picker/date-time-picker';
import { DossierViewLink } from '../dossier-view-link/dossier-view-link';

export type DossierStatus = 'Draft' | 'Published' | 'Unpublished' | 'Deleted';

export type EditStatus = {
  status: 'new' | 'modified' | 'published' | 'unpublished';
  date: string | undefined;
};

export type DossierLiveStatus = {
  status: DossierStatus;
  date: string | undefined;
};

export type PublishDates = { fromDate: Date | string | null; toDate: Date | string | null };

type StatusBoxProps = {
  children?: ReactNode;
  editStatus: EditStatus;
  dossierLiveStatus?: DossierLiveStatus | null;
  publishDates?: PublishDates | null;
  pages: components['schemas']['SearchPageDto'][];
  categoryId: string;
  dossierId: string;
  submitForm?: (values: PublishDates) => void;
  portalUrl: string;
};

type DatesForm = {
  fromDate: Date | string | null;
  toDate: Date | string | null;
};

type SearchPage = {
  label: string | null;
  slug: string | null;
};

const AutoSubmitForm = () => {
  const { values, submitForm } = useFormikContext<DatesForm>();

  useEffect(() => {
    if (values.fromDate) {
      submitForm();
    }
  }, [values, submitForm]);
  return null;
};

export function StatusBox({
  children,
  editStatus,
  dossierLiveStatus,
  publishDates,
  dossierId,
  categoryId,
  pages,
  portalUrl,
  submitForm,
}: StatusBoxProps) {
  const format = useFormatter();
  const t = useTranslations('components.statusBox');

  const getStatusTranslationKey = (status: DossierStatus | undefined): 'lastPublished' | 'lastUnpublished' | null => {
    switch (status) {
      case 'Published':
        return 'lastPublished';
      case 'Unpublished':
        return 'lastUnpublished';
      default:
        return null;
    }
  };

  const initialValues = useMemo(() => {
    return {
      fromDate: publishDates?.fromDate || new Date(),
      toDate: publishDates?.toDate || null,
    };
  }, [publishDates]);

  const pageSlug = useMemo((): SearchPage[] | undefined => {
    const pagesWithCategory = pages
      .filter((page) => page.pageState === 'Published')
      .filter((page) => {
        return page.categories?.some((category) => category.id === categoryId);
      })
      .sort((a, b) => a.categories.length - b.categories.length);

    return pagesWithCategory?.map((page) => ({ label: page.label, slug: page.slug }));
  }, [categoryId, pages]);

  return (
    <div className="sticky top-[110px] h-fit max-w-sm">
      <div className="rounded-input bg-highlight p-6">
        <div className="">
          <h2 className="text-base-00 font-heading mb-2 text-2xl font-semibold">{t('dossier')}</h2>
        </div>
        <Formik
          initialValues={initialValues}
          onSubmit={(values) => {
            submitForm?.(values);
          }}
        >
          <Form className="my-5 w-full">
            <AutoSubmitForm />
            <div className="mb-4">
              <DateTimePickerField
                name="fromDate"
                label={t('dossierDate')}
                fieldType={FieldType.DateTime}
                labelColor="text-base-00"
                descriptionColor="text-base-25"
                validationRules={[]}
                popperPlacement="left"
                description={t('dossierDateDescription')}
              />
            </div>
            <div className="mb-4">
              <DateTimePickerField
                name="toDate"
                label={t('expirationDate')}
                fieldType={FieldType.DateTime}
                labelColor="text-base-00"
                descriptionColor="text-base-25"
                validationRules={[]}
                popperPlacement="left"
                minDate={initialValues.fromDate}
                isClearable
                description={t('expirationDateDescription')}
              />
            </div>
          </Form>
        </Formik>

        {children}
      </div>
      <div className="rounded-input bg-highlight mt-6 p-6">
        <div className="">
          <h2 className="text-base-00 font-heading mb-2 text-2xl font-semibold">{t('title')}</h2>
          <p className="text-base-00 font-text capitalize">{t(editStatus.status)}</p>
          {editStatus.status === 'modified' && editStatus.date && (
            <p className="text-base-00 font-text">
              {format.dateTime(new Date(editStatus.date), { dateStyle: 'short' })}{' '}
              {format.dateTime(new Date(editStatus.date), { timeStyle: 'short' })}
            </p>
          )}
        </div>
        <div className="mt-3">
          {getStatusTranslationKey(dossierLiveStatus?.status) && (
            <label className="text-base-00 font-heading text-lg font-bold">
              {t(getStatusTranslationKey(dossierLiveStatus?.status)!)}
            </label>
          )}
          {dossierLiveStatus?.date && (
            <p className="text-base-00 font-text">
              {format.dateTime(new Date(dossierLiveStatus?.date as string), { dateStyle: 'short' })}{' '}
              {format.dateTime(new Date(dossierLiveStatus?.date as string), { timeStyle: 'short' })}
            </p>
          )}
        </div>
        {editStatus.status === 'published' || dossierLiveStatus?.status === 'Published' ? (
          <div className="mt-3">
            <label className="text-base-00 font-heading text-lg font-bold">{t('visibility')}</label>
            {pageSlug?.map((page) => (
              <div className="mb-2">
                <DossierViewLink key={page.slug} url={`${portalUrl}/${page.slug}/${dossierId}`} label={page.label} />
              </div>
            ))}
          </div>
        ) : null}
      </div>
    </div>
  );
}

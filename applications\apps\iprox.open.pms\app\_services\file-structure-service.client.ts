import { post } from '@/http/fetcher-axios';
import { components } from '@/iprox-open.interface';
import { AxiosProgressEvent } from 'axios';
import { KyInstance } from 'ky/distribution/types/ky';

export async function getNodeChildren(
  clientApi: KyInstance,
  folderNodeId: string
): Promise<components['schemas']['GetNodeChildrenResponse']> {
  try {
    return await clientApi
      .get(`dossier-file-structure/${folderNodeId}/node-children`)
      .json<components['schemas']['GetNodeChildrenResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function downloadFile(
  clientApi: KyInstance,
  nodeId: string
): Promise<components['schemas']['DownloadTokenResponse']> {
  try {
    return await clientApi.get(`download/token/${nodeId}`).json();
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function uploadZipToDossier(
  apiUrl: string,
  dossierId: string,
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<components['schemas']['UploadDossierZipResponse']> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    return await post<components['schemas']['UploadDossierZipResponse'], FormData>(
      `${apiUrl}/dossier/upload-zipped-dossier/${dossierId}`,
      formData,
      {
        onUploadProgress,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function deleteSelectedNodes(
  clientApi: KyInstance,
  dossierId: string,
  body: components['schemas']['DeleteFileFolderNodesCommand']
): Promise<components['schemas']['DeleteFileFolderNodesResponse']> {
  return await clientApi.delete(`dossier-file-structure/${dossierId}/nodes`, { json: body }).json();
}

export async function renameSelectedNode(
  clientApi: KyInstance,
  nodeId: string,
  body: components['schemas']['RenameFileFolderNodeCommand']
) {
  return await clientApi.patch(`dossier-file-structure/${nodeId}/name`, { json: body }).json();
}

export async function createFolder(
  clientApi: KyInstance,
  parentFolderNodeId: string,
  body: components['schemas']['CreateFolderCommand']
): Promise<components['schemas']['CreateFolderResponse']> {
  return await clientApi.post(`dossier-file-structure/${parentFolderNodeId}/folder`, { json: body }).json();
}

export async function uploadSingleFile(
  apiUrl: string,
  dossierId: string,
  file: File,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void,
  folderId?: string
) {
  try {
    const formData = new FormData();

    const url = folderId
      ? `${apiUrl}/dossier/${dossierId}/folder/${folderId}/upload-file`
      : `${apiUrl}/dossier/${dossierId}/upload-file`;

    formData.append('file', file);

    return await post(url, formData, {
      onUploadProgress,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  } catch (error) {
    return Promise.reject(error);
  }
}

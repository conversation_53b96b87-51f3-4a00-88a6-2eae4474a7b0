/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_instrument_node_modules_opentelemetry_instrumentation_build_esm_platform_node_sync_recursive-39498a";
exports.ids = ["_instrument_node_modules_opentelemetry_instrumentation_build_esm_platform_node_sync_recursive-39498a"];
exports.modules = {

/***/ "(instrument)/../../node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!***************************************************************************************!*\
  !*** ../../node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \***************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(instrument)/../../node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src sync recursive ^.*\\/lib\\/Connection$":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/ sync ^.*\/lib\/Connection$ ***!
  \*********************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src sync recursive ^.*\\/lib\\/Connection$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src sync recursive ^.*\\/lib\\/Pool$":
/*!***************************************************************************************!*\
  !*** ../../node_modules/diagnostic-channel-publishers/dist/src/ sync ^.*\/lib\/Pool$ ***!
  \***************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(instrument)/../../node_modules/diagnostic-channel-publishers/dist/src sync recursive ^.*\\/lib\\/Pool$";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "?28a9":
/*!****************************************************!*\
  !*** applicationinsights-native-metrics (ignored) ***!
  \****************************************************/
/***/ (() => {

/* (ignored) */

/***/ })

};
;
import { components } from '@/iprox-open.interface';
import { SearchParams, SearchType } from '@/models/search.model';
import { search } from '@/services/search-service';
import { Pagination, Text } from '@iprox/react-ui';
import { useTranslations } from 'next-intl';

import { ContentWrapper } from '@/components/content-wrapper';
import { ContentZones } from '@/components/content-zones';
import { DossierResult } from '@/components/dossier-result';
import { DossierSearchWrapper } from '@/components/dossiers-search-wrapper';
import { FileResult } from '@/components/file-result';
import { PageBreadcrumb } from '@/components/page-breadcrumb';

export const SearchPage = async ({
  page,
  searchParams,
}: {
  page: components['schemas']['SearchPageDto'];
  searchParams: SearchParams;
}) => {
  const defaultSearchType = [SearchType.Dossiers];
  const { count = '10', start = '0', query = '', type = defaultSearchType } = searchParams;

  let searchResults: components['schemas']['CognitiveSearchResponse'] | undefined;

  try {
    searchResults = await search({
      count,
      start,
      query,
      type,
      pageSlug: page.slug,
    });
  } catch (error) {
    console.error(error);
  }

  return (
    <ContentWrapper>
      <PageBreadcrumb page={page} />
      <div className="py-6">
        <h1 className="font-heading text-heading mb-4 hyphens-auto break-words text-3xl font-bold">{page.label}</h1>
        {page.pageZones.length > 0 && <ContentZones pageZones={page.pageZones} />}
      </div>
      <div className="pb-8">
        <DossierSearchWrapper defaultSearchType={defaultSearchType}>
          {searchResults && <SearchResults searchResults={searchResults} pageSlug={page.slug} />}
        </DossierSearchWrapper>
      </div>
    </ContentWrapper>
  );
};

const SearchResults: React.FC<{
  searchResults: components['schemas']['CognitiveSearchResponse'];
  pageSlug: string;
}> = ({ searchResults, pageSlug }) => {
  const { totalCount, count, start } = searchResults;
  const t = useTranslations('search');

  if (totalCount === 0) {
    return <Text className="font-text text-body mt-8 text-center text-sm">{t('noResults')}</Text>;
  }

  return (
    <>
      <div className="mt-12">
        {totalCount && (
          <Text className="font-heading mb-16 text-base font-bold">
            {totalCount}{' '}
            <Text className="font-heading inline text-base font-normal">{t('results', { count: totalCount })}</Text>
          </Text>
        )}
        {searchResults?.items.map((item) => (
          <div key={item.id} className="mb-16">
            {item?.type === 'Dossier' ? (
              <DossierResult
                page={pageSlug}
                item={item as components['schemas']['PublicCognitiveDossierSearchResultDto']}
              />
            ) : item?.type === 'File' ? (
              <FileResult
                pageSlug={pageSlug}
                item={item as components['schemas']['PublicCognitiveFileSearchResultDto']}
              />
            ) : null}
          </div>
        ))}
      </div>
      <div className="mt-10 flex flex-row justify-center">
        <Pagination count={count} start={start} totalCount={totalCount} maxVisiblePages={5} />
      </div>
    </>
  );
};

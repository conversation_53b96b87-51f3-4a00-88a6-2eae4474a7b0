import { useField } from 'formik';

import { FormFieldGroup } from '../../form-group/form-group';
import { useFormField } from '../../hooks/use-form-field.hook';
import { MultiFieldDefinition } from '../../models/form.models';
import { Checkbox } from '../checkbox-field/checkbox-field';

export function CheckboxGroup(props: MultiFieldDefinition) {
  const [field, meta, _helpers] = useField({ type: 'checkbox', ...props });
  const [labelProps, inputProps, descriptionProps, errorMessageProps] = useFormField(props, field, meta, 'checkbox');

  const formControlProps = {
    labelProps,
    descriptionProps,
    errorMessageProps,
  };

  return (
    <FormFieldGroup
      definition={props}
      errorMessage={meta.touched ? meta.error : undefined}
      {...formControlProps}
      displayModeRow={props.displayModeRow}
      value={field.value}
    >
      {props.options?.map(({ label, value }) => (
        <Checkbox
          key={`checkbox-field-${value}`}
          value={value}
          inputProps={inputProps}
          field={field}
          error={meta.touched ? meta.error : undefined}
          displayModeRow={props.displayModeRow}
        >
          {label}
        </Checkbox>
      ))}
    </FormFieldGroup>
  );
}

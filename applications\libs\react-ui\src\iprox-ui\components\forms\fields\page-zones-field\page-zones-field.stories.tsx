import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { ConfirmDialogProvider } from '../../../confirm-dialog/context/confirm-dialog.context';
import { FieldType } from '../../models/form.models';
import { PageZonesField } from './page-zones-field';

const meta: Meta<typeof PageZonesField> = {
  title: 'iprox-ui/forms/fields/page-zone-field',
  component: PageZonesField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof PageZonesField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'page-zone-field',
    value: [],
    fieldType: FieldType.PageZonesField,
  },
  decorators: [
    (Story) => (
      <ConfirmDialogProvider>
        <Story />
      </ConfirmDialogProvider>
    ),
  ],
};

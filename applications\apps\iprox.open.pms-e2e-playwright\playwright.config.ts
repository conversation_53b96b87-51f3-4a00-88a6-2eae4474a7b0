import { defineConfig, devices } from '@playwright/test';

import { baseUrl } from './config';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// require('dotenv').config();

const browserProjects = [
  {
    name: 'chromium',
    use: { ...devices['Desktop Chrome'] },
  },

  {
    name: 'firefox',
    use: { ...devices['Desktop Firefox'] },
  },

  {
    name: 'webkit',
    use: { ...devices['Desktop Safari'] },
  },
  // {
  //   name: 'Mobile Chrome',
  //   use: { ...devices['Pixel 5'] },
  // },
  // {
  //   name: 'Mobile Safari',
  //   use: { ...devices['iPhone 12'] },
  // },
  // {
  //   name: 'Microsoft Edge',
  //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
  // },
  // {
  //   name: 'Google Chrome',
  //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
  // },
];

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  expect: { timeout: 30 * 1000 },

  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [['html', { open: 'never' }]],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: baseUrl,

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    video: 'retain-on-failure',
    screenshot: 'only-on-failure',
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
    },
    //LightHouse Tests
    ...browserProjects.map((project) => ({
      ...project,
      name: `a11y-${project.name}`,
      testMatch: ['portal_lighthouse_check.spec.ts'],
      use: {
        ...project.use,
        viewport: { width: 1920, height: 1080 },
      },
      fullyParallel: true,
    })),
    // tests without automatic login
    ...browserProjects.map((project) => ({
      ...project,
      name: `login-${project.name}`,
      testMatch: '*.login.spec.ts',
      use: {
        ...project.use,
        viewport: { width: 1920, height: 1080 },
      },
    })),

    // tests with automatic login
    ...browserProjects.map((project) => ({
      ...project,
      name: `${project.name}`,
      testIgnore: ['*.login.spec.ts', 'portal_lighthouse_check.spec.ts'],
      use: {
        ...project.use,
        viewport: { width: 1920, height: 1080 },
        storageState: '../../../.auth/user.json',
      },
      dependencies: ['setup'],
    })),
  ],
});

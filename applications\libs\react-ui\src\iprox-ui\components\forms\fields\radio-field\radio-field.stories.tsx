import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { RadioGroup } from './radio-field';

const meta: Meta<typeof RadioGroup> = {
  title: 'iprox-ui/forms/fields/radiogroup',
  component: RadioGroup,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof RadioGroup>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    description: 'help text',
    name: 'radio-field',
    options: [
      {
        label: 'Label 01',
        value: 'label-1',
      },
      {
        label: 'Label 02',
        value: 'label-2',
      },
      {
        label: 'Label 03',
        value: 'label-3',
      },
    ],
    fieldType: FieldType.RadioButton,
  },
};

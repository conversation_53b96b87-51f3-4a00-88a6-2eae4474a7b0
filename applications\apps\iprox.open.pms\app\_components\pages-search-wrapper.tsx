'use client';

import { components } from '@/iprox-open.interface';
import { usePrevious } from '@/utils/previous.hook';
import { SelectField } from '@iprox/iprox-ui';
import { FieldType, Text, TextField } from '@iprox/react-ui';
import { Form, Formik, useFormikContext } from 'formik';
import { useTranslations } from 'next-intl';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo } from 'react';
import { useDebounce } from 'usehooks-ts';

interface PagesSearchWrapperProps {
  data: {
    dossierCategories: components['schemas']['DossierCategoryDto'][];
  };
  children?: React.ReactNode;
}

interface PagesSearchForm {
  query: string;
  status: string;
  type: string;
  category: string;
}

const SearchFormObserver: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const { values } = useFormikContext<PagesSearchForm>();
  const debouncedValues = useDebounce(values, 250);
  const previousValues = usePrevious<PagesSearchForm>(debouncedValues);

  useEffect(() => {
    const queryParams = new URLSearchParams({
      ...Object.fromEntries(searchParams),
      ...debouncedValues,
    });

    if (
      previousValues === undefined ||
      previousValues.query !== debouncedValues.query ||
      previousValues.status !== debouncedValues.status ||
      previousValues.type !== debouncedValues.type ||
      previousValues.category !== debouncedValues.category
    ) {
      queryParams.delete('count');
      queryParams.delete('start');
    }

    Object.entries(debouncedValues).forEach(([key, value]) => {
      if (value === '') {
        queryParams.delete(key);
      }
    });

    router.push(`${pathname}?${queryParams.toString()}`);
  }, [debouncedValues, pathname, previousValues, router, searchParams]);

  return null;
};

export function PagesSearchWrapper({ data, children }: PagesSearchWrapperProps) {
  const { dossierCategories } = data;

  const t = useTranslations('pages');
  const searchParams = useSearchParams();

  const initialState = useMemo(() => {
    return {
      query: searchParams.get('query') || '',
      status: searchParams.get('status') || '',
      type: searchParams.get('type') || '',
      category: searchParams.get('category') || '',
    };
  }, [searchParams]);

  const categoryFilterOptions = useMemo(() => {
    const allOption = { label: t('all'), value: '' };
    const options = dossierCategories.map((item) => ({
      label: item.label,
      value: item.id,
    }));
    return [allOption, ...options];
  }, [dossierCategories, t]);

  return (
    <div>
      <Text className="font-text text-base-100 mb-16 text-4xl font-bold">{t('heading')}</Text>
      <Formik
        initialValues={initialState}
        onSubmit={() => {
          // do nothing
        }}
      >
        <Form className="mb-5 grid grid-cols-5 gap-x-4">
          <SearchFormObserver />
          <div className="col-span-2">
            <TextField name="query" label={t('search')} fieldType={FieldType.Text} validationRules={[]} />
          </div>
          <div>
            <SelectField
              name="status"
              label={t('status')}
              fieldType={FieldType.Select}
              validationRules={[]}
              options={[
                { label: t('all'), value: '' },
                { label: t('published'), value: 'published' },
                { label: t('unpublished'), value: 'unpublished' },
              ]}
            />
          </div>
          <div>
            <SelectField
              name="type"
              label={t('type')}
              fieldType={FieldType.Select}
              validationRules={[]}
              options={[
                { label: t('all'), value: '' },
                { label: t('searchPage'), value: 'search' },
                { label: t('announcements'), value: 'announcements' },
              ]}
            />
          </div>
          <div>
            <SelectField
              name="category"
              label={t('category')}
              fieldType={FieldType.Select}
              validationRules={[]}
              options={categoryFilterOptions}
            />
          </div>
        </Form>
      </Formik>
      {children}
    </div>
  );
}

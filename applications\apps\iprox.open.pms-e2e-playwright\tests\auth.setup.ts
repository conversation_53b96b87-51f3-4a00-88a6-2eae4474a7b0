import { Page, expect, test as setup } from '@playwright/test';

import { password, username } from '../../../.auth/user';

const authFile = '../../../.auth/user.json';

async function navigateToLogin(page: Page, useSecondaryLogin: boolean): Promise<void> {
  if (useSecondaryLogin) {
    await page.goto('/api/auth/signin?callbackUrl=%2Fdashboard', { waitUntil: 'load' });
  } else {
    await page.goto('/login?callbackUrl=%2Fdashboard', { waitUntil: 'load' });
  }
  await page.waitForLoadState('domcontentloaded');
  await page.waitForTimeout(3000);
}

async function clickLoginButton(page: Page, useSecondaryLogin: boolean): Promise<void> {
  if (useSecondaryLogin) {
    await page.getByRole('button', { name: 'Sign in with Azure Active' }).nth(1).click(); // For PR env api auth login
  } else {
    await page.getByRole('button', { name: 'Inloggen met Azure' }).click({ timeout: 5000 }); // For dev env
  }
  await page.waitForLoadState('domcontentloaded');
}

async function fillUsername(page: Page, useSecondaryLogin: boolean): Promise<void> {
  if (useSecondaryLogin) {
    await page.getByPlaceholder('Email, phone, or Skype').fill(username);
  } else {
    await page.getByRole('textbox', { name: '<EMAIL>' }).fill(username);
  }
  await page.waitForLoadState('domcontentloaded');
}

async function fillPassword(page: Page, useSecondaryLogin: boolean): Promise<void> {
  const passwordField = page.getByPlaceholder('Password');
  await expect(passwordField).toBeVisible();
  await passwordField.fill(password);
  await page.waitForLoadState('domcontentloaded');

  const signinButton = page.getByRole('button', { name: 'Sign in' });
  await expect(signinButton).toBeVisible();
  await signinButton.click();

  if (useSecondaryLogin) {
    const yesButton = page.locator('input[type="submit"][value="Yes"]');
    await expect(yesButton).toBeVisible();
    await yesButton.click();
  } else {
    await page.waitForTimeout(1000 * 60); // Timeout to authenticate via authenticator app
  }
}

async function clickNextButton(page: Page): Promise<void> {
  const nextButton = page.getByRole('button', { name: 'Next' });
  await expect(nextButton).toBeVisible();
  await nextButton.click();
  await page.waitForLoadState('domcontentloaded');
}

setup('authenticate', async ({ page }) => {
  setup.slow();
  const useSecondaryLogin = username.includes('@testinfoprojects.onmicrosoft.com'); // Set this flag to switch between the two options

  await navigateToLogin(page, useSecondaryLogin);
  await clickLoginButton(page, useSecondaryLogin);
  await fillUsername(page, useSecondaryLogin);
  await clickNextButton(page);
  await fillPassword(page, useSecondaryLogin);

  await page.waitForLoadState('domcontentloaded');
  await page.waitForURL('/dashboard', { waitUntil: 'load' });
  await page.context().storageState({ path: authFile });
});

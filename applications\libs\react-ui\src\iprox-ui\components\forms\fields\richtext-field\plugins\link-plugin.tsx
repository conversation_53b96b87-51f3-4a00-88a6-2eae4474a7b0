import LexicalComposerContext from '@lexical/react/LexicalComposerContext';
import utils from '@lexical/utils';
import lexical from 'lexical';
import react from 'react';

import {
  CHANGE_LINK_COMMAND,
  LinkNode,
  REMOVE_LINK_COMMAND,
  TOGGLE_LINK_COMMAND,
  editLink,
  removeLink,
  toggleLink,
} from '../nodes/link';
import {
  CHANGE_SUPER_LINK_COMMAND,
  REMOVE_SUPER_LINK_COMMAND,
  TOGGLE_SUPER_LINK_COMMAND,
  editSuperLink,
  removeSuperLink,
  toggleSuperLink,
} from '../nodes/super-link';

type Props = {
  validateUrl?: (url: string) => boolean;
};

function LinkPlugin({ validateUrl }: Props) {
  const [editor] = LexicalComposerContext.useLexicalComposerContext();
  react.useEffect(() => {
    if (!editor.hasNodes([LinkNode])) {
      throw new Error('LinkPlugin: LinkNode not registered on editor');
    }

    return utils.mergeRegister(
      editor.registerCommand(
        TOGGLE_LINK_COMMAND,
        (payload) => {
          toggleLink(payload);
          return true;
        },
        lexical.COMMAND_PRIORITY_LOW
      ),
      editor.registerCommand(
        CHANGE_LINK_COMMAND,
        (payload) => {
          editLink(payload);
          return true;
        },
        lexical.COMMAND_PRIORITY_LOW
      ),
      editor.registerCommand(
        REMOVE_LINK_COMMAND,
        (payload) => {
          removeLink(payload);
          return true;
        },
        lexical.COMMAND_PRIORITY_LOW
      ),
      editor.registerCommand(
        TOGGLE_SUPER_LINK_COMMAND,
        (payload) => {
          toggleSuperLink(payload);
          return true;
        },
        lexical.COMMAND_PRIORITY_LOW
      ),
      editor.registerCommand(
        CHANGE_SUPER_LINK_COMMAND,
        (payload) => {
          editSuperLink(payload);
          return true;
        },
        lexical.COMMAND_PRIORITY_LOW
      ),
      editor.registerCommand(
        REMOVE_SUPER_LINK_COMMAND,
        (payload) => {
          removeSuperLink(payload);
          return true;
        },
        lexical.COMMAND_PRIORITY_LOW
      )
    );
  }, [editor, validateUrl]);

  return null;
}

export default LinkPlugin;

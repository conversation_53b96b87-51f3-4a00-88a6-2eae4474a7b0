import { getDossierLatestVersion } from '@/services/dossier-service.server';
import { getDossierVersions } from '@/services/dossier-service.server';
import { getSearchPages } from '@/services/page-service.server';

import PageContent from './page-content';

export async function DossierLoader({ id }: { id: string }) {
  const [dossierLatestVersion, dossierVersions, searchPages] = await Promise.all([
    getDossierLatestVersion(id),
    getDossierVersions(id),
    getSearchPages(),
  ]);

  return (
    <PageContent
      dossier={dossierLatestVersion?.dossier || null}
      versions={dossierVersions.dossiers ?? []}
      pages={searchPages.pages ?? []}
    />
  );
}

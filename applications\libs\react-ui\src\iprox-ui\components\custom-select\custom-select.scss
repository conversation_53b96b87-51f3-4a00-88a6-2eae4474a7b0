.custom-select {
  &__control {
    min-height: 50px !important;
    border-radius: theme('borderRadius.input') !important;
    padding: 5px 30px !important;
    cursor: pointer;

    &--is-focus {
      box-shadow: none;
    }

    &:hover {
      border-color: theme('colors.highlight');
      box-shadow: theme('boxShadow.input-hover');

      .custom-select__dropdown-indicator {
        color: theme('colors.highlight') !important;
      }
    }
  }

  &__placeholder {
    color: theme('colors.highlight') !important;
  }

  &__input-container {
    color: theme('colors.highlight') !important;
  }

  &__single-value {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  &__input {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;

    &:focus {
      box-shadow: none !important;
    }
  }

  &__value-container {
    font-family: theme('fontFamily.text') !important;
    font-weight: 400;
    font-size: 14px !important;
    color: theme('colors.base-00') !important;
    padding: 0 !important;
    height: 100%;
  }

  &__multi-value {
    border-radius: theme('borderRadius.input') !important;
    background-color: theme('colors.light-grey') !important;
    color: theme('colors.base-00') !important;
    padding: 5px 20px !important;
    font-weight: 600;
    position: relative;
    margin: 3px !important;
    margin-right: 10px !important;

    &__label {
      margin: 0 !important;
      padding: 0 !important;
      font-size: unset !important;
      line-height: 24px !important;
    }

    &__remove {
      position: absolute;
      right: -12px;
      top: -4px;
      color: theme('colors.base-85') !important;

      &:hover {
        background: transparent !important;
        color: theme('colors.base-100') !important;
      }
    }
  }

  &__indicators {
    color: theme('colors.highlight') !important;
  }

  &__menu {
    background-color: theme('colors.base-00') !important;
    border: 1px solid theme('colors.base-75') !important;
    border-radius: theme('borderRadius.input') !important;
    left: 0px !important;
    margin-top: 6px !important;
    overflow: hidden;

    /** scrollbar styles */
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: theme('colors.base-15');
      border-radius: 5px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: theme('colors.base-25');
      border-radius: 5px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: theme('colors.base-35');
    }
  }

  &__menu-list {
    padding: 0 !important;
  }

  &__option {
    background-color: transparent !important;

    &--is-focused {
      background-color: transparent;
    }
  }

  &__indicator-separator {
    display: none;
  }
}

import { Dossier<PERSON><PERSON><PERSON><PERSON>er, DossierListParams } from '@/models/dossier-list-filter.model';
import { IPageProps } from '@/models/page-props.model';
import { getDossierCategories } from '@/services/dossier-category-service.server';
import { getDossierPaged } from '@/services/dossier-service.server';
import { mapDossiers } from '@/utils/use-dossier-utils';

import DossiersList from '@/components/dossiers-list';
import { DossierSearchWrapper } from '@/components/dossiers-search-wrapper';

async function getDossierPagedData(searchParams: Record<string, string>) {
  const { count = '30', start = '0', sortField, sortDirection, query, type, status } = searchParams;

  const publishedFilterValue = status === 'published' ? 'isnotnull' : status === 'unpublished' ? 'isnull' : '';

  const filter: DossierListFilter = {
    'title:contains:': query,
    'categoryId:eq:': type,
    'published:': publishedFilterValue,
  };

  const filterString = Object.entries(filter)
    .reduce<string[]>((acc, [key, value]) => {
      if (value) {
        return [...acc, `${key}${value}`];
      }

      return acc;
    }, [])
    .join(',');

  const sortString = sortField && sortDirection ? `${sortField}:${sortDirection}` : '';

  const params: DossierListParams = {
    count,
    start,
    orderBy: sortString,
    filter: filterString,
  };

  return await getDossierPaged(params);
}

export default async function Page({ searchParams }: IPageProps) {
  // TODO: Promise.all() so they can run parallel. Or is this default behavior when rendering server-side?
  //       Also check cache on getDossierCategories. We don't have to call that API on each request.
  const dossierCategoriesData = await getDossierCategories();
  const dossierPagedData = await getDossierPagedData(searchParams as Record<string, string>);

  const formattedDossiers = mapDossiers(dossierPagedData?.items ?? []);

  return (
    <DossierSearchWrapper
      data={{
        dossierCategories: dossierCategoriesData?.dossierCategories ?? [],
        count: dossierPagedData?.count ?? 0,
        start: dossierPagedData?.start ?? 0,
        totalCount: dossierPagedData?.totalCount ?? 0,
      }}
    >
      <DossiersList dossiers={formattedDossiers} />
    </DossierSearchWrapper>
  );
}

import AxeBuilder from '@axe-core/playwright';
import * as OutlineIcons from '@heroicons/react/24/outline';
import { expect, test } from '@playwright/test';

import { loadStory } from '../../utils/common-utils';

type StatisticsCardArgs = {
  icon: keyof typeof OutlineIcons;
  label: string;
  value: number;
  animate?: boolean;
  animationDuration?: number;
};

const storyId = 'components-statistics-card--default';

test.describe('<StatisticsCard />', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await loadStory<StatisticsCardArgs>(page, storyId);

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('renders StatisticsCard without animation', async ({ page }) => {
    const props: StatisticsCardArgs = {
      icon: 'DocumentIcon',
      label: 'Number of files',
      value: 100,
      animate: false,
    };

    await loadStory<StatisticsCardArgs>(page, storyId, props);

    await expect(page.getByText(props.label)).toBeVisible();
    await expect(page.getByText(props.value.toString())).toBeVisible();
  });

  test('renders StatisticsCard with animation', async ({ page }) => {
    const props: StatisticsCardArgs = {
      icon: 'DocumentIcon',
      label: 'Number of files',
      value: 100,
      animate: true,
      animationDuration: 5000,
    };

    await loadStory<StatisticsCardArgs>(page, storyId, props);

    await page.waitForTimeout(props.animationDuration);
    await expect(page.getByText(props.value.toString())).toBeVisible();
  });
});

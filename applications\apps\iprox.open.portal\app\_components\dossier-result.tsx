import { components } from '@/iprox-open.interface';
import { FileTypeLabel, Text } from '@iprox/react-ui';
import { useFormatter, useTranslations } from 'next-intl';
import Link from 'next/link';
import { useMemo } from 'react';

import { RenderSearchHighlights } from './render-search-highlights';

export interface DossierResultProps {
  page: string;
  item: components['schemas']['PublicCognitiveDossierSearchResultDto'];
}

export function DossierResult({ page, item }: DossierResultProps) {
  const format = useFormatter();
  const t = useTranslations('dossierResult');

  const hasValidPublicationPeriod = useMemo(() => {
    if (!item?.publishFromDate || !item?.publishToDate) {
      return false;
    }

    const fromDate = new Date(item?.publishFromDate as string);
    const toDate = new Date(item?.publishToDate as string);

    return fromDate.getTime() > 0 && toDate.getTime() < new Date('9999-12-31').getTime();
  }, [item?.publishFromDate, item?.publishToDate]);

  const formattedFromDate = hasValidPublicationPeriod
    ? format.dateTime(new Date(item.publishFromDate as string), {
        dateStyle: 'long',
      })
    : null;
  const formattedToDate = hasValidPublicationPeriod
    ? format.dateTime(new Date(item.publishToDate as string), {
        dateStyle: 'long',
      })
    : null;

  return (
    <Link href={`/${page}/${item?.dossierId}`} className="group">
      <div className="mb-4 flex items-center gap-x-4">
        <FileTypeLabel label="Dossier" />
        <Text className="font-heading text-heading w-fit hyphens-auto break-words text-2xl font-semibold group-hover:underline">
          <RenderSearchHighlights
            highlightedContent={item.highlights?.DossierTitle}
            fallbackContent={item.dossierTitle}
          />
        </Text>
      </div>

      <div className="font-text text-body mb-3 line-clamp-2 text-lg">
        <RenderSearchHighlights
          highlightedContent={item.highlights?.DossierSummary}
          fallbackContent={item?.dossierSummary ?? ''}
        />
      </div>

      <Text className="font-text text-body text-sm">
        <span>
          {format.dateTime(new Date(item?.publishFromDate as string), {
            dateStyle: 'long',
          })}
        </span>
        {hasValidPublicationPeriod && (
          <span> {t('publishDateRange', { fromDate: formattedFromDate, toDate: formattedToDate })}</span>
        )}

        <span aria-hidden>{' | '}</span>
        {item?.dossierCategory}
      </Text>
    </Link>
  );
}

import { faker } from '@faker-js/faker';

import { colors } from '../config';
import * as api from '../enums/apiEnums';
import { Method } from '../enums/httpEnums';
import { DateTime } from '../utils/getCurrentDateTime';
import { BaseHelper } from './baseHelper';

export class DossierHelpersNew extends BaseHelper {
  // Create a dossier
  static async createDossier(title: string = 'test dossier', bearerToken: string, categoryId: string | null) {
    return this.makeRequest(Method.Post, api.EndpointsDossier.CREATE_DOSSIER, { title, categoryId }, bearerToken);
  }

  // Update a dossier
  static async updateDossier(dossierId: string, bearerToken: string) {
    const dynamicFieldValues = {
      'a63f2a45-e17c-46a8-8b6a-13de15932fab': DateTime.getCurrentDateTimeIsoFormat(),
      '6295da24-c598-415c-94b4-2e901b1b3602': `<p class="editor-paragraph" dir="ltr"><span>${faker.lorem.word()}</span></p>`,
      '2b62b7fa-a8c8-4649-a2b5-3ad4a8571864': faker.lorem.word(),
      '65805941-16a0-4554-a68d-5f5dc9c0dd3a': faker.lorem.word(),
      'a8f9c156-7aa8-4905-be7a-81aae88fd931': faker.lorem.word(),
      '9e651263-762b-475d-b011-a8d12cd2a7e8': [faker.lorem.word()],
      '6365b98d-6246-40d6-ba2f-ae107d67bf30': faker.lorem.word(),
    };

    const requestObject = {
      dossierId,
      summary: `<p class="editor-paragraph" dir="ltr"><span>${faker.lorem.word()}</span></p>`,
      dynamicFieldValues,
    };

    const response: any = await this.makeRequest(
      Method.Put,
      api.EndpointsDossier.UPDATE_DOSSIER,
      requestObject,
      bearerToken
    );
    return await response;
  }

  // Delete a dossier
  static async deleteDossier(dossierId: string, bearerToken: string): Promise<void> {
    const response: any = await this.makeRequest(
      Method.Delete,
      api.EndpointsDossier.DELETE_DOSSIER,
      { dossierId },
      bearerToken
    );
    console.log(colors.red + `Deleted dossier with id: ` + dossierId + colors.reset);
    return await response;
  }

  // Publish a dossier
  static async publishDossier(dossierId: string, bearerToken: string) {
    const response: any = await this.makeRequest(
      Method.Post,
      api.EndpointsDossier.PUBLISH_DOSSIER,
      { dossierId },
      bearerToken
    );
    return await response;
  }

  // Unpublish a dossier
  static async unpublishDossier(dossierId: string, bearerToken: string) {
    const response: any = await this.makeRequest(
      Method.Post,
      api.EndpointsDossier.UNPUBLISH_DOSSIER,
      { dossierId },
      bearerToken
    );
    return await response;
  }

  // Latest published version of the dossier
  static async latestPublishedVersionOfDossier(dossierId: string, bearerToken: string) {
    const response: any = await this.makeRequest(
      Method.Get,
      api.EndpointsDossier.LATEST_PUBLISHED_VERSION.replace('{ID}', dossierId),
      { dossierId },
      bearerToken
    );
    return await response;
  }

  // Latest version of the dossier
  static async latestVersionOfDossier(dossierId: string, bearerToken: string) {
    const response: any = await this.makeRequest(
      Method.Get,
      api.EndpointsDossier.LATEST_VERSION.replace('{ID}', dossierId),
      { dossierId },
      bearerToken
    );
    return await response;
  }

  // Get all the dossier category ids
  static async getDossierCategoryIds(bearerToken: string) {
    const response: any = await this.makeRequest(
      Method.Get,
      api.EndpointsDossierCategory.GET_DOSSIER_CATEGORIES,
      {},
      bearerToken
    );
    return await response;
  }

  // Get a random dossier category id based on 'require Publish Dates' flag
  static async getRandomCategoryUuid(requirePublishDates: boolean, bearerToken: string): Promise<string | null> {
    try {
      // Call the API endpoint
      const response = await this.getDossierCategoryIds(bearerToken);
      const data = await response.body;

      // Filter categories based on requirePublishDates
      const filteredCategories = await data.dossierCategories.filter(
        (category: { requirePublishDates: boolean }) => category.requirePublishDates === requirePublishDates
      );

      // If no matching categories, return null
      if (filteredCategories.length === 0) {
        return null;
      }

      // Select a random category and return its id
      const randomIndex = Math.floor(Math.random() * filteredCategories.length);
      console.log(colors.blue, 'getRandomCategoryUuid', filteredCategories[randomIndex].id, colors.reset);
      return filteredCategories[randomIndex].id;
    } catch (error) {
      console.error('Error fetching categories:', error);
      return null;
    }
  }

  // Create a search page
  static async createSearchPage(pageSlug: string = faker.lorem.word(), bearerToken: string): Promise<any> {
    const searchPageRequestObject = {
      label: pageSlug,
      slug: pageSlug,
    };
    return this.makeRequest(Method.Post, api.EndpointsPage.CREATE_SEARCH_PAGE, searchPageRequestObject, bearerToken);
  }

  // Update a search page with a category
  static async updateSearchPage(pageId: string, bearerToken: string, pageSlug: string): Promise<any> {
    // Get a random category id
    const categoryID = await this.getRandomCategoryUuid(false, bearerToken);

    // Prep request object and Update the search page with the category
    const searchPageUpdateRequestObject = {
      id: null, // using null for id as given in the swagger doc
      label: pageSlug,
      slug: pageSlug,
      categoryIds: [`${categoryID}`],
      zones: [],
    };

    return this.makeRequest(
      Method.Put,
      api.EndpointsPage.UPDATE_SEARCH_PAGE.replace('{ID}', pageId),
      searchPageUpdateRequestObject,
      bearerToken
    );
  }

  static async deleteSearchPage(pageId: string, bearerToken: string): Promise<any> {
    return this.makeRequest(
      Method.Delete,
      api.EndpointsPage.DELETE_SEARCH_PAGE.replace('{ID}', pageId),
      {},
      bearerToken
    );
  }

  static async publishSearchPage(pageId: string, bearerToken: string): Promise<any> {
    return this.makeRequest(Method.Get, api.EndpointsPage.PUBLISH_SEARCH_PAGE.replace('{ID}', pageId), {}, bearerToken);
  }

  static async unPublishSearchPage(pageId: string, bearerToken: string): Promise<any> {
    return this.makeRequest(
      Method.Get,
      api.EndpointsPage.UN_PUBLISH_SEARCH_PAGE.replace('{ID}', pageId),
      {},
      bearerToken
    );
  }
}

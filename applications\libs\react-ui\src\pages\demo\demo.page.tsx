import { TextField } from '../../src/components/forms/text-field/text-field';

export interface DemoPageProps {}

export function DemoPage(_props: DemoPageProps) {
  return (
    <div>
      <h1>Welcome to Button!</h1>
      <div className="btn btn-primary">Hello!</div>
      <div className="radial-progress" style={{ '--value': 0 } as React.CSSProperties}>
        0%
      </div>
      <div className="radial-progress" style={{ '--value': 20 } as React.CSSProperties}>
        20%
      </div>
      <div className="radial-progress" style={{ '--value': 60 } as React.CSSProperties}>
        60%
      </div>
      <div className="radial-progress" style={{ '--value': 80 } as React.CSSProperties}>
        80%
      </div>
      <div className="radial-progress" style={{ '--value': 100 } as React.CSSProperties}>
        100%
      </div>

      <hr />

      <div>
        <TextField />
      </div>

      <hr />
    </div>
  );
}

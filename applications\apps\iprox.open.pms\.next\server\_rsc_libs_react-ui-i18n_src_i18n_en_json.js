"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_libs_react-ui-i18n_src_i18n_en_json";
exports.ids = ["_rsc_libs_react-ui-i18n_src_i18n_en_json"];
exports.modules = {

/***/ "(rsc)/../../libs/react-ui-i18n/src/i18n/en.json":
/*!*************************************************!*\
  !*** ../../libs/react-ui-i18n/src/i18n/en.json ***!
  \*************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"components":{"pagination":{"pageAriaLabel":"Go to page {page}","pageDisplayLabel":"{page}","next":"next page","prev":"previous page"},"notificationCTA":{"notificationButtonAlt":"View notifications"},"hamburgerCTA":{"hamburgerButtonAlt":"Open sidebar"},"fileUpload":{"noFiles":"This document does not have any files yet.","multipleFileWarning":"Please select only one file.","fileTypeWarning":"File type not supported.","dropFileHere":"Drag your files here or","clickHereToBrowse":"click to browse","fileDropArea":"File drop area","fileInput":"File input","type":"Type","fileName":"File Name","size":"File size","maxFileCountWarning":"File limit exceeded: {maxFileCount}"},"statusBox":{"title":"Status","dossier":"Dossier","new":"New","modified":"Modified","published":"Published","unpublished":"Unpublished","lastPublished":"Latest publication","lastUnpublished":"Hidden on","visibility":"Visibility","ctaTextView":"View","ctaTextPublish":"Publish","dossierDate":"Dossier Date","expirationDate":"Expiration Date","dossierDateDescription":"This is the publication date of the file, as shown in the portal.","expirationDateDescription":"From this date onwards, the file will no longer be visible in the portal."},"dossierViewLink":{"view":"View","noAssociatedPage":"Category page not found"},"modal":{"close":"Close"},"fileStructure":{"amountOfFiles":"Number of files: {fileCount}","fileListLabel":"list","selectNode":"Selecteer {nodeName}","deselectNode":"Deselecteer {nodeName}","selectAllNodes":"Select all","folder":"Folder {folderName}","file":"File {fileName}","delete":"Delete","type":"Type","fileName":"File Name","size":"File size","addToThis":"add to this {nodeName}","rename":"Rename","cancelRename":"Cancel rename","saveName":"Save name","addFolder":"Add folder","uploadFiles":"Upload files"},"breadcrumb":{"home":"Home"},"imageUploader":{"upload":"Upload","uploading":"Uploading","imageAlt":"Dossier image preview","placeholder":"Add a photo","dropFileHere":"Drag your image here or","clickHereToBrowse":"click to browse"},"multipeTextField":{"removeItem":"Remove Item {item}","add":"Add"},"dateTimeRangePickerField":{"from":"From","to":"Until"},"richtexteditor":{"normal":"Normal","check":"Check List","h1":"Heading 1","h2":"Heading 2","h3":"Heading 3","h4":"Heading 4","h5":"Heading 5","h6":"Heading 6","number":"Numbered List","paragraph":"Normal","bullet":"Bullet List","undo":"Undo","redo":"Redo","shortcut":"Shortcut","bold":"Bold","boldDesc":"Format text as bold.","italic":"Italic","italicDesc":"Format text as italics.","underline":"Underline","underlineDesc":"Format text to underlined","insertLink":"Insert Link","insertSuperLink":"Insert Super Link","align":"Align","alignDesc":"Formatting options for text alignment","leftAlign":"Left Align","centerAlign":"Center Align","rightAlign":"Right Align","justifyAlign":"Justify Align","blockFormatLabel":"Formatting options for text style","insertTable":"Insert Table","columns":"Columns","rows":"Rows","tabelActions":{"insertRowAbove":"Insert {count, plural, =1 {row} other {# rows}} above","insertRowBelow":"Insert {count, plural, =1 {row} other {# rows}} below","insertColumnLeft":"Insert {count, plural, =1 {column} other {# columns}} left","insertColumnRight":"Insert {count, plural, =1 {column} other {# columns}} right","deleteColumn":"Delete column","deleteRow":"Delete row","deleteTable":"Delete table","removeRowHeader":"Remove row header","addRowHeader":"Add row header","removeColumnHeader":"remove column header","addColumnHeader":"Add column header"},"urlEditor":{"title":"Title","enterUrl":"Enter URL","insert":"Insert Link","cancel":"Cancel","invalidUrl":"Invalid URL","change":"Save","removeLink":"Remove Link","openlinkInNewTab":"Open link in new tab"},"err":{"deserializationError":"Error while setting value","serializationError":"Invalid value","invalidClipboard":"Invalid Clipboard Content"}},"confirmModal":{"warning":"Warning!","message":"Are you certain you want to navigate away? Any unsaved data will be lost!","cancel":"Cancel","confirm":"Confirm"},"colorPickerField":{"ariaLabel":"Color picker"},"validation":{"required":"Field \'\'{fieldName}\'\' is required","pastDate":"Field \'\'{fieldName}\'\' must be a future date","futureDate":"Field \'\'{fieldName}\'\' must be a past date","numberRange":"Field \'\'{fieldName}\'\' should be between {min} and {max}","stringRangeLength":"Field \'\'{fieldName}\'\' should be between {min} and {max} characters","stringMaxLength":"Field \'\'{fieldName}\'\' should be a maximum of {max} characters","integerOnly":"Field \'\'{fieldName}\'\' should be an integer","disallowedCharacters":"Field \'\'{fieldName}\'\' should not contain disallowed characters","dateTimeRange":"De endtime of field \'\'{fieldName}\'\' should be at a later moment than the starttime","bothFieldsRequiredOrEmpty":"Fill in both fields or leave them empty"},"buttonGroup":{"openDropdown":"Open dropdown"},"pageZoneField":{"pageZone":"Page Zone","richTextField":"Rich Text-veld","addZone":"Add a zone","deleteZone":"Delete zone","deleteZoneMessage":"Are you sure you want to delete this page zone?"},"AddZoneModal":{"further":"Further","column":"{count}"},"error":{"message":"Something went wrong, use the button to try again.\\nIf the problem persists, please contact our","serviceDesk":"service desk","tryAgain":"Try again","moreDetails":"Click here to see more technical details"},"publishDateContainer":{"dayRemaining":"{days} day left","daysRemaining":"{days} days left","dateRange":"Review period: {fromDate} t/m {toDate}"}}}');

/***/ })

};
;
import { fetcher } from '@/http/fetcher-public';
import { components } from '@/iprox-open.interface';

const apiUrl = process.env.IPROX_OPEN_API_URL;

export async function getUser(token: string): Promise<components['schemas']['GetLoggedInUserResponse']> {
  try {
    return await fetcher
      .get(`${apiUrl}/user/me`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      })
      .json<components['schemas']['GetLoggedInUserResponse']>();
  } catch (error) {
    return Promise.reject(error);
  }
}

import AxeBuilder from '@axe-core/playwright';
import { expect, test } from '@playwright/test';

import { loadStory } from '../../utils/common-utils';

test.describe('<FormBuilder />', () => {
  test('should not have any automatically detectable accessibility issues', async ({ page }) => {
    await loadStory(page, 'iprox-ui-forms-form-builder--default');

    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

    expect(accessibilityScanResults.violations).toEqual([]);
  });
});

import { type Locator, type Page, expect } from '@playwright/test';

export class SearchPage {
  private page: Page;
  private pageName: Locator;
  private pageStatus: Locator;
  private simplePageStatus: Locator;
  private slugInputField: Locator;
  private dossierCategoryDropdown: Locator;
  private dropdownOptions: Locator;
  private dropdownPublishAction: Locator;
  private dropdownUnPublishAction: Locator;
  private dropdownDeleteAction: Locator;
  private modalConfirmationButton: Locator;
  private modalCancelButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.pageName = this.page.locator(`//span[text()="Zoekpagina"]/following-sibling::span[1]`);
    this.pageStatus = this.page.locator(`//span[text()="Zoekpagina"]/following-sibling::span//child::span`);
    this.simplePageStatus = this.page.locator(
      `//span[text()="Eenvoudige pagina"]/following-sibling::span//child::span`
    );
    this.dossierCategoryDropdown = this.page.getByText('Categorieën', { exact: true });
    this.dropdownOptions = this.page.getByRole('button', { name: 'Toon opties' });
    this.dropdownDeleteAction = this.page.getByRole('button', { name: 'Verwijderen' });
    this.dropdownPublishAction = this.page.getByRole('button', { name: 'Publiceren' });
    this.dropdownUnPublishAction = this.page.getByRole('button', { name: 'Verbergen' });
    this.slugInputField = this.page.getByText('Slug');
  }

  async clickSlugInputField() {
    await this.slugInputField.click();
  }

  async clickDossierCategoryDropdown() {
    await this.dossierCategoryDropdown.click();
  }

  async selectDossierCategory(string: string) {
    await this.page.getByText(string, { exact: true }).click();
  }

  async clickDropdownOptions() {
    await this.dropdownOptions.click();
  }

  async selectZone(): Promise<void> {
    await this.page.getByText('4,4,4').click();
  }

  async clickDropdownPublishAction() {
    await this.dropdownPublishAction.click();
  }

  async clickDropdownUnpublishPage() {
    await this.dropdownUnPublishAction.click();
  }

  async clickDropdownDeletePage() {
    await this.dropdownDeleteAction.click();
  }

  async clickModalConfirmationButton() {
    this.modalConfirmationButton = this.page.getByRole('button', { name: 'Bevestigen' });
    await this.modalConfirmationButton.click();
  }

  async clickModalCancelButton() {
    this.modalCancelButton = this.page.getByRole('button', { name: 'Annuleren' });
    await this.modalCancelButton.click();
  }

  async assertPagePublishedSuccessfully() {
    await this.page.getByText('Pagina gepubliceerd').click();
  }

  async assertPublishButtonIsDisabled() {
    await this.clickDropdownOptions();
    const publishButton = this.page.getByRole('button', { name: 'Publiceren' });
    await expect(publishButton).toBeDisabled();
  }

  async assertPageStatus(status: string) {
    await expect(this.pageStatus).toHaveText(status);
  }

  async assertSimplePageStatus(status: string) {
    await expect(this.simplePageStatus).toHaveText(status);
  }
}

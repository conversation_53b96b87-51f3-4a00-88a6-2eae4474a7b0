import { components } from '@/iprox-open.interface';
import { getNavigationStructure } from '@/services/navigation-service.server';
import { Breadcrumb } from '@iprox/react-ui';

export async function PageBreadcrumb({
  page,
  dossierLabel,
}: {
  page?: components['schemas']['SimplePageDto'];
  dossierLabel?: string;
}) {
  const navigationStructure = await getNavigationStructure();

  if (!navigationStructure || !navigationStructure.navigation) {
    console.error('Navigation structure is undefined or invalid.');
    return null;
  }

  function findBreadcrumbsRecursively(
    navigation: components['schemas']['NavigationItemDto'][],
    currentPageId: string,
    breadcrumb: { slug: string; label: string }[] = []
  ): { slug: string; label: string }[] | null {
    for (const item of navigation) {
      const updatedBreadcrumbs = [
        ...breadcrumb,
        ...(item.page ? [{ slug: item.page.slug, label: item.page.title }] : []),
      ];

      if (item.id === currentPageId) {
        return updatedBreadcrumbs;
      }

      if (item.children) {
        const result = findBreadcrumbsRecursively(item.children, currentPageId, updatedBreadcrumbs);
        if (result) {
          return result;
        }
      }
    }

    return null;
  }

  let breadcrumbs: { slug: string; label: string }[] = [];
  if (page) {
    breadcrumbs = findBreadcrumbsRecursively(navigationStructure.navigation, page.id) || [
      { slug: page.slug, label: page.label },
    ];
  }

  if (dossierLabel) {
    breadcrumbs = [...breadcrumbs, { slug: '', label: dossierLabel }];
  }

  return <Breadcrumb items={breadcrumbs} />;
}

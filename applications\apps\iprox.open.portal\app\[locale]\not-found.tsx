import Link from 'next/link';

import { ContentWrapper } from '@/components/content-wrapper';

export const dynamic = 'force-dynamic';

export default function NotFound() {
  return (
    <ContentWrapper>
      <div className="flex h-full flex-1 flex-col items-center justify-center py-40">
        <h2>Not Found!</h2>
        <p>Could not find requested resource</p>
        <Link href="/" className="text-anchor underline underline-offset-2">
          Return to home
        </Link>
      </div>
    </ContentWrapper>
  );
}

'use client';

import React, { createContext, useReducer } from 'react';

import { ContextProvider, StateAction, StateContext } from '../../../models/context.model';
import { ApplicationShellActionTypes } from './application-shell.actions';

export interface ApplicationShellState {
  isSideNavigationOpen: boolean;
  isNotificationTrayOpen: boolean;
}

type ApplicationShellActions =
  | StateAction<ApplicationShellActionTypes, ApplicationShellState>
  | StateAction<ApplicationShellActionTypes, ApplicationShellState, unknown>;

const ApplicationShellContext = createContext<StateContext<ApplicationShellState, ApplicationShellActions>>({
  state: { isNotificationTrayOpen: false, isSideNavigationOpen: false },
  dispatch: () => null,
});

/**
 * reducer for Application Shell Context
 * @param state ApplicationShellState
 * @param action ActionType
 * @returns ApplicationShellState
 */
function ApplicationShellReducer(state: ApplicationShellState, action: ApplicationShellActions) {
  if (action.reducer) {
    if ('payload' in action) {
      return action.reducer(state, action.payload);
    }

    return action.reducer(state);
  }

  return state;
}

/**
 * Application Shell Context provider
 * @param param ApplicationShellProviderType
 * @returns ApplicationShellContext
 */
function ApplicationShellProvider({ children }: ContextProvider) {
  const [state, dispatch] = useReducer(ApplicationShellReducer, {
    isNotificationTrayOpen: false,
    isSideNavigationOpen: false,
  });

  return <ApplicationShellContext.Provider value={{ state, dispatch }}>{children}</ApplicationShellContext.Provider>;
}

/**
 * Application Shell Context hook
 * @returns ApplicationShellContext
 */
function useApplicationShellContext() {
  const context = React.useContext(ApplicationShellContext);

  if (context === undefined) {
    throw new Error('useApplicationShellContext must be used within a ApplicationShellProvider');
  }

  return context;
}

export { ApplicationShellProvider, useApplicationShellContext };

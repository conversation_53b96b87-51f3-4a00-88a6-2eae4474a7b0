import { components } from '@/iprox-open.interface';

export function getFontFileFormat(filePath: string) {
  const parts = filePath.split('.');
  if (parts.length > 1) {
    if (parts[parts.length - 1] === 'ttf') {
      return 'truetype';
    }
    if (parts[parts.length - 1] === 'otf') {
      return 'opentype';
    }
    return parts[parts.length - 1];
  }
  return '';
}

/**
 * get the fonts array and return the font style variabls and fontfaces
 * @param fonts components['schemas']['SiteAssetFontDto'][]
 * @param apiUrl baseUrl to complete the fontSource url(string)
 * @returns
 */
export function generateFontFaceRules(fonts: components['schemas']['SiteAssetFontDto'][], apiUrl: string) {
  const { fontFaces, styleVariables } = fonts.reduce(
    (
      acc: {
        fontFaces: string[];
        styleVariables: { [key: string]: string };
      },
      font
    ) => {
      const { descriptors, fonts: fontPaths, fontCategory } = font;
      const fontFamily = `${descriptors['font-family']}`;
      const fontSources = fontPaths
        .map((path) => `url(${apiUrl}/asset${path}) format(${getFontFileFormat(path)})`)
        .join(', ');

      if (fontCategory === 'Heading') {
        acc.styleVariables['--sys_font_family_heading'] =
          acc.styleVariables['--sys_font_family_heading'] ?? `${fontFamily}`;
      } else if (fontCategory === 'Text') {
        acc.styleVariables['--sys_font_family_text'] = acc.styleVariables['--sys_font_family_text'] ?? `${fontFamily}`;
      }

      const primaryFontFamily = fontFamily.split(',')[0].trim();

      const fontFace = `
        @font-face {
          font-family: ${primaryFontFamily};
          font-style: ${descriptors['font-style']?.toLowerCase()};
          font-weight: ${descriptors['font-weight']?.replace(/\D/g, '')};
          font-display: swap;
          src: ${fontSources};
        }
      `;

      acc.fontFaces.push(fontFace);

      return acc;
    },
    { fontFaces: [], styleVariables: {} }
  );

  if (!styleVariables['--sys_font_family_heading']) {
    styleVariables['--sys_font_family_heading'] = 'ArticulatCF';
  }
  if (!styleVariables['--sys_font_family_text']) {
    styleVariables['--sys_font_family_text'] = 'Inter';
  }

  return {
    fontFaces,
    styleVariables: Object.entries(styleVariables).map(([key, value]) => `${key}: ${value};`),
  };
}

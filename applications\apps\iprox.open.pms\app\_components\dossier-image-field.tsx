import { ALLOWED_IMAGE_FILE_TYPES } from '@/config/allowed-file-types';
import { updateDossierImage } from '@/services/dossier-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { ImageUploader } from '@iprox/iprox-ui';
import { showToast } from '@iprox/react-ui';
import { useAppSettings } from '@iprox/shared-context';
import { AxiosProgressEvent } from 'axios';
import { useTranslations } from 'next-intl';

interface DossierImageFieldProps {
  dossierId: string;
  imagePath?: string | null;
}

export function DossierImageField({ dossierId, imagePath }: DossierImageFieldProps) {
  const settings = useAppSettings();
  const t = useTranslations('dossier');

  const uploadDossierImage = async (image: File, handleProgress: (progressEvent: AxiosProgressEvent) => void) => {
    try {
      await updateDossierImage(settings.apiUrl, dossierId, image, handleProgress);
      showToast(t('imageUpdated'), { type: 'success' });
    } catch (error) {
      const errorMessages = await getErrorMessages(error);
      showToast(errorMessages, { type: 'error' });
      throw error;
    }
    return `${settings.apiUrl}/dossier/${dossierId}/image`;
  };

  return (
    <ImageUploader
      label={t('uploadImage')}
      allowedFileTypes={ALLOWED_IMAGE_FILE_TYPES}
      uploadPromise={uploadDossierImage}
      overflowText={t('changeImage')}
      initialImageSrc={imagePath ? `${settings.apiUrl}/dossier/${dossierId}/image` : undefined}
      wide
    />
  );
}

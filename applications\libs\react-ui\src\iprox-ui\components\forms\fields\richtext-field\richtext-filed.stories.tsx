import { withFormik } from '@bbbtech/storybook-formik';
import type { Meta, StoryObj } from '@storybook/react';

import { FieldType } from '../../models/form.models';
import { RichTextField } from './richtext-field';

const meta: Meta<typeof RichTextField> = {
  title: 'iprox-ui/forms/fields/richtext',
  component: RichTextField,
  decorators: [withFormik],
  argTypes: {
    fieldType: {
      table: {
        disable: true,
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof RichTextField>;

export const Default: Story = {
  name: 'default',
  args: {
    label: 'Label',
    defaultValue: `<h1 class="editor-heading-h1" dir="ltr"><span>Heading 1</span></h1><p class="editor-paragraph" dir="ltr"><span>this is a sample paragraph</span></p>`,
    description: 'help text',
    name: 'text-aria-field',
    fieldType: FieldType.RichText,
    contentEditorClass: 'h-[300px]',
    enableSuperlink: true,
  },
};

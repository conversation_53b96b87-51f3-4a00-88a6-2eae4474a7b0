import { post } from '@/http/fetcher-axios';
import { components } from '@/iprox-open.interface';
import { AxiosProgressEvent } from 'axios';
import { KyInstance } from 'ky/distribution/types/ky';

export async function createFontAsset(
  clientApi: KyInstance,
  fontConfig: components['schemas']['CreateFontAssetCommand']
): Promise<components['schemas']['CreateFontAssetResponse']> {
  return await clientApi
    .post('font-asset/create', { json: fontConfig })
    .json<components['schemas']['CreateFontAssetResponse']>();
}

export async function updateFontAsset(
  clientApi: KyInstance,
  fontConfig: components['schemas']['UpdateFontAssetCommand']
): Promise<components['schemas']['UpdateFontAssetResponse']> {
  return await clientApi
    .put('font-asset/update', { json: fontConfig })
    .json<components['schemas']['UpdateFontAssetResponse']>();
}

export async function deleteFontAsset(
  clientApi: KyInstance,
  body: components['schemas']['DeleteFontAssetCommand']
): Promise<components['schemas']['DeleteFontAssetResponse']> {
  return await clientApi.delete('font-asset', { json: body }).json<components['schemas']['DeleteFontAssetResponse']>();
}

export async function uploadFontFiles(
  apiUrl: string,
  fontAssetId: string,
  files: FileList,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<components['schemas']['GetFontAssetUploadedFontsResponse']> {
  try {
    const formData = new FormData();

    Array.from(files).forEach((file) => {
      formData.append('file', file);
    });

    return await post<components['schemas']['GetFontAssetUploadedFontsResponse'], FormData>(
      `${apiUrl}/font-asset/upload-fonts/${fontAssetId}`,
      formData,
      {
        onUploadProgress,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  } catch (error) {
    return Promise.reject(error);
  }
}

export async function deleteFontFiles(
  clientApi: KyInstance,
  body: components['schemas']['DeleteFontsCommand']
): Promise<components['schemas']['DeleteFontsResponse']> {
  return await clientApi
    .delete('font-asset/fonts', { json: body })
    .json<components['schemas']['DeleteFontsResponse']>();
}

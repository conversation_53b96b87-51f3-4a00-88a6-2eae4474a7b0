import { DocumentPlusIcon } from '@heroicons/react/24/outline';
import cx from 'classnames';
import { useTranslations } from 'next-intl';
import React, { useRef, useState } from 'react';

import { getFileExtension } from '../../../utils/file-extention-util';
import { Text } from '../text/text';
import { ALLOWED_FONT_FILE_EXTENSIONS, ALLOWED_FONT_FILE_TYPES } from './utils/allowd-font-file-types';

interface FontUploaderProps {
  onFileSelect: (file: FileList) => void;
}

export function FontUploader({ onFileSelect }: FontUploaderProps) {
  const t = useTranslations('components.fileUpload');
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDragging(false);

    const files = event.dataTransfer.files;
    if (files) {
      handleFileSelect(files);
    }
  };

  const handleOnChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files ? event.target.files : null;
    if (files) {
      handleFileSelect(files);
    }
  };

  const handleFileSelect = (files: FileList) => {
    if (
      Array.from(files).some((file) => {
        const mimeType = file.type;
        const ext = getFileExtension(file.name).toLowerCase();

        return !(ALLOWED_FONT_FILE_TYPES.includes(mimeType) || ALLOWED_FONT_FILE_EXTENSIONS.includes(ext));
      })
    ) {
      alert(t('fileTypeWarning'));
      return;
    }

    onFileSelect(files);
  };

  const handleIconClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div
      className={cx(
        'bg-base-15 after:bg-base-100 after:z-9 relative rounded-lg px-6 py-6 after:pointer-events-none after:absolute after:inset-0 after:rounded-lg after:opacity-0 after:transition-all after:duration-300 after:ease-in-out',
        { 'after:opacity-75': isDragging }
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      aria-label={t('dropFileHere')}
      role="group"
    >
      <div className="border-content-lite rounded-lg border-2 border-dashed px-16 py-7">
        <input
          type="file"
          accept="application/font-woff, application/font-woff2, .ttf, .otf, .woff2, .eot"
          ref={fileInputRef}
          className="hidden"
          onChange={handleOnChange}
          aria-label={t('fileInput')}
          multiple
        />
        <div className="mb-5 flex justify-center">
          <DocumentPlusIcon className="group-hover:text-base-100 h-20 w-20" />
        </div>
        <Text className="font-heading text-heading text-center text-base font-bold">
          {t('dropFileHere')}
          <span
            className="block cursor-pointer underline"
            onClick={handleIconClick}
            role="button"
            tabIndex={0}
            onKeyDown={(event) => {
              if (event.key === 'Enter') {
                handleIconClick();
              }
            }}
          >
            {t('clickHereToBrowse')}
          </span>
        </Text>
      </div>
    </div>
  );
}

'use client';

import { AppSettingsContext } from '@/services/settings.context';
import ky from 'ky';
import { KyInstance } from 'ky/distribution/types/ky';
import { useContext, useMemo } from 'react';

/** Use this to call the iprox.open API in **client** components */
const clientApi = ky.extend({
  headers: {
    'Content-Type': 'application/json',
  },
  cache: 'no-store',
});

/** Returns a `KyInstance` which can be used to call the iprox.open API. It takes care Authorization headers and a prefixUrl */
export const useClientApi = (): KyInstance => {
  const context = useContext(AppSettingsContext);

  if (context === undefined) {
    throw new Error('useClientApi must be used within a AppSettingsProvider');
  }

  return useMemo(
    () =>
      clientApi.extend({
        prefixUrl: context.apiUrl,
      }),
    [context]
  );
};

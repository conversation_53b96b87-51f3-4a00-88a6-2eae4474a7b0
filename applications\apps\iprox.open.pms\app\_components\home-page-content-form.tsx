'use client';

import { useClientApi } from '@/http/fetcher-api.client';
import { components } from '@/iprox-open.interface';
import { updateHomePage } from '@/services/page-service.client';
import { getErrorMessages } from '@/utils/error-handler';
import { pageZoneMapper } from '@/utils/page-zone-mapper';
import { getSortedPageZones, reorderZones } from '@/utils/page-zone-utils';
import { Button, FieldType, FormBuilder, FormSubmitValues, PageZone, showToast } from '@iprox/iprox-ui';
import { useTranslations } from 'next-intl';
import { useMemo, useState } from 'react';

interface HomePageContentFormProps {
  pageZones: components['schemas']['PageZoneDto'][];
}

export function HomePageContentForm({ pageZones }: HomePageContentFormProps) {
  const clientApi = useClientApi();
  const t = useTranslations('site');

  const [loading, setLoading] = useState(false);

  const formFields = useMemo(() => {
    return [
      {
        name: 'pageContent',
        label: t('pageContent'),
        fieldType: FieldType.PageZonesField,
        enableSuperlink: true,
        value: getSortedPageZones(pageZones),
        validationRules: [],
      },
    ];
  }, [pageZones, t]);

  const handleSubmit = (values: FormSubmitValues) => {
    setLoading(true);
    updateHomePage(clientApi, {
      zones: pageZoneMapper(Array.isArray(values.pageContent) ? reorderZones(values.pageContent as PageZone[]) : []),
    })
      .then(() => {
        showToast(t('contentUpdated'), { type: 'success' });
      })
      .catch(async (error) => {
        const errorMessages = await getErrorMessages(error);
        showToast(errorMessages, { type: 'error' });
      })
      .finally(() => setLoading(false));
  };

  return (
    <FormBuilder
      fields={formFields}
      onSubmit={handleSubmit}
      disableButton={loading}
      buttons={
        <Button variant="primary" type="submit" disabled={loading} className="mt-[56px]">
          {t('save')}
        </Button>
      }
    />
  );
}

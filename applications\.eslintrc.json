{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}, {"sourceTag": "scope:shared", "onlyDependOnLibsWithTags": ["scope:shared"]}, {"sourceTag": "scope:admin", "onlyDependOnLibsWithTags": ["scope:shared", "scope:admin"]}, {"sourceTag": "scope:client", "onlyDependOnLibsWithTags": ["scope:shared", "scope:client"]}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["@infoprojects/eslint-config", "prettier"], "rules": {}}, {"files": ["*.js", "*.jsx"], "extends": ["@infoprojects/eslint-config", "prettier"], "rules": {}}, {"files": "*.json", "parser": "jsonc-eslint-parser", "rules": {}}], "extends": ["plugin:storybook/recommended"]}
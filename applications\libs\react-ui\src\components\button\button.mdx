import { ArgsT<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@storybook/blocks';

import { Button } from './button';
import * as ButtonStories from './button.stories';

<Meta of={ButtonStories} />

# Button

This is the `Button` component.

<Canvas>
  <Story of={ButtonStories} name="primary" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="primary disabled" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="primary with icon left" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="primary with icon right" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="secondary" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="secondary disabled" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="secondary with icon left" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="secondary with icon right" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="tertiary" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="tertiary disabled" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="tertiary with icon left" />
</Canvas>

<Canvas>
  <Story of={ButtonStories} name="tertiary with icon right" />
</Canvas>

## Props

<ArgsTable of={Button} />

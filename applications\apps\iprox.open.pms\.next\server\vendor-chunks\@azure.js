"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@azure";
exports.ids = ["vendor-chunks/@azure"];
exports.modules = {

/***/ "(instrument)/../../node_modules/@azure/abort-controller/dist-esm/src/AbortController.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/@azure/abort-controller/dist-esm/src/AbortController.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortController: () => (/* binding */ AbortController),\n/* harmony export */   AbortError: () => (/* binding */ AbortError)\n/* harmony export */ });\n/* harmony import */ var _AbortSignal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AbortSignal */ \"(instrument)/../../node_modules/@azure/abort-controller/dist-esm/src/AbortSignal.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * This error is thrown when an asynchronous operation has been aborted.\n * Check for this error by testing the `name` that the name property of the\n * error matches `\"AbortError\"`.\n *\n * @example\n * ```ts\n * const controller = new AbortController();\n * controller.abort();\n * try {\n *   doAsyncWork(controller.signal)\n * } catch (e) {\n *   if (e.name === 'AbortError') {\n *     // handle abort error here.\n *   }\n * }\n * ```\n */\nclass AbortError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = \"AbortError\";\n    }\n}\n/**\n * An AbortController provides an AbortSignal and the associated controls to signal\n * that an asynchronous operation should be aborted.\n *\n * @example\n * Abort an operation when another event fires\n * ```ts\n * const controller = new AbortController();\n * const signal = controller.signal;\n * doAsyncWork(signal);\n * button.addEventListener('click', () => controller.abort());\n * ```\n *\n * @example\n * Share aborter cross multiple operations in 30s\n * ```ts\n * // Upload the same data to 2 different data centers at the same time,\n * // abort another when any of them is finished\n * const controller = AbortController.withTimeout(30 * 1000);\n * doAsyncWork(controller.signal).then(controller.abort);\n * doAsyncWork(controller.signal).then(controller.abort);\n *```\n *\n * @example\n * Cascaded aborting\n * ```ts\n * // All operations can't take more than 30 seconds\n * const aborter = Aborter.timeout(30 * 1000);\n *\n * // Following 2 operations can't take more than 25 seconds\n * await doAsyncWork(aborter.withTimeout(25 * 1000));\n * await doAsyncWork(aborter.withTimeout(25 * 1000));\n * ```\n */\nclass AbortController {\n    // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n    constructor(parentSignals) {\n        this._signal = new _AbortSignal__WEBPACK_IMPORTED_MODULE_0__.AbortSignal();\n        if (!parentSignals) {\n            return;\n        }\n        // coerce parentSignals into an array\n        if (!Array.isArray(parentSignals)) {\n            // eslint-disable-next-line prefer-rest-params\n            parentSignals = arguments;\n        }\n        for (const parentSignal of parentSignals) {\n            // if the parent signal has already had abort() called,\n            // then call abort on this signal as well.\n            if (parentSignal.aborted) {\n                this.abort();\n            }\n            else {\n                // when the parent signal aborts, this signal should as well.\n                parentSignal.addEventListener(\"abort\", () => {\n                    this.abort();\n                });\n            }\n        }\n    }\n    /**\n     * The AbortSignal associated with this controller that will signal aborted\n     * when the abort method is called on this controller.\n     *\n     * @readonly\n     */\n    get signal() {\n        return this._signal;\n    }\n    /**\n     * Signal that any operations passed this controller's associated abort signal\n     * to cancel any remaining work and throw an `AbortError`.\n     */\n    abort() {\n        (0,_AbortSignal__WEBPACK_IMPORTED_MODULE_0__.abortSignal)(this._signal);\n    }\n    /**\n     * Creates a new AbortSignal instance that will abort after the provided ms.\n     * @param ms - Elapsed time in milliseconds to trigger an abort.\n     */\n    static timeout(ms) {\n        const signal = new _AbortSignal__WEBPACK_IMPORTED_MODULE_0__.AbortSignal();\n        const timer = setTimeout(_AbortSignal__WEBPACK_IMPORTED_MODULE_0__.abortSignal, ms, signal);\n        // Prevent the active Timer from keeping the Node.js event loop active.\n        if (typeof timer.unref === \"function\") {\n            timer.unref();\n        }\n        return signal;\n    }\n}\n//# sourceMappingURL=AbortController.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/abort-controller/dist-esm/src/AbortController.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/abort-controller/dist-esm/src/AbortSignal.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/@azure/abort-controller/dist-esm/src/AbortSignal.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortSignal: () => (/* binding */ AbortSignal),\n/* harmony export */   abortSignal: () => (/* binding */ abortSignal)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/// <reference path=\"../shims-public.d.ts\" />\nconst listenersMap = new WeakMap();\nconst abortedMap = new WeakMap();\n/**\n * An aborter instance implements AbortSignal interface, can abort HTTP requests.\n *\n * - Call AbortSignal.none to create a new AbortSignal instance that cannot be cancelled.\n * Use `AbortSignal.none` when you are required to pass a cancellation token but the operation\n * cannot or will not ever be cancelled.\n *\n * @example\n * Abort without timeout\n * ```ts\n * await doAsyncWork(AbortSignal.none);\n * ```\n */\nclass AbortSignal {\n    constructor() {\n        /**\n         * onabort event listener.\n         */\n        this.onabort = null;\n        listenersMap.set(this, []);\n        abortedMap.set(this, false);\n    }\n    /**\n     * Status of whether aborted or not.\n     *\n     * @readonly\n     */\n    get aborted() {\n        if (!abortedMap.has(this)) {\n            throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n        }\n        return abortedMap.get(this);\n    }\n    /**\n     * Creates a new AbortSignal instance that will never be aborted.\n     *\n     * @readonly\n     */\n    static get none() {\n        return new AbortSignal();\n    }\n    /**\n     * Added new \"abort\" event listener, only support \"abort\" event.\n     *\n     * @param _type - Only support \"abort\" event\n     * @param listener - The listener to be added\n     */\n    addEventListener(\n    // tslint:disable-next-line:variable-name\n    _type, listener) {\n        if (!listenersMap.has(this)) {\n            throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n        }\n        const listeners = listenersMap.get(this);\n        listeners.push(listener);\n    }\n    /**\n     * Remove \"abort\" event listener, only support \"abort\" event.\n     *\n     * @param _type - Only support \"abort\" event\n     * @param listener - The listener to be removed\n     */\n    removeEventListener(\n    // tslint:disable-next-line:variable-name\n    _type, listener) {\n        if (!listenersMap.has(this)) {\n            throw new TypeError(\"Expected `this` to be an instance of AbortSignal.\");\n        }\n        const listeners = listenersMap.get(this);\n        const index = listeners.indexOf(listener);\n        if (index > -1) {\n            listeners.splice(index, 1);\n        }\n    }\n    /**\n     * Dispatches a synthetic event to the AbortSignal.\n     */\n    dispatchEvent(_event) {\n        throw new Error(\"This is a stub dispatchEvent implementation that should not be used.  It only exists for type-checking purposes.\");\n    }\n}\n/**\n * Helper to trigger an abort event immediately, the onabort and all abort event listeners will be triggered.\n * Will try to trigger abort event for all linked AbortSignal nodes.\n *\n * - If there is a timeout, the timer will be cancelled.\n * - If aborted is true, nothing will happen.\n *\n * @internal\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-use-interface-parameters\nfunction abortSignal(signal) {\n    if (signal.aborted) {\n        return;\n    }\n    if (signal.onabort) {\n        signal.onabort.call(signal);\n    }\n    const listeners = listenersMap.get(signal);\n    if (listeners) {\n        // Create a copy of listeners so mutations to the array\n        // (e.g. via removeListener calls) don't affect the listeners\n        // we invoke.\n        listeners.slice().forEach((listener) => {\n            listener.call(signal, { type: \"abort\" });\n        });\n    }\n    abortedMap.set(signal, true);\n}\n//# sourceMappingURL=AbortSignal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/abort-controller/dist-esm/src/AbortSignal.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_RETRY_POLICY_COUNT: () => (/* binding */ DEFAULT_RETRY_POLICY_COUNT),\n/* harmony export */   SDK_VERSION: () => (/* binding */ SDK_VERSION)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nconst SDK_VERSION = \"1.10.1\";\nconst DEFAULT_RETRY_POLICY_COUNT = 3;\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ087QUFDQTtBQUNQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9jb25zdGFudHMuanM/NTc5MCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmV4cG9ydCBjb25zdCBTREtfVkVSU0lPTiA9IFwiMS4xMC4xXCI7XG5leHBvcnQgY29uc3QgREVGQVVMVF9SRVRSWV9QT0xJQ1lfQ09VTlQgPSAzO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/createPipelineFromOptions.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/createPipelineFromOptions.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPipelineFromOptions: () => (/* binding */ createPipelineFromOptions)\n/* harmony export */ });\n/* harmony import */ var _policies_logPolicy__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./policies/logPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/logPolicy.js\");\n/* harmony import */ var _pipeline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pipeline */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipeline.js\");\n/* harmony import */ var _policies_redirectPolicy__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./policies/redirectPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/redirectPolicy.js\");\n/* harmony import */ var _policies_userAgentPolicy__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./policies/userAgentPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/userAgentPolicy.js\");\n/* harmony import */ var _policies_decompressResponsePolicy__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./policies/decompressResponsePolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/decompressResponsePolicy.js\");\n/* harmony import */ var _policies_defaultRetryPolicy__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./policies/defaultRetryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/defaultRetryPolicy.js\");\n/* harmony import */ var _policies_formDataPolicy__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./policies/formDataPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/formDataPolicy.js\");\n/* harmony import */ var _azure_core_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/core-util */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/index.js\");\n/* harmony import */ var _policies_proxyPolicy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./policies/proxyPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/proxyPolicy.js\");\n/* harmony import */ var _policies_setClientRequestIdPolicy__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./policies/setClientRequestIdPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/setClientRequestIdPolicy.js\");\n/* harmony import */ var _policies_tlsPolicy__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./policies/tlsPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tlsPolicy.js\");\n/* harmony import */ var _policies_tracingPolicy__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./policies/tracingPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tracingPolicy.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Create a new pipeline with a default set of customizable policies.\n * @param options - Options to configure a custom pipeline.\n */\nfunction createPipelineFromOptions(options) {\n    const pipeline = (0,_pipeline__WEBPACK_IMPORTED_MODULE_1__.createEmptyPipeline)();\n    if (_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.isNode) {\n        if (options.tlsOptions) {\n            pipeline.addPolicy((0,_policies_tlsPolicy__WEBPACK_IMPORTED_MODULE_2__.tlsPolicy)(options.tlsOptions));\n        }\n        pipeline.addPolicy((0,_policies_proxyPolicy__WEBPACK_IMPORTED_MODULE_3__.proxyPolicy)(options.proxyOptions));\n        pipeline.addPolicy((0,_policies_decompressResponsePolicy__WEBPACK_IMPORTED_MODULE_4__.decompressResponsePolicy)());\n    }\n    pipeline.addPolicy((0,_policies_formDataPolicy__WEBPACK_IMPORTED_MODULE_5__.formDataPolicy)());\n    pipeline.addPolicy((0,_policies_userAgentPolicy__WEBPACK_IMPORTED_MODULE_6__.userAgentPolicy)(options.userAgentOptions));\n    pipeline.addPolicy((0,_policies_setClientRequestIdPolicy__WEBPACK_IMPORTED_MODULE_7__.setClientRequestIdPolicy)());\n    pipeline.addPolicy((0,_policies_defaultRetryPolicy__WEBPACK_IMPORTED_MODULE_8__.defaultRetryPolicy)(options.retryOptions), { phase: \"Retry\" });\n    pipeline.addPolicy((0,_policies_tracingPolicy__WEBPACK_IMPORTED_MODULE_9__.tracingPolicy)(options.userAgentOptions), { afterPhase: \"Retry\" });\n    if (_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.isNode) {\n        // Both XHR and Fetch expect to handle redirects automatically,\n        // so only include this policy when we're in Node.\n        pipeline.addPolicy((0,_policies_redirectPolicy__WEBPACK_IMPORTED_MODULE_10__.redirectPolicy)(options.redirectOptions), { afterPhase: \"Retry\" });\n    }\n    pipeline.addPolicy((0,_policies_logPolicy__WEBPACK_IMPORTED_MODULE_11__.logPolicy)(options.loggingOptions), { afterPhase: \"Sign\" });\n    return pipeline;\n}\n//# sourceMappingURL=createPipelineFromOptions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/createPipelineFromOptions.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/defaultHttpClient.js":
/*!**************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/defaultHttpClient.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDefaultHttpClient: () => (/* binding */ createDefaultHttpClient)\n/* harmony export */ });\n/* harmony import */ var _nodeHttpClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./nodeHttpClient */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/nodeHttpClient.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nfunction createDefaultHttpClient() {\n    return (0,_nodeHttpClient__WEBPACK_IMPORTED_MODULE_0__.createNodeHttpClient)();\n}\n//# sourceMappingURL=defaultHttpClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9kZWZhdWx0SHR0cENsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDd0Q7QUFDeEQ7QUFDQTtBQUNBO0FBQ087QUFDUCxXQUFXLHFFQUFvQjtBQUMvQjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9kZWZhdWx0SHR0cENsaWVudC5qcz9mZjVlIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxuaW1wb3J0IHsgY3JlYXRlTm9kZUh0dHBDbGllbnQgfSBmcm9tIFwiLi9ub2RlSHR0cENsaWVudFwiO1xuLyoqXG4gKiBDcmVhdGUgdGhlIGNvcnJlY3QgSHR0cENsaWVudCBmb3IgdGhlIGN1cnJlbnQgZW52aXJvbm1lbnQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVEZWZhdWx0SHR0cENsaWVudCgpIHtcbiAgICByZXR1cm4gY3JlYXRlTm9kZUh0dHBDbGllbnQoKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRlZmF1bHRIdHRwQ2xpZW50LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/defaultHttpClient.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/httpHeaders.js":
/*!********************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/httpHeaders.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createHttpHeaders: () => (/* binding */ createHttpHeaders)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nfunction normalizeName(name) {\n    return name.toLowerCase();\n}\nfunction* headerIterator(map) {\n    for (const entry of map.values()) {\n        yield [entry.name, entry.value];\n    }\n}\nclass HttpHeadersImpl {\n    constructor(rawHeaders) {\n        this._headersMap = new Map();\n        if (rawHeaders) {\n            for (const headerName of Object.keys(rawHeaders)) {\n                this.set(headerName, rawHeaders[headerName]);\n            }\n        }\n    }\n    /**\n     * Set a header in this collection with the provided name and value. The name is\n     * case-insensitive.\n     * @param name - The name of the header to set. This value is case-insensitive.\n     * @param value - The value of the header to set.\n     */\n    set(name, value) {\n        this._headersMap.set(normalizeName(name), { name, value: String(value) });\n    }\n    /**\n     * Get the header value for the provided header name, or undefined if no header exists in this\n     * collection with the provided name.\n     * @param name - The name of the header. This value is case-insensitive.\n     */\n    get(name) {\n        var _a;\n        return (_a = this._headersMap.get(normalizeName(name))) === null || _a === void 0 ? void 0 : _a.value;\n    }\n    /**\n     * Get whether or not this header collection contains a header entry for the provided header name.\n     * @param name - The name of the header to set. This value is case-insensitive.\n     */\n    has(name) {\n        return this._headersMap.has(normalizeName(name));\n    }\n    /**\n     * Remove the header with the provided headerName.\n     * @param name - The name of the header to remove.\n     */\n    delete(name) {\n        this._headersMap.delete(normalizeName(name));\n    }\n    /**\n     * Get the JSON object representation of this HTTP header collection.\n     */\n    toJSON(options = {}) {\n        const result = {};\n        if (options.preserveCase) {\n            for (const entry of this._headersMap.values()) {\n                result[entry.name] = entry.value;\n            }\n        }\n        else {\n            for (const [normalizedName, entry] of this._headersMap) {\n                result[normalizedName] = entry.value;\n            }\n        }\n        return result;\n    }\n    /**\n     * Get the string representation of this HTTP header collection.\n     */\n    toString() {\n        return JSON.stringify(this.toJSON({ preserveCase: true }));\n    }\n    /**\n     * Iterate over tuples of header [name, value] pairs.\n     */\n    [Symbol.iterator]() {\n        return headerIterator(this._headersMap);\n    }\n}\n/**\n * Creates an object that satisfies the `HttpHeaders` interface.\n * @param rawHeaders - A simple object representing initial headers\n */\nfunction createHttpHeaders(rawHeaders) {\n    return new HttpHeadersImpl(rawHeaders);\n}\n//# sourceMappingURL=httpHeaders.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/httpHeaders.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/index.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/index.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RestError: () => (/* reexport safe */ _restError__WEBPACK_IMPORTED_MODULE_5__.RestError),\n/* harmony export */   bearerTokenAuthenticationPolicy: () => (/* reexport safe */ _policies_bearerTokenAuthenticationPolicy__WEBPACK_IMPORTED_MODULE_20__.bearerTokenAuthenticationPolicy),\n/* harmony export */   bearerTokenAuthenticationPolicyName: () => (/* reexport safe */ _policies_bearerTokenAuthenticationPolicy__WEBPACK_IMPORTED_MODULE_20__.bearerTokenAuthenticationPolicyName),\n/* harmony export */   createDefaultHttpClient: () => (/* reexport safe */ _defaultHttpClient__WEBPACK_IMPORTED_MODULE_2__.createDefaultHttpClient),\n/* harmony export */   createEmptyPipeline: () => (/* reexport safe */ _pipeline__WEBPACK_IMPORTED_MODULE_0__.createEmptyPipeline),\n/* harmony export */   createHttpHeaders: () => (/* reexport safe */ _httpHeaders__WEBPACK_IMPORTED_MODULE_3__.createHttpHeaders),\n/* harmony export */   createPipelineFromOptions: () => (/* reexport safe */ _createPipelineFromOptions__WEBPACK_IMPORTED_MODULE_1__.createPipelineFromOptions),\n/* harmony export */   createPipelineRequest: () => (/* reexport safe */ _pipelineRequest__WEBPACK_IMPORTED_MODULE_4__.createPipelineRequest),\n/* harmony export */   decompressResponsePolicy: () => (/* reexport safe */ _policies_decompressResponsePolicy__WEBPACK_IMPORTED_MODULE_6__.decompressResponsePolicy),\n/* harmony export */   decompressResponsePolicyName: () => (/* reexport safe */ _policies_decompressResponsePolicy__WEBPACK_IMPORTED_MODULE_6__.decompressResponsePolicyName),\n/* harmony export */   defaultRetryPolicy: () => (/* reexport safe */ _policies_defaultRetryPolicy__WEBPACK_IMPORTED_MODULE_16__.defaultRetryPolicy),\n/* harmony export */   exponentialRetryPolicy: () => (/* reexport safe */ _policies_exponentialRetryPolicy__WEBPACK_IMPORTED_MODULE_7__.exponentialRetryPolicy),\n/* harmony export */   exponentialRetryPolicyName: () => (/* reexport safe */ _policies_exponentialRetryPolicy__WEBPACK_IMPORTED_MODULE_7__.exponentialRetryPolicyName),\n/* harmony export */   formDataPolicy: () => (/* reexport safe */ _policies_formDataPolicy__WEBPACK_IMPORTED_MODULE_19__.formDataPolicy),\n/* harmony export */   formDataPolicyName: () => (/* reexport safe */ _policies_formDataPolicy__WEBPACK_IMPORTED_MODULE_19__.formDataPolicyName),\n/* harmony export */   getDefaultProxySettings: () => (/* reexport safe */ _policies_proxyPolicy__WEBPACK_IMPORTED_MODULE_10__.getDefaultProxySettings),\n/* harmony export */   isRestError: () => (/* reexport safe */ _restError__WEBPACK_IMPORTED_MODULE_5__.isRestError),\n/* harmony export */   logPolicy: () => (/* reexport safe */ _policies_logPolicy__WEBPACK_IMPORTED_MODULE_9__.logPolicy),\n/* harmony export */   logPolicyName: () => (/* reexport safe */ _policies_logPolicy__WEBPACK_IMPORTED_MODULE_9__.logPolicyName),\n/* harmony export */   ndJsonPolicy: () => (/* reexport safe */ _policies_ndJsonPolicy__WEBPACK_IMPORTED_MODULE_21__.ndJsonPolicy),\n/* harmony export */   ndJsonPolicyName: () => (/* reexport safe */ _policies_ndJsonPolicy__WEBPACK_IMPORTED_MODULE_21__.ndJsonPolicyName),\n/* harmony export */   proxyPolicy: () => (/* reexport safe */ _policies_proxyPolicy__WEBPACK_IMPORTED_MODULE_10__.proxyPolicy),\n/* harmony export */   proxyPolicyName: () => (/* reexport safe */ _policies_proxyPolicy__WEBPACK_IMPORTED_MODULE_10__.proxyPolicyName),\n/* harmony export */   redirectPolicy: () => (/* reexport safe */ _policies_redirectPolicy__WEBPACK_IMPORTED_MODULE_11__.redirectPolicy),\n/* harmony export */   redirectPolicyName: () => (/* reexport safe */ _policies_redirectPolicy__WEBPACK_IMPORTED_MODULE_11__.redirectPolicyName),\n/* harmony export */   retryPolicy: () => (/* reexport safe */ _policies_retryPolicy__WEBPACK_IMPORTED_MODULE_14__.retryPolicy),\n/* harmony export */   setClientRequestIdPolicy: () => (/* reexport safe */ _policies_setClientRequestIdPolicy__WEBPACK_IMPORTED_MODULE_8__.setClientRequestIdPolicy),\n/* harmony export */   setClientRequestIdPolicyName: () => (/* reexport safe */ _policies_setClientRequestIdPolicy__WEBPACK_IMPORTED_MODULE_8__.setClientRequestIdPolicyName),\n/* harmony export */   systemErrorRetryPolicy: () => (/* reexport safe */ _policies_systemErrorRetryPolicy__WEBPACK_IMPORTED_MODULE_12__.systemErrorRetryPolicy),\n/* harmony export */   systemErrorRetryPolicyName: () => (/* reexport safe */ _policies_systemErrorRetryPolicy__WEBPACK_IMPORTED_MODULE_12__.systemErrorRetryPolicyName),\n/* harmony export */   throttlingRetryPolicy: () => (/* reexport safe */ _policies_throttlingRetryPolicy__WEBPACK_IMPORTED_MODULE_13__.throttlingRetryPolicy),\n/* harmony export */   throttlingRetryPolicyName: () => (/* reexport safe */ _policies_throttlingRetryPolicy__WEBPACK_IMPORTED_MODULE_13__.throttlingRetryPolicyName),\n/* harmony export */   tlsPolicy: () => (/* reexport safe */ _policies_tlsPolicy__WEBPACK_IMPORTED_MODULE_18__.tlsPolicy),\n/* harmony export */   tlsPolicyName: () => (/* reexport safe */ _policies_tlsPolicy__WEBPACK_IMPORTED_MODULE_18__.tlsPolicyName),\n/* harmony export */   tracingPolicy: () => (/* reexport safe */ _policies_tracingPolicy__WEBPACK_IMPORTED_MODULE_15__.tracingPolicy),\n/* harmony export */   tracingPolicyName: () => (/* reexport safe */ _policies_tracingPolicy__WEBPACK_IMPORTED_MODULE_15__.tracingPolicyName),\n/* harmony export */   userAgentPolicy: () => (/* reexport safe */ _policies_userAgentPolicy__WEBPACK_IMPORTED_MODULE_17__.userAgentPolicy),\n/* harmony export */   userAgentPolicyName: () => (/* reexport safe */ _policies_userAgentPolicy__WEBPACK_IMPORTED_MODULE_17__.userAgentPolicyName)\n/* harmony export */ });\n/* harmony import */ var _pipeline__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pipeline */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipeline.js\");\n/* harmony import */ var _createPipelineFromOptions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createPipelineFromOptions */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/createPipelineFromOptions.js\");\n/* harmony import */ var _defaultHttpClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./defaultHttpClient */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/defaultHttpClient.js\");\n/* harmony import */ var _httpHeaders__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./httpHeaders */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/httpHeaders.js\");\n/* harmony import */ var _pipelineRequest__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pipelineRequest */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipelineRequest.js\");\n/* harmony import */ var _restError__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./restError */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/restError.js\");\n/* harmony import */ var _policies_decompressResponsePolicy__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./policies/decompressResponsePolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/decompressResponsePolicy.js\");\n/* harmony import */ var _policies_exponentialRetryPolicy__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./policies/exponentialRetryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/exponentialRetryPolicy.js\");\n/* harmony import */ var _policies_setClientRequestIdPolicy__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./policies/setClientRequestIdPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/setClientRequestIdPolicy.js\");\n/* harmony import */ var _policies_logPolicy__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./policies/logPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/logPolicy.js\");\n/* harmony import */ var _policies_proxyPolicy__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./policies/proxyPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/proxyPolicy.js\");\n/* harmony import */ var _policies_redirectPolicy__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./policies/redirectPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/redirectPolicy.js\");\n/* harmony import */ var _policies_systemErrorRetryPolicy__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./policies/systemErrorRetryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/systemErrorRetryPolicy.js\");\n/* harmony import */ var _policies_throttlingRetryPolicy__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./policies/throttlingRetryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/throttlingRetryPolicy.js\");\n/* harmony import */ var _policies_retryPolicy__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./policies/retryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/retryPolicy.js\");\n/* harmony import */ var _policies_tracingPolicy__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./policies/tracingPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tracingPolicy.js\");\n/* harmony import */ var _policies_defaultRetryPolicy__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./policies/defaultRetryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/defaultRetryPolicy.js\");\n/* harmony import */ var _policies_userAgentPolicy__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./policies/userAgentPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/userAgentPolicy.js\");\n/* harmony import */ var _policies_tlsPolicy__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./policies/tlsPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tlsPolicy.js\");\n/* harmony import */ var _policies_formDataPolicy__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./policies/formDataPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/formDataPolicy.js\");\n/* harmony import */ var _policies_bearerTokenAuthenticationPolicy__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./policies/bearerTokenAuthenticationPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/bearerTokenAuthenticationPolicy.js\");\n/* harmony import */ var _policies_ndJsonPolicy__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./policies/ndJsonPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/ndJsonPolicy.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/log.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/log.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var _azure_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/logger */ \"(instrument)/../../node_modules/@azure/logger/dist-esm/src/index.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nconst logger = (0,_azure_logger__WEBPACK_IMPORTED_MODULE_0__.createClientLogger)(\"core-rest-pipeline\");\n//# sourceMappingURL=log.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9sb2cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ21EO0FBQzVDLGVBQWUsaUVBQWtCO0FBQ3hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9sb2cuanM/N2UyNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmltcG9ydCB7IGNyZWF0ZUNsaWVudExvZ2dlciB9IGZyb20gXCJAYXp1cmUvbG9nZ2VyXCI7XG5leHBvcnQgY29uc3QgbG9nZ2VyID0gY3JlYXRlQ2xpZW50TG9nZ2VyKFwiY29yZS1yZXN0LXBpcGVsaW5lXCIpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9nLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/log.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/nodeHttpClient.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/nodeHttpClient.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNodeHttpClient: () => (/* binding */ createNodeHttpClient),\n/* harmony export */   getBodyLength: () => (/* binding */ getBodyLength)\n/* harmony export */ });\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! http */ \"http\");\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(http__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var https__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! https */ \"https\");\n/* harmony import */ var https__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(https__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var zlib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zlib */ \"zlib\");\n/* harmony import */ var zlib__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(zlib__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _azure_abort_controller__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @azure/abort-controller */ \"(instrument)/../../node_modules/@azure/abort-controller/dist-esm/src/AbortController.js\");\n/* harmony import */ var _httpHeaders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./httpHeaders */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/httpHeaders.js\");\n/* harmony import */ var _restError__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./restError */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/restError.js\");\n/* harmony import */ var _log__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./log */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/log.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n\n\n\n\n\nconst DEFAULT_TLS_SETTINGS = {};\nfunction isReadableStream(body) {\n    return body && typeof body.pipe === \"function\";\n}\nfunction isStreamComplete(stream) {\n    return new Promise((resolve) => {\n        stream.on(\"close\", resolve);\n        stream.on(\"end\", resolve);\n        stream.on(\"error\", resolve);\n    });\n}\nfunction isArrayBuffer(body) {\n    return body && typeof body.byteLength === \"number\";\n}\nclass ReportTransform extends stream__WEBPACK_IMPORTED_MODULE_3__.Transform {\n    constructor(progressCallback) {\n        super();\n        this.loadedBytes = 0;\n        this.progressCallback = progressCallback;\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    _transform(chunk, _encoding, callback) {\n        this.push(chunk);\n        this.loadedBytes += chunk.length;\n        try {\n            this.progressCallback({ loadedBytes: this.loadedBytes });\n            callback();\n        }\n        catch (e) {\n            callback(e);\n        }\n    }\n}\n/**\n * A HttpClient implementation that uses Node's \"https\" module to send HTTPS requests.\n * @internal\n */\nclass NodeHttpClient {\n    constructor() {\n        this.cachedHttpsAgents = new WeakMap();\n    }\n    /**\n     * Makes a request over an underlying transport layer and returns the response.\n     * @param request - The request to be made.\n     */\n    async sendRequest(request) {\n        var _a, _b, _c;\n        const abortController = new _azure_abort_controller__WEBPACK_IMPORTED_MODULE_4__.AbortController();\n        let abortListener;\n        if (request.abortSignal) {\n            if (request.abortSignal.aborted) {\n                throw new _azure_abort_controller__WEBPACK_IMPORTED_MODULE_4__.AbortError(\"The operation was aborted.\");\n            }\n            abortListener = (event) => {\n                if (event.type === \"abort\") {\n                    abortController.abort();\n                }\n            };\n            request.abortSignal.addEventListener(\"abort\", abortListener);\n        }\n        if (request.timeout > 0) {\n            setTimeout(() => {\n                abortController.abort();\n            }, request.timeout);\n        }\n        const acceptEncoding = request.headers.get(\"Accept-Encoding\");\n        const shouldDecompress = (acceptEncoding === null || acceptEncoding === void 0 ? void 0 : acceptEncoding.includes(\"gzip\")) || (acceptEncoding === null || acceptEncoding === void 0 ? void 0 : acceptEncoding.includes(\"deflate\"));\n        let body = typeof request.body === \"function\" ? request.body() : request.body;\n        if (body && !request.headers.has(\"Content-Length\")) {\n            const bodyLength = getBodyLength(body);\n            if (bodyLength !== null) {\n                request.headers.set(\"Content-Length\", bodyLength);\n            }\n        }\n        let responseStream;\n        try {\n            if (body && request.onUploadProgress) {\n                const onUploadProgress = request.onUploadProgress;\n                const uploadReportStream = new ReportTransform(onUploadProgress);\n                uploadReportStream.on(\"error\", (e) => {\n                    _log__WEBPACK_IMPORTED_MODULE_5__.logger.error(\"Error in upload progress\", e);\n                });\n                if (isReadableStream(body)) {\n                    body.pipe(uploadReportStream);\n                }\n                else {\n                    uploadReportStream.end(body);\n                }\n                body = uploadReportStream;\n            }\n            const res = await this.makeRequest(request, abortController, body);\n            const headers = getResponseHeaders(res);\n            const status = (_a = res.statusCode) !== null && _a !== void 0 ? _a : 0;\n            const response = {\n                status,\n                headers,\n                request,\n            };\n            // Responses to HEAD must not have a body.\n            // If they do return a body, that body must be ignored.\n            if (request.method === \"HEAD\") {\n                // call resume() and not destroy() to avoid closing the socket\n                // and losing keep alive\n                res.resume();\n                return response;\n            }\n            responseStream = shouldDecompress ? getDecodedResponseStream(res, headers) : res;\n            const onDownloadProgress = request.onDownloadProgress;\n            if (onDownloadProgress) {\n                const downloadReportStream = new ReportTransform(onDownloadProgress);\n                downloadReportStream.on(\"error\", (e) => {\n                    _log__WEBPACK_IMPORTED_MODULE_5__.logger.error(\"Error in download progress\", e);\n                });\n                responseStream.pipe(downloadReportStream);\n                responseStream = downloadReportStream;\n            }\n            if (\n            // Value of POSITIVE_INFINITY in streamResponseStatusCodes is considered as any status code\n            ((_b = request.streamResponseStatusCodes) === null || _b === void 0 ? void 0 : _b.has(Number.POSITIVE_INFINITY)) ||\n                ((_c = request.streamResponseStatusCodes) === null || _c === void 0 ? void 0 : _c.has(response.status))) {\n                response.readableStreamBody = responseStream;\n            }\n            else {\n                response.bodyAsText = await streamToText(responseStream);\n            }\n            return response;\n        }\n        finally {\n            // clean up event listener\n            if (request.abortSignal && abortListener) {\n                let uploadStreamDone = Promise.resolve();\n                if (isReadableStream(body)) {\n                    uploadStreamDone = isStreamComplete(body);\n                }\n                let downloadStreamDone = Promise.resolve();\n                if (isReadableStream(responseStream)) {\n                    downloadStreamDone = isStreamComplete(responseStream);\n                }\n                Promise.all([uploadStreamDone, downloadStreamDone])\n                    .then(() => {\n                    var _a;\n                    // eslint-disable-next-line promise/always-return\n                    if (abortListener) {\n                        (_a = request.abortSignal) === null || _a === void 0 ? void 0 : _a.removeEventListener(\"abort\", abortListener);\n                    }\n                })\n                    .catch((e) => {\n                    _log__WEBPACK_IMPORTED_MODULE_5__.logger.warning(\"Error when cleaning up abortListener on httpRequest\", e);\n                });\n            }\n        }\n    }\n    makeRequest(request, abortController, body) {\n        var _a;\n        const url = new URL(request.url);\n        const isInsecure = url.protocol !== \"https:\";\n        if (isInsecure && !request.allowInsecureConnection) {\n            throw new Error(`Cannot connect to ${request.url} while allowInsecureConnection is false.`);\n        }\n        const agent = (_a = request.agent) !== null && _a !== void 0 ? _a : this.getOrCreateAgent(request, isInsecure);\n        const options = {\n            agent,\n            hostname: url.hostname,\n            path: `${url.pathname}${url.search}`,\n            port: url.port,\n            method: request.method,\n            headers: request.headers.toJSON({ preserveCase: true }),\n        };\n        return new Promise((resolve, reject) => {\n            const req = isInsecure ? http__WEBPACK_IMPORTED_MODULE_0__.request(options, resolve) : https__WEBPACK_IMPORTED_MODULE_1__.request(options, resolve);\n            req.once(\"error\", (err) => {\n                var _a;\n                reject(new _restError__WEBPACK_IMPORTED_MODULE_6__.RestError(err.message, { code: (_a = err.code) !== null && _a !== void 0 ? _a : _restError__WEBPACK_IMPORTED_MODULE_6__.RestError.REQUEST_SEND_ERROR, request }));\n            });\n            abortController.signal.addEventListener(\"abort\", () => {\n                const abortError = new _azure_abort_controller__WEBPACK_IMPORTED_MODULE_4__.AbortError(\"The operation was aborted.\");\n                req.destroy(abortError);\n                reject(abortError);\n            });\n            if (body && isReadableStream(body)) {\n                body.pipe(req);\n            }\n            else if (body) {\n                if (typeof body === \"string\" || Buffer.isBuffer(body)) {\n                    req.end(body);\n                }\n                else if (isArrayBuffer(body)) {\n                    req.end(ArrayBuffer.isView(body) ? Buffer.from(body.buffer) : Buffer.from(body));\n                }\n                else {\n                    _log__WEBPACK_IMPORTED_MODULE_5__.logger.error(\"Unrecognized body type\", body);\n                    reject(new _restError__WEBPACK_IMPORTED_MODULE_6__.RestError(\"Unrecognized body type\"));\n                }\n            }\n            else {\n                // streams don't like \"undefined\" being passed as data\n                req.end();\n            }\n        });\n    }\n    getOrCreateAgent(request, isInsecure) {\n        var _a;\n        const disableKeepAlive = request.disableKeepAlive;\n        // Handle Insecure requests first\n        if (isInsecure) {\n            if (disableKeepAlive) {\n                // keepAlive:false is the default so we don't need a custom Agent\n                return http__WEBPACK_IMPORTED_MODULE_0__.globalAgent;\n            }\n            if (!this.cachedHttpAgent) {\n                // If there is no cached agent create a new one and cache it.\n                this.cachedHttpAgent = new http__WEBPACK_IMPORTED_MODULE_0__.Agent({ keepAlive: true });\n            }\n            return this.cachedHttpAgent;\n        }\n        else {\n            if (disableKeepAlive && !request.tlsSettings) {\n                // When there are no tlsSettings and keepAlive is false\n                // we don't need a custom agent\n                return https__WEBPACK_IMPORTED_MODULE_1__.globalAgent;\n            }\n            // We use the tlsSettings to index cached clients\n            const tlsSettings = (_a = request.tlsSettings) !== null && _a !== void 0 ? _a : DEFAULT_TLS_SETTINGS;\n            // Get the cached agent or create a new one with the\n            // provided values for keepAlive and tlsSettings\n            let agent = this.cachedHttpsAgents.get(tlsSettings);\n            if (agent && agent.options.keepAlive === !disableKeepAlive) {\n                return agent;\n            }\n            _log__WEBPACK_IMPORTED_MODULE_5__.logger.info(\"No cached TLS Agent exist, creating a new Agent\");\n            agent = new https__WEBPACK_IMPORTED_MODULE_1__.Agent(Object.assign({ \n                // keepAlive is true if disableKeepAlive is false.\n                keepAlive: !disableKeepAlive }, tlsSettings));\n            this.cachedHttpsAgents.set(tlsSettings, agent);\n            return agent;\n        }\n    }\n}\nfunction getResponseHeaders(res) {\n    const headers = (0,_httpHeaders__WEBPACK_IMPORTED_MODULE_7__.createHttpHeaders)();\n    for (const header of Object.keys(res.headers)) {\n        const value = res.headers[header];\n        if (Array.isArray(value)) {\n            if (value.length > 0) {\n                headers.set(header, value[0]);\n            }\n        }\n        else if (value) {\n            headers.set(header, value);\n        }\n    }\n    return headers;\n}\nfunction getDecodedResponseStream(stream, headers) {\n    const contentEncoding = headers.get(\"Content-Encoding\");\n    if (contentEncoding === \"gzip\") {\n        const unzip = zlib__WEBPACK_IMPORTED_MODULE_2__.createGunzip();\n        stream.pipe(unzip);\n        return unzip;\n    }\n    else if (contentEncoding === \"deflate\") {\n        const inflate = zlib__WEBPACK_IMPORTED_MODULE_2__.createInflate();\n        stream.pipe(inflate);\n        return inflate;\n    }\n    return stream;\n}\nfunction streamToText(stream) {\n    return new Promise((resolve, reject) => {\n        const buffer = [];\n        stream.on(\"data\", (chunk) => {\n            if (Buffer.isBuffer(chunk)) {\n                buffer.push(chunk);\n            }\n            else {\n                buffer.push(Buffer.from(chunk));\n            }\n        });\n        stream.on(\"end\", () => {\n            resolve(Buffer.concat(buffer).toString(\"utf8\"));\n        });\n        stream.on(\"error\", (e) => {\n            if (e && (e === null || e === void 0 ? void 0 : e.name) === \"AbortError\") {\n                reject(e);\n            }\n            else {\n                reject(new _restError__WEBPACK_IMPORTED_MODULE_6__.RestError(`Error reading response as text: ${e.message}`, {\n                    code: _restError__WEBPACK_IMPORTED_MODULE_6__.RestError.PARSE_ERROR,\n                }));\n            }\n        });\n    });\n}\n/** @internal */\nfunction getBodyLength(body) {\n    if (!body) {\n        return 0;\n    }\n    else if (Buffer.isBuffer(body)) {\n        return body.length;\n    }\n    else if (isReadableStream(body)) {\n        return null;\n    }\n    else if (isArrayBuffer(body)) {\n        return body.byteLength;\n    }\n    else if (typeof body === \"string\") {\n        return Buffer.from(body).length;\n    }\n    else {\n        return null;\n    }\n}\n/**\n * Create a new HttpClient instance for the NodeJS environment.\n * @internal\n */\nfunction createNodeHttpClient() {\n    return new NodeHttpClient();\n}\n//# sourceMappingURL=nodeHttpClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/nodeHttpClient.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipeline.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipeline.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmptyPipeline: () => (/* binding */ createEmptyPipeline)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nconst ValidPhaseNames = new Set([\"Deserialize\", \"Serialize\", \"Retry\", \"Sign\"]);\n/**\n * A private implementation of Pipeline.\n * Do not export this class from the package.\n * @internal\n */\nclass HttpPipeline {\n    constructor(policies) {\n        var _a;\n        this._policies = [];\n        this._policies = (_a = policies === null || policies === void 0 ? void 0 : policies.slice(0)) !== null && _a !== void 0 ? _a : [];\n        this._orderedPolicies = undefined;\n    }\n    addPolicy(policy, options = {}) {\n        if (options.phase && options.afterPhase) {\n            throw new Error(\"Policies inside a phase cannot specify afterPhase.\");\n        }\n        if (options.phase && !ValidPhaseNames.has(options.phase)) {\n            throw new Error(`Invalid phase name: ${options.phase}`);\n        }\n        if (options.afterPhase && !ValidPhaseNames.has(options.afterPhase)) {\n            throw new Error(`Invalid afterPhase name: ${options.afterPhase}`);\n        }\n        this._policies.push({\n            policy,\n            options,\n        });\n        this._orderedPolicies = undefined;\n    }\n    removePolicy(options) {\n        const removedPolicies = [];\n        this._policies = this._policies.filter((policyDescriptor) => {\n            if ((options.name && policyDescriptor.policy.name === options.name) ||\n                (options.phase && policyDescriptor.options.phase === options.phase)) {\n                removedPolicies.push(policyDescriptor.policy);\n                return false;\n            }\n            else {\n                return true;\n            }\n        });\n        this._orderedPolicies = undefined;\n        return removedPolicies;\n    }\n    sendRequest(httpClient, request) {\n        const policies = this.getOrderedPolicies();\n        const pipeline = policies.reduceRight((next, policy) => {\n            return (req) => {\n                return policy.sendRequest(req, next);\n            };\n        }, (req) => httpClient.sendRequest(req));\n        return pipeline(request);\n    }\n    getOrderedPolicies() {\n        if (!this._orderedPolicies) {\n            this._orderedPolicies = this.orderPolicies();\n        }\n        return this._orderedPolicies;\n    }\n    clone() {\n        return new HttpPipeline(this._policies);\n    }\n    static create() {\n        return new HttpPipeline();\n    }\n    orderPolicies() {\n        /**\n         * The goal of this method is to reliably order pipeline policies\n         * based on their declared requirements when they were added.\n         *\n         * Order is first determined by phase:\n         *\n         * 1. Serialize Phase\n         * 2. Policies not in a phase\n         * 3. Deserialize Phase\n         * 4. Retry Phase\n         * 5. Sign Phase\n         *\n         * Within each phase, policies are executed in the order\n         * they were added unless they were specified to execute\n         * before/after other policies or after a particular phase.\n         *\n         * To determine the final order, we will walk the policy list\n         * in phase order multiple times until all dependencies are\n         * satisfied.\n         *\n         * `afterPolicies` are the set of policies that must be\n         * executed before a given policy. This requirement is\n         * considered satisfied when each of the listed policies\n         * have been scheduled.\n         *\n         * `beforePolicies` are the set of policies that must be\n         * executed after a given policy. Since this dependency\n         * can be expressed by converting it into a equivalent\n         * `afterPolicies` declarations, they are normalized\n         * into that form for simplicity.\n         *\n         * An `afterPhase` dependency is considered satisfied when all\n         * policies in that phase have scheduled.\n         *\n         */\n        const result = [];\n        // Track all policies we know about.\n        const policyMap = new Map();\n        function createPhase(name) {\n            return {\n                name,\n                policies: new Set(),\n                hasRun: false,\n                hasAfterPolicies: false,\n            };\n        }\n        // Track policies for each phase.\n        const serializePhase = createPhase(\"Serialize\");\n        const noPhase = createPhase(\"None\");\n        const deserializePhase = createPhase(\"Deserialize\");\n        const retryPhase = createPhase(\"Retry\");\n        const signPhase = createPhase(\"Sign\");\n        // a list of phases in order\n        const orderedPhases = [serializePhase, noPhase, deserializePhase, retryPhase, signPhase];\n        // Small helper function to map phase name to each Phase\n        function getPhase(phase) {\n            if (phase === \"Retry\") {\n                return retryPhase;\n            }\n            else if (phase === \"Serialize\") {\n                return serializePhase;\n            }\n            else if (phase === \"Deserialize\") {\n                return deserializePhase;\n            }\n            else if (phase === \"Sign\") {\n                return signPhase;\n            }\n            else {\n                return noPhase;\n            }\n        }\n        // First walk each policy and create a node to track metadata.\n        for (const descriptor of this._policies) {\n            const policy = descriptor.policy;\n            const options = descriptor.options;\n            const policyName = policy.name;\n            if (policyMap.has(policyName)) {\n                throw new Error(\"Duplicate policy names not allowed in pipeline\");\n            }\n            const node = {\n                policy,\n                dependsOn: new Set(),\n                dependants: new Set(),\n            };\n            if (options.afterPhase) {\n                node.afterPhase = getPhase(options.afterPhase);\n                node.afterPhase.hasAfterPolicies = true;\n            }\n            policyMap.set(policyName, node);\n            const phase = getPhase(options.phase);\n            phase.policies.add(node);\n        }\n        // Now that each policy has a node, connect dependency references.\n        for (const descriptor of this._policies) {\n            const { policy, options } = descriptor;\n            const policyName = policy.name;\n            const node = policyMap.get(policyName);\n            if (!node) {\n                throw new Error(`Missing node for policy ${policyName}`);\n            }\n            if (options.afterPolicies) {\n                for (const afterPolicyName of options.afterPolicies) {\n                    const afterNode = policyMap.get(afterPolicyName);\n                    if (afterNode) {\n                        // Linking in both directions helps later\n                        // when we want to notify dependants.\n                        node.dependsOn.add(afterNode);\n                        afterNode.dependants.add(node);\n                    }\n                }\n            }\n            if (options.beforePolicies) {\n                for (const beforePolicyName of options.beforePolicies) {\n                    const beforeNode = policyMap.get(beforePolicyName);\n                    if (beforeNode) {\n                        // To execute before another node, make it\n                        // depend on the current node.\n                        beforeNode.dependsOn.add(node);\n                        node.dependants.add(beforeNode);\n                    }\n                }\n            }\n        }\n        function walkPhase(phase) {\n            phase.hasRun = true;\n            // Sets iterate in insertion order\n            for (const node of phase.policies) {\n                if (node.afterPhase && (!node.afterPhase.hasRun || node.afterPhase.policies.size)) {\n                    // If this node is waiting on a phase to complete,\n                    // we need to skip it for now.\n                    // Even if the phase is empty, we should wait for it\n                    // to be walked to avoid re-ordering policies.\n                    continue;\n                }\n                if (node.dependsOn.size === 0) {\n                    // If there's nothing else we're waiting for, we can\n                    // add this policy to the result list.\n                    result.push(node.policy);\n                    // Notify anything that depends on this policy that\n                    // the policy has been scheduled.\n                    for (const dependant of node.dependants) {\n                        dependant.dependsOn.delete(node);\n                    }\n                    policyMap.delete(node.policy.name);\n                    phase.policies.delete(node);\n                }\n            }\n        }\n        function walkPhases() {\n            for (const phase of orderedPhases) {\n                walkPhase(phase);\n                // if the phase isn't complete\n                if (phase.policies.size > 0 && phase !== noPhase) {\n                    if (!noPhase.hasRun) {\n                        // Try running noPhase to see if that unblocks this phase next tick.\n                        // This can happen if a phase that happens before noPhase\n                        // is waiting on a noPhase policy to complete.\n                        walkPhase(noPhase);\n                    }\n                    // Don't proceed to the next phase until this phase finishes.\n                    return;\n                }\n                if (phase.hasAfterPolicies) {\n                    // Run any policies unblocked by this phase\n                    walkPhase(noPhase);\n                }\n            }\n        }\n        // Iterate until we've put every node in the result list.\n        let iteration = 0;\n        while (policyMap.size > 0) {\n            iteration++;\n            const initialResultLength = result.length;\n            // Keep walking each phase in order until we can order every node.\n            walkPhases();\n            // The result list *should* get at least one larger each time\n            // after the first full pass.\n            // Otherwise, we're going to loop forever.\n            if (result.length <= initialResultLength && iteration > 1) {\n                throw new Error(\"Cannot satisfy policy dependencies due to requirements cycle.\");\n            }\n        }\n        return result;\n    }\n}\n/**\n * Creates a totally empty pipeline.\n * Useful for testing or creating a custom one.\n */\nfunction createEmptyPipeline() {\n    return HttpPipeline.create();\n}\n//# sourceMappingURL=pipeline.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipeline.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipelineRequest.js":
/*!************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipelineRequest.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPipelineRequest: () => (/* binding */ createPipelineRequest)\n/* harmony export */ });\n/* harmony import */ var _httpHeaders__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./httpHeaders */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/httpHeaders.js\");\n/* harmony import */ var _util_uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/uuid */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/uuid.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\nclass PipelineRequestImpl {\n    constructor(options) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        this.url = options.url;\n        this.body = options.body;\n        this.headers = (_a = options.headers) !== null && _a !== void 0 ? _a : (0,_httpHeaders__WEBPACK_IMPORTED_MODULE_0__.createHttpHeaders)();\n        this.method = (_b = options.method) !== null && _b !== void 0 ? _b : \"GET\";\n        this.timeout = (_c = options.timeout) !== null && _c !== void 0 ? _c : 0;\n        this.formData = options.formData;\n        this.disableKeepAlive = (_d = options.disableKeepAlive) !== null && _d !== void 0 ? _d : false;\n        this.proxySettings = options.proxySettings;\n        this.streamResponseStatusCodes = options.streamResponseStatusCodes;\n        this.withCredentials = (_e = options.withCredentials) !== null && _e !== void 0 ? _e : false;\n        this.abortSignal = options.abortSignal;\n        this.tracingOptions = options.tracingOptions;\n        this.onUploadProgress = options.onUploadProgress;\n        this.onDownloadProgress = options.onDownloadProgress;\n        this.requestId = options.requestId || (0,_util_uuid__WEBPACK_IMPORTED_MODULE_1__.generateUuid)();\n        this.allowInsecureConnection = (_f = options.allowInsecureConnection) !== null && _f !== void 0 ? _f : false;\n        this.enableBrowserStreams = (_g = options.enableBrowserStreams) !== null && _g !== void 0 ? _g : false;\n    }\n}\n/**\n * Creates a new pipeline request with the given options.\n * This method is to allow for the easy setting of default values and not required.\n * @param options - The options to create the request with.\n */\nfunction createPipelineRequest(options) {\n    return new PipelineRequestImpl(options);\n}\n//# sourceMappingURL=pipelineRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/pipelineRequest.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/bearerTokenAuthenticationPolicy.js":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/bearerTokenAuthenticationPolicy.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bearerTokenAuthenticationPolicy: () => (/* binding */ bearerTokenAuthenticationPolicy),\n/* harmony export */   bearerTokenAuthenticationPolicyName: () => (/* binding */ bearerTokenAuthenticationPolicyName)\n/* harmony export */ });\n/* harmony import */ var _util_tokenCycler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/tokenCycler */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/tokenCycler.js\");\n/* harmony import */ var _log__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../log */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/log.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n/**\n * The programmatic identifier of the bearerTokenAuthenticationPolicy.\n */\nconst bearerTokenAuthenticationPolicyName = \"bearerTokenAuthenticationPolicy\";\n/**\n * Default authorize request handler\n */\nasync function defaultAuthorizeRequest(options) {\n    const { scopes, getAccessToken, request } = options;\n    const getTokenOptions = {\n        abortSignal: request.abortSignal,\n        tracingOptions: request.tracingOptions,\n    };\n    const accessToken = await getAccessToken(scopes, getTokenOptions);\n    if (accessToken) {\n        options.request.headers.set(\"Authorization\", `Bearer ${accessToken.token}`);\n    }\n}\n/**\n * We will retrieve the challenge only if the response status code was 401,\n * and if the response contained the header \"WWW-Authenticate\" with a non-empty value.\n */\nfunction getChallenge(response) {\n    const challenge = response.headers.get(\"WWW-Authenticate\");\n    if (response.status === 401 && challenge) {\n        return challenge;\n    }\n    return;\n}\n/**\n * A policy that can request a token from a TokenCredential implementation and\n * then apply it to the Authorization header of a request as a Bearer token.\n */\nfunction bearerTokenAuthenticationPolicy(options) {\n    var _a;\n    const { credential, scopes, challengeCallbacks } = options;\n    const logger = options.logger || _log__WEBPACK_IMPORTED_MODULE_0__.logger;\n    const callbacks = Object.assign({ authorizeRequest: (_a = challengeCallbacks === null || challengeCallbacks === void 0 ? void 0 : challengeCallbacks.authorizeRequest) !== null && _a !== void 0 ? _a : defaultAuthorizeRequest, authorizeRequestOnChallenge: challengeCallbacks === null || challengeCallbacks === void 0 ? void 0 : challengeCallbacks.authorizeRequestOnChallenge }, challengeCallbacks);\n    // This function encapsulates the entire process of reliably retrieving the token\n    // The options are left out of the public API until there's demand to configure this.\n    // Remember to extend `BearerTokenAuthenticationPolicyOptions` with `TokenCyclerOptions`\n    // in order to pass through the `options` object.\n    const getAccessToken = credential\n        ? (0,_util_tokenCycler__WEBPACK_IMPORTED_MODULE_1__.createTokenCycler)(credential /* , options */)\n        : () => Promise.resolve(null);\n    return {\n        name: bearerTokenAuthenticationPolicyName,\n        /**\n         * If there's no challenge parameter:\n         * - It will try to retrieve the token using the cache, or the credential's getToken.\n         * - Then it will try the next policy with or without the retrieved token.\n         *\n         * It uses the challenge parameters to:\n         * - Skip a first attempt to get the token from the credential if there's no cached token,\n         *   since it expects the token to be retrievable only after the challenge.\n         * - Prepare the outgoing request if the `prepareRequest` method has been provided.\n         * - Send an initial request to receive the challenge if it fails.\n         * - Process a challenge if the response contains it.\n         * - Retrieve a token with the challenge information, then re-send the request.\n         */\n        async sendRequest(request, next) {\n            if (!request.url.toLowerCase().startsWith(\"https://\")) {\n                throw new Error(\"Bearer token authentication is not permitted for non-TLS protected (non-https) URLs.\");\n            }\n            await callbacks.authorizeRequest({\n                scopes: Array.isArray(scopes) ? scopes : [scopes],\n                request,\n                getAccessToken,\n                logger,\n            });\n            let response;\n            let error;\n            try {\n                response = await next(request);\n            }\n            catch (err) {\n                error = err;\n                response = err.response;\n            }\n            if (callbacks.authorizeRequestOnChallenge &&\n                (response === null || response === void 0 ? void 0 : response.status) === 401 &&\n                getChallenge(response)) {\n                // processes challenge\n                const shouldSendRequest = await callbacks.authorizeRequestOnChallenge({\n                    scopes: Array.isArray(scopes) ? scopes : [scopes],\n                    request,\n                    response,\n                    getAccessToken,\n                    logger,\n                });\n                if (shouldSendRequest) {\n                    return next(request);\n                }\n            }\n            if (error) {\n                throw error;\n            }\n            else {\n                return response;\n            }\n        },\n    };\n}\n//# sourceMappingURL=bearerTokenAuthenticationPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/bearerTokenAuthenticationPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/decompressResponsePolicy.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/decompressResponsePolicy.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decompressResponsePolicy: () => (/* binding */ decompressResponsePolicy),\n/* harmony export */   decompressResponsePolicyName: () => (/* binding */ decompressResponsePolicyName)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/**\n * The programmatic identifier of the decompressResponsePolicy.\n */\nconst decompressResponsePolicyName = \"decompressResponsePolicy\";\n/**\n * A policy to enable response decompression according to Accept-Encoding header\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding\n */\nfunction decompressResponsePolicy() {\n    return {\n        name: decompressResponsePolicyName,\n        async sendRequest(request, next) {\n            // HEAD requests have no body\n            if (request.method !== \"HEAD\") {\n                request.headers.set(\"Accept-Encoding\", \"gzip,deflate\");\n            }\n            return next(request);\n        },\n    };\n}\n//# sourceMappingURL=decompressResponsePolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy9kZWNvbXByZXNzUmVzcG9uc2VQb2xpY3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL0BhenVyZS9jb3JlLXJlc3QtcGlwZWxpbmUvZGlzdC1lc20vc3JjL3BvbGljaWVzL2RlY29tcHJlc3NSZXNwb25zZVBvbGljeS5qcz81NDI4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxuLyoqXG4gKiBUaGUgcHJvZ3JhbW1hdGljIGlkZW50aWZpZXIgb2YgdGhlIGRlY29tcHJlc3NSZXNwb25zZVBvbGljeS5cbiAqL1xuZXhwb3J0IGNvbnN0IGRlY29tcHJlc3NSZXNwb25zZVBvbGljeU5hbWUgPSBcImRlY29tcHJlc3NSZXNwb25zZVBvbGljeVwiO1xuLyoqXG4gKiBBIHBvbGljeSB0byBlbmFibGUgcmVzcG9uc2UgZGVjb21wcmVzc2lvbiBhY2NvcmRpbmcgdG8gQWNjZXB0LUVuY29kaW5nIGhlYWRlclxuICogaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvSFRUUC9IZWFkZXJzL0FjY2VwdC1FbmNvZGluZ1xuICovXG5leHBvcnQgZnVuY3Rpb24gZGVjb21wcmVzc1Jlc3BvbnNlUG9saWN5KCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIG5hbWU6IGRlY29tcHJlc3NSZXNwb25zZVBvbGljeU5hbWUsXG4gICAgICAgIGFzeW5jIHNlbmRSZXF1ZXN0KHJlcXVlc3QsIG5leHQpIHtcbiAgICAgICAgICAgIC8vIEhFQUQgcmVxdWVzdHMgaGF2ZSBubyBib2R5XG4gICAgICAgICAgICBpZiAocmVxdWVzdC5tZXRob2QgIT09IFwiSEVBRFwiKSB7XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5oZWFkZXJzLnNldChcIkFjY2VwdC1FbmNvZGluZ1wiLCBcImd6aXAsZGVmbGF0ZVwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBuZXh0KHJlcXVlc3QpO1xuICAgICAgICB9LFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZWNvbXByZXNzUmVzcG9uc2VQb2xpY3kuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/decompressResponsePolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/defaultRetryPolicy.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/defaultRetryPolicy.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultRetryPolicy: () => (/* binding */ defaultRetryPolicy),\n/* harmony export */   defaultRetryPolicyName: () => (/* binding */ defaultRetryPolicyName)\n/* harmony export */ });\n/* harmony import */ var _retryStrategies_exponentialRetryStrategy__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../retryStrategies/exponentialRetryStrategy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/exponentialRetryStrategy.js\");\n/* harmony import */ var _retryStrategies_throttlingRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../retryStrategies/throttlingRetryStrategy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/throttlingRetryStrategy.js\");\n/* harmony import */ var _retryPolicy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./retryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/retryPolicy.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constants */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n\n/**\n * Name of the {@link defaultRetryPolicy}\n */\nconst defaultRetryPolicyName = \"defaultRetryPolicy\";\n/**\n * A policy that retries according to three strategies:\n * - When the server sends a 429 response with a Retry-After header.\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails, it will retry with an exponentially increasing delay.\n */\nfunction defaultRetryPolicy(options = {}) {\n    var _a;\n    return {\n        name: defaultRetryPolicyName,\n        sendRequest: (0,_retryPolicy__WEBPACK_IMPORTED_MODULE_0__.retryPolicy)([(0,_retryStrategies_throttlingRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.throttlingRetryStrategy)(), (0,_retryStrategies_exponentialRetryStrategy__WEBPACK_IMPORTED_MODULE_2__.exponentialRetryStrategy)(options)], {\n            maxRetries: (_a = options.maxRetries) !== null && _a !== void 0 ? _a : _constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_RETRY_POLICY_COUNT,\n        }).sendRequest,\n    };\n}\n//# sourceMappingURL=defaultRetryPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/defaultRetryPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/exponentialRetryPolicy.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/exponentialRetryPolicy.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exponentialRetryPolicy: () => (/* binding */ exponentialRetryPolicy),\n/* harmony export */   exponentialRetryPolicyName: () => (/* binding */ exponentialRetryPolicyName)\n/* harmony export */ });\n/* harmony import */ var _retryStrategies_exponentialRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../retryStrategies/exponentialRetryStrategy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/exponentialRetryStrategy.js\");\n/* harmony import */ var _retryPolicy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./retryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/retryPolicy.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n/**\n * The programmatic identifier of the exponentialRetryPolicy.\n */\nconst exponentialRetryPolicyName = \"exponentialRetryPolicy\";\n/**\n * A policy that attempts to retry requests while introducing an exponentially increasing delay.\n * @param options - Options that configure retry logic.\n */\nfunction exponentialRetryPolicy(options = {}) {\n    var _a;\n    return (0,_retryPolicy__WEBPACK_IMPORTED_MODULE_0__.retryPolicy)([\n        (0,_retryStrategies_exponentialRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.exponentialRetryStrategy)(Object.assign(Object.assign({}, options), { ignoreSystemErrors: true })),\n    ], {\n        maxRetries: (_a = options.maxRetries) !== null && _a !== void 0 ? _a : _constants__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_RETRY_POLICY_COUNT,\n    });\n}\n//# sourceMappingURL=exponentialRetryPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy9leHBvbmVudGlhbFJldHJ5UG9saWN5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUN1RjtBQUMzQztBQUNjO0FBQzFEO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTyw0Q0FBNEM7QUFDbkQ7QUFDQSxXQUFXLHlEQUFXO0FBQ3RCLFFBQVEsbUdBQXdCLCtCQUErQixjQUFjLDBCQUEwQjtBQUN2RztBQUNBLCtFQUErRSxrRUFBMEI7QUFDekcsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL0BhenVyZS9jb3JlLXJlc3QtcGlwZWxpbmUvZGlzdC1lc20vc3JjL3BvbGljaWVzL2V4cG9uZW50aWFsUmV0cnlQb2xpY3kuanM/YzU2OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmltcG9ydCB7IGV4cG9uZW50aWFsUmV0cnlTdHJhdGVneSB9IGZyb20gXCIuLi9yZXRyeVN0cmF0ZWdpZXMvZXhwb25lbnRpYWxSZXRyeVN0cmF0ZWd5XCI7XG5pbXBvcnQgeyByZXRyeVBvbGljeSB9IGZyb20gXCIuL3JldHJ5UG9saWN5XCI7XG5pbXBvcnQgeyBERUZBVUxUX1JFVFJZX1BPTElDWV9DT1VOVCB9IGZyb20gXCIuLi9jb25zdGFudHNcIjtcbi8qKlxuICogVGhlIHByb2dyYW1tYXRpYyBpZGVudGlmaWVyIG9mIHRoZSBleHBvbmVudGlhbFJldHJ5UG9saWN5LlxuICovXG5leHBvcnQgY29uc3QgZXhwb25lbnRpYWxSZXRyeVBvbGljeU5hbWUgPSBcImV4cG9uZW50aWFsUmV0cnlQb2xpY3lcIjtcbi8qKlxuICogQSBwb2xpY3kgdGhhdCBhdHRlbXB0cyB0byByZXRyeSByZXF1ZXN0cyB3aGlsZSBpbnRyb2R1Y2luZyBhbiBleHBvbmVudGlhbGx5IGluY3JlYXNpbmcgZGVsYXkuXG4gKiBAcGFyYW0gb3B0aW9ucyAtIE9wdGlvbnMgdGhhdCBjb25maWd1cmUgcmV0cnkgbG9naWMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBleHBvbmVudGlhbFJldHJ5UG9saWN5KG9wdGlvbnMgPSB7fSkge1xuICAgIHZhciBfYTtcbiAgICByZXR1cm4gcmV0cnlQb2xpY3koW1xuICAgICAgICBleHBvbmVudGlhbFJldHJ5U3RyYXRlZ3koT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBvcHRpb25zKSwgeyBpZ25vcmVTeXN0ZW1FcnJvcnM6IHRydWUgfSkpLFxuICAgIF0sIHtcbiAgICAgICAgbWF4UmV0cmllczogKF9hID0gb3B0aW9ucy5tYXhSZXRyaWVzKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBERUZBVUxUX1JFVFJZX1BPTElDWV9DT1VOVCxcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWV4cG9uZW50aWFsUmV0cnlQb2xpY3kuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/exponentialRetryPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/formDataPolicy.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/formDataPolicy.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formDataPolicy: () => (/* binding */ formDataPolicy),\n/* harmony export */   formDataPolicyName: () => (/* binding */ formDataPolicyName)\n/* harmony export */ });\n/* harmony import */ var form_data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! form-data */ \"(instrument)/../../node_modules/form-data/lib/form_data.js\");\n/* harmony import */ var form_data__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(form_data__WEBPACK_IMPORTED_MODULE_0__);\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The programmatic identifier of the formDataPolicy.\n */\nconst formDataPolicyName = \"formDataPolicy\";\n/**\n * A policy that encodes FormData on the request into the body.\n */\nfunction formDataPolicy() {\n    return {\n        name: formDataPolicyName,\n        async sendRequest(request, next) {\n            if (request.formData) {\n                const contentType = request.headers.get(\"Content-Type\");\n                if (contentType && contentType.indexOf(\"application/x-www-form-urlencoded\") !== -1) {\n                    request.body = wwwFormUrlEncode(request.formData);\n                    request.formData = undefined;\n                }\n                else {\n                    await prepareFormData(request.formData, request);\n                }\n            }\n            return next(request);\n        },\n    };\n}\nfunction wwwFormUrlEncode(formData) {\n    const urlSearchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(formData)) {\n        if (Array.isArray(value)) {\n            for (const subValue of value) {\n                urlSearchParams.append(key, subValue.toString());\n            }\n        }\n        else {\n            urlSearchParams.append(key, value.toString());\n        }\n    }\n    return urlSearchParams.toString();\n}\nasync function prepareFormData(formData, request) {\n    const requestForm = new (form_data__WEBPACK_IMPORTED_MODULE_0___default())();\n    for (const formKey of Object.keys(formData)) {\n        const formValue = formData[formKey];\n        if (Array.isArray(formValue)) {\n            for (const subValue of formValue) {\n                requestForm.append(formKey, subValue);\n            }\n        }\n        else {\n            requestForm.append(formKey, formValue);\n        }\n    }\n    request.body = requestForm;\n    request.formData = undefined;\n    const contentType = request.headers.get(\"Content-Type\");\n    if (contentType && contentType.indexOf(\"multipart/form-data\") !== -1) {\n        request.headers.set(\"Content-Type\", `multipart/form-data; boundary=${requestForm.getBoundary()}`);\n    }\n    try {\n        const contentLength = await new Promise((resolve, reject) => {\n            requestForm.getLength((err, length) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(length);\n                }\n            });\n        });\n        request.headers.set(\"Content-Length\", contentLength);\n    }\n    catch (e) {\n        // ignore setting the length if this fails\n    }\n}\n//# sourceMappingURL=formDataPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy9mb3JtRGF0YVBvbGljeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNpQztBQUNqQztBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGtEQUFRO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtFQUFrRSxXQUFXLDBCQUEwQjtBQUN2RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL0BhenVyZS9jb3JlLXJlc3QtcGlwZWxpbmUvZGlzdC1lc20vc3JjL3BvbGljaWVzL2Zvcm1EYXRhUG9saWN5LmpzPzI3YmMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UuXG5pbXBvcnQgRm9ybURhdGEgZnJvbSBcImZvcm0tZGF0YVwiO1xuLyoqXG4gKiBUaGUgcHJvZ3JhbW1hdGljIGlkZW50aWZpZXIgb2YgdGhlIGZvcm1EYXRhUG9saWN5LlxuICovXG5leHBvcnQgY29uc3QgZm9ybURhdGFQb2xpY3lOYW1lID0gXCJmb3JtRGF0YVBvbGljeVwiO1xuLyoqXG4gKiBBIHBvbGljeSB0aGF0IGVuY29kZXMgRm9ybURhdGEgb24gdGhlIHJlcXVlc3QgaW50byB0aGUgYm9keS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1EYXRhUG9saWN5KCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIG5hbWU6IGZvcm1EYXRhUG9saWN5TmFtZSxcbiAgICAgICAgYXN5bmMgc2VuZFJlcXVlc3QocmVxdWVzdCwgbmV4dCkge1xuICAgICAgICAgICAgaWYgKHJlcXVlc3QuZm9ybURhdGEpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBjb250ZW50VHlwZSA9IHJlcXVlc3QuaGVhZGVycy5nZXQoXCJDb250ZW50LVR5cGVcIik7XG4gICAgICAgICAgICAgICAgaWYgKGNvbnRlbnRUeXBlICYmIGNvbnRlbnRUeXBlLmluZGV4T2YoXCJhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWRcIikgIT09IC0xKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3QuYm9keSA9IHd3d0Zvcm1VcmxFbmNvZGUocmVxdWVzdC5mb3JtRGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3QuZm9ybURhdGEgPSB1bmRlZmluZWQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCBwcmVwYXJlRm9ybURhdGEocmVxdWVzdC5mb3JtRGF0YSwgcmVxdWVzdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIG5leHQocmVxdWVzdCk7XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbmZ1bmN0aW9uIHd3d0Zvcm1VcmxFbmNvZGUoZm9ybURhdGEpIHtcbiAgICBjb25zdCB1cmxTZWFyY2hQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoZm9ybURhdGEpKSB7XG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICAgICAgZm9yIChjb25zdCBzdWJWYWx1ZSBvZiB2YWx1ZSkge1xuICAgICAgICAgICAgICAgIHVybFNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCBzdWJWYWx1ZS50b1N0cmluZygpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHVybFNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZS50b1N0cmluZygpKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdXJsU2VhcmNoUGFyYW1zLnRvU3RyaW5nKCk7XG59XG5hc3luYyBmdW5jdGlvbiBwcmVwYXJlRm9ybURhdGEoZm9ybURhdGEsIHJlcXVlc3QpIHtcbiAgICBjb25zdCByZXF1ZXN0Rm9ybSA9IG5ldyBGb3JtRGF0YSgpO1xuICAgIGZvciAoY29uc3QgZm9ybUtleSBvZiBPYmplY3Qua2V5cyhmb3JtRGF0YSkpIHtcbiAgICAgICAgY29uc3QgZm9ybVZhbHVlID0gZm9ybURhdGFbZm9ybUtleV07XG4gICAgICAgIGlmIChBcnJheS5pc0FycmF5KGZvcm1WYWx1ZSkpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3Qgc3ViVmFsdWUgb2YgZm9ybVZhbHVlKSB7XG4gICAgICAgICAgICAgICAgcmVxdWVzdEZvcm0uYXBwZW5kKGZvcm1LZXksIHN1YlZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJlcXVlc3RGb3JtLmFwcGVuZChmb3JtS2V5LCBmb3JtVmFsdWUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJlcXVlc3QuYm9keSA9IHJlcXVlc3RGb3JtO1xuICAgIHJlcXVlc3QuZm9ybURhdGEgPSB1bmRlZmluZWQ7XG4gICAgY29uc3QgY29udGVudFR5cGUgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KFwiQ29udGVudC1UeXBlXCIpO1xuICAgIGlmIChjb250ZW50VHlwZSAmJiBjb250ZW50VHlwZS5pbmRleE9mKFwibXVsdGlwYXJ0L2Zvcm0tZGF0YVwiKSAhPT0gLTEpIHtcbiAgICAgICAgcmVxdWVzdC5oZWFkZXJzLnNldChcIkNvbnRlbnQtVHlwZVwiLCBgbXVsdGlwYXJ0L2Zvcm0tZGF0YTsgYm91bmRhcnk9JHtyZXF1ZXN0Rm9ybS5nZXRCb3VuZGFyeSgpfWApO1xuICAgIH1cbiAgICB0cnkge1xuICAgICAgICBjb25zdCBjb250ZW50TGVuZ3RoID0gYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgcmVxdWVzdEZvcm0uZ2V0TGVuZ3RoKChlcnIsIGxlbmd0aCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKGxlbmd0aCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgICAgICByZXF1ZXN0LmhlYWRlcnMuc2V0KFwiQ29udGVudC1MZW5ndGhcIiwgY29udGVudExlbmd0aCk7XG4gICAgfVxuICAgIGNhdGNoIChlKSB7XG4gICAgICAgIC8vIGlnbm9yZSBzZXR0aW5nIHRoZSBsZW5ndGggaWYgdGhpcyBmYWlsc1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWZvcm1EYXRhUG9saWN5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/formDataPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/logPolicy.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/logPolicy.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logPolicy: () => (/* binding */ logPolicy),\n/* harmony export */   logPolicyName: () => (/* binding */ logPolicyName)\n/* harmony export */ });\n/* harmony import */ var _log__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../log */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/log.js\");\n/* harmony import */ var _util_sanitizer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/sanitizer */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/sanitizer.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n/**\n * The programmatic identifier of the logPolicy.\n */\nconst logPolicyName = \"logPolicy\";\n/**\n * A policy that logs all requests and responses.\n * @param options - Options to configure logPolicy.\n */\nfunction logPolicy(options = {}) {\n    var _a;\n    const logger = (_a = options.logger) !== null && _a !== void 0 ? _a : _log__WEBPACK_IMPORTED_MODULE_0__.logger.info;\n    const sanitizer = new _util_sanitizer__WEBPACK_IMPORTED_MODULE_1__.Sanitizer({\n        additionalAllowedHeaderNames: options.additionalAllowedHeaderNames,\n        additionalAllowedQueryParameters: options.additionalAllowedQueryParameters,\n    });\n    return {\n        name: logPolicyName,\n        async sendRequest(request, next) {\n            if (!logger.enabled) {\n                return next(request);\n            }\n            logger(`Request: ${sanitizer.sanitize(request)}`);\n            const response = await next(request);\n            logger(`Response status code: ${response.status}`);\n            logger(`Headers: ${sanitizer.sanitize(response.headers)}`);\n            return response;\n        },\n    };\n}\n//# sourceMappingURL=logPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/logPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/ndJsonPolicy.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/ndJsonPolicy.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ndJsonPolicy: () => (/* binding */ ndJsonPolicy),\n/* harmony export */   ndJsonPolicyName: () => (/* binding */ ndJsonPolicyName)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/**\n * The programmatic identifier of the ndJsonPolicy.\n */\nconst ndJsonPolicyName = \"ndJsonPolicy\";\n/**\n * ndJsonPolicy is a policy used to control keep alive settings for every request.\n */\nfunction ndJsonPolicy() {\n    return {\n        name: ndJsonPolicyName,\n        async sendRequest(request, next) {\n            // There currently isn't a good way to bypass the serializer\n            if (typeof request.body === \"string\" && request.body.startsWith(\"[\")) {\n                const body = JSON.parse(request.body);\n                if (Array.isArray(body)) {\n                    request.body = body.map((item) => JSON.stringify(item) + \"\\n\").join(\"\");\n                }\n            }\n            return next(request);\n        },\n    };\n}\n//# sourceMappingURL=ndJsonPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy9uZEpzb25Qb2xpY3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy9uZEpzb25Qb2xpY3kuanM/YTdjZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbi8qKlxuICogVGhlIHByb2dyYW1tYXRpYyBpZGVudGlmaWVyIG9mIHRoZSBuZEpzb25Qb2xpY3kuXG4gKi9cbmV4cG9ydCBjb25zdCBuZEpzb25Qb2xpY3lOYW1lID0gXCJuZEpzb25Qb2xpY3lcIjtcbi8qKlxuICogbmRKc29uUG9saWN5IGlzIGEgcG9saWN5IHVzZWQgdG8gY29udHJvbCBrZWVwIGFsaXZlIHNldHRpbmdzIGZvciBldmVyeSByZXF1ZXN0LlxuICovXG5leHBvcnQgZnVuY3Rpb24gbmRKc29uUG9saWN5KCkge1xuICAgIHJldHVybiB7XG4gICAgICAgIG5hbWU6IG5kSnNvblBvbGljeU5hbWUsXG4gICAgICAgIGFzeW5jIHNlbmRSZXF1ZXN0KHJlcXVlc3QsIG5leHQpIHtcbiAgICAgICAgICAgIC8vIFRoZXJlIGN1cnJlbnRseSBpc24ndCBhIGdvb2Qgd2F5IHRvIGJ5cGFzcyB0aGUgc2VyaWFsaXplclxuICAgICAgICAgICAgaWYgKHR5cGVvZiByZXF1ZXN0LmJvZHkgPT09IFwic3RyaW5nXCIgJiYgcmVxdWVzdC5ib2R5LnN0YXJ0c1dpdGgoXCJbXCIpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgYm9keSA9IEpTT04ucGFyc2UocmVxdWVzdC5ib2R5KTtcbiAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShib2R5KSkge1xuICAgICAgICAgICAgICAgICAgICByZXF1ZXN0LmJvZHkgPSBib2R5Lm1hcCgoaXRlbSkgPT4gSlNPTi5zdHJpbmdpZnkoaXRlbSkgKyBcIlxcblwiKS5qb2luKFwiXCIpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBuZXh0KHJlcXVlc3QpO1xuICAgICAgICB9LFxuICAgIH07XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1uZEpzb25Qb2xpY3kuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/ndJsonPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/proxyPolicy.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/proxyPolicy.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultProxySettings: () => (/* binding */ getDefaultProxySettings),\n/* harmony export */   getProxyAgentOptions: () => (/* binding */ getProxyAgentOptions),\n/* harmony export */   globalNoProxyList: () => (/* binding */ globalNoProxyList),\n/* harmony export */   loadNoProxy: () => (/* binding */ loadNoProxy),\n/* harmony export */   proxyPolicy: () => (/* binding */ proxyPolicy),\n/* harmony export */   proxyPolicyName: () => (/* binding */ proxyPolicyName)\n/* harmony export */ });\n/* harmony import */ var https_proxy_agent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! https-proxy-agent */ \"(instrument)/../../node_modules/https-proxy-agent/dist/index.js\");\n/* harmony import */ var https_proxy_agent__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(https_proxy_agent__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var http_proxy_agent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! http-proxy-agent */ \"(instrument)/../../node_modules/http-proxy-agent/dist/index.js\");\n/* harmony import */ var http_proxy_agent__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(http_proxy_agent__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _log__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../log */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/log.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\nconst HTTPS_PROXY = \"HTTPS_PROXY\";\nconst HTTP_PROXY = \"HTTP_PROXY\";\nconst ALL_PROXY = \"ALL_PROXY\";\nconst NO_PROXY = \"NO_PROXY\";\n/**\n * The programmatic identifier of the proxyPolicy.\n */\nconst proxyPolicyName = \"proxyPolicy\";\n/**\n * Stores the patterns specified in NO_PROXY environment variable.\n * @internal\n */\nconst globalNoProxyList = [];\nlet noProxyListLoaded = false;\n/** A cache of whether a host should bypass the proxy. */\nconst globalBypassedMap = new Map();\nfunction getEnvironmentValue(name) {\n    if (process.env[name]) {\n        return process.env[name];\n    }\n    else if (process.env[name.toLowerCase()]) {\n        return process.env[name.toLowerCase()];\n    }\n    return undefined;\n}\nfunction loadEnvironmentProxyValue() {\n    if (!process) {\n        return undefined;\n    }\n    const httpsProxy = getEnvironmentValue(HTTPS_PROXY);\n    const allProxy = getEnvironmentValue(ALL_PROXY);\n    const httpProxy = getEnvironmentValue(HTTP_PROXY);\n    return httpsProxy || allProxy || httpProxy;\n}\n/**\n * Check whether the host of a given `uri` matches any pattern in the no proxy list.\n * If there's a match, any request sent to the same host shouldn't have the proxy settings set.\n * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210\n */\nfunction isBypassed(uri, noProxyList, bypassedMap) {\n    if (noProxyList.length === 0) {\n        return false;\n    }\n    const host = new URL(uri).hostname;\n    if (bypassedMap === null || bypassedMap === void 0 ? void 0 : bypassedMap.has(host)) {\n        return bypassedMap.get(host);\n    }\n    let isBypassedFlag = false;\n    for (const pattern of noProxyList) {\n        if (pattern[0] === \".\") {\n            // This should match either domain it self or any subdomain or host\n            // .foo.com will match foo.com it self or *.foo.com\n            if (host.endsWith(pattern)) {\n                isBypassedFlag = true;\n            }\n            else {\n                if (host.length === pattern.length - 1 && host === pattern.slice(1)) {\n                    isBypassedFlag = true;\n                }\n            }\n        }\n        else {\n            if (host === pattern) {\n                isBypassedFlag = true;\n            }\n        }\n    }\n    bypassedMap === null || bypassedMap === void 0 ? void 0 : bypassedMap.set(host, isBypassedFlag);\n    return isBypassedFlag;\n}\nfunction loadNoProxy() {\n    const noProxy = getEnvironmentValue(NO_PROXY);\n    noProxyListLoaded = true;\n    if (noProxy) {\n        return noProxy\n            .split(\",\")\n            .map((item) => item.trim())\n            .filter((item) => item.length);\n    }\n    return [];\n}\n/**\n * This method converts a proxy url into `ProxySettings` for use with ProxyPolicy.\n * If no argument is given, it attempts to parse a proxy URL from the environment\n * variables `HTTPS_PROXY` or `HTTP_PROXY`.\n * @param proxyUrl - The url of the proxy to use. May contain authentication information.\n */\nfunction getDefaultProxySettings(proxyUrl) {\n    if (!proxyUrl) {\n        proxyUrl = loadEnvironmentProxyValue();\n        if (!proxyUrl) {\n            return undefined;\n        }\n    }\n    const parsedUrl = new URL(proxyUrl);\n    const schema = parsedUrl.protocol ? parsedUrl.protocol + \"//\" : \"\";\n    return {\n        host: schema + parsedUrl.hostname,\n        port: Number.parseInt(parsedUrl.port || \"80\"),\n        username: parsedUrl.username,\n        password: parsedUrl.password,\n    };\n}\n/**\n * @internal\n */\nfunction getProxyAgentOptions(proxySettings, { headers, tlsSettings }) {\n    let parsedProxyUrl;\n    try {\n        parsedProxyUrl = new URL(proxySettings.host);\n    }\n    catch (_error) {\n        throw new Error(`Expecting a valid host string in proxy settings, but found \"${proxySettings.host}\".`);\n    }\n    if (tlsSettings) {\n        _log__WEBPACK_IMPORTED_MODULE_2__.logger.warning(\"TLS settings are not supported in combination with custom Proxy, certificates provided to the client will be ignored.\");\n    }\n    const proxyAgentOptions = {\n        hostname: parsedProxyUrl.hostname,\n        port: proxySettings.port,\n        protocol: parsedProxyUrl.protocol,\n        headers: headers.toJSON(),\n    };\n    if (proxySettings.username && proxySettings.password) {\n        proxyAgentOptions.auth = `${proxySettings.username}:${proxySettings.password}`;\n    }\n    else if (proxySettings.username) {\n        proxyAgentOptions.auth = `${proxySettings.username}`;\n    }\n    return proxyAgentOptions;\n}\nfunction setProxyAgentOnRequest(request, cachedAgents) {\n    // Custom Agent should take precedence so if one is present\n    // we should skip to avoid overwriting it.\n    if (request.agent) {\n        return;\n    }\n    const url = new URL(request.url);\n    const isInsecure = url.protocol !== \"https:\";\n    const proxySettings = request.proxySettings;\n    if (proxySettings) {\n        if (isInsecure) {\n            if (!cachedAgents.httpProxyAgent) {\n                const proxyAgentOptions = getProxyAgentOptions(proxySettings, request);\n                cachedAgents.httpProxyAgent = new http_proxy_agent__WEBPACK_IMPORTED_MODULE_1__.HttpProxyAgent(proxyAgentOptions);\n            }\n            request.agent = cachedAgents.httpProxyAgent;\n        }\n        else {\n            if (!cachedAgents.httpsProxyAgent) {\n                const proxyAgentOptions = getProxyAgentOptions(proxySettings, request);\n                cachedAgents.httpsProxyAgent = new https_proxy_agent__WEBPACK_IMPORTED_MODULE_0__.HttpsProxyAgent(proxyAgentOptions);\n            }\n            request.agent = cachedAgents.httpsProxyAgent;\n        }\n    }\n}\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nfunction proxyPolicy(proxySettings = getDefaultProxySettings(), options) {\n    if (!noProxyListLoaded) {\n        globalNoProxyList.push(...loadNoProxy());\n    }\n    const cachedAgents = {};\n    return {\n        name: proxyPolicyName,\n        async sendRequest(request, next) {\n            var _a;\n            if (!request.proxySettings &&\n                !isBypassed(request.url, (_a = options === null || options === void 0 ? void 0 : options.customNoProxyList) !== null && _a !== void 0 ? _a : globalNoProxyList, (options === null || options === void 0 ? void 0 : options.customNoProxyList) ? undefined : globalBypassedMap)) {\n                request.proxySettings = proxySettings;\n            }\n            if (request.proxySettings) {\n                setProxyAgentOnRequest(request, cachedAgents);\n            }\n            return next(request);\n        },\n    };\n}\n//# sourceMappingURL=proxyPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/proxyPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/redirectPolicy.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/redirectPolicy.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   redirectPolicy: () => (/* binding */ redirectPolicy),\n/* harmony export */   redirectPolicyName: () => (/* binding */ redirectPolicyName)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/**\n * The programmatic identifier of the redirectPolicy.\n */\nconst redirectPolicyName = \"redirectPolicy\";\n/**\n * Methods that are allowed to follow redirects 301 and 302\n */\nconst allowedRedirect = [\"GET\", \"HEAD\"];\n/**\n * A policy to follow Location headers from the server in order\n * to support server-side redirection.\n * In the browser, this policy is not used.\n * @param options - Options to control policy behavior.\n */\nfunction redirectPolicy(options = {}) {\n    const { maxRetries = 20 } = options;\n    return {\n        name: redirectPolicyName,\n        async sendRequest(request, next) {\n            const response = await next(request);\n            return handleRedirect(next, response, maxRetries);\n        },\n    };\n}\nasync function handleRedirect(next, response, maxRetries, currentRetries = 0) {\n    const { request, status, headers } = response;\n    const locationHeader = headers.get(\"location\");\n    if (locationHeader &&\n        (status === 300 ||\n            (status === 301 && allowedRedirect.includes(request.method)) ||\n            (status === 302 && allowedRedirect.includes(request.method)) ||\n            (status === 303 && request.method === \"POST\") ||\n            status === 307) &&\n        currentRetries < maxRetries) {\n        const url = new URL(locationHeader, request.url);\n        request.url = url.toString();\n        // POST request with Status code 303 should be converted into a\n        // redirected GET request if the redirect url is present in the location header\n        if (status === 303) {\n            request.method = \"GET\";\n            request.headers.delete(\"Content-Length\");\n            delete request.body;\n        }\n        request.headers.delete(\"Authorization\");\n        const res = await next(request);\n        return handleRedirect(next, res, maxRetries, currentRetries + 1);\n    }\n    return response;\n}\n//# sourceMappingURL=redirectPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/redirectPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/retryPolicy.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/retryPolicy.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   retryPolicy: () => (/* binding */ retryPolicy)\n/* harmony export */ });\n/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/helpers */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/helpers.js\");\n/* harmony import */ var _azure_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/logger */ \"(instrument)/../../node_modules/@azure/logger/dist-esm/src/index.js\");\n/* harmony import */ var _azure_abort_controller__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/abort-controller */ \"(instrument)/../../node_modules/@azure/abort-controller/dist-esm/src/AbortController.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n\nconst retryPolicyLogger = (0,_azure_logger__WEBPACK_IMPORTED_MODULE_0__.createClientLogger)(\"core-rest-pipeline retryPolicy\");\n/**\n * The programmatic identifier of the retryPolicy.\n */\nconst retryPolicyName = \"retryPolicy\";\n/**\n * retryPolicy is a generic policy to enable retrying requests when certain conditions are met\n */\nfunction retryPolicy(strategies, options = { maxRetries: _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_RETRY_POLICY_COUNT }) {\n    const logger = options.logger || retryPolicyLogger;\n    return {\n        name: retryPolicyName,\n        async sendRequest(request, next) {\n            var _a, _b;\n            let response;\n            let responseError;\n            let retryCount = -1;\n            // eslint-disable-next-line no-constant-condition\n            retryRequest: while (true) {\n                retryCount += 1;\n                response = undefined;\n                responseError = undefined;\n                try {\n                    logger.info(`Retry ${retryCount}: Attempting to send request`, request.requestId);\n                    response = await next(request);\n                    logger.info(`Retry ${retryCount}: Received a response from request`, request.requestId);\n                }\n                catch (e) {\n                    logger.error(`Retry ${retryCount}: Received an error from request`, request.requestId);\n                    // RestErrors are valid targets for the retry strategies.\n                    // If none of the retry strategies can work with them, they will be thrown later in this policy.\n                    // If the received error is not a RestError, it is immediately thrown.\n                    responseError = e;\n                    if (!e || responseError.name !== \"RestError\") {\n                        throw e;\n                    }\n                    response = responseError.response;\n                }\n                if ((_a = request.abortSignal) === null || _a === void 0 ? void 0 : _a.aborted) {\n                    logger.error(`Retry ${retryCount}: Request aborted.`);\n                    const abortError = new _azure_abort_controller__WEBPACK_IMPORTED_MODULE_2__.AbortError();\n                    throw abortError;\n                }\n                if (retryCount >= ((_b = options.maxRetries) !== null && _b !== void 0 ? _b : _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_RETRY_POLICY_COUNT)) {\n                    logger.info(`Retry ${retryCount}: Maximum retries reached. Returning the last received response, or throwing the last received error.`);\n                    if (responseError) {\n                        throw responseError;\n                    }\n                    else if (response) {\n                        return response;\n                    }\n                    else {\n                        throw new Error(\"Maximum retries reached with no response or error to throw\");\n                    }\n                }\n                logger.info(`Retry ${retryCount}: Processing ${strategies.length} retry strategies.`);\n                strategiesLoop: for (const strategy of strategies) {\n                    const strategyLogger = strategy.logger || retryPolicyLogger;\n                    strategyLogger.info(`Retry ${retryCount}: Processing retry strategy ${strategy.name}.`);\n                    const modifiers = strategy.retry({\n                        retryCount,\n                        response,\n                        responseError,\n                    });\n                    if (modifiers.skipStrategy) {\n                        strategyLogger.info(`Retry ${retryCount}: Skipped.`);\n                        continue strategiesLoop;\n                    }\n                    const { errorToThrow, retryAfterInMs, redirectTo } = modifiers;\n                    if (errorToThrow) {\n                        strategyLogger.error(`Retry ${retryCount}: Retry strategy ${strategy.name} throws error:`, errorToThrow);\n                        throw errorToThrow;\n                    }\n                    if (retryAfterInMs || retryAfterInMs === 0) {\n                        strategyLogger.info(`Retry ${retryCount}: Retry strategy ${strategy.name} retries after ${retryAfterInMs}`);\n                        await (0,_util_helpers__WEBPACK_IMPORTED_MODULE_3__.delay)(retryAfterInMs, undefined, { abortSignal: request.abortSignal });\n                        continue retryRequest;\n                    }\n                    if (redirectTo) {\n                        strategyLogger.info(`Retry ${retryCount}: Retry strategy ${strategy.name} redirects to ${redirectTo}`);\n                        request.url = redirectTo;\n                        continue retryRequest;\n                    }\n                }\n                if (responseError) {\n                    logger.info(`None of the retry strategies could work with the received error. Throwing it.`);\n                    throw responseError;\n                }\n                if (response) {\n                    logger.info(`None of the retry strategies could work with the received response. Returning it.`);\n                    return response;\n                }\n                // If all the retries skip and there's no response,\n                // we're still in the retry loop, so a new request will be sent\n                // until `maxRetries` is reached.\n            }\n        },\n    };\n}\n//# sourceMappingURL=retryPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/retryPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/setClientRequestIdPolicy.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/setClientRequestIdPolicy.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setClientRequestIdPolicy: () => (/* binding */ setClientRequestIdPolicy),\n/* harmony export */   setClientRequestIdPolicyName: () => (/* binding */ setClientRequestIdPolicyName)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/**\n * The programmatic identifier of the setClientRequestIdPolicy.\n */\nconst setClientRequestIdPolicyName = \"setClientRequestIdPolicy\";\n/**\n * Each PipelineRequest gets a unique id upon creation.\n * This policy passes that unique id along via an HTTP header to enable better\n * telemetry and tracing.\n * @param requestIdHeaderName - The name of the header to pass the request ID to.\n */\nfunction setClientRequestIdPolicy(requestIdHeaderName = \"x-ms-client-request-id\") {\n    return {\n        name: setClientRequestIdPolicyName,\n        async sendRequest(request, next) {\n            if (!request.headers.has(requestIdHeaderName)) {\n                request.headers.set(requestIdHeaderName, request.requestId);\n            }\n            return next(request);\n        },\n    };\n}\n//# sourceMappingURL=setClientRequestIdPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy9zZXRDbGllbnRSZXF1ZXN0SWRQb2xpY3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uLi8uLi9ub2RlX21vZHVsZXMvQGF6dXJlL2NvcmUtcmVzdC1waXBlbGluZS9kaXN0LWVzbS9zcmMvcG9saWNpZXMvc2V0Q2xpZW50UmVxdWVzdElkUG9saWN5LmpzPzc5NWMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UuXG4vKipcbiAqIFRoZSBwcm9ncmFtbWF0aWMgaWRlbnRpZmllciBvZiB0aGUgc2V0Q2xpZW50UmVxdWVzdElkUG9saWN5LlxuICovXG5leHBvcnQgY29uc3Qgc2V0Q2xpZW50UmVxdWVzdElkUG9saWN5TmFtZSA9IFwic2V0Q2xpZW50UmVxdWVzdElkUG9saWN5XCI7XG4vKipcbiAqIEVhY2ggUGlwZWxpbmVSZXF1ZXN0IGdldHMgYSB1bmlxdWUgaWQgdXBvbiBjcmVhdGlvbi5cbiAqIFRoaXMgcG9saWN5IHBhc3NlcyB0aGF0IHVuaXF1ZSBpZCBhbG9uZyB2aWEgYW4gSFRUUCBoZWFkZXIgdG8gZW5hYmxlIGJldHRlclxuICogdGVsZW1ldHJ5IGFuZCB0cmFjaW5nLlxuICogQHBhcmFtIHJlcXVlc3RJZEhlYWRlck5hbWUgLSBUaGUgbmFtZSBvZiB0aGUgaGVhZGVyIHRvIHBhc3MgdGhlIHJlcXVlc3QgSUQgdG8uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzZXRDbGllbnRSZXF1ZXN0SWRQb2xpY3kocmVxdWVzdElkSGVhZGVyTmFtZSA9IFwieC1tcy1jbGllbnQtcmVxdWVzdC1pZFwiKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbmFtZTogc2V0Q2xpZW50UmVxdWVzdElkUG9saWN5TmFtZSxcbiAgICAgICAgYXN5bmMgc2VuZFJlcXVlc3QocmVxdWVzdCwgbmV4dCkge1xuICAgICAgICAgICAgaWYgKCFyZXF1ZXN0LmhlYWRlcnMuaGFzKHJlcXVlc3RJZEhlYWRlck5hbWUpKSB7XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5oZWFkZXJzLnNldChyZXF1ZXN0SWRIZWFkZXJOYW1lLCByZXF1ZXN0LnJlcXVlc3RJZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gbmV4dChyZXF1ZXN0KTtcbiAgICAgICAgfSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2V0Q2xpZW50UmVxdWVzdElkUG9saWN5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/setClientRequestIdPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/systemErrorRetryPolicy.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/systemErrorRetryPolicy.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   systemErrorRetryPolicy: () => (/* binding */ systemErrorRetryPolicy),\n/* harmony export */   systemErrorRetryPolicyName: () => (/* binding */ systemErrorRetryPolicyName)\n/* harmony export */ });\n/* harmony import */ var _retryStrategies_exponentialRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../retryStrategies/exponentialRetryStrategy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/exponentialRetryStrategy.js\");\n/* harmony import */ var _retryPolicy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./retryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/retryPolicy.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n/**\n * Name of the {@link systemErrorRetryPolicy}\n */\nconst systemErrorRetryPolicyName = \"systemErrorRetryPolicy\";\n/**\n * A retry policy that specifically seeks to handle errors in the\n * underlying transport layer (e.g. DNS lookup failures) rather than\n * retryable error codes from the server itself.\n * @param options - Options that customize the policy.\n */\nfunction systemErrorRetryPolicy(options = {}) {\n    var _a;\n    return {\n        name: systemErrorRetryPolicyName,\n        sendRequest: (0,_retryPolicy__WEBPACK_IMPORTED_MODULE_0__.retryPolicy)([\n            (0,_retryStrategies_exponentialRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.exponentialRetryStrategy)(Object.assign(Object.assign({}, options), { ignoreHttpStatusCodes: true })),\n        ], {\n            maxRetries: (_a = options.maxRetries) !== null && _a !== void 0 ? _a : _constants__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_RETRY_POLICY_COUNT,\n        }).sendRequest,\n    };\n}\n//# sourceMappingURL=systemErrorRetryPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/systemErrorRetryPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/throttlingRetryPolicy.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/throttlingRetryPolicy.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   throttlingRetryPolicy: () => (/* binding */ throttlingRetryPolicy),\n/* harmony export */   throttlingRetryPolicyName: () => (/* binding */ throttlingRetryPolicyName)\n/* harmony export */ });\n/* harmony import */ var _retryStrategies_throttlingRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../retryStrategies/throttlingRetryStrategy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/throttlingRetryStrategy.js\");\n/* harmony import */ var _retryPolicy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./retryPolicy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/retryPolicy.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n/**\n * Name of the {@link throttlingRetryPolicy}\n */\nconst throttlingRetryPolicyName = \"throttlingRetryPolicy\";\n/**\n * A policy that retries when the server sends a 429 response with a Retry-After header.\n *\n * To learn more, please refer to\n * https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-manager-request-limits,\n * https://docs.microsoft.com/en-us/azure/azure-subscription-service-limits and\n * https://docs.microsoft.com/en-us/azure/virtual-machines/troubleshooting/troubleshooting-throttling-errors\n *\n * @param options - Options that configure retry logic.\n */\nfunction throttlingRetryPolicy(options = {}) {\n    var _a;\n    return {\n        name: throttlingRetryPolicyName,\n        sendRequest: (0,_retryPolicy__WEBPACK_IMPORTED_MODULE_0__.retryPolicy)([(0,_retryStrategies_throttlingRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.throttlingRetryStrategy)()], {\n            maxRetries: (_a = options.maxRetries) !== null && _a !== void 0 ? _a : _constants__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_RETRY_POLICY_COUNT,\n        }).sendRequest,\n    };\n}\n//# sourceMappingURL=throttlingRetryPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/throttlingRetryPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tlsPolicy.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tlsPolicy.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tlsPolicy: () => (/* binding */ tlsPolicy),\n/* harmony export */   tlsPolicyName: () => (/* binding */ tlsPolicyName)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/**\n * Name of the TLS Policy\n */\nconst tlsPolicyName = \"tlsPolicy\";\n/**\n * Gets a pipeline policy that adds the client certificate to the HttpClient agent for authentication.\n */\nfunction tlsPolicy(tlsSettings) {\n    return {\n        name: tlsPolicyName,\n        sendRequest: async (req, next) => {\n            // Users may define a request tlsSettings, honor those over the client level one\n            if (!req.tlsSettings) {\n                req.tlsSettings = tlsSettings;\n            }\n            return next(req);\n        },\n    };\n}\n//# sourceMappingURL=tlsPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy90bHNQb2xpY3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy90bHNQb2xpY3kuanM/ODczMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbi8qKlxuICogTmFtZSBvZiB0aGUgVExTIFBvbGljeVxuICovXG5leHBvcnQgY29uc3QgdGxzUG9saWN5TmFtZSA9IFwidGxzUG9saWN5XCI7XG4vKipcbiAqIEdldHMgYSBwaXBlbGluZSBwb2xpY3kgdGhhdCBhZGRzIHRoZSBjbGllbnQgY2VydGlmaWNhdGUgdG8gdGhlIEh0dHBDbGllbnQgYWdlbnQgZm9yIGF1dGhlbnRpY2F0aW9uLlxuICovXG5leHBvcnQgZnVuY3Rpb24gdGxzUG9saWN5KHRsc1NldHRpbmdzKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbmFtZTogdGxzUG9saWN5TmFtZSxcbiAgICAgICAgc2VuZFJlcXVlc3Q6IGFzeW5jIChyZXEsIG5leHQpID0+IHtcbiAgICAgICAgICAgIC8vIFVzZXJzIG1heSBkZWZpbmUgYSByZXF1ZXN0IHRsc1NldHRpbmdzLCBob25vciB0aG9zZSBvdmVyIHRoZSBjbGllbnQgbGV2ZWwgb25lXG4gICAgICAgICAgICBpZiAoIXJlcS50bHNTZXR0aW5ncykge1xuICAgICAgICAgICAgICAgIHJlcS50bHNTZXR0aW5ncyA9IHRsc1NldHRpbmdzO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIG5leHQocmVxKTtcbiAgICAgICAgfSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dGxzUG9saWN5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tlsPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tracingPolicy.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tracingPolicy.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tracingPolicy: () => (/* binding */ tracingPolicy),\n/* harmony export */   tracingPolicyName: () => (/* binding */ tracingPolicyName)\n/* harmony export */ });\n/* harmony import */ var _azure_core_tracing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @azure/core-tracing */ \"(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/tracingClient.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constants */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js\");\n/* harmony import */ var _util_userAgent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/userAgent */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgent.js\");\n/* harmony import */ var _log__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../log */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/log.js\");\n/* harmony import */ var _azure_core_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/core-util */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/index.js\");\n/* harmony import */ var _restError__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../restError */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/restError.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n\n\n\n/**\n * The programmatic identifier of the tracingPolicy.\n */\nconst tracingPolicyName = \"tracingPolicy\";\n/**\n * A simple policy to create OpenTelemetry Spans for each request made by the pipeline\n * that has SpanOptions with a parent.\n * Requests made without a parent Span will not be recorded.\n * @param options - Options to configure the telemetry logged by the tracing policy.\n */\nfunction tracingPolicy(options = {}) {\n    const userAgent = (0,_util_userAgent__WEBPACK_IMPORTED_MODULE_1__.getUserAgentValue)(options.userAgentPrefix);\n    const tracingClient = tryCreateTracingClient();\n    return {\n        name: tracingPolicyName,\n        async sendRequest(request, next) {\n            var _a, _b;\n            if (!tracingClient || !((_a = request.tracingOptions) === null || _a === void 0 ? void 0 : _a.tracingContext)) {\n                return next(request);\n            }\n            const { span, tracingContext } = (_b = tryCreateSpan(tracingClient, request, userAgent)) !== null && _b !== void 0 ? _b : {};\n            if (!span || !tracingContext) {\n                return next(request);\n            }\n            try {\n                const response = await tracingClient.withContext(tracingContext, next, request);\n                tryProcessResponse(span, response);\n                return response;\n            }\n            catch (err) {\n                tryProcessError(span, err);\n                throw err;\n            }\n        },\n    };\n}\nfunction tryCreateTracingClient() {\n    try {\n        return (0,_azure_core_tracing__WEBPACK_IMPORTED_MODULE_2__.createTracingClient)({\n            namespace: \"\",\n            packageName: \"@azure/core-rest-pipeline\",\n            packageVersion: _constants__WEBPACK_IMPORTED_MODULE_3__.SDK_VERSION,\n        });\n    }\n    catch (e) {\n        _log__WEBPACK_IMPORTED_MODULE_4__.logger.warning(`Error when creating the TracingClient: ${(0,_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(e)}`);\n        return undefined;\n    }\n}\nfunction tryCreateSpan(tracingClient, request, userAgent) {\n    try {\n        // As per spec, we do not need to differentiate between HTTP and HTTPS in span name.\n        const { span, updatedOptions } = tracingClient.startSpan(`HTTP ${request.method}`, { tracingOptions: request.tracingOptions }, {\n            spanKind: \"client\",\n            spanAttributes: {\n                \"http.method\": request.method,\n                \"http.url\": request.url,\n                requestId: request.requestId,\n            },\n        });\n        // If the span is not recording, don't do any more work.\n        if (!span.isRecording()) {\n            span.end();\n            return undefined;\n        }\n        if (userAgent) {\n            span.setAttribute(\"http.user_agent\", userAgent);\n        }\n        // set headers\n        const headers = tracingClient.createRequestHeaders(updatedOptions.tracingOptions.tracingContext);\n        for (const [key, value] of Object.entries(headers)) {\n            request.headers.set(key, value);\n        }\n        return { span, tracingContext: updatedOptions.tracingOptions.tracingContext };\n    }\n    catch (e) {\n        _log__WEBPACK_IMPORTED_MODULE_4__.logger.warning(`Skipping creating a tracing span due to an error: ${(0,_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(e)}`);\n        return undefined;\n    }\n}\nfunction tryProcessError(span, error) {\n    try {\n        span.setStatus({\n            status: \"error\",\n            error: (0,_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.isError)(error) ? error : undefined,\n        });\n        if ((0,_restError__WEBPACK_IMPORTED_MODULE_5__.isRestError)(error) && error.statusCode) {\n            span.setAttribute(\"http.status_code\", error.statusCode);\n        }\n        span.end();\n    }\n    catch (e) {\n        _log__WEBPACK_IMPORTED_MODULE_4__.logger.warning(`Skipping tracing span processing due to an error: ${(0,_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(e)}`);\n    }\n}\nfunction tryProcessResponse(span, response) {\n    try {\n        span.setAttribute(\"http.status_code\", response.status);\n        const serviceRequestId = response.headers.get(\"x-ms-request-id\");\n        if (serviceRequestId) {\n            span.setAttribute(\"serviceRequestId\", serviceRequestId);\n        }\n        span.setStatus({\n            status: \"success\",\n        });\n        span.end();\n    }\n    catch (e) {\n        _log__WEBPACK_IMPORTED_MODULE_4__.logger.warning(`Skipping tracing span processing due to an error: ${(0,_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.getErrorMessage)(e)}`);\n    }\n}\n//# sourceMappingURL=tracingPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/tracingPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/userAgentPolicy.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/userAgentPolicy.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   userAgentPolicy: () => (/* binding */ userAgentPolicy),\n/* harmony export */   userAgentPolicyName: () => (/* binding */ userAgentPolicyName)\n/* harmony export */ });\n/* harmony import */ var _util_userAgent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/userAgent */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgent.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nconst UserAgentHeaderName = (0,_util_userAgent__WEBPACK_IMPORTED_MODULE_0__.getUserAgentHeaderName)();\n/**\n * The programmatic identifier of the userAgentPolicy.\n */\nconst userAgentPolicyName = \"userAgentPolicy\";\n/**\n * A policy that sets the User-Agent header (or equivalent) to reflect\n * the library version.\n * @param options - Options to customize the user agent value.\n */\nfunction userAgentPolicy(options = {}) {\n    const userAgentValue = (0,_util_userAgent__WEBPACK_IMPORTED_MODULE_0__.getUserAgentValue)(options.userAgentPrefix);\n    return {\n        name: userAgentPolicyName,\n        async sendRequest(request, next) {\n            if (!request.headers.has(UserAgentHeaderName)) {\n                request.headers.set(UserAgentHeaderName, userAgentValue);\n            }\n            return next(request);\n        },\n    };\n}\n//# sourceMappingURL=userAgentPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy9wb2xpY2llcy91c2VyQWdlbnRQb2xpY3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUM4RTtBQUM5RSw0QkFBNEIsdUVBQXNCO0FBQ2xEO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLHFDQUFxQztBQUM1QywyQkFBMkIsa0VBQWlCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uLi8uLi9ub2RlX21vZHVsZXMvQGF6dXJlL2NvcmUtcmVzdC1waXBlbGluZS9kaXN0LWVzbS9zcmMvcG9saWNpZXMvdXNlckFnZW50UG9saWN5LmpzPzg3Y2QiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UuXG5pbXBvcnQgeyBnZXRVc2VyQWdlbnRIZWFkZXJOYW1lLCBnZXRVc2VyQWdlbnRWYWx1ZSB9IGZyb20gXCIuLi91dGlsL3VzZXJBZ2VudFwiO1xuY29uc3QgVXNlckFnZW50SGVhZGVyTmFtZSA9IGdldFVzZXJBZ2VudEhlYWRlck5hbWUoKTtcbi8qKlxuICogVGhlIHByb2dyYW1tYXRpYyBpZGVudGlmaWVyIG9mIHRoZSB1c2VyQWdlbnRQb2xpY3kuXG4gKi9cbmV4cG9ydCBjb25zdCB1c2VyQWdlbnRQb2xpY3lOYW1lID0gXCJ1c2VyQWdlbnRQb2xpY3lcIjtcbi8qKlxuICogQSBwb2xpY3kgdGhhdCBzZXRzIHRoZSBVc2VyLUFnZW50IGhlYWRlciAob3IgZXF1aXZhbGVudCkgdG8gcmVmbGVjdFxuICogdGhlIGxpYnJhcnkgdmVyc2lvbi5cbiAqIEBwYXJhbSBvcHRpb25zIC0gT3B0aW9ucyB0byBjdXN0b21pemUgdGhlIHVzZXIgYWdlbnQgdmFsdWUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VyQWdlbnRQb2xpY3kob3B0aW9ucyA9IHt9KSB7XG4gICAgY29uc3QgdXNlckFnZW50VmFsdWUgPSBnZXRVc2VyQWdlbnRWYWx1ZShvcHRpb25zLnVzZXJBZ2VudFByZWZpeCk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbmFtZTogdXNlckFnZW50UG9saWN5TmFtZSxcbiAgICAgICAgYXN5bmMgc2VuZFJlcXVlc3QocmVxdWVzdCwgbmV4dCkge1xuICAgICAgICAgICAgaWYgKCFyZXF1ZXN0LmhlYWRlcnMuaGFzKFVzZXJBZ2VudEhlYWRlck5hbWUpKSB7XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5oZWFkZXJzLnNldChVc2VyQWdlbnRIZWFkZXJOYW1lLCB1c2VyQWdlbnRWYWx1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gbmV4dChyZXF1ZXN0KTtcbiAgICAgICAgfSxcbiAgICB9O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlckFnZW50UG9saWN5LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/policies/userAgentPolicy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/restError.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/restError.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RestError: () => (/* binding */ RestError),\n/* harmony export */   isRestError: () => (/* binding */ isRestError)\n/* harmony export */ });\n/* harmony import */ var _azure_core_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/core-util */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/index.js\");\n/* harmony import */ var _util_inspect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/inspect */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/inspect.js\");\n/* harmony import */ var _util_sanitizer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/sanitizer */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/sanitizer.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\nconst errorSanitizer = new _util_sanitizer__WEBPACK_IMPORTED_MODULE_1__.Sanitizer();\n/**\n * A custom error type for failed pipeline requests.\n */\nclass RestError extends Error {\n    constructor(message, options = {}) {\n        super(message);\n        this.name = \"RestError\";\n        this.code = options.code;\n        this.statusCode = options.statusCode;\n        this.request = options.request;\n        this.response = options.response;\n        Object.setPrototypeOf(this, RestError.prototype);\n    }\n    /**\n     * Logging method for util.inspect in Node\n     */\n    [_util_inspect__WEBPACK_IMPORTED_MODULE_2__.custom]() {\n        return `RestError: ${this.message} \\n ${errorSanitizer.sanitize(this)}`;\n    }\n}\n/**\n * Something went wrong when making the request.\n * This means the actual request failed for some reason,\n * such as a DNS issue or the connection being lost.\n */\nRestError.REQUEST_SEND_ERROR = \"REQUEST_SEND_ERROR\";\n/**\n * This means that parsing the response from the server failed.\n * It may have been malformed.\n */\nRestError.PARSE_ERROR = \"PARSE_ERROR\";\n/**\n * Typeguard for RestError\n * @param e - Something caught by a catch clause.\n */\nfunction isRestError(e) {\n    if (e instanceof RestError) {\n        return true;\n    }\n    return (0,_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.isError)(e) && e.name === \"RestError\";\n}\n//# sourceMappingURL=restError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/restError.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/exponentialRetryStrategy.js":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/exponentialRetryStrategy.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exponentialRetryStrategy: () => (/* binding */ exponentialRetryStrategy),\n/* harmony export */   isExponentialRetryResponse: () => (/* binding */ isExponentialRetryResponse),\n/* harmony export */   isSystemError: () => (/* binding */ isSystemError)\n/* harmony export */ });\n/* harmony import */ var _azure_core_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/core-util */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/index.js\");\n/* harmony import */ var _throttlingRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./throttlingRetryStrategy */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/throttlingRetryStrategy.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n// intervals are in milliseconds\nconst DEFAULT_CLIENT_RETRY_INTERVAL = 1000;\nconst DEFAULT_CLIENT_MAX_RETRY_INTERVAL = 1000 * 64;\n/**\n * A retry strategy that retries with an exponentially increasing delay in these two cases:\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails (408, greater or equal than 500, except for 501 and 505).\n */\nfunction exponentialRetryStrategy(options = {}) {\n    var _a, _b;\n    const retryInterval = (_a = options.retryDelayInMs) !== null && _a !== void 0 ? _a : DEFAULT_CLIENT_RETRY_INTERVAL;\n    const maxRetryInterval = (_b = options.maxRetryDelayInMs) !== null && _b !== void 0 ? _b : DEFAULT_CLIENT_MAX_RETRY_INTERVAL;\n    let retryAfterInMs = retryInterval;\n    return {\n        name: \"exponentialRetryStrategy\",\n        retry({ retryCount, response, responseError }) {\n            const matchedSystemError = isSystemError(responseError);\n            const ignoreSystemErrors = matchedSystemError && options.ignoreSystemErrors;\n            const isExponential = isExponentialRetryResponse(response);\n            const ignoreExponentialResponse = isExponential && options.ignoreHttpStatusCodes;\n            const unknownResponse = response && ((0,_throttlingRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.isThrottlingRetryResponse)(response) || !isExponential);\n            if (unknownResponse || ignoreExponentialResponse || ignoreSystemErrors) {\n                return { skipStrategy: true };\n            }\n            if (responseError && !matchedSystemError && !isExponential) {\n                return { errorToThrow: responseError };\n            }\n            // Exponentially increase the delay each time\n            const exponentialDelay = retryAfterInMs * Math.pow(2, retryCount);\n            // Don't let the delay exceed the maximum\n            const clampedExponentialDelay = Math.min(maxRetryInterval, exponentialDelay);\n            // Allow the final value to have some \"jitter\" (within 50% of the delay size) so\n            // that retries across multiple clients don't occur simultaneously.\n            retryAfterInMs =\n                clampedExponentialDelay / 2 + (0,_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.getRandomIntegerInclusive)(0, clampedExponentialDelay / 2);\n            return { retryAfterInMs };\n        },\n    };\n}\n/**\n * A response is a retry response if it has status codes:\n * - 408, or\n * - Greater or equal than 500, except for 501 and 505.\n */\nfunction isExponentialRetryResponse(response) {\n    return Boolean(response &&\n        response.status !== undefined &&\n        (response.status >= 500 || response.status === 408) &&\n        response.status !== 501 &&\n        response.status !== 505);\n}\n/**\n * Determines whether an error from a pipeline response was triggered in the network layer.\n */\nfunction isSystemError(err) {\n    if (!err) {\n        return false;\n    }\n    return (err.code === \"ETIMEDOUT\" ||\n        err.code === \"ESOCKETTIMEDOUT\" ||\n        err.code === \"ECONNREFUSED\" ||\n        err.code === \"ECONNRESET\" ||\n        err.code === \"ENOENT\");\n}\n//# sourceMappingURL=exponentialRetryStrategy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/exponentialRetryStrategy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/throttlingRetryStrategy.js":
/*!************************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/throttlingRetryStrategy.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isThrottlingRetryResponse: () => (/* binding */ isThrottlingRetryResponse),\n/* harmony export */   throttlingRetryStrategy: () => (/* binding */ throttlingRetryStrategy)\n/* harmony export */ });\n/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/helpers */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/helpers.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The header that comes back from Azure services representing\n * the amount of time (minimum) to wait to retry (in seconds or timestamp after which we can retry).\n */\nconst RetryAfterHeader = \"Retry-After\";\n/**\n * The headers that come back from Azure services representing\n * the amount of time (minimum) to wait to retry.\n *\n * \"retry-after-ms\", \"x-ms-retry-after-ms\" : milliseconds\n * \"Retry-After\" : seconds or timestamp\n */\nconst AllRetryAfterHeaders = [\"retry-after-ms\", \"x-ms-retry-after-ms\", RetryAfterHeader];\n/**\n * A response is a throttling retry response if it has a throttling status code (429 or 503),\n * as long as one of the [ \"Retry-After\" or \"retry-after-ms\" or \"x-ms-retry-after-ms\" ] headers has a valid value.\n *\n * Returns the `retryAfterInMs` value if the response is a throttling retry response.\n * If not throttling retry response, returns `undefined`.\n *\n * @internal\n */\nfunction getRetryAfterInMs(response) {\n    if (!(response && [429, 503].includes(response.status)))\n        return undefined;\n    try {\n        // Headers: \"retry-after-ms\", \"x-ms-retry-after-ms\", \"Retry-After\"\n        for (const header of AllRetryAfterHeaders) {\n            const retryAfterValue = (0,_util_helpers__WEBPACK_IMPORTED_MODULE_0__.parseHeaderValueAsNumber)(response, header);\n            if (retryAfterValue === 0 || retryAfterValue) {\n                // \"Retry-After\" header ==> seconds\n                // \"retry-after-ms\", \"x-ms-retry-after-ms\" headers ==> milli-seconds\n                const multiplyingFactor = header === RetryAfterHeader ? 1000 : 1;\n                return retryAfterValue * multiplyingFactor; // in milli-seconds\n            }\n        }\n        // RetryAfterHeader (\"Retry-After\") has a special case where it might be formatted as a date instead of a number of seconds\n        const retryAfterHeader = response.headers.get(RetryAfterHeader);\n        if (!retryAfterHeader)\n            return;\n        const date = Date.parse(retryAfterHeader);\n        const diff = date - Date.now();\n        // negative diff would mean a date in the past, so retry asap with 0 milliseconds\n        return Number.isFinite(diff) ? Math.max(0, diff) : undefined;\n    }\n    catch (e) {\n        return undefined;\n    }\n}\n/**\n * A response is a retry response if it has a throttling status code (429 or 503),\n * as long as one of the [ \"Retry-After\" or \"retry-after-ms\" or \"x-ms-retry-after-ms\" ] headers has a valid value.\n */\nfunction isThrottlingRetryResponse(response) {\n    return Number.isFinite(getRetryAfterInMs(response));\n}\nfunction throttlingRetryStrategy() {\n    return {\n        name: \"throttlingRetryStrategy\",\n        retry({ response }) {\n            const retryAfterInMs = getRetryAfterInMs(response);\n            if (!Number.isFinite(retryAfterInMs)) {\n                return { skipStrategy: true };\n            }\n            return {\n                retryAfterInMs,\n            };\n        },\n    };\n}\n//# sourceMappingURL=throttlingRetryStrategy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/retryStrategies/throttlingRetryStrategy.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/helpers.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/helpers.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   parseHeaderValueAsNumber: () => (/* binding */ parseHeaderValueAsNumber)\n/* harmony export */ });\n/* harmony import */ var _azure_abort_controller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/abort-controller */ \"(instrument)/../../node_modules/@azure/abort-controller/dist-esm/src/AbortController.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nconst StandardAbortMessage = \"The operation was aborted.\";\n/**\n * A wrapper for setTimeout that resolves a promise after delayInMs milliseconds.\n * @param delayInMs - The number of milliseconds to be delayed.\n * @param value - The value to be resolved with after a timeout of t milliseconds.\n * @param options - The options for delay - currently abort options\n *                  - abortSignal - The abortSignal associated with containing operation.\n *                  - abortErrorMsg - The abort error message associated with containing operation.\n * @returns Resolved promise\n */\nfunction delay(delayInMs, value, options) {\n    return new Promise((resolve, reject) => {\n        let timer = undefined;\n        let onAborted = undefined;\n        const rejectOnAbort = () => {\n            return reject(new _azure_abort_controller__WEBPACK_IMPORTED_MODULE_0__.AbortError((options === null || options === void 0 ? void 0 : options.abortErrorMsg) ? options === null || options === void 0 ? void 0 : options.abortErrorMsg : StandardAbortMessage));\n        };\n        const removeListeners = () => {\n            if ((options === null || options === void 0 ? void 0 : options.abortSignal) && onAborted) {\n                options.abortSignal.removeEventListener(\"abort\", onAborted);\n            }\n        };\n        onAborted = () => {\n            if (timer) {\n                clearTimeout(timer);\n            }\n            removeListeners();\n            return rejectOnAbort();\n        };\n        if ((options === null || options === void 0 ? void 0 : options.abortSignal) && options.abortSignal.aborted) {\n            return rejectOnAbort();\n        }\n        timer = setTimeout(() => {\n            removeListeners();\n            resolve(value);\n        }, delayInMs);\n        if (options === null || options === void 0 ? void 0 : options.abortSignal) {\n            options.abortSignal.addEventListener(\"abort\", onAborted);\n        }\n    });\n}\n/**\n * @internal\n * @returns the parsed value or undefined if the parsed value is invalid.\n */\nfunction parseHeaderValueAsNumber(response, headerName) {\n    const value = response.headers.get(headerName);\n    if (!value)\n        return;\n    const valueAsNum = Number(value);\n    if (Number.isNaN(valueAsNum))\n        return;\n    return valueAsNum;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/helpers.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/inspect.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/inspect.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   custom: () => (/* binding */ custom)\n/* harmony export */ });\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_0__);\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nconst custom = util__WEBPACK_IMPORTED_MODULE_0__.inspect.custom;\n//# sourceMappingURL=inspect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy91dGlsL2luc3BlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUMrQjtBQUN4QixlQUFlLHlDQUFPO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy91dGlsL2luc3BlY3QuanM/N2E5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmltcG9ydCB7IGluc3BlY3QgfSBmcm9tIFwidXRpbFwiO1xuZXhwb3J0IGNvbnN0IGN1c3RvbSA9IGluc3BlY3QuY3VzdG9tO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5zcGVjdC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/inspect.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/sanitizer.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/sanitizer.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sanitizer: () => (/* binding */ Sanitizer)\n/* harmony export */ });\n/* harmony import */ var _azure_core_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/core-util */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/index.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nconst RedactedString = \"REDACTED\";\n// Make sure this list is up-to-date with the one under core/logger/Readme#Keyconcepts\nconst defaultAllowedHeaderNames = [\n    \"x-ms-client-request-id\",\n    \"x-ms-return-client-request-id\",\n    \"x-ms-useragent\",\n    \"x-ms-correlation-request-id\",\n    \"x-ms-request-id\",\n    \"client-request-id\",\n    \"ms-cv\",\n    \"return-client-request-id\",\n    \"traceparent\",\n    \"Access-Control-Allow-Credentials\",\n    \"Access-Control-Allow-Headers\",\n    \"Access-Control-Allow-Methods\",\n    \"Access-Control-Allow-Origin\",\n    \"Access-Control-Expose-Headers\",\n    \"Access-Control-Max-Age\",\n    \"Access-Control-Request-Headers\",\n    \"Access-Control-Request-Method\",\n    \"Origin\",\n    \"Accept\",\n    \"Accept-Encoding\",\n    \"Cache-Control\",\n    \"Connection\",\n    \"Content-Length\",\n    \"Content-Type\",\n    \"Date\",\n    \"ETag\",\n    \"Expires\",\n    \"If-Match\",\n    \"If-Modified-Since\",\n    \"If-None-Match\",\n    \"If-Unmodified-Since\",\n    \"Last-Modified\",\n    \"Pragma\",\n    \"Request-Id\",\n    \"Retry-After\",\n    \"Server\",\n    \"Transfer-Encoding\",\n    \"User-Agent\",\n    \"WWW-Authenticate\",\n];\nconst defaultAllowedQueryParameters = [\"api-version\"];\n/**\n * @internal\n */\nclass Sanitizer {\n    constructor({ additionalAllowedHeaderNames: allowedHeaderNames = [], additionalAllowedQueryParameters: allowedQueryParameters = [], } = {}) {\n        allowedHeaderNames = defaultAllowedHeaderNames.concat(allowedHeaderNames);\n        allowedQueryParameters = defaultAllowedQueryParameters.concat(allowedQueryParameters);\n        this.allowedHeaderNames = new Set(allowedHeaderNames.map((n) => n.toLowerCase()));\n        this.allowedQueryParameters = new Set(allowedQueryParameters.map((p) => p.toLowerCase()));\n    }\n    sanitize(obj) {\n        const seen = new Set();\n        return JSON.stringify(obj, (key, value) => {\n            // Ensure Errors include their interesting non-enumerable members\n            if (value instanceof Error) {\n                return Object.assign(Object.assign({}, value), { name: value.name, message: value.message });\n            }\n            if (key === \"headers\") {\n                return this.sanitizeHeaders(value);\n            }\n            else if (key === \"url\") {\n                return this.sanitizeUrl(value);\n            }\n            else if (key === \"query\") {\n                return this.sanitizeQuery(value);\n            }\n            else if (key === \"body\") {\n                // Don't log the request body\n                return undefined;\n            }\n            else if (key === \"response\") {\n                // Don't log response again\n                return undefined;\n            }\n            else if (key === \"operationSpec\") {\n                // When using sendOperationRequest, the request carries a massive\n                // field with the autorest spec. No need to log it.\n                return undefined;\n            }\n            else if (Array.isArray(value) || (0,_azure_core_util__WEBPACK_IMPORTED_MODULE_0__.isObject)(value)) {\n                if (seen.has(value)) {\n                    return \"[Circular]\";\n                }\n                seen.add(value);\n            }\n            return value;\n        }, 2);\n    }\n    sanitizeHeaders(obj) {\n        const sanitized = {};\n        for (const key of Object.keys(obj)) {\n            if (this.allowedHeaderNames.has(key.toLowerCase())) {\n                sanitized[key] = obj[key];\n            }\n            else {\n                sanitized[key] = RedactedString;\n            }\n        }\n        return sanitized;\n    }\n    sanitizeQuery(value) {\n        if (typeof value !== \"object\" || value === null) {\n            return value;\n        }\n        const sanitized = {};\n        for (const k of Object.keys(value)) {\n            if (this.allowedQueryParameters.has(k.toLowerCase())) {\n                sanitized[k] = value[k];\n            }\n            else {\n                sanitized[k] = RedactedString;\n            }\n        }\n        return sanitized;\n    }\n    sanitizeUrl(value) {\n        if (typeof value !== \"string\" || value === null) {\n            return value;\n        }\n        const url = new URL(value);\n        if (!url.search) {\n            return value;\n        }\n        for (const [key] of url.searchParams) {\n            if (!this.allowedQueryParameters.has(key.toLowerCase())) {\n                url.searchParams.set(key, RedactedString);\n            }\n        }\n        return url.toString();\n    }\n}\n//# sourceMappingURL=sanitizer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/sanitizer.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/tokenCycler.js":
/*!*************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/tokenCycler.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CYCLER_OPTIONS: () => (/* binding */ DEFAULT_CYCLER_OPTIONS),\n/* harmony export */   createTokenCycler: () => (/* binding */ createTokenCycler)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/helpers.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n// Default options for the cycler if none are provided\nconst DEFAULT_CYCLER_OPTIONS = {\n    forcedRefreshWindowInMs: 1000,\n    retryIntervalInMs: 3000,\n    refreshWindowInMs: 1000 * 60 * 2, // Start refreshing 2m before expiry\n};\n/**\n * Converts an an unreliable access token getter (which may resolve with null)\n * into an AccessTokenGetter by retrying the unreliable getter in a regular\n * interval.\n *\n * @param getAccessToken - A function that produces a promise of an access token that may fail by returning null.\n * @param retryIntervalInMs - The time (in milliseconds) to wait between retry attempts.\n * @param refreshTimeout - The timestamp after which the refresh attempt will fail, throwing an exception.\n * @returns - A promise that, if it resolves, will resolve with an access token.\n */\nasync function beginRefresh(getAccessToken, retryIntervalInMs, refreshTimeout) {\n    // This wrapper handles exceptions gracefully as long as we haven't exceeded\n    // the timeout.\n    async function tryGetAccessToken() {\n        if (Date.now() < refreshTimeout) {\n            try {\n                return await getAccessToken();\n            }\n            catch (_a) {\n                return null;\n            }\n        }\n        else {\n            const finalToken = await getAccessToken();\n            // Timeout is up, so throw if it's still null\n            if (finalToken === null) {\n                throw new Error(\"Failed to refresh access token.\");\n            }\n            return finalToken;\n        }\n    }\n    let token = await tryGetAccessToken();\n    while (token === null) {\n        await (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.delay)(retryIntervalInMs);\n        token = await tryGetAccessToken();\n    }\n    return token;\n}\n/**\n * Creates a token cycler from a credential, scopes, and optional settings.\n *\n * A token cycler represents a way to reliably retrieve a valid access token\n * from a TokenCredential. It will handle initializing the token, refreshing it\n * when it nears expiration, and synchronizes refresh attempts to avoid\n * concurrency hazards.\n *\n * @param credential - the underlying TokenCredential that provides the access\n * token\n * @param tokenCyclerOptions - optionally override default settings for the cycler\n *\n * @returns - a function that reliably produces a valid access token\n */\nfunction createTokenCycler(credential, tokenCyclerOptions) {\n    let refreshWorker = null;\n    let token = null;\n    let tenantId;\n    const options = Object.assign(Object.assign({}, DEFAULT_CYCLER_OPTIONS), tokenCyclerOptions);\n    /**\n     * This little holder defines several predicates that we use to construct\n     * the rules of refreshing the token.\n     */\n    const cycler = {\n        /**\n         * Produces true if a refresh job is currently in progress.\n         */\n        get isRefreshing() {\n            return refreshWorker !== null;\n        },\n        /**\n         * Produces true if the cycler SHOULD refresh (we are within the refresh\n         * window and not already refreshing)\n         */\n        get shouldRefresh() {\n            var _a;\n            return (!cycler.isRefreshing &&\n                ((_a = token === null || token === void 0 ? void 0 : token.expiresOnTimestamp) !== null && _a !== void 0 ? _a : 0) - options.refreshWindowInMs < Date.now());\n        },\n        /**\n         * Produces true if the cycler MUST refresh (null or nearly-expired\n         * token).\n         */\n        get mustRefresh() {\n            return (token === null || token.expiresOnTimestamp - options.forcedRefreshWindowInMs < Date.now());\n        },\n    };\n    /**\n     * Starts a refresh job or returns the existing job if one is already\n     * running.\n     */\n    function refresh(scopes, getTokenOptions) {\n        var _a;\n        if (!cycler.isRefreshing) {\n            // We bind `scopes` here to avoid passing it around a lot\n            const tryGetAccessToken = () => credential.getToken(scopes, getTokenOptions);\n            // Take advantage of promise chaining to insert an assignment to `token`\n            // before the refresh can be considered done.\n            refreshWorker = beginRefresh(tryGetAccessToken, options.retryIntervalInMs, \n            // If we don't have a token, then we should timeout immediately\n            (_a = token === null || token === void 0 ? void 0 : token.expiresOnTimestamp) !== null && _a !== void 0 ? _a : Date.now())\n                .then((_token) => {\n                refreshWorker = null;\n                token = _token;\n                tenantId = getTokenOptions.tenantId;\n                return token;\n            })\n                .catch((reason) => {\n                // We also should reset the refresher if we enter a failed state.  All\n                // existing awaiters will throw, but subsequent requests will start a\n                // new retry chain.\n                refreshWorker = null;\n                token = null;\n                tenantId = undefined;\n                throw reason;\n            });\n        }\n        return refreshWorker;\n    }\n    return async (scopes, tokenOptions) => {\n        //\n        // Simple rules:\n        // - If we MUST refresh, then return the refresh task, blocking\n        //   the pipeline until a token is available.\n        // - If we SHOULD refresh, then run refresh but don't return it\n        //   (we can still use the cached token).\n        // - Return the token, since it's fine if we didn't return in\n        //   step 1.\n        //\n        // If the tenantId passed in token options is different to the one we have\n        // Or if we are in claim challenge and the token was rejected and a new access token need to be issued, we need to\n        // refresh the token with the new tenantId or token.\n        const mustRefresh = tenantId !== tokenOptions.tenantId || Boolean(tokenOptions.claims) || cycler.mustRefresh;\n        if (mustRefresh)\n            return refresh(scopes, tokenOptions);\n        if (cycler.shouldRefresh) {\n            refresh(scopes, tokenOptions);\n        }\n        return token;\n    };\n}\n//# sourceMappingURL=tokenCycler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/tokenCycler.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgent.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgent.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserAgentHeaderName: () => (/* binding */ getUserAgentHeaderName),\n/* harmony export */   getUserAgentValue: () => (/* binding */ getUserAgentValue)\n/* harmony export */ });\n/* harmony import */ var _userAgentPlatform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./userAgentPlatform */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgentPlatform.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants */ \"(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/constants.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\nfunction getUserAgentString(telemetryInfo) {\n    const parts = [];\n    for (const [key, value] of telemetryInfo) {\n        const token = value ? `${key}/${value}` : key;\n        parts.push(token);\n    }\n    return parts.join(\" \");\n}\n/**\n * @internal\n */\nfunction getUserAgentHeaderName() {\n    return (0,_userAgentPlatform__WEBPACK_IMPORTED_MODULE_0__.getHeaderName)();\n}\n/**\n * @internal\n */\nfunction getUserAgentValue(prefix) {\n    const runtimeInfo = new Map();\n    runtimeInfo.set(\"core-rest-pipeline\", _constants__WEBPACK_IMPORTED_MODULE_1__.SDK_VERSION);\n    (0,_userAgentPlatform__WEBPACK_IMPORTED_MODULE_0__.setPlatformSpecificData)(runtimeInfo);\n    const defaultAgent = getUserAgentString(runtimeInfo);\n    const userAgentValue = prefix ? `${prefix} ${defaultAgent}` : defaultAgent;\n    return userAgentValue;\n}\n//# sourceMappingURL=userAgent.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy91dGlsL3VzZXJBZ2VudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUM2RTtBQUNsQztBQUMzQztBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsSUFBSSxHQUFHLE1BQU07QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFdBQVcsaUVBQWE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsMENBQTBDLG1EQUFXO0FBQ3JELElBQUksMkVBQXVCO0FBQzNCO0FBQ0EsdUNBQXVDLFFBQVEsRUFBRSxhQUFhO0FBQzlEO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uLi8uLi9ub2RlX21vZHVsZXMvQGF6dXJlL2NvcmUtcmVzdC1waXBlbGluZS9kaXN0LWVzbS9zcmMvdXRpbC91c2VyQWdlbnQuanM/Y2NjMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmltcG9ydCB7IGdldEhlYWRlck5hbWUsIHNldFBsYXRmb3JtU3BlY2lmaWNEYXRhIH0gZnJvbSBcIi4vdXNlckFnZW50UGxhdGZvcm1cIjtcbmltcG9ydCB7IFNES19WRVJTSU9OIH0gZnJvbSBcIi4uL2NvbnN0YW50c1wiO1xuZnVuY3Rpb24gZ2V0VXNlckFnZW50U3RyaW5nKHRlbGVtZXRyeUluZm8pIHtcbiAgICBjb25zdCBwYXJ0cyA9IFtdO1xuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIHRlbGVtZXRyeUluZm8pIHtcbiAgICAgICAgY29uc3QgdG9rZW4gPSB2YWx1ZSA/IGAke2tleX0vJHt2YWx1ZX1gIDoga2V5O1xuICAgICAgICBwYXJ0cy5wdXNoKHRva2VuKTtcbiAgICB9XG4gICAgcmV0dXJuIHBhcnRzLmpvaW4oXCIgXCIpO1xufVxuLyoqXG4gKiBAaW50ZXJuYWxcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFVzZXJBZ2VudEhlYWRlck5hbWUoKSB7XG4gICAgcmV0dXJuIGdldEhlYWRlck5hbWUoKTtcbn1cbi8qKlxuICogQGludGVybmFsXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRVc2VyQWdlbnRWYWx1ZShwcmVmaXgpIHtcbiAgICBjb25zdCBydW50aW1lSW5mbyA9IG5ldyBNYXAoKTtcbiAgICBydW50aW1lSW5mby5zZXQoXCJjb3JlLXJlc3QtcGlwZWxpbmVcIiwgU0RLX1ZFUlNJT04pO1xuICAgIHNldFBsYXRmb3JtU3BlY2lmaWNEYXRhKHJ1bnRpbWVJbmZvKTtcbiAgICBjb25zdCBkZWZhdWx0QWdlbnQgPSBnZXRVc2VyQWdlbnRTdHJpbmcocnVudGltZUluZm8pO1xuICAgIGNvbnN0IHVzZXJBZ2VudFZhbHVlID0gcHJlZml4ID8gYCR7cHJlZml4fSAke2RlZmF1bHRBZ2VudH1gIDogZGVmYXVsdEFnZW50O1xuICAgIHJldHVybiB1c2VyQWdlbnRWYWx1ZTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZXJBZ2VudC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgent.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgentPlatform.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgentPlatform.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHeaderName: () => (/* binding */ getHeaderName),\n/* harmony export */   setPlatformSpecificData: () => (/* binding */ setPlatformSpecificData)\n/* harmony export */ });\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(os__WEBPACK_IMPORTED_MODULE_0__);\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * @internal\n */\nfunction getHeaderName() {\n    return \"User-Agent\";\n}\n/**\n * @internal\n */\nfunction setPlatformSpecificData(map) {\n    map.set(\"Node\", process.version);\n    map.set(\"OS\", `(${os__WEBPACK_IMPORTED_MODULE_0__.arch()}-${os__WEBPACK_IMPORTED_MODULE_0__.type()}-${os__WEBPACK_IMPORTED_MODULE_0__.release()})`);\n}\n//# sourceMappingURL=userAgentPlatform.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy91dGlsL3VzZXJBZ2VudFBsYXRmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ3lCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxzQkFBc0Isb0NBQU8sR0FBRyxHQUFHLG9DQUFPLEdBQUcsR0FBRyx1Q0FBVSxHQUFHO0FBQzdEO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL0BhenVyZS9jb3JlLXJlc3QtcGlwZWxpbmUvZGlzdC1lc20vc3JjL3V0aWwvdXNlckFnZW50UGxhdGZvcm0uanM/NWUwOSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmltcG9ydCAqIGFzIG9zIGZyb20gXCJvc1wiO1xuLyoqXG4gKiBAaW50ZXJuYWxcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEhlYWRlck5hbWUoKSB7XG4gICAgcmV0dXJuIFwiVXNlci1BZ2VudFwiO1xufVxuLyoqXG4gKiBAaW50ZXJuYWxcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNldFBsYXRmb3JtU3BlY2lmaWNEYXRhKG1hcCkge1xuICAgIG1hcC5zZXQoXCJOb2RlXCIsIHByb2Nlc3MudmVyc2lvbik7XG4gICAgbWFwLnNldChcIk9TXCIsIGAoJHtvcy5hcmNoKCl9LSR7b3MudHlwZSgpfS0ke29zLnJlbGVhc2UoKX0pYCk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD11c2VyQWdlbnRQbGF0Zm9ybS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/userAgentPlatform.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/uuid.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/uuid.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateUuid: () => (/* binding */ generateUuid)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uuid */ \"(instrument)/../../node_modules/uuid/dist/esm-node/v4.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n * @internal\n */\nfunction generateUuid() {\n    return (0,uuid__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n}\n//# sourceMappingURL=uuid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy91dGlsL3V1aWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ29DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsV0FBVyxnREFBTTtBQUNqQjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS1yZXN0LXBpcGVsaW5lL2Rpc3QtZXNtL3NyYy91dGlsL3V1aWQuanM/NzYxNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmltcG9ydCB7IHY0IGFzIHV1aWR2NCB9IGZyb20gXCJ1dWlkXCI7XG4vKipcbiAqIEdlbmVyYXRlZCBVbml2ZXJzYWxseSBVbmlxdWUgSWRlbnRpZmllclxuICpcbiAqIEByZXR1cm5zIFJGQzQxMjIgdjQgVVVJRC5cbiAqIEBpbnRlcm5hbFxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVVdWlkKCkge1xuICAgIHJldHVybiB1dWlkdjQoKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV1aWQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-rest-pipeline/dist-esm/src/util/uuid.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/instrumenter.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/@azure/core-tracing/dist-esm/src/instrumenter.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDefaultInstrumenter: () => (/* binding */ createDefaultInstrumenter),\n/* harmony export */   createDefaultTracingSpan: () => (/* binding */ createDefaultTracingSpan),\n/* harmony export */   getInstrumenter: () => (/* binding */ getInstrumenter),\n/* harmony export */   useInstrumenter: () => (/* binding */ useInstrumenter)\n/* harmony export */ });\n/* harmony import */ var _tracingContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tracingContext */ \"(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/tracingContext.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nfunction createDefaultTracingSpan() {\n    return {\n        end: () => {\n            // noop\n        },\n        isRecording: () => false,\n        recordException: () => {\n            // noop\n        },\n        setAttribute: () => {\n            // noop\n        },\n        setStatus: () => {\n            // noop\n        },\n    };\n}\nfunction createDefaultInstrumenter() {\n    return {\n        createRequestHeaders: () => {\n            return {};\n        },\n        parseTraceparentHeader: () => {\n            return undefined;\n        },\n        startSpan: (_name, spanOptions) => {\n            return {\n                span: createDefaultTracingSpan(),\n                tracingContext: (0,_tracingContext__WEBPACK_IMPORTED_MODULE_0__.createTracingContext)({ parentContext: spanOptions.tracingContext }),\n            };\n        },\n        withContext(_context, callback, ...callbackArgs) {\n            return callback(...callbackArgs);\n        },\n    };\n}\n/** @internal */\nlet instrumenterImplementation;\n/**\n * Extends the Azure SDK with support for a given instrumenter implementation.\n *\n * @param instrumenter - The instrumenter implementation to use.\n */\nfunction useInstrumenter(instrumenter) {\n    instrumenterImplementation = instrumenter;\n}\n/**\n * Gets the currently set instrumenter, a No-Op instrumenter by default.\n *\n * @returns The currently set instrumenter\n */\nfunction getInstrumenter() {\n    if (!instrumenterImplementation) {\n        instrumenterImplementation = createDefaultInstrumenter();\n    }\n    return instrumenterImplementation;\n}\n//# sourceMappingURL=instrumenter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/instrumenter.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/tracingClient.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/@azure/core-tracing/dist-esm/src/tracingClient.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTracingClient: () => (/* binding */ createTracingClient)\n/* harmony export */ });\n/* harmony import */ var _instrumenter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./instrumenter */ \"(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/instrumenter.js\");\n/* harmony import */ var _tracingContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tracingContext */ \"(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/tracingContext.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n/**\n * Creates a new tracing client.\n *\n * @param options - Options used to configure the tracing client.\n * @returns - An instance of {@link TracingClient}.\n */\nfunction createTracingClient(options) {\n    const { namespace, packageName, packageVersion } = options;\n    function startSpan(name, operationOptions, spanOptions) {\n        var _a;\n        const startSpanResult = (0,_instrumenter__WEBPACK_IMPORTED_MODULE_0__.getInstrumenter)().startSpan(name, Object.assign(Object.assign({}, spanOptions), { packageName: packageName, packageVersion: packageVersion, tracingContext: (_a = operationOptions === null || operationOptions === void 0 ? void 0 : operationOptions.tracingOptions) === null || _a === void 0 ? void 0 : _a.tracingContext }));\n        let tracingContext = startSpanResult.tracingContext;\n        const span = startSpanResult.span;\n        if (!tracingContext.getValue(_tracingContext__WEBPACK_IMPORTED_MODULE_1__.knownContextKeys.namespace)) {\n            tracingContext = tracingContext.setValue(_tracingContext__WEBPACK_IMPORTED_MODULE_1__.knownContextKeys.namespace, namespace);\n        }\n        span.setAttribute(\"az.namespace\", tracingContext.getValue(_tracingContext__WEBPACK_IMPORTED_MODULE_1__.knownContextKeys.namespace));\n        const updatedOptions = Object.assign({}, operationOptions, {\n            tracingOptions: Object.assign(Object.assign({}, operationOptions === null || operationOptions === void 0 ? void 0 : operationOptions.tracingOptions), { tracingContext }),\n        });\n        return {\n            span,\n            updatedOptions,\n        };\n    }\n    async function withSpan(name, operationOptions, callback, spanOptions) {\n        const { span, updatedOptions } = startSpan(name, operationOptions, spanOptions);\n        try {\n            const result = await withContext(updatedOptions.tracingOptions.tracingContext, () => Promise.resolve(callback(updatedOptions, span)));\n            span.setStatus({ status: \"success\" });\n            return result;\n        }\n        catch (err) {\n            span.setStatus({ status: \"error\", error: err });\n            throw err;\n        }\n        finally {\n            span.end();\n        }\n    }\n    function withContext(context, callback, ...callbackArgs) {\n        return (0,_instrumenter__WEBPACK_IMPORTED_MODULE_0__.getInstrumenter)().withContext(context, callback, ...callbackArgs);\n    }\n    /**\n     * Parses a traceparent header value into a span identifier.\n     *\n     * @param traceparentHeader - The traceparent header to parse.\n     * @returns An implementation-specific identifier for the span.\n     */\n    function parseTraceparentHeader(traceparentHeader) {\n        return (0,_instrumenter__WEBPACK_IMPORTED_MODULE_0__.getInstrumenter)().parseTraceparentHeader(traceparentHeader);\n    }\n    /**\n     * Creates a set of request headers to propagate tracing information to a backend.\n     *\n     * @param tracingContext - The context containing the span to serialize.\n     * @returns The set of headers to add to a request.\n     */\n    function createRequestHeaders(tracingContext) {\n        return (0,_instrumenter__WEBPACK_IMPORTED_MODULE_0__.getInstrumenter)().createRequestHeaders(tracingContext);\n    }\n    return {\n        startSpan,\n        withSpan,\n        withContext,\n        parseTraceparentHeader,\n        createRequestHeaders,\n    };\n}\n//# sourceMappingURL=tracingClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/tracingClient.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/tracingContext.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@azure/core-tracing/dist-esm/src/tracingContext.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TracingContextImpl: () => (/* binding */ TracingContextImpl),\n/* harmony export */   createTracingContext: () => (/* binding */ createTracingContext),\n/* harmony export */   knownContextKeys: () => (/* binding */ knownContextKeys)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/** @internal */\nconst knownContextKeys = {\n    span: Symbol.for(\"@azure/core-tracing span\"),\n    namespace: Symbol.for(\"@azure/core-tracing namespace\"),\n};\n/**\n * Creates a new {@link TracingContext} with the given options.\n * @param options - A set of known keys that may be set on the context.\n * @returns A new {@link TracingContext} with the given options.\n *\n * @internal\n */\nfunction createTracingContext(options = {}) {\n    let context = new TracingContextImpl(options.parentContext);\n    if (options.span) {\n        context = context.setValue(knownContextKeys.span, options.span);\n    }\n    if (options.namespace) {\n        context = context.setValue(knownContextKeys.namespace, options.namespace);\n    }\n    return context;\n}\n/** @internal */\nclass TracingContextImpl {\n    constructor(initialContext) {\n        this._contextMap =\n            initialContext instanceof TracingContextImpl\n                ? new Map(initialContext._contextMap)\n                : new Map();\n    }\n    setValue(key, value) {\n        const newContext = new TracingContextImpl(this);\n        newContext._contextMap.set(key, value);\n        return newContext;\n    }\n    getValue(key) {\n        return this._contextMap.get(key);\n    }\n    deleteValue(key) {\n        const newContext = new TracingContextImpl(this);\n        newContext._contextMap.delete(key);\n        return newContext;\n    }\n}\n//# sourceMappingURL=tracingContext.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-tracing/dist-esm/src/tracingContext.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/logger/dist-esm/src/debug.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@azure/logger/dist-esm/src/debug.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _log__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./log */ \"(instrument)/../../node_modules/@azure/logger/dist-esm/src/log.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nconst debugEnvVariable = (typeof process !== \"undefined\" && process.env && process.env.DEBUG) || undefined;\nlet enabledString;\nlet enabledNamespaces = [];\nlet skippedNamespaces = [];\nconst debuggers = [];\nif (debugEnvVariable) {\n    enable(debugEnvVariable);\n}\nconst debugObj = Object.assign((namespace) => {\n    return createDebugger(namespace);\n}, {\n    enable,\n    enabled,\n    disable,\n    log: _log__WEBPACK_IMPORTED_MODULE_0__.log,\n});\nfunction enable(namespaces) {\n    enabledString = namespaces;\n    enabledNamespaces = [];\n    skippedNamespaces = [];\n    const wildcard = /\\*/g;\n    const namespaceList = namespaces.split(\",\").map((ns) => ns.trim().replace(wildcard, \".*?\"));\n    for (const ns of namespaceList) {\n        if (ns.startsWith(\"-\")) {\n            skippedNamespaces.push(new RegExp(`^${ns.substr(1)}$`));\n        }\n        else {\n            enabledNamespaces.push(new RegExp(`^${ns}$`));\n        }\n    }\n    for (const instance of debuggers) {\n        instance.enabled = enabled(instance.namespace);\n    }\n}\nfunction enabled(namespace) {\n    if (namespace.endsWith(\"*\")) {\n        return true;\n    }\n    for (const skipped of skippedNamespaces) {\n        if (skipped.test(namespace)) {\n            return false;\n        }\n    }\n    for (const enabledNamespace of enabledNamespaces) {\n        if (enabledNamespace.test(namespace)) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction disable() {\n    const result = enabledString || \"\";\n    enable(\"\");\n    return result;\n}\nfunction createDebugger(namespace) {\n    const newDebugger = Object.assign(debug, {\n        enabled: enabled(namespace),\n        destroy,\n        log: debugObj.log,\n        namespace,\n        extend,\n    });\n    function debug(...args) {\n        if (!newDebugger.enabled) {\n            return;\n        }\n        if (args.length > 0) {\n            args[0] = `${namespace} ${args[0]}`;\n        }\n        newDebugger.log(...args);\n    }\n    debuggers.push(newDebugger);\n    return newDebugger;\n}\nfunction destroy() {\n    const index = debuggers.indexOf(this);\n    if (index >= 0) {\n        debuggers.splice(index, 1);\n        return true;\n    }\n    return false;\n}\nfunction extend(namespace) {\n    const newDebugger = createDebugger(`${this.namespace}:${namespace}`);\n    newDebugger.log = this.log;\n    return newDebugger;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (debugObj);\n//# sourceMappingURL=debug.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/logger/dist-esm/src/debug.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/logger/dist-esm/src/index.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@azure/logger/dist-esm/src/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AzureLogger: () => (/* binding */ AzureLogger),\n/* harmony export */   createClientLogger: () => (/* binding */ createClientLogger),\n/* harmony export */   getLogLevel: () => (/* binding */ getLogLevel),\n/* harmony export */   setLogLevel: () => (/* binding */ setLogLevel)\n/* harmony export */ });\n/* harmony import */ var _debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./debug */ \"(instrument)/../../node_modules/@azure/logger/dist-esm/src/debug.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nconst registeredLoggers = new Set();\nconst logLevelFromEnv = (typeof process !== \"undefined\" && process.env && process.env.AZURE_LOG_LEVEL) || undefined;\nlet azureLogLevel;\n/**\n * The AzureLogger provides a mechanism for overriding where logs are output to.\n * By default, logs are sent to stderr.\n * Override the `log` method to redirect logs to another location.\n */\nconst AzureLogger = (0,_debug__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"azure\");\nAzureLogger.log = (...args) => {\n    _debug__WEBPACK_IMPORTED_MODULE_0__[\"default\"].log(...args);\n};\nconst AZURE_LOG_LEVELS = [\"verbose\", \"info\", \"warning\", \"error\"];\nif (logLevelFromEnv) {\n    // avoid calling setLogLevel because we don't want a mis-set environment variable to crash\n    if (isAzureLogLevel(logLevelFromEnv)) {\n        setLogLevel(logLevelFromEnv);\n    }\n    else {\n        console.error(`AZURE_LOG_LEVEL set to unknown log level '${logLevelFromEnv}'; logging is not enabled. Acceptable values: ${AZURE_LOG_LEVELS.join(\", \")}.`);\n    }\n}\n/**\n * Immediately enables logging at the specified log level. If no level is specified, logging is disabled.\n * @param level - The log level to enable for logging.\n * Options from most verbose to least verbose are:\n * - verbose\n * - info\n * - warning\n * - error\n */\nfunction setLogLevel(level) {\n    if (level && !isAzureLogLevel(level)) {\n        throw new Error(`Unknown log level '${level}'. Acceptable values: ${AZURE_LOG_LEVELS.join(\",\")}`);\n    }\n    azureLogLevel = level;\n    const enabledNamespaces = [];\n    for (const logger of registeredLoggers) {\n        if (shouldEnable(logger)) {\n            enabledNamespaces.push(logger.namespace);\n        }\n    }\n    _debug__WEBPACK_IMPORTED_MODULE_0__[\"default\"].enable(enabledNamespaces.join(\",\"));\n}\n/**\n * Retrieves the currently specified log level.\n */\nfunction getLogLevel() {\n    return azureLogLevel;\n}\nconst levelMap = {\n    verbose: 400,\n    info: 300,\n    warning: 200,\n    error: 100,\n};\n/**\n * Creates a logger for use by the Azure SDKs that inherits from `AzureLogger`.\n * @param namespace - The name of the SDK package.\n * @hidden\n */\nfunction createClientLogger(namespace) {\n    const clientRootLogger = AzureLogger.extend(namespace);\n    patchLogMethod(AzureLogger, clientRootLogger);\n    return {\n        error: createLogger(clientRootLogger, \"error\"),\n        warning: createLogger(clientRootLogger, \"warning\"),\n        info: createLogger(clientRootLogger, \"info\"),\n        verbose: createLogger(clientRootLogger, \"verbose\"),\n    };\n}\nfunction patchLogMethod(parent, child) {\n    child.log = (...args) => {\n        parent.log(...args);\n    };\n}\nfunction createLogger(parent, level) {\n    const logger = Object.assign(parent.extend(level), {\n        level,\n    });\n    patchLogMethod(parent, logger);\n    if (shouldEnable(logger)) {\n        const enabledNamespaces = _debug__WEBPACK_IMPORTED_MODULE_0__[\"default\"].disable();\n        _debug__WEBPACK_IMPORTED_MODULE_0__[\"default\"].enable(enabledNamespaces + \",\" + logger.namespace);\n    }\n    registeredLoggers.add(logger);\n    return logger;\n}\nfunction shouldEnable(logger) {\n    return Boolean(azureLogLevel && levelMap[logger.level] <= levelMap[azureLogLevel]);\n}\nfunction isAzureLogLevel(logLevel) {\n    return AZURE_LOG_LEVELS.includes(logLevel);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/logger/dist-esm/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/logger/dist-esm/src/log.js":
/*!************************************************************!*\
  !*** ../../node_modules/@azure/logger/dist-esm/src/log.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   log: () => (/* binding */ log)\n/* harmony export */ });\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(os__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_1__);\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\nfunction log(message, ...args) {\n    process.stderr.write(`${util__WEBPACK_IMPORTED_MODULE_1___default().format(message, ...args)}${os__WEBPACK_IMPORTED_MODULE_0__.EOL}`);\n}\n//# sourceMappingURL=log.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvbG9nZ2VyL2Rpc3QtZXNtL3NyYy9sb2cuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ3lCO0FBQ0Q7QUFDakI7QUFDUCw0QkFBNEIsa0RBQVcsbUJBQW1CLEVBQUUsbUNBQUcsQ0FBQztBQUNoRTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvbG9nZ2VyL2Rpc3QtZXNtL3NyYy9sb2cuanM/YjIzZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmltcG9ydCB7IEVPTCB9IGZyb20gXCJvc1wiO1xuaW1wb3J0IHV0aWwgZnJvbSBcInV0aWxcIjtcbmV4cG9ydCBmdW5jdGlvbiBsb2cobWVzc2FnZSwgLi4uYXJncykge1xuICAgIHByb2Nlc3Muc3RkZXJyLndyaXRlKGAke3V0aWwuZm9ybWF0KG1lc3NhZ2UsIC4uLmFyZ3MpfSR7RU9MfWApO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9nLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/logger/dist-esm/src/log.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/configuration.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/configuration.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SDK_VERSION: () => (/* binding */ SDK_VERSION),\n/* harmony export */   envVarToBoolean: () => (/* binding */ envVarToBoolean),\n/* harmony export */   environmentCache: () => (/* binding */ environmentCache)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\nconst SDK_VERSION = \"1.0.0-beta.5\";\n/**\n * @internal\n *\n * Cached values of environment variables that were fetched.\n */\nconst environmentCache = new Map();\n/**\n * Converts an environment variable to Boolean.\n * the strings \"false\" and \"0\" are treated as falsy values.\n *\n * @internal\n */\nfunction envVarToBoolean(key) {\n    var _a;\n    if (!environmentCache.has(key)) {\n        loadEnvironmentVariable(key);\n    }\n    const value = ((_a = environmentCache.get(key)) !== null && _a !== void 0 ? _a : \"\").toLowerCase();\n    return value !== \"false\" && value !== \"0\" && Boolean(value);\n}\nfunction loadEnvironmentVariable(key) {\n    var _a;\n    if (typeof process !== \"undefined\" && process.env) {\n        const rawValue = (_a = process.env[key]) !== null && _a !== void 0 ? _a : process.env[key.toLowerCase()];\n        environmentCache.set(key, rawValue);\n    }\n}\n//# sourceMappingURL=configuration.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/configuration.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/index.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAzureSdkInstrumentation: () => (/* reexport safe */ _instrumentation__WEBPACK_IMPORTED_MODULE_1__.createAzureSdkInstrumentation),\n/* harmony export */   logger: () => (/* reexport safe */ _logger__WEBPACK_IMPORTED_MODULE_0__.logger)\n/* harmony export */ });\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./logger */ \"(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/logger.js\");\n/* harmony import */ var _instrumentation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/instrumentation.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvb3BlbnRlbGVtZXRyeS1pbnN0cnVtZW50YXRpb24tYXp1cmUtc2RrL2Rpc3QtZXNtL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUN5QjtBQUNTO0FBQ2xDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvb3BlbnRlbGVtZXRyeS1pbnN0cnVtZW50YXRpb24tYXp1cmUtc2RrL2Rpc3QtZXNtL3NyYy9pbmRleC5qcz9kZGMyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxuZXhwb3J0ICogZnJvbSBcIi4vbG9nZ2VyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9pbnN0cnVtZW50YXRpb25cIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/instrumentation.js":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/instrumentation.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAzureSdkInstrumentation: () => (/* binding */ createAzureSdkInstrumentation)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_instrumentation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js\");\n/* harmony import */ var _opentelemetry_instrumentation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentationNodeModuleDefinition.js\");\n/* harmony import */ var _instrumenter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./instrumenter */ \"(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/instrumenter.js\");\n/* harmony import */ var _configuration__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./configuration */ \"(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/configuration.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n/**\n * The instrumentation module for the Azure SDK. Implements OpenTelemetry's {@link Instrumentation}.\n */\nclass AzureSdkInstrumentation extends _opentelemetry_instrumentation__WEBPACK_IMPORTED_MODULE_0__.InstrumentationBase {\n    constructor(options = {}) {\n        super(\"@azure/opentelemetry-instrumentation-azure-sdk\", _configuration__WEBPACK_IMPORTED_MODULE_1__.SDK_VERSION, Object.assign({}, options));\n    }\n    /**\n     * Entrypoint for the module registration.\n     *\n     * @returns The patched \\@azure/core-tracing module after setting its instrumenter.\n     */\n    init() {\n        const result = new _opentelemetry_instrumentation__WEBPACK_IMPORTED_MODULE_2__.InstrumentationNodeModuleDefinition(\"@azure/core-tracing\", [\"^1.0.0-preview.14\", \"^1.0.0\"], (moduleExports) => {\n            if (typeof moduleExports.useInstrumenter === \"function\") {\n                moduleExports.useInstrumenter(new _instrumenter__WEBPACK_IMPORTED_MODULE_3__.OpenTelemetryInstrumenter());\n            }\n            return moduleExports;\n        });\n        // Needed to support 1.0.0-preview.14\n        result.includePrerelease = true;\n        return result;\n    }\n}\n/**\n * Enables Azure SDK Instrumentation using OpenTelemetry for Azure SDK client libraries.\n *\n * When registered, any Azure data plane package will begin emitting tracing spans for internal calls\n * as well as network calls\n *\n * Example usage:\n * ```ts\n * const openTelemetryInstrumentation = require(\"@opentelemetry/instrumentation\");\n * openTelemetryInstrumentation.registerInstrumentations({\n *   instrumentations: [createAzureSdkInstrumentation()],\n * })\n * ```\n *\n * @remarks\n *\n * As OpenTelemetry instrumentations rely on patching required modules, you should register\n * this instrumentation as early as possible and before loading any Azure Client Libraries.\n */\nfunction createAzureSdkInstrumentation(options = {}) {\n    return new AzureSdkInstrumentation(options);\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/instrumenter.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/instrumenter.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenTelemetryInstrumenter: () => (/* binding */ OpenTelemetryInstrumenter),\n/* harmony export */   propagator: () => (/* binding */ propagator)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/@opentelemetry/api/build/esm/context-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/@opentelemetry/api/build/esm/trace/invalid-span-constants.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/@opentelemetry/api/build/esm/propagation/TextMapPropagator.js\");\n/* harmony import */ var _opentelemetry_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/@opentelemetry/core/build/esm/trace/W3CTraceContextPropagator.js\");\n/* harmony import */ var _opentelemetry_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/@opentelemetry/core/build/esm/trace/suppress-tracing.js\");\n/* harmony import */ var _spanWrapper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./spanWrapper */ \"(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/spanWrapper.js\");\n/* harmony import */ var _configuration__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./configuration */ \"(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/configuration.js\");\n/* harmony import */ var _transformations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./transformations */ \"(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/transformations.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n\n\n\n\n// While default propagation is user-configurable, Azure services always use the W3C implementation.\nconst propagator = new _opentelemetry_core__WEBPACK_IMPORTED_MODULE_0__.W3CTraceContextPropagator();\nclass OpenTelemetryInstrumenter {\n    startSpan(name, spanOptions) {\n        let ctx = (spanOptions === null || spanOptions === void 0 ? void 0 : spanOptions.tracingContext) || _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.context.active();\n        let span;\n        if ((0,_configuration__WEBPACK_IMPORTED_MODULE_2__.envVarToBoolean)(\"AZURE_TRACING_DISABLED\")) {\n            // disable only our spans but not any downstream spans\n            span = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__.trace.wrapSpanContext(_opentelemetry_api__WEBPACK_IMPORTED_MODULE_4__.INVALID_SPAN_CONTEXT);\n        }\n        else {\n            // Create our span\n            span = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__.trace\n                .getTracer(spanOptions.packageName, spanOptions.packageVersion)\n                .startSpan(name, (0,_transformations__WEBPACK_IMPORTED_MODULE_5__.toSpanOptions)(spanOptions), ctx);\n            if ((0,_configuration__WEBPACK_IMPORTED_MODULE_2__.envVarToBoolean)(\"AZURE_HTTP_TRACING_CHILDREN_DISABLED\") &&\n                name.toUpperCase().startsWith(\"HTTP\")) {\n                // disable downstream spans\n                ctx = (0,_opentelemetry_core__WEBPACK_IMPORTED_MODULE_6__.suppressTracing)(ctx);\n            }\n        }\n        return {\n            span: new _spanWrapper__WEBPACK_IMPORTED_MODULE_7__.OpenTelemetrySpanWrapper(span),\n            tracingContext: _opentelemetry_api__WEBPACK_IMPORTED_MODULE_3__.trace.setSpan(ctx, span),\n        };\n    }\n    withContext(tracingContext, callback, ...callbackArgs) {\n        return _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.context.with(tracingContext, callback, \n        /** Assume caller will bind `this` or use arrow functions */ undefined, ...callbackArgs);\n    }\n    parseTraceparentHeader(traceparentHeader) {\n        return propagator.extract(_opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.context.active(), { traceparent: traceparentHeader }, _opentelemetry_api__WEBPACK_IMPORTED_MODULE_8__.defaultTextMapGetter);\n    }\n    createRequestHeaders(tracingContext) {\n        const headers = {};\n        propagator.inject(tracingContext || _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.context.active(), headers, _opentelemetry_api__WEBPACK_IMPORTED_MODULE_8__.defaultTextMapSetter);\n        return headers;\n    }\n}\n//# sourceMappingURL=instrumenter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/instrumenter.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/logger.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/logger.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var _azure_logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/logger */ \"(instrument)/../../node_modules/@azure/logger/dist-esm/src/index.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * The \\@azure/logger configuration for this package.\n */\nconst logger = (0,_azure_logger__WEBPACK_IMPORTED_MODULE_0__.createClientLogger)(\"opentelemetry-instrumentation-azure-sdk\");\n//# sourceMappingURL=logger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvb3BlbnRlbGVtZXRyeS1pbnN0cnVtZW50YXRpb24tYXp1cmUtc2RrL2Rpc3QtZXNtL3NyYy9sb2dnZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ21EO0FBQ25EO0FBQ0E7QUFDQTtBQUNPLGVBQWUsaUVBQWtCO0FBQ3hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvb3BlbnRlbGVtZXRyeS1pbnN0cnVtZW50YXRpb24tYXp1cmUtc2RrL2Rpc3QtZXNtL3NyYy9sb2dnZXIuanM/MjY4YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbmltcG9ydCB7IGNyZWF0ZUNsaWVudExvZ2dlciB9IGZyb20gXCJAYXp1cmUvbG9nZ2VyXCI7XG4vKipcbiAqIFRoZSBcXEBhenVyZS9sb2dnZXIgY29uZmlndXJhdGlvbiBmb3IgdGhpcyBwYWNrYWdlLlxuICovXG5leHBvcnQgY29uc3QgbG9nZ2VyID0gY3JlYXRlQ2xpZW50TG9nZ2VyKFwib3BlbnRlbGVtZXRyeS1pbnN0cnVtZW50YXRpb24tYXp1cmUtc2RrXCIpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9nZ2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/logger.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/spanWrapper.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/spanWrapper.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenTelemetrySpanWrapper: () => (/* binding */ OpenTelemetrySpanWrapper)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/@opentelemetry/api/build/esm/trace/status.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nclass OpenTelemetrySpanWrapper {\n    constructor(span) {\n        this._span = span;\n    }\n    setStatus(status) {\n        if (status.status === \"error\") {\n            if (status.error) {\n                this._span.setStatus({ code: _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__.SpanStatusCode.ERROR, message: status.error.toString() });\n                this.recordException(status.error);\n            }\n            else {\n                this._span.setStatus({ code: _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__.SpanStatusCode.ERROR });\n            }\n        }\n        else if (status.status === \"success\") {\n            this._span.setStatus({ code: _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__.SpanStatusCode.OK });\n        }\n    }\n    setAttribute(name, value) {\n        if (value !== null && value !== undefined) {\n            this._span.setAttribute(name, value);\n        }\n    }\n    end() {\n        this._span.end();\n    }\n    recordException(exception) {\n        this._span.recordException(exception);\n    }\n    isRecording() {\n        return this._span.isRecording();\n    }\n    /**\n     * Allows getting the wrapped span as needed.\n     * @internal\n     *\n     * @returns The underlying span\n     */\n    unwrap() {\n        return this._span;\n    }\n}\n//# sourceMappingURL=spanWrapper.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/spanWrapper.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/transformations.js":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/transformations.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toOpenTelemetrySpanKind: () => (/* binding */ toOpenTelemetrySpanKind),\n/* harmony export */   toSpanOptions: () => (/* binding */ toSpanOptions)\n/* harmony export */ });\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/@opentelemetry/api/build/esm/trace/span_kind.js\");\n/* harmony import */ var _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/@opentelemetry/api/build/esm/trace-api.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n/**\n * Converts our TracingSpanKind to the corresponding OpenTelemetry SpanKind.\n *\n * By default it will return {@link SpanKind.INTERNAL}\n * @param tracingSpanKind - The core tracing {@link TracingSpanKind}\n * @returns - The OpenTelemetry {@link SpanKind}\n */\nfunction toOpenTelemetrySpanKind(tracingSpanKind) {\n    const key = (tracingSpanKind || \"internal\").toUpperCase();\n    return _opentelemetry_api__WEBPACK_IMPORTED_MODULE_0__.SpanKind[key];\n}\n/**\n * Converts core-tracing's TracingSpanLink to OpenTelemetry's Link\n *\n * @param spanLinks - The core tracing {@link TracingSpanLink} to convert\n * @returns A set of {@link Link}s\n */\nfunction toOpenTelemetryLinks(spanLinks = []) {\n    return spanLinks.reduce((acc, tracingSpanLink) => {\n        const spanContext = _opentelemetry_api__WEBPACK_IMPORTED_MODULE_1__.trace.getSpanContext(tracingSpanLink.tracingContext);\n        if (spanContext) {\n            acc.push({\n                context: spanContext,\n                attributes: toOpenTelemetrySpanAttributes(tracingSpanLink.attributes),\n            });\n        }\n        return acc;\n    }, []);\n}\n/**\n * Converts core-tracing's span attributes to OpenTelemetry attributes.\n *\n * @param spanAttributes - The set of attributes to convert.\n * @returns An {@link SpanAttributes} to set on a span.\n */\nfunction toOpenTelemetrySpanAttributes(spanAttributes) {\n    const attributes = {};\n    for (const key in spanAttributes) {\n        // Any non-nullish value is allowed.\n        if (spanAttributes[key] !== null && spanAttributes[key] !== undefined) {\n            attributes[key] = spanAttributes[key];\n        }\n    }\n    return attributes;\n}\n/**\n * Converts core-tracing span options to OpenTelemetry options.\n *\n * @param spanOptions - The {@link InstrumenterSpanOptions} to convert.\n * @returns An OpenTelemetry {@link SpanOptions} that can be used when creating a span.\n */\nfunction toSpanOptions(spanOptions) {\n    const { spanAttributes, spanLinks, spanKind } = spanOptions || {};\n    const attributes = toOpenTelemetrySpanAttributes(spanAttributes);\n    const kind = toOpenTelemetrySpanKind(spanKind);\n    const links = toOpenTelemetryLinks(spanLinks);\n    return {\n        attributes,\n        kind,\n        links,\n    };\n}\n//# sourceMappingURL=transformations.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/opentelemetry-instrumentation-azure-sdk/dist-esm/src/transformations.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/aborterUtils.js":
/*!********************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/aborterUtils.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cancelablePromiseRace: () => (/* binding */ cancelablePromiseRace)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n/**\n * promise.race() wrapper that aborts rest of promises as soon as the first promise settles.\n */\nasync function cancelablePromiseRace(abortablePromiseBuilders, options) {\n    var _a, _b;\n    const aborter = new AbortController();\n    function abortHandler() {\n        aborter.abort();\n    }\n    (_a = options === null || options === void 0 ? void 0 : options.abortSignal) === null || _a === void 0 ? void 0 : _a.addEventListener(\"abort\", abortHandler);\n    try {\n        return await Promise.race(abortablePromiseBuilders.map((p) => p({ abortSignal: aborter.signal })));\n    }\n    finally {\n        aborter.abort();\n        (_b = options === null || options === void 0 ? void 0 : options.abortSignal) === null || _b === void 0 ? void 0 : _b.removeEventListener(\"abort\", abortHandler);\n    }\n}\n//# sourceMappingURL=aborterUtils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL2Fib3J0ZXJVdGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwRUFBMEUsNkJBQTZCO0FBQ3ZHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL2Fib3J0ZXJVdGlscy5qcz9mYmEzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuLyoqXG4gKiBwcm9taXNlLnJhY2UoKSB3cmFwcGVyIHRoYXQgYWJvcnRzIHJlc3Qgb2YgcHJvbWlzZXMgYXMgc29vbiBhcyB0aGUgZmlyc3QgcHJvbWlzZSBzZXR0bGVzLlxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY2FuY2VsYWJsZVByb21pc2VSYWNlKGFib3J0YWJsZVByb21pc2VCdWlsZGVycywgb3B0aW9ucykge1xuICAgIHZhciBfYSwgX2I7XG4gICAgY29uc3QgYWJvcnRlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICBmdW5jdGlvbiBhYm9ydEhhbmRsZXIoKSB7XG4gICAgICAgIGFib3J0ZXIuYWJvcnQoKTtcbiAgICB9XG4gICAgKF9hID0gb3B0aW9ucyA9PT0gbnVsbCB8fCBvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRpb25zLmFib3J0U2lnbmFsKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuYWRkRXZlbnRMaXN0ZW5lcihcImFib3J0XCIsIGFib3J0SGFuZGxlcik7XG4gICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIGF3YWl0IFByb21pc2UucmFjZShhYm9ydGFibGVQcm9taXNlQnVpbGRlcnMubWFwKChwKSA9PiBwKHsgYWJvcnRTaWduYWw6IGFib3J0ZXIuc2lnbmFsIH0pKSk7XG4gICAgfVxuICAgIGZpbmFsbHkge1xuICAgICAgICBhYm9ydGVyLmFib3J0KCk7XG4gICAgICAgIChfYiA9IG9wdGlvbnMgPT09IG51bGwgfHwgb3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0aW9ucy5hYm9ydFNpZ25hbCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJhYm9ydFwiLCBhYm9ydEhhbmRsZXIpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFib3J0ZXJVdGlscy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/aborterUtils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/bytesEncoding.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/bytesEncoding.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringToUint8Array: () => (/* binding */ stringToUint8Array),\n/* harmony export */   uint8ArrayToString: () => (/* binding */ uint8ArrayToString)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n/**\n * The helper that transforms bytes with specific character encoding into string\n * @param bytes - the uint8array bytes\n * @param format - the format we use to encode the byte\n * @returns a string of the encoded string\n */\nfunction uint8ArrayToString(bytes, format) {\n    return Buffer.from(bytes).toString(format);\n}\n/**\n * The helper that transforms string to specific character encoded bytes array.\n * @param value - the string to be converted\n * @param format - the format we use to decode the value\n * @returns a uint8array\n */\nfunction stringToUint8Array(value, format) {\n    return Buffer.from(value, format);\n}\n//# sourceMappingURL=bytesEncoding.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL2J5dGVzRW5jb2RpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL0BhenVyZS9jb3JlLXV0aWwvZGlzdC9lc20vYnl0ZXNFbmNvZGluZy5qcz9kYjQ5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuLyoqXG4gKiBUaGUgaGVscGVyIHRoYXQgdHJhbnNmb3JtcyBieXRlcyB3aXRoIHNwZWNpZmljIGNoYXJhY3RlciBlbmNvZGluZyBpbnRvIHN0cmluZ1xuICogQHBhcmFtIGJ5dGVzIC0gdGhlIHVpbnQ4YXJyYXkgYnl0ZXNcbiAqIEBwYXJhbSBmb3JtYXQgLSB0aGUgZm9ybWF0IHdlIHVzZSB0byBlbmNvZGUgdGhlIGJ5dGVcbiAqIEByZXR1cm5zIGEgc3RyaW5nIG9mIHRoZSBlbmNvZGVkIHN0cmluZ1xuICovXG5leHBvcnQgZnVuY3Rpb24gdWludDhBcnJheVRvU3RyaW5nKGJ5dGVzLCBmb3JtYXQpIHtcbiAgICByZXR1cm4gQnVmZmVyLmZyb20oYnl0ZXMpLnRvU3RyaW5nKGZvcm1hdCk7XG59XG4vKipcbiAqIFRoZSBoZWxwZXIgdGhhdCB0cmFuc2Zvcm1zIHN0cmluZyB0byBzcGVjaWZpYyBjaGFyYWN0ZXIgZW5jb2RlZCBieXRlcyBhcnJheS5cbiAqIEBwYXJhbSB2YWx1ZSAtIHRoZSBzdHJpbmcgdG8gYmUgY29udmVydGVkXG4gKiBAcGFyYW0gZm9ybWF0IC0gdGhlIGZvcm1hdCB3ZSB1c2UgdG8gZGVjb2RlIHRoZSB2YWx1ZVxuICogQHJldHVybnMgYSB1aW50OGFycmF5XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJpbmdUb1VpbnQ4QXJyYXkodmFsdWUsIGZvcm1hdCkge1xuICAgIHJldHVybiBCdWZmZXIuZnJvbSh2YWx1ZSwgZm9ybWF0KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJ5dGVzRW5jb2RpbmcuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/bytesEncoding.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/checkEnvironment.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/checkEnvironment.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isBun: () => (/* binding */ isBun),\n/* harmony export */   isDeno: () => (/* binding */ isDeno),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isNodeLike: () => (/* binding */ isNodeLike),\n/* harmony export */   isNodeRuntime: () => (/* binding */ isNodeRuntime),\n/* harmony export */   isReactNative: () => (/* binding */ isReactNative),\n/* harmony export */   isWebWorker: () => (/* binding */ isWebWorker)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nvar _a, _b, _c, _d;\n/**\n * A constant that indicates whether the environment the code is running is a Web Browser.\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-no-window\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\n/**\n * A constant that indicates whether the environment the code is running is a Web Worker.\n */\nconst isWebWorker = typeof self === \"object\" &&\n    typeof (self === null || self === void 0 ? void 0 : self.importScripts) === \"function\" &&\n    (((_a = self.constructor) === null || _a === void 0 ? void 0 : _a.name) === \"DedicatedWorkerGlobalScope\" ||\n        ((_b = self.constructor) === null || _b === void 0 ? void 0 : _b.name) === \"ServiceWorkerGlobalScope\" ||\n        ((_c = self.constructor) === null || _c === void 0 ? void 0 : _c.name) === \"SharedWorkerGlobalScope\");\n/**\n * A constant that indicates whether the environment the code is running is Deno.\n */\nconst isDeno = typeof Deno !== \"undefined\" &&\n    typeof Deno.version !== \"undefined\" &&\n    typeof Deno.version.deno !== \"undefined\";\n/**\n * A constant that indicates whether the environment the code is running is Bun.sh.\n */\nconst isBun = typeof Bun !== \"undefined\" && typeof Bun.version !== \"undefined\";\n/**\n * A constant that indicates whether the environment the code is running is a Node.js compatible environment.\n */\nconst isNodeLike = typeof globalThis.process !== \"undefined\" &&\n    Boolean(globalThis.process.version) &&\n    Boolean((_d = globalThis.process.versions) === null || _d === void 0 ? void 0 : _d.node);\n/**\n * A constant that indicates whether the environment the code is running is a Node.js compatible environment.\n * @deprecated Use `isNodeLike` instead.\n */\nconst isNode = isNodeLike;\n/**\n * A constant that indicates whether the environment the code is running is Node.JS.\n */\nconst isNodeRuntime = isNodeLike && !isBun && !isDeno;\n/**\n * A constant that indicates whether the environment the code is running is in React-Native.\n */\n// https://github.com/facebook/react-native/blob/main/packages/react-native/Libraries/Core/setUpNavigator.js\nconst isReactNative = typeof navigator !== \"undefined\" && (navigator === null || navigator === void 0 ? void 0 : navigator.product) === \"ReactNative\";\n//# sourceMappingURL=checkEnvironment.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/checkEnvironment.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/createAbortablePromise.js":
/*!******************************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/createAbortablePromise.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAbortablePromise: () => (/* binding */ createAbortablePromise)\n/* harmony export */ });\n/* harmony import */ var _azure_abort_controller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @azure/abort-controller */ \"(instrument)/../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/dist/esm/index.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Creates an abortable promise.\n * @param buildPromise - A function that takes the resolve and reject functions as parameters.\n * @param options - The options for the abortable promise.\n * @returns A promise that can be aborted.\n */\nfunction createAbortablePromise(buildPromise, options) {\n    const { cleanupBeforeAbort, abortSignal, abortErrorMsg } = options !== null && options !== void 0 ? options : {};\n    return new Promise((resolve, reject) => {\n        function rejectOnAbort() {\n            reject(new _azure_abort_controller__WEBPACK_IMPORTED_MODULE_0__.AbortError(abortErrorMsg !== null && abortErrorMsg !== void 0 ? abortErrorMsg : \"The operation was aborted.\"));\n        }\n        function removeListeners() {\n            abortSignal === null || abortSignal === void 0 ? void 0 : abortSignal.removeEventListener(\"abort\", onAbort);\n        }\n        function onAbort() {\n            cleanupBeforeAbort === null || cleanupBeforeAbort === void 0 ? void 0 : cleanupBeforeAbort();\n            removeListeners();\n            rejectOnAbort();\n        }\n        if (abortSignal === null || abortSignal === void 0 ? void 0 : abortSignal.aborted) {\n            return rejectOnAbort();\n        }\n        try {\n            buildPromise((x) => {\n                removeListeners();\n                resolve(x);\n            }, (x) => {\n                removeListeners();\n                reject(x);\n            });\n        }\n        catch (err) {\n            reject(err);\n        }\n        abortSignal === null || abortSignal === void 0 ? void 0 : abortSignal.addEventListener(\"abort\", onAbort);\n    });\n}\n//# sourceMappingURL=createAbortablePromise.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL2NyZWF0ZUFib3J0YWJsZVByb21pc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ3FEO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsWUFBWSxpREFBaUQ7QUFDN0Q7QUFDQTtBQUNBLHVCQUF1QiwrREFBVTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uLi8uLi9ub2RlX21vZHVsZXMvQGF6dXJlL2NvcmUtdXRpbC9kaXN0L2VzbS9jcmVhdGVBYm9ydGFibGVQcm9taXNlLmpzPzZmZTQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5pbXBvcnQgeyBBYm9ydEVycm9yIH0gZnJvbSBcIkBhenVyZS9hYm9ydC1jb250cm9sbGVyXCI7XG4vKipcbiAqIENyZWF0ZXMgYW4gYWJvcnRhYmxlIHByb21pc2UuXG4gKiBAcGFyYW0gYnVpbGRQcm9taXNlIC0gQSBmdW5jdGlvbiB0aGF0IHRha2VzIHRoZSByZXNvbHZlIGFuZCByZWplY3QgZnVuY3Rpb25zIGFzIHBhcmFtZXRlcnMuXG4gKiBAcGFyYW0gb3B0aW9ucyAtIFRoZSBvcHRpb25zIGZvciB0aGUgYWJvcnRhYmxlIHByb21pc2UuXG4gKiBAcmV0dXJucyBBIHByb21pc2UgdGhhdCBjYW4gYmUgYWJvcnRlZC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUFib3J0YWJsZVByb21pc2UoYnVpbGRQcm9taXNlLCBvcHRpb25zKSB7XG4gICAgY29uc3QgeyBjbGVhbnVwQmVmb3JlQWJvcnQsIGFib3J0U2lnbmFsLCBhYm9ydEVycm9yTXNnIH0gPSBvcHRpb25zICE9PSBudWxsICYmIG9wdGlvbnMgIT09IHZvaWQgMCA/IG9wdGlvbnMgOiB7fTtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICBmdW5jdGlvbiByZWplY3RPbkFib3J0KCkge1xuICAgICAgICAgICAgcmVqZWN0KG5ldyBBYm9ydEVycm9yKGFib3J0RXJyb3JNc2cgIT09IG51bGwgJiYgYWJvcnRFcnJvck1zZyAhPT0gdm9pZCAwID8gYWJvcnRFcnJvck1zZyA6IFwiVGhlIG9wZXJhdGlvbiB3YXMgYWJvcnRlZC5cIikpO1xuICAgICAgICB9XG4gICAgICAgIGZ1bmN0aW9uIHJlbW92ZUxpc3RlbmVycygpIHtcbiAgICAgICAgICAgIGFib3J0U2lnbmFsID09PSBudWxsIHx8IGFib3J0U2lnbmFsID09PSB2b2lkIDAgPyB2b2lkIDAgOiBhYm9ydFNpZ25hbC5yZW1vdmVFdmVudExpc3RlbmVyKFwiYWJvcnRcIiwgb25BYm9ydCk7XG4gICAgICAgIH1cbiAgICAgICAgZnVuY3Rpb24gb25BYm9ydCgpIHtcbiAgICAgICAgICAgIGNsZWFudXBCZWZvcmVBYm9ydCA9PT0gbnVsbCB8fCBjbGVhbnVwQmVmb3JlQWJvcnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGNsZWFudXBCZWZvcmVBYm9ydCgpO1xuICAgICAgICAgICAgcmVtb3ZlTGlzdGVuZXJzKCk7XG4gICAgICAgICAgICByZWplY3RPbkFib3J0KCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGFib3J0U2lnbmFsID09PSBudWxsIHx8IGFib3J0U2lnbmFsID09PSB2b2lkIDAgPyB2b2lkIDAgOiBhYm9ydFNpZ25hbC5hYm9ydGVkKSB7XG4gICAgICAgICAgICByZXR1cm4gcmVqZWN0T25BYm9ydCgpO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBidWlsZFByb21pc2UoKHgpID0+IHtcbiAgICAgICAgICAgICAgICByZW1vdmVMaXN0ZW5lcnMoKTtcbiAgICAgICAgICAgICAgICByZXNvbHZlKHgpO1xuICAgICAgICAgICAgfSwgKHgpID0+IHtcbiAgICAgICAgICAgICAgICByZW1vdmVMaXN0ZW5lcnMoKTtcbiAgICAgICAgICAgICAgICByZWplY3QoeCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICByZWplY3QoZXJyKTtcbiAgICAgICAgfVxuICAgICAgICBhYm9ydFNpZ25hbCA9PT0gbnVsbCB8fCBhYm9ydFNpZ25hbCA9PT0gdm9pZCAwID8gdm9pZCAwIDogYWJvcnRTaWduYWwuYWRkRXZlbnRMaXN0ZW5lcihcImFib3J0XCIsIG9uQWJvcnQpO1xuICAgIH0pO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y3JlYXRlQWJvcnRhYmxlUHJvbWlzZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/createAbortablePromise.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/delay.js":
/*!*************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/delay.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateRetryDelay: () => (/* binding */ calculateRetryDelay),\n/* harmony export */   delay: () => (/* binding */ delay)\n/* harmony export */ });\n/* harmony import */ var _createAbortablePromise_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createAbortablePromise.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/createAbortablePromise.js\");\n/* harmony import */ var _random_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./random.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/random.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n\nconst StandardAbortMessage = \"The delay was aborted.\";\n/**\n * A wrapper for setTimeout that resolves a promise after timeInMs milliseconds.\n * @param timeInMs - The number of milliseconds to be delayed.\n * @param options - The options for delay - currently abort options\n * @returns Promise that is resolved after timeInMs\n */\nfunction delay(timeInMs, options) {\n    let token;\n    const { abortSignal, abortErrorMsg } = options !== null && options !== void 0 ? options : {};\n    return (0,_createAbortablePromise_js__WEBPACK_IMPORTED_MODULE_0__.createAbortablePromise)((resolve) => {\n        token = setTimeout(resolve, timeInMs);\n    }, {\n        cleanupBeforeAbort: () => clearTimeout(token),\n        abortSignal,\n        abortErrorMsg: abortErrorMsg !== null && abortErrorMsg !== void 0 ? abortErrorMsg : StandardAbortMessage,\n    });\n}\n/**\n * Calculates the delay interval for retry attempts using exponential delay with jitter.\n * @param retryAttempt - The current retry attempt number.\n * @param config - The exponential retry configuration.\n * @returns An object containing the calculated retry delay.\n */\nfunction calculateRetryDelay(retryAttempt, config) {\n    // Exponentially increase the delay each time\n    const exponentialDelay = config.retryDelayInMs * Math.pow(2, retryAttempt);\n    // Don't let the delay exceed the maximum\n    const clampedDelay = Math.min(config.maxRetryDelayInMs, exponentialDelay);\n    // Allow the final value to have some \"jitter\" (within 50% of the delay size) so\n    // that retries across multiple clients don't occur simultaneously.\n    const retryAfterInMs = clampedDelay / 2 + (0,_random_js__WEBPACK_IMPORTED_MODULE_1__.getRandomIntegerInclusive)(0, clampedDelay / 2);\n    return { retryAfterInMs };\n}\n//# sourceMappingURL=delay.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/delay.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/error.js":
/*!*************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/error.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isError: () => (/* binding */ isError)\n/* harmony export */ });\n/* harmony import */ var _object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./object.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/object.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Typeguard for an error object shape (has name and message)\n * @param e - Something caught by a catch clause.\n */\nfunction isError(e) {\n    if ((0,_object_js__WEBPACK_IMPORTED_MODULE_0__.isObject)(e)) {\n        const hasName = typeof e.name === \"string\";\n        const hasMessage = typeof e.message === \"string\";\n        return hasName && hasMessage;\n    }\n    return false;\n}\n/**\n * Given what is thought to be an error object, return the message if possible.\n * If the message is missing, returns a stringified version of the input.\n * @param e - Something thrown from a try block\n * @returns The error message or a string of the input\n */\nfunction getErrorMessage(e) {\n    if (isError(e)) {\n        return e.message;\n    }\n    else {\n        let stringified;\n        try {\n            if (typeof e === \"object\" && e) {\n                stringified = JSON.stringify(e);\n            }\n            else {\n                stringified = String(e);\n            }\n        }\n        catch (err) {\n            stringified = \"[unable to stringify input]\";\n        }\n        return `Unknown error ${stringified}`;\n    }\n}\n//# sourceMappingURL=error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/error.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/index.js":
/*!*************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateRetryDelay: () => (/* reexport safe */ _delay_js__WEBPACK_IMPORTED_MODULE_0__.calculateRetryDelay),\n/* harmony export */   cancelablePromiseRace: () => (/* reexport safe */ _aborterUtils_js__WEBPACK_IMPORTED_MODULE_1__.cancelablePromiseRace),\n/* harmony export */   computeSha256Hash: () => (/* reexport safe */ _sha256_js__WEBPACK_IMPORTED_MODULE_6__.computeSha256Hash),\n/* harmony export */   computeSha256Hmac: () => (/* reexport safe */ _sha256_js__WEBPACK_IMPORTED_MODULE_6__.computeSha256Hmac),\n/* harmony export */   createAbortablePromise: () => (/* reexport safe */ _createAbortablePromise_js__WEBPACK_IMPORTED_MODULE_2__.createAbortablePromise),\n/* harmony export */   delay: () => (/* reexport safe */ _delay_js__WEBPACK_IMPORTED_MODULE_0__.delay),\n/* harmony export */   getErrorMessage: () => (/* reexport safe */ _error_js__WEBPACK_IMPORTED_MODULE_5__.getErrorMessage),\n/* harmony export */   getRandomIntegerInclusive: () => (/* reexport safe */ _random_js__WEBPACK_IMPORTED_MODULE_3__.getRandomIntegerInclusive),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__.isBrowser),\n/* harmony export */   isBun: () => (/* reexport safe */ _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__.isBun),\n/* harmony export */   isDefined: () => (/* reexport safe */ _typeGuards_js__WEBPACK_IMPORTED_MODULE_7__.isDefined),\n/* harmony export */   isDeno: () => (/* reexport safe */ _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__.isDeno),\n/* harmony export */   isError: () => (/* reexport safe */ _error_js__WEBPACK_IMPORTED_MODULE_5__.isError),\n/* harmony export */   isNode: () => (/* reexport safe */ _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__.isNode),\n/* harmony export */   isNodeLike: () => (/* reexport safe */ _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__.isNodeLike),\n/* harmony export */   isNodeRuntime: () => (/* reexport safe */ _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__.isNodeRuntime),\n/* harmony export */   isObject: () => (/* reexport safe */ _object_js__WEBPACK_IMPORTED_MODULE_4__.isObject),\n/* harmony export */   isObjectWithProperties: () => (/* reexport safe */ _typeGuards_js__WEBPACK_IMPORTED_MODULE_7__.isObjectWithProperties),\n/* harmony export */   isReactNative: () => (/* reexport safe */ _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__.isReactNative),\n/* harmony export */   isWebWorker: () => (/* reexport safe */ _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__.isWebWorker),\n/* harmony export */   objectHasProperty: () => (/* reexport safe */ _typeGuards_js__WEBPACK_IMPORTED_MODULE_7__.objectHasProperty),\n/* harmony export */   randomUUID: () => (/* reexport safe */ _uuidUtils_js__WEBPACK_IMPORTED_MODULE_8__.randomUUID),\n/* harmony export */   stringToUint8Array: () => (/* reexport safe */ _bytesEncoding_js__WEBPACK_IMPORTED_MODULE_10__.stringToUint8Array),\n/* harmony export */   uint8ArrayToString: () => (/* reexport safe */ _bytesEncoding_js__WEBPACK_IMPORTED_MODULE_10__.uint8ArrayToString)\n/* harmony export */ });\n/* harmony import */ var _delay_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./delay.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/delay.js\");\n/* harmony import */ var _aborterUtils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./aborterUtils.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/aborterUtils.js\");\n/* harmony import */ var _createAbortablePromise_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createAbortablePromise.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/createAbortablePromise.js\");\n/* harmony import */ var _random_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./random.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/random.js\");\n/* harmony import */ var _object_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./object.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/object.js\");\n/* harmony import */ var _error_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./error.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/error.js\");\n/* harmony import */ var _sha256_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./sha256.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/sha256.js\");\n/* harmony import */ var _typeGuards_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./typeGuards.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/typeGuards.js\");\n/* harmony import */ var _uuidUtils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./uuidUtils.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/uuidUtils.js\");\n/* harmony import */ var _checkEnvironment_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./checkEnvironment.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/checkEnvironment.js\");\n/* harmony import */ var _bytesEncoding_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./bytesEncoding.js */ \"(instrument)/../../node_modules/@azure/core-util/dist/esm/bytesEncoding.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n\n\n\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUN3RDtBQUNHO0FBQ1c7QUFDZDtBQUNqQjtBQUNlO0FBQ2E7QUFDb0I7QUFDM0M7QUFDcUY7QUFDckQ7QUFDNUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL0BhenVyZS9jb3JlLXV0aWwvZGlzdC9lc20vaW5kZXguanM/ZTkyNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cbmV4cG9ydCB7IGRlbGF5LCBjYWxjdWxhdGVSZXRyeURlbGF5IH0gZnJvbSBcIi4vZGVsYXkuanNcIjtcbmV4cG9ydCB7IGNhbmNlbGFibGVQcm9taXNlUmFjZSwgfSBmcm9tIFwiLi9hYm9ydGVyVXRpbHMuanNcIjtcbmV4cG9ydCB7IGNyZWF0ZUFib3J0YWJsZVByb21pc2UsIH0gZnJvbSBcIi4vY3JlYXRlQWJvcnRhYmxlUHJvbWlzZS5qc1wiO1xuZXhwb3J0IHsgZ2V0UmFuZG9tSW50ZWdlckluY2x1c2l2ZSB9IGZyb20gXCIuL3JhbmRvbS5qc1wiO1xuZXhwb3J0IHsgaXNPYmplY3QgfSBmcm9tIFwiLi9vYmplY3QuanNcIjtcbmV4cG9ydCB7IGlzRXJyb3IsIGdldEVycm9yTWVzc2FnZSB9IGZyb20gXCIuL2Vycm9yLmpzXCI7XG5leHBvcnQgeyBjb21wdXRlU2hhMjU2SGFzaCwgY29tcHV0ZVNoYTI1NkhtYWMgfSBmcm9tIFwiLi9zaGEyNTYuanNcIjtcbmV4cG9ydCB7IGlzRGVmaW5lZCwgaXNPYmplY3RXaXRoUHJvcGVydGllcywgb2JqZWN0SGFzUHJvcGVydHkgfSBmcm9tIFwiLi90eXBlR3VhcmRzLmpzXCI7XG5leHBvcnQgeyByYW5kb21VVUlEIH0gZnJvbSBcIi4vdXVpZFV0aWxzLmpzXCI7XG5leHBvcnQgeyBpc0Jyb3dzZXIsIGlzQnVuLCBpc05vZGUsIGlzTm9kZUxpa2UsIGlzTm9kZVJ1bnRpbWUsIGlzRGVubywgaXNSZWFjdE5hdGl2ZSwgaXNXZWJXb3JrZXIsIH0gZnJvbSBcIi4vY2hlY2tFbnZpcm9ubWVudC5qc1wiO1xuZXhwb3J0IHsgdWludDhBcnJheVRvU3RyaW5nLCBzdHJpbmdUb1VpbnQ4QXJyYXkgfSBmcm9tIFwiLi9ieXRlc0VuY29kaW5nLmpzXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/object.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/object.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObject: () => (/* binding */ isObject)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n/**\n * Helper to determine when an input is a generic JS object.\n * @returns true when input is an object type that is not null, Array, RegExp, or Date.\n */\nfunction isObject(input) {\n    return (typeof input === \"object\" &&\n        input !== null &&\n        !Array.isArray(input) &&\n        !(input instanceof RegExp) &&\n        !(input instanceof Date));\n}\n//# sourceMappingURL=object.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL29iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uLi8uLi9ub2RlX21vZHVsZXMvQGF6dXJlL2NvcmUtdXRpbC9kaXN0L2VzbS9vYmplY3QuanM/ZDA0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cbi8qKlxuICogSGVscGVyIHRvIGRldGVybWluZSB3aGVuIGFuIGlucHV0IGlzIGEgZ2VuZXJpYyBKUyBvYmplY3QuXG4gKiBAcmV0dXJucyB0cnVlIHdoZW4gaW5wdXQgaXMgYW4gb2JqZWN0IHR5cGUgdGhhdCBpcyBub3QgbnVsbCwgQXJyYXksIFJlZ0V4cCwgb3IgRGF0ZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzT2JqZWN0KGlucHV0KSB7XG4gICAgcmV0dXJuICh0eXBlb2YgaW5wdXQgPT09IFwib2JqZWN0XCIgJiZcbiAgICAgICAgaW5wdXQgIT09IG51bGwgJiZcbiAgICAgICAgIUFycmF5LmlzQXJyYXkoaW5wdXQpICYmXG4gICAgICAgICEoaW5wdXQgaW5zdGFuY2VvZiBSZWdFeHApICYmXG4gICAgICAgICEoaW5wdXQgaW5zdGFuY2VvZiBEYXRlKSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1vYmplY3QuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/object.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/random.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/random.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRandomIntegerInclusive: () => (/* binding */ getRandomIntegerInclusive)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n/**\n * Returns a random integer value between a lower and upper bound,\n * inclusive of both bounds.\n * Note that this uses Math.random and isn't secure. If you need to use\n * this for any kind of security purpose, find a better source of random.\n * @param min - The smallest integer value allowed.\n * @param max - The largest integer value allowed.\n */\nfunction getRandomIntegerInclusive(min, max) {\n    // Make sure inputs are integers.\n    min = Math.ceil(min);\n    max = Math.floor(max);\n    // Pick a random offset from zero to the size of the range.\n    // Since Math.random() can never return 1, we have to make the range one larger\n    // in order to be inclusive of the maximum value after we take the floor.\n    const offset = Math.floor(Math.random() * (max - min + 1));\n    return offset + min;\n}\n//# sourceMappingURL=random.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL3JhbmRvbS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL3JhbmRvbS5qcz9hMmM0Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuLyoqXG4gKiBSZXR1cm5zIGEgcmFuZG9tIGludGVnZXIgdmFsdWUgYmV0d2VlbiBhIGxvd2VyIGFuZCB1cHBlciBib3VuZCxcbiAqIGluY2x1c2l2ZSBvZiBib3RoIGJvdW5kcy5cbiAqIE5vdGUgdGhhdCB0aGlzIHVzZXMgTWF0aC5yYW5kb20gYW5kIGlzbid0IHNlY3VyZS4gSWYgeW91IG5lZWQgdG8gdXNlXG4gKiB0aGlzIGZvciBhbnkga2luZCBvZiBzZWN1cml0eSBwdXJwb3NlLCBmaW5kIGEgYmV0dGVyIHNvdXJjZSBvZiByYW5kb20uXG4gKiBAcGFyYW0gbWluIC0gVGhlIHNtYWxsZXN0IGludGVnZXIgdmFsdWUgYWxsb3dlZC5cbiAqIEBwYXJhbSBtYXggLSBUaGUgbGFyZ2VzdCBpbnRlZ2VyIHZhbHVlIGFsbG93ZWQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRSYW5kb21JbnRlZ2VySW5jbHVzaXZlKG1pbiwgbWF4KSB7XG4gICAgLy8gTWFrZSBzdXJlIGlucHV0cyBhcmUgaW50ZWdlcnMuXG4gICAgbWluID0gTWF0aC5jZWlsKG1pbik7XG4gICAgbWF4ID0gTWF0aC5mbG9vcihtYXgpO1xuICAgIC8vIFBpY2sgYSByYW5kb20gb2Zmc2V0IGZyb20gemVybyB0byB0aGUgc2l6ZSBvZiB0aGUgcmFuZ2UuXG4gICAgLy8gU2luY2UgTWF0aC5yYW5kb20oKSBjYW4gbmV2ZXIgcmV0dXJuIDEsIHdlIGhhdmUgdG8gbWFrZSB0aGUgcmFuZ2Ugb25lIGxhcmdlclxuICAgIC8vIGluIG9yZGVyIHRvIGJlIGluY2x1c2l2ZSBvZiB0aGUgbWF4aW11bSB2YWx1ZSBhZnRlciB3ZSB0YWtlIHRoZSBmbG9vci5cbiAgICBjb25zdCBvZmZzZXQgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAobWF4IC0gbWluICsgMSkpO1xuICAgIHJldHVybiBvZmZzZXQgKyBtaW47XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yYW5kb20uanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/random.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/sha256.js":
/*!**************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/sha256.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   computeSha256Hash: () => (/* binding */ computeSha256Hash),\n/* harmony export */   computeSha256Hmac: () => (/* binding */ computeSha256Hmac)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Generates a SHA-256 HMAC signature.\n * @param key - The HMAC key represented as a base64 string, used to generate the cryptographic HMAC hash.\n * @param stringToSign - The data to be signed.\n * @param encoding - The textual encoding to use for the returned HMAC digest.\n */\nasync function computeSha256Hmac(key, stringToSign, encoding) {\n    const decodedKey = Buffer.from(key, \"base64\");\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.createHmac)(\"sha256\", decodedKey).update(stringToSign).digest(encoding);\n}\n/**\n * Generates a SHA-256 hash.\n * @param content - The data to be included in the hash.\n * @param encoding - The textual encoding to use for the returned hash.\n */\nasync function computeSha256Hash(content, encoding) {\n    return (0,crypto__WEBPACK_IMPORTED_MODULE_0__.createHash)(\"sha256\").update(content).digest(encoding);\n}\n//# sourceMappingURL=sha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL3NoYTI1Ni5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ2dEO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxXQUFXLGtEQUFVO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsV0FBVyxrREFBVTtBQUNyQjtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL3NoYTI1Ni5qcz8yZWQ5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuaW1wb3J0IHsgY3JlYXRlSGFzaCwgY3JlYXRlSG1hYyB9IGZyb20gXCJjcnlwdG9cIjtcbi8qKlxuICogR2VuZXJhdGVzIGEgU0hBLTI1NiBITUFDIHNpZ25hdHVyZS5cbiAqIEBwYXJhbSBrZXkgLSBUaGUgSE1BQyBrZXkgcmVwcmVzZW50ZWQgYXMgYSBiYXNlNjQgc3RyaW5nLCB1c2VkIHRvIGdlbmVyYXRlIHRoZSBjcnlwdG9ncmFwaGljIEhNQUMgaGFzaC5cbiAqIEBwYXJhbSBzdHJpbmdUb1NpZ24gLSBUaGUgZGF0YSB0byBiZSBzaWduZWQuXG4gKiBAcGFyYW0gZW5jb2RpbmcgLSBUaGUgdGV4dHVhbCBlbmNvZGluZyB0byB1c2UgZm9yIHRoZSByZXR1cm5lZCBITUFDIGRpZ2VzdC5cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNvbXB1dGVTaGEyNTZIbWFjKGtleSwgc3RyaW5nVG9TaWduLCBlbmNvZGluZykge1xuICAgIGNvbnN0IGRlY29kZWRLZXkgPSBCdWZmZXIuZnJvbShrZXksIFwiYmFzZTY0XCIpO1xuICAgIHJldHVybiBjcmVhdGVIbWFjKFwic2hhMjU2XCIsIGRlY29kZWRLZXkpLnVwZGF0ZShzdHJpbmdUb1NpZ24pLmRpZ2VzdChlbmNvZGluZyk7XG59XG4vKipcbiAqIEdlbmVyYXRlcyBhIFNIQS0yNTYgaGFzaC5cbiAqIEBwYXJhbSBjb250ZW50IC0gVGhlIGRhdGEgdG8gYmUgaW5jbHVkZWQgaW4gdGhlIGhhc2guXG4gKiBAcGFyYW0gZW5jb2RpbmcgLSBUaGUgdGV4dHVhbCBlbmNvZGluZyB0byB1c2UgZm9yIHRoZSByZXR1cm5lZCBoYXNoLlxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY29tcHV0ZVNoYTI1Nkhhc2goY29udGVudCwgZW5jb2RpbmcpIHtcbiAgICByZXR1cm4gY3JlYXRlSGFzaChcInNoYTI1NlwiKS51cGRhdGUoY29udGVudCkuZGlnZXN0KGVuY29kaW5nKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNoYTI1Ni5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/sha256.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/typeGuards.js":
/*!******************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/typeGuards.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   isObjectWithProperties: () => (/* binding */ isObjectWithProperties),\n/* harmony export */   objectHasProperty: () => (/* binding */ objectHasProperty)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n/**\n * Helper TypeGuard that checks if something is defined or not.\n * @param thing - Anything\n */\nfunction isDefined(thing) {\n    return typeof thing !== \"undefined\" && thing !== null;\n}\n/**\n * Helper TypeGuard that checks if the input is an object with the specified properties.\n * @param thing - Anything.\n * @param properties - The name of the properties that should appear in the object.\n */\nfunction isObjectWithProperties(thing, properties) {\n    if (!isDefined(thing) || typeof thing !== \"object\") {\n        return false;\n    }\n    for (const property of properties) {\n        if (!objectHasProperty(thing, property)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Helper TypeGuard that checks if the input is an object with the specified property.\n * @param thing - Any object.\n * @param property - The name of the property that should appear in the object.\n */\nfunction objectHasProperty(thing, property) {\n    return (isDefined(thing) && typeof thing === \"object\" && property in thing);\n}\n//# sourceMappingURL=typeGuards.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/typeGuards.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/dist/esm/uuidUtils.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/@azure/core-util/dist/esm/uuidUtils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   randomUUID: () => (/* binding */ randomUUID)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nvar _a;\n\n// NOTE: This is a workaround until we can use `globalThis.crypto.randomUUID` in Node.js 19+.\nconst uuidFunction = typeof ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.crypto) === null || _a === void 0 ? void 0 : _a.randomUUID) === \"function\"\n    ? globalThis.crypto.randomUUID.bind(globalThis.crypto)\n    : crypto__WEBPACK_IMPORTED_MODULE_0__.randomUUID;\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nfunction randomUUID() {\n    return uuidFunction();\n}\n//# sourceMappingURL=uuidUtils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL2Rpc3QvZXNtL3V1aWRVdGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQSxNQUFNLDhDQUFZO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uLi8uLi9ub2RlX21vZHVsZXMvQGF6dXJlL2NvcmUtdXRpbC9kaXN0L2VzbS91dWlkVXRpbHMuanM/ZTA3NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cbnZhciBfYTtcbmltcG9ydCB7IHJhbmRvbVVVSUQgYXMgdjRSYW5kb21VVUlEIH0gZnJvbSBcImNyeXB0b1wiO1xuLy8gTk9URTogVGhpcyBpcyBhIHdvcmthcm91bmQgdW50aWwgd2UgY2FuIHVzZSBgZ2xvYmFsVGhpcy5jcnlwdG8ucmFuZG9tVVVJRGAgaW4gTm9kZS5qcyAxOSsuXG5jb25zdCB1dWlkRnVuY3Rpb24gPSB0eXBlb2YgKChfYSA9IGdsb2JhbFRoaXMgPT09IG51bGwgfHwgZ2xvYmFsVGhpcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogZ2xvYmFsVGhpcy5jcnlwdG8pID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5yYW5kb21VVUlEKSA9PT0gXCJmdW5jdGlvblwiXG4gICAgPyBnbG9iYWxUaGlzLmNyeXB0by5yYW5kb21VVUlELmJpbmQoZ2xvYmFsVGhpcy5jcnlwdG8pXG4gICAgOiB2NFJhbmRvbVVVSUQ7XG4vKipcbiAqIEdlbmVyYXRlZCBVbml2ZXJzYWxseSBVbmlxdWUgSWRlbnRpZmllclxuICpcbiAqIEByZXR1cm5zIFJGQzQxMjIgdjQgVVVJRC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJhbmRvbVVVSUQoKSB7XG4gICAgcmV0dXJuIHV1aWRGdW5jdGlvbigpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXVpZFV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/dist/esm/uuidUtils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/dist/esm/AbortError.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/dist/esm/AbortError.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortError: () => (/* binding */ AbortError)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n/**\n * This error is thrown when an asynchronous operation has been aborted.\n * Check for this error by testing the `name` that the name property of the\n * error matches `\"AbortError\"`.\n *\n * @example\n * ```ts\n * const controller = new AbortController();\n * controller.abort();\n * try {\n *   doAsyncWork(controller.signal)\n * } catch (e) {\n *   if (e.name === 'AbortError') {\n *     // handle abort error here.\n *   }\n * }\n * ```\n */\nclass AbortError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = \"AbortError\";\n    }\n}\n//# sourceMappingURL=AbortError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL25vZGVfbW9kdWxlcy9AYXp1cmUvYWJvcnQtY29udHJvbGxlci9kaXN0L2VzbS9BYm9ydEVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovLy8uLi8uLi9ub2RlX21vZHVsZXMvQGF6dXJlL2NvcmUtdXRpbC9ub2RlX21vZHVsZXMvQGF6dXJlL2Fib3J0LWNvbnRyb2xsZXIvZGlzdC9lc20vQWJvcnRFcnJvci5qcz85ZjcyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxuLyoqXG4gKiBUaGlzIGVycm9yIGlzIHRocm93biB3aGVuIGFuIGFzeW5jaHJvbm91cyBvcGVyYXRpb24gaGFzIGJlZW4gYWJvcnRlZC5cbiAqIENoZWNrIGZvciB0aGlzIGVycm9yIGJ5IHRlc3RpbmcgdGhlIGBuYW1lYCB0aGF0IHRoZSBuYW1lIHByb3BlcnR5IG9mIHRoZVxuICogZXJyb3IgbWF0Y2hlcyBgXCJBYm9ydEVycm9yXCJgLlxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c1xuICogY29uc3QgY29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAqIGNvbnRyb2xsZXIuYWJvcnQoKTtcbiAqIHRyeSB7XG4gKiAgIGRvQXN5bmNXb3JrKGNvbnRyb2xsZXIuc2lnbmFsKVxuICogfSBjYXRjaCAoZSkge1xuICogICBpZiAoZS5uYW1lID09PSAnQWJvcnRFcnJvcicpIHtcbiAqICAgICAvLyBoYW5kbGUgYWJvcnQgZXJyb3IgaGVyZS5cbiAqICAgfVxuICogfVxuICogYGBgXG4gKi9cbmV4cG9ydCBjbGFzcyBBYm9ydEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9IFwiQWJvcnRFcnJvclwiO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUFib3J0RXJyb3IuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/dist/esm/AbortError.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/dist/esm/index.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/dist/esm/index.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortError: () => (/* reexport safe */ _AbortError_js__WEBPACK_IMPORTED_MODULE_0__.AbortError)\n/* harmony export */ });\n/* harmony import */ var _AbortError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AbortError.js */ \"(instrument)/../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/dist/esm/AbortError.js\");\n// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy9AYXp1cmUvY29yZS11dGlsL25vZGVfbW9kdWxlcy9AYXp1cmUvYWJvcnQtY29udHJvbGxlci9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDNkM7QUFDN0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi4vLi4vbm9kZV9tb2R1bGVzL0BhenVyZS9jb3JlLXV0aWwvbm9kZV9tb2R1bGVzL0BhenVyZS9hYm9ydC1jb250cm9sbGVyL2Rpc3QvZXNtL2luZGV4LmpzPzM1MmEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UuXG5leHBvcnQgeyBBYm9ydEVycm9yIH0gZnJvbSBcIi4vQWJvcnRFcnJvci5qc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/@azure/core-util/node_modules/@azure/abort-controller/dist/esm/index.js\n");

/***/ })

};
;